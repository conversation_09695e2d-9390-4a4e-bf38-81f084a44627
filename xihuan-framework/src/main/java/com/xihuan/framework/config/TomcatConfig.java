package com.xihuan.framework.config;

import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Tomcat配置类
 * 解决CVE-2024-56337相关的启动问题
 * 
 * <AUTHOR>
 */
@Configuration
public class TomcatConfig {

    /**
     * 配置Tomcat以解决CVE-2024-56337相关问题
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            // 设置系统属性以禁用文件名缓存
            System.setProperty("org.apache.catalina.webresources.StandardRoot.CACHE_CANONICAL_PATHS", "false");
            
            // 添加JVM参数来解决安全漏洞相关问题
            factory.addConnectorCustomizers(connector -> {
                // 可以在这里添加更多的连接器自定义配置
            });
        };
    }
}
