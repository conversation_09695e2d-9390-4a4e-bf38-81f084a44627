package com.xihuan.framework.web.service;

import com.alibaba.fastjson2.JSONObject;
import com.xihuan.common.constant.CacheConstants;
import com.xihuan.common.constant.Constants;
import com.xihuan.common.constant.UserConstants;
import com.xihuan.common.core.domain.entity.SysUser;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.core.redis.RedisCache;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.exception.user.*;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.MessageUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.common.utils.ip.IpUtils;
import com.xihuan.common.utils.wechat.WxUtils;
import com.xihuan.framework.manager.AsyncManager;
import com.xihuan.framework.manager.factory.AsyncFactory;
import com.xihuan.framework.security.context.AuthenticationContextHolder;
import com.xihuan.system.service.ISysConfigService;
import com.xihuan.system.service.ISysRoleService;
import com.xihuan.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private WxUtils wxUtils;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private SysPermissionService permissionService;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            if (captcha == null) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            redisCache.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha)) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        System.out.println( password);
        System.out.println(username);
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 创建或获取用户，并返回登录token
     *
     * @param username 用户名
     * @param phone 手机号
     * @param openId 微信openId
     * @param nickname 昵称
     * @param loginType 登录类型（用于日志记录）
     * @return token
     */
    private String createOrGetUserAndLogin(String username, String phone, String openId, String nickname, String loginType) {
        // 1. 查询用户是否存在
        SysUser user = null;
        if (StringUtils.isNotEmpty(phone)) {
            user = userService.selectUserByPhone(phone);
        } else if (StringUtils.isNotEmpty(openId)) {
            user = userService.selectUserByOpenId(openId);
        }

        // 2. 用户不存在则自动注册
        if (user == null) {
            user = new SysUser();
            user.setUserName(username);
            user.setPhonenumber(phone);
            user.setWxOpenId(openId);
            user.setPassword(new BCryptPasswordEncoder().encode(username));
            user.setDeptId(200L);
            user.setNickName(nickname);
            user.setStatus("0");
            if (StringUtils.isEmpty(nickname)) {
                user.setNickName(StringUtils.isNotEmpty(phone) ? "手机用户" : "微信用户");
            }
            if (StringUtils.isEmpty(username)) {
                user.setUserName(StringUtils.isNotEmpty(phone) ? phone : openId);
            }

            // 设置默认角色为小程序用户角色
            Long[] roleIds = {2L}; // 2L为小程序用户角色ID
            user.setRoleIds(roleIds);

            // 使用insertUser方法，会自动处理角色关联
            userService.insertUser(user);
        }

        // 3. 记录登录信息
        recordLoginInfo(user.getUserId());
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, loginType + "登录成功"));

        // 4. 获取用户权限
        Set<String> permissions = permissionService.getMenuPermission(user);

        // 5. 生成token
        LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(), user, permissions);
        return tokenService.createToken(loginUser);
    }

    /**
     * 手机验证码登录
     *
     * @param phone 手机号
     * @param code  验证码
     * @return token
     */
    public String phoneLogin(String phone, String code) {
        // 1. 校验验证码
        validateSmsCode(phone, code);
        // 2. 创建或获取用户并登录
        return createOrGetUserAndLogin(phone, phone, null, null, "手机验证码");
    }

    /**
     * 微信手机号登录
     *
     * @param code 手机号获取凭证
     * @return token
     */
    public String wxPhoneLogin(String code) {
        // 1. 通过code获取手机号
        JSONObject phoneInfo = wxUtils.getPhoneNumber(code);
        if (phoneInfo == null || !phoneInfo.containsKey("phone_info")) {
            throw new ServiceException("获取手机号失败");
        }
        String phoneNumber = phoneInfo.getJSONObject("phone_info").getString("phoneNumber");

        // 2. 创建或获取用户并登录
        return createOrGetUserAndLogin(phoneNumber, phoneNumber, null, null, "微信手机号");
    }

    /**
     * 微信登录
     *
     * @param code 微信临时code
     * @return token
     */
    public String wxLogin(String code) {
        // 1. 通过code换取openid
        JSONObject sessionInfo = wxUtils.code2Session(code);
        String openId = sessionInfo.getString("openid");
        
        // 2. 创建或获取用户并登录
        return createOrGetUserAndLogin(null, null, openId, null, "微信");
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    /**
     * 发送短信验证码
     *
     * @param phone 手机号
     * @return 是否发送成功
     */
    public boolean sendSmsCode(String phone) {
        // 1. 生成6位验证码
        String code = String.valueOf(100000 + new java.util.Random().nextInt(900000));

        // 2. 存储到Redis，5分钟过期
        String key = getSmsCodeKey(phone);
        redisCache.setCacheObject(key, "570791", 5, TimeUnit.MINUTES);

        // 调用阿里云发送短信（需实现）
//        if(SmsUtil.sendCode(phone, code)){
//            return AjaxResult.success("发送成功");
//        }
        // TODO: 3. 调用短信服务发送验证码
        // 这里暂时返回true，实际需要对接短信服务
        return true;
    }

    /**
     * 校验短信验证码
     *
     * @param phone 手机号
     * @param code  验证码
     */
    private void validateSmsCode(String phone, String code) {
        String key = getSmsCodeKey(phone);
        String cacheCode = redisCache.getCacheObject(key);
        
        if (StringUtils.isEmpty(cacheCode)) {
            throw new ServiceException("验证码已过期");
        }
        
        if (!code.equals(cacheCode)) {
            throw new ServiceException("验证码错误");
        }
        
        // 验证成功后删除缓存
        redisCache.deleteObject(key);
    }

    /**
     * 获取短信验证码的Redis key
     *
     * @param phone 手机号
     * @return Redis key
     */
    private String getSmsCodeKey(String phone) {
        return CacheConstants.SMS_CODE_KEY + phone;
    }
}
