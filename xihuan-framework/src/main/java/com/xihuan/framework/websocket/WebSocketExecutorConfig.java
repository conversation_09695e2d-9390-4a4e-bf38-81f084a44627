package com.xihuan.framework.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class WebSocketExecutorConfig {

    /**
     * WebSocket 消息处理专用线程池
     * 核心参数说明：
     * - corePoolSize: 常驻线程数（根据CPU核心数设置）
     * - maxPoolSize: 最大线程数（突发流量缓冲）
     * - queueCapacity: 队列容量（防止OOM）
     * - threadNamePrefix: 线程名称前缀（方便监控）
     * - awaitTerminationSeconds: 关闭等待时间
     * - waitForTasksToCompleteOnShutdown: 优雅关闭
     */
    @Bean(name = "webSocketMessageExecutor")
    public ThreadPoolTaskExecutor webSocketMessageExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("websocket-exec-");
        executor.setAwaitTerminationSeconds(30);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }
}