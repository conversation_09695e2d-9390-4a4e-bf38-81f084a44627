<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyCourseImageResourceMapper">

    <resultMap id="BaseResultMap" type="PsyCourseImageResource">
        <id column="id" property="id"/>
        <result column="resource_id" property="resourceId"/>
        <result column="resource_type" property="resourceType"/>
        <result column="image_url" property="imageUrl"/>
        <result column="image_order" property="imageOrder"/>
        <result column="is_main" property="isMain"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <select id="selectImageResourceList" parameterType="PsyCourseImageResource" resultMap="BaseResultMap">
        SELECT * FROM psy_course_image_resource
        <where>
            del_flag = 0
            <if test="resourceId != null">AND resource_id = #{resourceId}</if>
            <if test="resourceType != null">AND resource_type = #{resourceType}</if>
            <if test="isMain != null">AND is_main = #{isMain}</if>
        </where>
        ORDER BY resource_type, resource_id, image_order ASC
    </select>

    <select id="selectImageResourceById" resultMap="BaseResultMap">
        SELECT * FROM psy_course_image_resource WHERE id = #{id} AND del_flag = 0
    </select>

    <select id="selectImageResourceByResourceId" resultMap="BaseResultMap">
        SELECT * FROM psy_course_image_resource 
        WHERE resource_id = #{resourceId} AND resource_type = #{resourceType} AND del_flag = 0
        ORDER BY image_order ASC
    </select>

    <insert id="insertImageResource" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_course_image_resource (
            resource_id, resource_type, image_url, image_order, is_main, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{resourceId}, #{resourceType}, #{imageUrl}, #{imageOrder}, #{isMain}, #{delFlag},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <insert id="batchInsertImageResource">
        INSERT INTO psy_course_image_resource (
            resource_id, resource_type, image_url, image_order, is_main, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.resourceId}, #{item.resourceType}, #{item.imageUrl}, #{item.imageOrder}, 
             #{item.isMain}, #{item.delFlag}, #{item.createBy}, #{item.createTime}, 
             #{item.updateBy}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateImageResource" parameterType="PsyCourseImageResource">
        UPDATE psy_course_image_resource
        <set>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="imageOrder != null">image_order = #{imageOrder},</if>
            <if test="isMain != null">is_main = #{isMain},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteImageResourceById">
        UPDATE psy_course_image_resource SET del_flag = 1 WHERE id = #{id}
    </update>

    <update id="deleteImageResourceByIds">
        UPDATE psy_course_image_resource SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteImageResourceByResourceId">
        UPDATE psy_course_image_resource SET del_flag = 1
        WHERE resource_id = #{resourceId} AND resource_type = #{resourceType}
    </update>

    <update id="setMainImage">
        <!-- 先将该资源的所有图片设为非主图 -->
        UPDATE psy_course_image_resource 
        SET is_main = 0, update_time = NOW()
        WHERE resource_id = #{resourceId} AND resource_type = #{resourceType} AND del_flag = 0;
        
        <!-- 再将指定图片设为主图 -->
        UPDATE psy_course_image_resource 
        SET is_main = 1, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="updateImageOrder">
        UPDATE psy_course_image_resource
        SET image_order = #{imageOrder}, update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
