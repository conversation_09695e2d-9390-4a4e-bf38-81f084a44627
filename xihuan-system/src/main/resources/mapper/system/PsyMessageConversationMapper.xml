<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyMessageConversationMapper">
    
    <resultMap type="PsyMessageConversation" id="PsyMessageConversationResult">
        <id     property="conversationId"         column="conversation_id"          />
        <result property="userId"                 column="user_id"                  />
        <result property="consultantId"           column="consultant_id"            />
        <result property="lastSenderId"           column="last_sender_id"           />
        <result property="lastMessage"            column="last_message"             />
        <result property="lastMessageTime"        column="last_message_time"        />
        <result property="userUnreadCount"        column="user_unread_count"        />
        <result property="consultantUnreadCount"  column="consultant_unread_count"  />
        <result property="userName"               column="user_name"                />
        <result property="userAvatar"             column="user_avatar"              />
        <result property="counselorName"          column="counselor_name"           />
        <result property="consultantAvatar"       column="consultant_avatar"        />
    </resultMap>
    
    <sql id="selectPsyMessageConversationVo">
        select c.conversation_id, c.user_id, c.consultant_id, c.last_sender_id, c.last_message, 
               c.last_message_time, c.user_unread_count, c.consultant_unread_count,
               u.user_name, u.avatar as user_avatar,
               su.nick_name as counselor_name, su.avatar as consultant_avatar
        from psy_message_conversation c
        left join sys_user u on c.user_id = u.user_id
        left join sys_user su on c.consultant_id = su.user_id
    </sql>
    
    <insert id="insertConversation" parameterType="PsyMessageConversation" useGeneratedKeys="true" keyProperty="conversationId">
        insert into psy_message_conversation (
            <if test="userId != null">user_id,</if>
            <if test="consultantId != null">consultant_id,</if>
            <if test="lastSenderId != null">last_sender_id,</if>
            <if test="lastMessage != null">last_message,</if>
            <if test="lastMessageTime != null">last_message_time,</if>
            <if test="userUnreadCount != null">user_unread_count,</if>
            <if test="consultantUnreadCount != null">consultant_unread_count</if>
        ) values (
            <if test="userId != null">#{userId},</if>
            <if test="consultantId != null">#{consultantId},</if>
            <if test="lastSenderId != null">#{lastSenderId},</if>
            <if test="lastMessage != null">#{lastMessage},</if>
            <if test="lastMessageTime != null">#{lastMessageTime},</if>
            <if test="userUnreadCount != null">#{userUnreadCount},</if>
            <if test="consultantUnreadCount != null">#{consultantUnreadCount}</if>
        )
    </insert>
    
    <select id="selectConversationById" parameterType="Long" resultMap="PsyMessageConversationResult">
        <include refid="selectPsyMessageConversationVo"/>
        where c.conversation_id = #{conversationId}
    </select>
    
    <select id="selectConversationByUserAndConsultant" resultMap="PsyMessageConversationResult">
        <include refid="selectPsyMessageConversationVo"/>
        where c.user_id = #{userId} and c.consultant_id = #{consultantId}
    </select>
    
    <select id="selectConversationsByUserId" parameterType="Long" resultMap="PsyMessageConversationResult">
        <include refid="selectPsyMessageConversationVo"/>
        where c.user_id = #{userId}
        order by c.last_message_time desc
    </select>
    
    <select id="selectConversationsByConsultantId" parameterType="Long" resultMap="PsyMessageConversationResult">
        <include refid="selectPsyMessageConversationVo"/>
        where c.consultant_id = #{consultantId}
        order by c.last_message_time desc
    </select>
    
    <select id="selectAllConversations" resultMap="PsyMessageConversationResult">
        <include refid="selectPsyMessageConversationVo"/>
        order by c.last_message_time desc
    </select>
    
    <update id="updateConversation" parameterType="PsyMessageConversation">
        update psy_message_conversation
        <set>
            <if test="lastSenderId != null">last_sender_id = #{lastSenderId},</if>
            <if test="lastMessage != null">last_message = #{lastMessage},</if>
            <if test="lastMessageTime != null">last_message_time = #{lastMessageTime},</if>
            <if test="userUnreadCount != null">user_unread_count = #{userUnreadCount},</if>
            <if test="consultantUnreadCount != null">consultant_unread_count = #{consultantUnreadCount}</if>
        </set>
        where conversation_id = #{conversationId}
    </update>
    
    <update id="incrementUserUnreadCount" parameterType="Long">
        update psy_message_conversation
        set user_unread_count = user_unread_count + 1
        where conversation_id = #{conversationId}
    </update>
    
    <update id="incrementConsultantUnreadCount" parameterType="Long">
        update psy_message_conversation
        set consultant_unread_count = consultant_unread_count + 1
        where conversation_id = #{conversationId}
    </update>
    
    <update id="resetUserUnreadCount" parameterType="Long">
        update psy_message_conversation
        set user_unread_count = 0
        where conversation_id = #{conversationId}
    </update>
    
    <update id="resetConsultantUnreadCount" parameterType="Long">
        update psy_message_conversation
        set consultant_unread_count = 0
        where conversation_id = #{conversationId}
    </update>
    
</mapper> 