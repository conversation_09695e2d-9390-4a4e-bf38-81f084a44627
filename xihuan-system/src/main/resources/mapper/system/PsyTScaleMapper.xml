<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTScaleMapper">

    <!-- 结果映射 -->
    <resultMap id="ScaleResultMap" type="PsyTScale">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="categoryId" column="category_id"/>
        <result property="description" column="description"/>
        <result property="introduction" column="introduction"/>
        <result property="testNotice" column="test_notice"/>
        <result property="testPurpose" column="test_purpose"/>
        <result property="testObject" column="test_object"/>
        <result property="testPreparation" column="test_preparation"/>
        <result property="testProcessing" column="test_processing"/>
        <result property="testAttention" column="test_attention"/>
        <result property="testTheory" column="test_theory"/>
        <result property="testApplication" column="test_application"/>
        <result property="referenceLiterature" column="reference_literature"/>
        <result property="questionCount" column="question_count"/>
        <result property="scoringType" column="scoring_type"/>
        <result property="duration" column="duration"/>
        <result property="normMean" column="norm_mean"/>
        <result property="normSd" column="norm_sd"/>
        <result property="applicableAge" column="applicable_age"/>
        <result property="imageUrl" column="image_url"/>
        <result property="price" column="price"/>
        <result property="payMode" column="pay_mode"/>
        <result property="payPhase" column="pay_phase"/>
        <result property="freeVipLevel" column="free_vip_level"/>
        <result property="freeReportLevel" column="free_report_level"/>
        <result property="paidReportLevel" column="paid_report_level"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="status" column="status"/>
        <result property="sort" column="sort"/>
        <result property="searchKeywords" column="search_keywords"/>
        <result property="searchCount" column="search_count"/>
        <result property="viewCount" column="view_count"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <!-- 新增计分配置字段 -->
        <result property="scoringMethod" column="scoring_method"/>
        <result property="hasReverseItems" column="has_reverse_items"/>
        <result property="hasStandardScore" column="has_standard_score"/>
        <result property="standardScoreMultiplier" column="standard_score_multiplier"/>
        <result property="dimensionCount" column="dimension_count"/>
        <result property="rawScoreRange" column="raw_score_range"/>
        <result property="standardScoreRange" column="standard_score_range"/>
    </resultMap>

    <!-- 详细结果映射 -->
    <resultMap id="ScaleDetailMap" type="PsyTScale" extends="ScaleResultMap">
        <collection property="questions" ofType="PsyTQuestion" column="id" select="com.xihuan.system.mapper.PsyTQuestionMapper.selectQuestionsByScaleId"/>
        <collection property="subscales" ofType="PsyTSubscale" column="id" select="com.xihuan.system.mapper.PsyTSubscaleMapper.selectSubscalesByScaleId"/>
        <collection property="scoringRules" ofType="PsyTScoringRule" column="id" select="com.xihuan.system.mapper.PsyTScoringRuleMapper.selectRulesByScaleId"/>
    </resultMap>

    <!-- 查询量表列表 -->
    <select id="selectScaleList" parameterType="PsyTScale" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0'
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="code != null and code != ''">
            AND code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="categoryId != null">
            AND category_id = #{categoryId}
        </if>
        <if test="scoringType != null and scoringType != ''">
            AND scoring_type = #{scoringType}
        </if>
        <if test="payMode != null">
            AND pay_mode = #{payMode}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="enterpriseId != null">
            AND enterprise_id = #{enterpriseId}
        </if>
        <if test="searchKeywords != null and searchKeywords != ''">
            AND (
                name LIKE CONCAT('%', #{searchKeywords}, '%')
                OR description LIKE CONCAT('%', #{searchKeywords}, '%')
                OR search_keywords LIKE CONCAT('%', #{searchKeywords}, '%')
            )
        </if>
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据ID查询量表 -->
    <select id="selectScaleById" parameterType="Long" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale WHERE id = #{id} AND del_flag = '0'
    </select>

    <!-- 查询量表详情 -->
    <select id="selectScaleWithDetails" parameterType="Long" resultMap="ScaleDetailMap">
        SELECT * FROM psy_t_scale WHERE id = #{id} AND del_flag = '0'
    </select>

    <!-- 根据编码查询量表 -->
    <select id="selectScaleByCode" parameterType="String" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale WHERE code = #{code} AND del_flag = '0'
    </select>

    <!-- 新增量表 -->
    <insert id="insertScale" parameterType="PsyTScale" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_scale
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="description != null">description,</if>
            <if test="introduction != null">introduction,</if>
            <if test="testNotice != null">test_notice,</if>
            <if test="testPurpose != null">test_purpose,</if>
            <if test="testObject != null">test_object,</if>
            <if test="testPreparation != null">test_preparation,</if>
            <if test="testProcessing != null">test_processing,</if>
            <if test="testAttention != null">test_attention,</if>
            <if test="testTheory != null">test_theory,</if>
            <if test="testApplication != null">test_application,</if>
            <if test="referenceLiterature != null">reference_literature,</if>
            <if test="questionCount != null">question_count,</if>
            <if test="scoringType != null">scoring_type,</if>
            <if test="duration != null">duration,</if>
            <if test="normMean != null">norm_mean,</if>
            <if test="normSd != null">norm_sd,</if>
            <if test="applicableAge != null">applicable_age,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="price != null">price,</if>
            <if test="payMode != null">pay_mode,</if>
            <if test="payPhase != null">pay_phase,</if>
            <if test="freeVipLevel != null">free_vip_level,</if>
            <if test="freeReportLevel != null">free_report_level,</if>
            <if test="paidReportLevel != null">paid_report_level,</if>
            <if test="enterpriseId != null">enterprise_id,</if>
            <if test="status != null">status,</if>
            <if test="sort != null">sort,</if>
            <if test="searchKeywords != null">search_keywords,</if>
            <if test="searchCount != null">search_count,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            <if test="scoringMethod != null">scoring_method,</if>
            <if test="hasReverseItems != null">has_reverse_items,</if>
            <if test="hasStandardScore != null">has_standard_score,</if>
            <if test="standardScoreMultiplier != null">standard_score_multiplier,</if>
            <if test="dimensionCount != null">dimension_count,</if>
            <if test="rawScoreRange != null">raw_score_range,</if>
            <if test="standardScoreRange != null">standard_score_range,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="description != null">#{description},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="testNotice != null">#{testNotice},</if>
            <if test="testPurpose != null">#{testPurpose},</if>
            <if test="testObject != null">#{testObject},</if>
            <if test="testPreparation != null">#{testPreparation},</if>
            <if test="testProcessing != null">#{testProcessing},</if>
            <if test="testAttention != null">#{testAttention},</if>
            <if test="testTheory != null">#{testTheory},</if>
            <if test="testApplication != null">#{testApplication},</if>
            <if test="referenceLiterature != null">#{referenceLiterature},</if>
            <if test="questionCount != null">#{questionCount},</if>
            <if test="scoringType != null">#{scoringType},</if>
            <if test="duration != null">#{duration},</if>
            <if test="normMean != null">#{normMean},</if>
            <if test="normSd != null">#{normSd},</if>
            <if test="applicableAge != null">#{applicableAge},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="price != null">#{price},</if>
            <if test="payMode != null">#{payMode},</if>
            <if test="payPhase != null">#{payPhase},</if>
            <if test="freeVipLevel != null">#{freeVipLevel},</if>
            <if test="freeReportLevel != null">#{freeReportLevel},</if>
            <if test="paidReportLevel != null">#{paidReportLevel},</if>
            <if test="enterpriseId != null">#{enterpriseId},</if>
            <if test="status != null">#{status},</if>
            <if test="sort != null">#{sort},</if>
            <if test="searchKeywords != null">#{searchKeywords},</if>
            <if test="searchCount != null">#{searchCount},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="scoringMethod != null">#{scoringMethod},</if>
            <if test="hasReverseItems != null">#{hasReverseItems},</if>
            <if test="hasStandardScore != null">#{hasStandardScore},</if>
            <if test="standardScoreMultiplier != null">#{standardScoreMultiplier},</if>
            <if test="dimensionCount != null">#{dimensionCount},</if>
            <if test="rawScoreRange != null">#{rawScoreRange},</if>
            <if test="standardScoreRange != null">#{standardScoreRange},</if>
            NOW()
        </trim>
    </insert>

    <!-- 修改量表 -->
    <update id="updateScale" parameterType="PsyTScale">
        UPDATE psy_t_scale
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="testNotice != null">test_notice = #{testNotice},</if>
            <if test="testPurpose != null">test_purpose = #{testPurpose},</if>
            <if test="testObject != null">test_object = #{testObject},</if>
            <if test="testPreparation != null">test_preparation = #{testPreparation},</if>
            <if test="testProcessing != null">test_processing = #{testProcessing},</if>
            <if test="testAttention != null">test_attention = #{testAttention},</if>
            <if test="testTheory != null">test_theory = #{testTheory},</if>
            <if test="testApplication != null">test_application = #{testApplication},</if>
            <if test="referenceLiterature != null">reference_literature = #{referenceLiterature},</if>
            <if test="questionCount != null">question_count = #{questionCount},</if>
            <if test="scoringType != null">scoring_type = #{scoringType},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="normMean != null">norm_mean = #{normMean},</if>
            <if test="normSd != null">norm_sd = #{normSd},</if>
            <if test="applicableAge != null">applicable_age = #{applicableAge},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="price != null">price = #{price},</if>
            <if test="payMode != null">pay_mode = #{payMode},</if>
            <if test="payPhase != null">pay_phase = #{payPhase},</if>
            <if test="freeVipLevel != null">free_vip_level = #{freeVipLevel},</if>
            <if test="freeReportLevel != null">free_report_level = #{freeReportLevel},</if>
            <if test="paidReportLevel != null">paid_report_level = #{paidReportLevel},</if>
            <if test="enterpriseId != null">enterprise_id = #{enterpriseId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="searchKeywords != null">search_keywords = #{searchKeywords},</if>
            <if test="searchCount != null">search_count = #{searchCount},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="scoringMethod != null">scoring_method = #{scoringMethod},</if>
            <if test="hasReverseItems != null">has_reverse_items = #{hasReverseItems},</if>
            <if test="hasStandardScore != null">has_standard_score = #{hasStandardScore},</if>
            <if test="standardScoreMultiplier != null">standard_score_multiplier = #{standardScoreMultiplier},</if>
            <if test="dimensionCount != null">dimension_count = #{dimensionCount},</if>
            <if test="rawScoreRange != null">raw_score_range = #{rawScoreRange},</if>
            <if test="standardScoreRange != null">standard_score_range = #{standardScoreRange},</if>
            update_time = NOW()
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 删除量表 -->
    <delete id="deleteScaleById" parameterType="Long">
        DELETE FROM psy_t_scale WHERE id = #{id}
    </delete>

    <!-- 批量删除量表 -->
    <delete id="deleteScaleByIds" parameterType="String">
        DELETE FROM psy_t_scale WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询热门量表 -->
    <select id="selectHotScales" parameterType="Integer" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0' AND status = 1
        ORDER BY view_count DESC, search_count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询免费量表 -->
    <select id="selectFreeScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale 
        WHERE del_flag = '0' AND status = 1 AND pay_mode = 0
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询最新量表 -->
    <select id="selectLatestScales" parameterType="Integer" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale 
        WHERE del_flag = '0' AND status = 1
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 更新查看次数 -->
    <update id="updateViewCount" parameterType="Long">
        UPDATE psy_t_scale SET view_count = view_count + 1 WHERE id = #{id}
    </update>

    <!-- 更新搜索次数 -->
    <update id="updateSearchCount" parameterType="Long">
        UPDATE psy_t_scale SET search_count = search_count + 1 WHERE id = #{id}
    </update>

    <!-- 检查量表编码唯一性 -->
    <select id="checkScaleCodeUnique" resultType="int">
        SELECT COUNT(1) FROM psy_t_scale WHERE code = #{code} AND del_flag = '0'
        <if test="excludeId != null and excludeId != 0">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查量表是否被使用 -->
    <select id="checkScaleInUse" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM (
            SELECT 1 FROM psy_t_assessment_record WHERE scale_id = #{id} LIMIT 1
            UNION ALL
            SELECT 1 FROM psy_t_assessment_order WHERE scale_id = #{id} LIMIT 1
        ) t
    </select>

    <!-- 搜索量表 -->
    <select id="searchScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0'
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (
                name LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%')
                OR search_keywords LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="categoryId != null">
            AND category_id = #{categoryId}
        </if>
        <if test="enterpriseId != null">
            AND enterprise_id = #{enterpriseId}
        </if>
        ORDER BY sort ASC, test_count DESC, create_time DESC
    </select>

    <!-- 统计量表数量 -->
    <select id="countScales" parameterType="PsyTScale" resultType="int">
        SELECT COUNT(1) FROM psy_t_scale
        WHERE del_flag = '0'
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="payMode != null">
            AND pay_mode = #{payMode}
        </if>
    </select>

    <!-- 批量更新量表状态 -->
    <update id="batchUpdateScaleStatus">
        UPDATE psy_t_scale SET status = #{status}, update_time = sysdate()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询相似量表 -->
    <select id="selectSimilarScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0' AND status = 1 AND id != #{scaleId}
        AND (
            category_id = (SELECT category_id FROM psy_t_scale WHERE id = #{scaleId})
            OR scoring_type = (SELECT scoring_type FROM psy_t_scale WHERE id = #{scaleId})
            OR applicable_age = (SELECT applicable_age FROM psy_t_scale WHERE id = #{scaleId})
        )
        ORDER BY
            CASE WHEN category_id = (SELECT category_id FROM psy_t_scale WHERE id = #{scaleId}) THEN 1 ELSE 2 END,
            view_count DESC,
            create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询企业可用量表 -->
    <select id="selectEnterpriseScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0' AND status = 1
        AND (enterprise_id = #{enterpriseId} OR enterprise_id IS NULL)
        ORDER BY enterprise_id DESC, sort ASC, create_time DESC
    </select>

    <!-- 查询用户收藏的量表 -->
    <select id="selectFavoriteScalesByUserId" parameterType="Long" resultMap="ScaleResultMap">
        SELECT s.* FROM psy_t_scale s
        INNER JOIN psy_user_scale_favorite f ON s.id = f.scale_id
        WHERE s.del_flag = '0' AND s.status = 1 AND f.user_id = #{userId}
        ORDER BY f.create_time DESC
    </select>

    <!-- 查询推荐量表 -->
    <select id="selectRecommendedScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0' AND status = 1
        ORDER BY view_count DESC, search_count DESC, create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据分类查询量表 -->
    <select id="selectScalesByCategory" parameterType="Integer" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0' AND status = 1 AND category_id = #{categoryId}
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询量表统计信息 -->
    <select id="selectScaleStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT ar.user_id) as test_user_count,
            COUNT(ar.id) as total_test_count,
            COUNT(CASE WHEN ar.status = 1 THEN 1 END) as completed_count,
            ROUND(AVG(CASE WHEN ar.status = 1 THEN ar.total_score END), 2) as avg_score,
            ROUND(AVG(CASE WHEN ar.status = 1 THEN ar.duration END), 2) as avg_duration
        FROM psy_t_assessment_record ar
        WHERE ar.scale_id = #{scaleId} AND ar.del_flag = '0'
    </select>

    <!-- 更新量表测试次数 -->
    <update id="updateTestCount" parameterType="Long">
        UPDATE psy_t_scale SET test_count = test_count + 1 WHERE id = #{id}
    </update>

    <!-- 查询量表完成率统计 -->
    <select id="selectScaleCompletionStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_attempts,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completed_attempts,
            ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate,
            COUNT(CASE WHEN status = 0 THEN 1 END) as in_progress,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandoned
        FROM psy_t_assessment_record
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

</mapper>
