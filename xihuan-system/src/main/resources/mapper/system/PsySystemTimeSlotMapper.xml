<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsySystemTimeSlotMapper">
    
    <resultMap type="PsySystemTimeSlot" id="PsySystemTimeSlotResult">
        <result property="id"                   column="id"                   />
        <result property="centerId"             column="center_id"            />
        <result property="dateKey"              column="date_key"             />
        <result property="weekDay"              column="week_day"             />
        <result property="rangeId"              column="range_id"             />
        <result property="startTime"            column="start_time"           />
        <result property="endTime"              column="end_time"             />
        <result property="availableCounselors"  column="available_counselors" />
        <result property="totalCounselors"      column="total_counselors"     />
        <result property="hasAvailable"         column="has_available"        />
        <result property="status"               column="status"               />
        <result property="delFlag"              column="del_flag"             />
        <result property="createBy"             column="create_by"            />
        <result property="createTime"           column="create_time"          />
        <result property="updateBy"             column="update_by"            />
        <result property="updateTime"           column="update_time"          />
        <result property="remark"               column="remark"               />
    </resultMap>

    <resultMap type="PsySystemTimeSlot" id="PsySystemTimeSlotWithRangeResult" extends="PsySystemTimeSlotResult">
        <association property="timeRange" javaType="PsyTimeRange">
            <result property="id"           column="range_id"         />
            <result property="name"         column="range_name"       />
            <result property="iconUrl"      column="range_icon_url"   />
            <result property="startHour"    column="range_start_hour" />
            <result property="endHour"      column="range_end_hour"   />
        </association>
    </resultMap>

    <sql id="selectPsySystemTimeSlotVo">
        select sts.id, sts.center_id, sts.date_key, sts.week_day, sts.range_id,
               sts.start_time, sts.end_time, sts.available_counselors, sts.total_counselors,
               sts.has_available, sts.status, sts.del_flag, sts.create_by, sts.create_time,
               sts.update_by, sts.update_time, sts.remark
        from psy_system_time_slot sts
    </sql>

    <sql id="selectPsySystemTimeSlotWithRangeVo">
        select sts.id, sts.center_id, sts.date_key, sts.week_day, sts.range_id,
               sts.start_time, sts.end_time, sts.available_counselors, sts.total_counselors,
               sts.has_available, sts.status, sts.del_flag, sts.create_by, sts.create_time,
               sts.update_by, sts.update_time, sts.remark,
               tr.name as range_name, tr.icon_url as range_icon_url, 
               tr.start_hour as range_start_hour, tr.end_hour as range_end_hour
        from psy_system_time_slot sts
        left join psy_time_range tr on sts.range_id = tr.id
    </sql>

    <select id="selectSystemTimeSlotList" parameterType="PsySystemTimeSlot" resultMap="PsySystemTimeSlotWithRangeResult">
        <include refid="selectPsySystemTimeSlotWithRangeVo"/>
        <where>
            <if test="centerId != null">and sts.center_id = #{centerId}</if>
            <if test="dateKey != null and dateKey != ''">and sts.date_key = #{dateKey}</if>
            <if test="weekDay != null and weekDay != ''">and sts.week_day = #{weekDay}</if>
            <if test="rangeId != null">and sts.range_id = #{rangeId}</if>
            <if test="startTime != null">and sts.start_time = #{startTime}</if>
            <if test="endTime != null">and sts.end_time = #{endTime}</if>
            <if test="availableCounselors != null">and sts.available_counselors = #{availableCounselors}</if>
            <if test="totalCounselors != null">and sts.total_counselors = #{totalCounselors}</if>
            <if test="hasAvailable != null">and sts.has_available = #{hasAvailable}</if>
            <if test="status != null">and sts.status = #{status}</if>
            <if test="delFlag != null">and sts.del_flag = #{delFlag}</if>
            <if test="params.startDate != null and params.startDate != ''">
                and sts.date_key &gt;= #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                and sts.date_key &lt;= #{params.endDate}
            </if>
        </where>
        order by sts.date_key, sts.start_time
    </select>
    
    <select id="selectSystemTimeSlotById" parameterType="Long" resultMap="PsySystemTimeSlotWithRangeResult">
        <include refid="selectPsySystemTimeSlotWithRangeVo"/>
        where sts.id = #{id}
    </select>
    
    <select id="selectSlotsByDateRange" resultMap="PsySystemTimeSlotWithRangeResult">
        <include refid="selectPsySystemTimeSlotWithRangeVo"/>
        where sts.date_key &gt;= #{startDate} 
          and sts.date_key &lt;= #{endDate}
          and sts.center_id = #{centerId}
          and sts.del_flag = 0
        order by sts.date_key, sts.start_time
    </select>
    
    <select id="selectSlotsByDate" resultMap="PsySystemTimeSlotWithRangeResult">
        <include refid="selectPsySystemTimeSlotWithRangeVo"/>
        where sts.date_key = #{dateKey} 
          and sts.center_id = #{centerId}
          and sts.del_flag = 0
        order by sts.start_time
    </select>
    
    <select id="selectAvailableSlots" resultMap="PsySystemTimeSlotWithRangeResult">
        <include refid="selectPsySystemTimeSlotWithRangeVo"/>
        where sts.date_key &gt;= #{startDate} 
          and sts.date_key &lt;= #{endDate}
          and sts.center_id = #{centerId}
          and sts.has_available = 1
          and sts.del_flag = 0
        order by sts.date_key, sts.start_time
    </select>
        
    <insert id="insertSystemTimeSlot" parameterType="PsySystemTimeSlot" useGeneratedKeys="true" keyProperty="id">
        insert into psy_system_time_slot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="centerId != null">center_id,</if>
            <if test="dateKey != null and dateKey != ''">date_key,</if>
            <if test="weekDay != null">week_day,</if>
            <if test="rangeId != null">range_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="availableCounselors != null">available_counselors,</if>
            <if test="totalCounselors != null">total_counselors,</if>
            <if test="hasAvailable != null">has_available,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="centerId != null">#{centerId},</if>
            <if test="dateKey != null and dateKey != ''">#{dateKey},</if>
            <if test="weekDay != null">#{weekDay},</if>
            <if test="rangeId != null">#{rangeId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="availableCounselors != null">#{availableCounselors},</if>
            <if test="totalCounselors != null">#{totalCounselors},</if>
            <if test="hasAvailable != null">#{hasAvailable},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertSystemTimeSlots">
        insert ignore into psy_system_time_slot (center_id, date_key, week_day, range_id, start_time, end_time,
                                                  available_counselors, total_counselors, has_available, status, del_flag, create_time)
        values
        <foreach collection="systemTimeSlots" item="slot" separator=",">
            (#{slot.centerId}, #{slot.dateKey}, #{slot.weekDay}, #{slot.rangeId}, #{slot.startTime}, #{slot.endTime},
             #{slot.availableCounselors}, #{slot.totalCounselors}, #{slot.hasAvailable}, #{slot.status}, #{slot.delFlag}, #{slot.createTime})
        </foreach>
    </insert>

    <update id="updateSystemTimeSlot" parameterType="PsySystemTimeSlot">
        update psy_system_time_slot
        <trim prefix="SET" suffixOverrides=",">
            <if test="centerId != null">center_id = #{centerId},</if>
            <if test="dateKey != null and dateKey != ''">date_key = #{dateKey},</if>
            <if test="weekDay != null">week_day = #{weekDay},</if>
            <if test="rangeId != null">range_id = #{rangeId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="availableCounselors != null">available_counselors = #{availableCounselors},</if>
            <if test="totalCounselors != null">total_counselors = #{totalCounselors},</if>
            <if test="hasAvailable != null">has_available = #{hasAvailable},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdateAvailabilityStats">
        update psy_system_time_slot sts
        set available_counselors = (
            select count(distinct ts.counselor_id)
            from psy_time_slot ts
            where ts.date_key = sts.date_key
              and ts.start_time = sts.start_time
              and ts.center_id = sts.center_id
              and ts.status = 0
              and ts.del_flag = 0
        ),
        total_counselors = (
            select count(distinct ts.counselor_id)
            from psy_time_slot ts
            where ts.date_key = sts.date_key
              and ts.start_time = sts.start_time
              and ts.center_id = sts.center_id
              and ts.del_flag = 0
        ),
        has_available = case when (
            select count(distinct ts.counselor_id)
            from psy_time_slot ts
            where ts.date_key = sts.date_key
              and ts.start_time = sts.start_time
              and ts.center_id = sts.center_id
              and ts.status = 0
              and ts.del_flag = 0
        ) &gt; 0 then 1 else 0 end,
        update_time = now()
        where sts.date_key = #{dateKey} and sts.center_id = #{centerId} and sts.del_flag = 0
    </update>

    <delete id="deleteSystemTimeSlotById" parameterType="Long">
        update psy_system_time_slot set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteSystemTimeSlotByIds" parameterType="String">
        update psy_system_time_slot set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSlotsByDateRange">
        update psy_system_time_slot set del_flag = 1 
        where date_key &gt;= #{startDate} and date_key &lt;= #{endDate}
        <if test="centerId != null"> and center_id = #{centerId}</if>
    </delete>
    
    <select id="checkSystemSlotExists" resultType="int">
        select count(1) from psy_system_time_slot 
        where center_id = #{centerId} and date_key = #{dateKey} and start_time = #{startTime} and del_flag = 0
    </select>
    
    <select id="countAvailableCounselors" resultType="map">
        select 
            count(distinct case when ts.status = 0 then ts.counselor_id end) as availableCount,
            count(distinct ts.counselor_id) as totalCount
        from psy_time_slot ts
        where ts.center_id = #{centerId}
          and ts.date_key = #{dateKey}
          and ts.start_time = #{startTime}
          and ts.end_time = #{endTime}
          and ts.del_flag = 0
    </select>

    <!-- 批量更新系统时间槽状态 -->
    <update id="batchUpdateSlotStatus">
        UPDATE psy_system_time_slot
        SET status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="slotIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </update>

    <!-- 更新过期的系统时间槽状态 -->
    <update id="updateExpiredSlotStatus">
        UPDATE psy_system_time_slot
        SET status = 2, update_time = NOW()
        WHERE center_id = #{centerId}
          AND status = 0
          AND del_flag = 0
          AND CONCAT(date_key, ' ', end_time) &lt;
          <choose>
              <when test="delayHours > 0">
                  DATE_SUB(NOW(), INTERVAL #{delayHours} HOUR)
              </when>
              <otherwise>
                  NOW()
              </otherwise>
          </choose>
    </update>

    <!-- 清理过期的系统时间槽 -->
    <delete id="cleanExpiredSystemSlots">
        DELETE FROM psy_system_time_slot
        WHERE center_id = #{centerId}
          AND date_key &lt; #{beforeDate}
          AND del_flag = 0
    </delete>

</mapper>
