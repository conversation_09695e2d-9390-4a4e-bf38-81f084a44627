<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyConsultantMapper">

    <resultMap type="PsyConsultant" id="ConsultantResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="imageUrl" column="image_url"/>
        <result property="bgImg" column="bg_img"/>
        <result property="location" column="location"/>
        <result property="address" column="address"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="phone" column="phone"/>
        <result property="wechat" column="wechat"/>
        <result property="name" column="name"/>
        <result property="birthDate" column="birth_date"/>
        <result property="gender" column="gender"/>
        <result property="idCardNumber" column="id_card_number"/>
        <result property="idFrontImg" column="id_front_img"/>
        <result property="idBackImg" column="id_back_img"/>
        <result property="startYear" column="start_year"/>
        <result property="personalIntro" column="personal_intro"/>
        <result property="personalTitle" column="personal_title"/>
        <result property="totalCases" column="total_cases"/>
        <result property="serviceCount" column="service_count"/>
        <result property="serviceHours" column="service_hours"/>
        <result property="canTeach" column="can_teach"/>
        <result property="canTravel" column="can_travel"/>
        <result property="availableTime" column="available_time"/>
        <result property="price" column="price"/>
        <result property="minFee" column="min_fee"/>
        <result property="maxFee" column="max_fee"/>
        <result property="workCase" column="work_case"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="workStatus" column="work_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <resultMap type="PsyConsultant" id="ConsultantListResult" extends="ConsultantResult">
        <collection property="consultStyles" ofType="PsyConsultantStyle" column="id" select="selectConsultStyles"/>
    </resultMap>

    <resultMap type="PsyConsultant" id="ConsultantDetailResult" extends="ConsultantResult">
        <collection property="serviceMethods" ofType="PsyConsultantMethod" column="id" select="selectServiceMethods"/>
        <collection property="consultStyles" ofType="PsyConsultantStyle" column="id" select="selectConsultStyles"/>
        <collection property="expertises" ofType="PsyDictExpertise" column="id" select="selectExpertises"/>
        <collection property="specialMethods" ofType="PsyConsultantSpecial" column="id" select="selectSpecialMethods"/>
<!--        <collection property="availableSlots" ofType="PsyAvailableSlot" column="id" select="selectAvailableSlots"/>-->
        <collection property="certificates" ofType="PsyCertificate" column="id" select="selectCertificates"/>
        <collection property="educations" ofType="PsyEducation" column="id" select="selectEducations"/>
        <collection property="supervisions" ofType="PsySupervision" column="id" select="selectSupervisions"/>
        <collection property="trainings" ofType="PsyTraining" column="id" select="selectTrainings"/>
    </resultMap>

    <sql id="selectConsultantVo">
        select id, user_id, image_url, bg_img, location, address, province, city, district,
               phone, wechat, name, CAST(birth_date AS SIGNED) as birth_date, gender,
               CAST(start_year AS SIGNED) as start_year, personal_intro, personal_title, total_cases, service_count,
               service_hours, can_teach, can_travel, available_time, price, min_fee, max_fee,
               work_case, audit_status, work_status, create_time, update_time, create_by, update_by
        from psy_consultants
    </sql>

    <!-- 管理员查询时使用的SQL，包含敏感信息 -->
    <sql id="selectConsultantVoAdmin">
        select id, user_id, image_url, bg_img, location, address, province, city, district,
               phone, wechat, name, CAST(birth_date AS SIGNED) as birth_date, gender, id_card_number,
               id_front_img, id_back_img, CAST(start_year AS SIGNED) as start_year, personal_intro, personal_title, total_cases,
               service_count, service_hours, can_teach, can_travel, available_time, price,
               min_fee, max_fee, work_case, audit_status, work_status, create_time,
               update_time, create_by, update_by
        from psy_consultants
    </sql>

    <!-- 查询关联数据的子查询 -->
    <select id="selectServiceMethods" resultType="PsyConsultantMethod">
        select cm.id, cm.consultant_id, cm.dict_value as dictValue,
               d.dict_label as dictLabel, d.dict_type as dictType
        from psy_consultant_method cm
                 left join sys_dict_data d on cm.dict_value = d.dict_value and d.dict_type = 'psy_service_type'
        where cm.consultant_id = #{id}
    </select>

    <select id="selectConsultStyles" resultType="PsyConsultantStyle">
        select cs.id, cs.consultant_id, cs.dict_value as dictValue,
               d.dict_label as dictLabel, d.dict_type as dictType,
               d.remark
        from psy_consultant_style cs
        left join sys_dict_data d on cs.dict_value = d.dict_value and d.dict_type = 'psy_consulting_style'
        where cs.consultant_id = #{id}
    </select>

    <!-- 擅长领域结果映射 -->
    <resultMap id="ExpertiseResult" type="PsyDictExpertise">
        <id property="id" column="id"/>
        <result property="typeName" column="type_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="icon" column="icon"/>
        <result property="sort" column="sort"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <collection property="children" ofType="PsyDictExpertise">
            <result property="id" column="child_id"/>
            <result property="typeName" column="child_type_name"/>
            <result property="parentId" column="child_parent_id"/>
            <result property="icon" column="child_icon"/>
            <result property="sort" column="child_sort"/>
            <result property="remark" column="child_remark"/>
            <result property="createBy" column="child_create_by"/>
            <result property="createTime" column="child_create_time"/>
            <result property="updateBy" column="child_update_by"/>
            <result property="updateTime" column="child_update_time"/>
        </collection>
    </resultMap>

    <!-- 查询擅长领域（包含关联的子项） -->
    <select id="selectExpertises" resultMap="ExpertiseResult">
        select
            e.id, e.type_name, e.parent_id,
            e.icon, e.sort, e.remark,
            e.create_by, e.create_time,
            e.update_by, e.update_time,
            c.id as child_id,
            c.type_name as child_type_name,
            c.parent_id as child_parent_id,
            c.icon as child_icon,
            c.sort as child_sort,
            c.remark as child_remark,
            c.create_by as child_create_by,
            c.create_time as child_create_time,
            c.update_by as child_update_by,
            c.update_time as child_update_time
        from psy_consultant_expertise ce1
        join psy_dict_expertise e on ce1.expertise_id = e.id
        left join psy_consultant_expertise ce2 on ce2.consultant_id = ce1.consultant_id
        left join psy_dict_expertise c on c.id = ce2.expertise_id and c.parent_id = e.id
        where ce1.consultant_id = #{id}
        and e.parent_id is null
        order by e.sort, c.sort
    </select>

    <select id="selectSpecialMethods" resultType="PsyConsultantSpecial">
        select cs.id, cs.consultant_id, cs.dict_value as dictValue,
               d.dict_label as dictLabel, d.dict_type as dictType,
               d.remark
        from psy_consultant_special cs
                 left join sys_dict_data d on cs.dict_value = d.dict_value and d.dict_type = 'psy_characteristic_consultation'
        where cs.consultant_id = #{id}
    </select>

    <select id="selectAvailableSlots" resultType="PsyAvailableSlot">
        select id, consultant_id, dict_day_value, dict_time_value
        from psy_available_slots
        where consultant_id = #{id}
    </select>

    <select id="selectCertificates" resultType="PsyCertificate">
        select id, consultant_id, name, number, issue_date, expire_date, image_url, sort_order
        from psy_certificates
        where consultant_id = #{id}
        order by sort_order
    </select>

    <select id="selectEducations" resultType="PsyEducation">
        select id, 
               consultant_id as consultantId,
               school,
               major,
               CAST(start_year as CHAR) as startYear,
               CAST(end_year as CHAR) as endYear,
               degree,
               certificate
        from psy_educations
        where consultant_id = #{id}
        order by start_year desc
    </select>

    <select id="selectSupervisions" resultType="PsySupervision">
        select id, consultant_id, type, hours, supervisor, start_date, end_date, certificate
        from psy_supervisions
        where consultant_id = #{id}
        order by start_date desc
    </select>

    <select id="selectTrainings" resultType="PsyTraining">
        select id, consultant_id as consultantId, name, organizer, hours, certificate,
               DATE_FORMAT(start_date, '%Y-%m-%d') as startDate,
               DATE_FORMAT(end_date, '%Y-%m-%d') as endDate
        from psy_trainings
        where consultant_id = #{id}
        order by start_date desc
    </select>

    <!-- 管理员查询咨询师详情 -->
    <select id="selectConsultantWithUserById" parameterType="Long" resultMap="ConsultantDetailResult">
        <include refid="selectConsultantVoAdmin"/>
        where id = #{id}
    </select>

    <!-- 管理员查询咨询师列表 -->
    <select id="selectConsultantListByPage" parameterType="Map" resultMap="ConsultantListResult">
        <include refid="selectConsultantVoAdmin"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="workStatus != null and workStatus != ''">
                AND work_status = #{workStatus}
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                AND audit_status = #{auditStatus}
            </if>
            <if test="minPrice != null">
                AND price >= #{minPrice}
            </if>
            <if test="maxPrice != null">
                AND price &lt;= #{maxPrice}
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 普通用户查询咨询师列表 -->
    <select id="selectAllConsultants" parameterType="Map" resultMap="ConsultantDetailResult">
        <include refid="selectConsultantVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="field != null and field != ''">
                AND id IN (
                    select consultant_id from psy_consultant_expertise ce
                    join psy_dict_expertise e on ce.expertise_id = e.id
                    where e.type_name = #{field}
                )
            </if>
            AND work_status = '0' and audit_status = '1'
        </where>
        order by create_time desc
    </select>

    <!-- 普通用户查询咨询师详情 -->
    <select id="selectConsultantFullDetails" parameterType="Long" resultMap="ConsultantDetailResult">
        <include refid="selectConsultantVo"/>
        where id = #{id} AND work_status = '0' and audit_status = '1'
    </select>

    <!-- 根据用户ID查询咨询师 -->
    <select id="selectConsultantByUserId" parameterType="Long" resultMap="ConsultantResult">
        <include refid="selectConsultantVoAdmin"/>
        where user_id = #{userId}
    </select>

    <!-- 查询所有可用咨询师 -->
    <select id="selectAllConsultantsWithDetails" resultMap="ConsultantDetailResult">
        <include refid="selectConsultantVo"/>
        where work_status = '0' and audit_status = '1'
        order by create_time desc
    </select>

    <insert id="insertConsultant" parameterType="PsyConsultant" useGeneratedKeys="true" keyProperty="id">
        insert into psy_consultants
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="bgImg != null">bg_img,</if>
            <if test="location != null">location,</if>
            <if test="address != null">address,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="phone != null">phone,</if>
            <if test="wechat != null">wechat,</if>
            <if test="name != null">name,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="gender != null">gender,</if>
            <if test="idCardNumber != null">id_card_number,</if>
            <if test="idFrontImg != null">id_front_img,</if>
            <if test="idBackImg != null">id_back_img,</if>
            <if test="startYear != null">start_year,</if>
            <if test="personalIntro != null">personal_intro,</if>
            <if test="personalTitle != null">personal_title,</if>
            <if test="totalCases != null">total_cases,</if>
            <if test="serviceCount != null">service_count,</if>
            <if test="serviceHours != null">service_hours,</if>
            <if test="canTeach != null">can_teach,</if>
            <if test="canTravel != null">can_travel,</if>
            <if test="availableTime != null">available_time,</if>
            <if test="price != null">price,</if>
            <if test="minFee != null">min_fee,</if>
            <if test="maxFee != null">max_fee,</if>
            <if test="workCase != null">work_case,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="workStatus != null">work_status,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="bgImg != null">#{bgImg},</if>
            <if test="location != null">#{location},</if>
            <if test="address != null">#{address},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="phone != null">#{phone},</if>
            <if test="wechat != null">#{wechat},</if>
            <if test="name != null">#{name},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="gender != null">#{gender},</if>
            <if test="idCardNumber != null">#{idCardNumber},</if>
            <if test="idFrontImg != null">#{idFrontImg},</if>
            <if test="idBackImg != null">#{idBackImg},</if>
            <if test="startYear != null">#{startYear},</if>
            <if test="personalIntro != null">#{personalIntro},</if>
            <if test="personalTitle != null">#{personalTitle},</if>
            <if test="totalCases != null">#{totalCases},</if>
            <if test="serviceCount != null">#{serviceCount},</if>
            <if test="serviceHours != null">#{serviceHours},</if>
            <if test="canTeach != null">#{canTeach},</if>
            <if test="canTravel != null">#{canTravel},</if>
            <if test="availableTime != null">#{availableTime},</if>
            <if test="price != null">#{price},</if>
            <if test="minFee != null">#{minFee},</if>
            <if test="maxFee != null">#{maxFee},</if>
            <if test="workCase != null">#{workCase},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="workStatus != null">#{workStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateConsultant" parameterType="PsyConsultant">
        update psy_consultants
        <trim prefix="SET" suffixOverrides=",">
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="bgImg != null">bg_img = #{bgImg},</if>
            <if test="location != null">location = #{location},</if>
            <if test="address != null">address = #{address},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="wechat != null">wechat = #{wechat},</if>
            <if test="name != null">name = #{name},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="idCardNumber != null">id_card_number = #{idCardNumber},</if>
            <if test="idFrontImg != null">id_front_img = #{idFrontImg},</if>
            <if test="idBackImg != null">id_back_img = #{idBackImg},</if>
            <if test="startYear != null">start_year = #{startYear},</if>
            <if test="personalIntro != null">personal_intro = #{personalIntro},</if>
            <if test="personalTitle != null">personal_title = #{personalTitle},</if>
            <if test="totalCases != null">total_cases = #{totalCases},</if>
            <if test="serviceCount != null">service_count = #{serviceCount},</if>
            <if test="serviceHours != null">service_hours = #{serviceHours},</if>
            <if test="canTeach != null">can_teach = #{canTeach},</if>
            <if test="canTravel != null">can_travel = #{canTravel},</if>
            <if test="availableTime != null">available_time = #{availableTime},</if>
            <if test="price != null">price = #{price},</if>
            <if test="minFee != null">min_fee = #{minFee},</if>
            <if test="maxFee != null">max_fee = #{maxFee},</if>
            <if test="workCase != null">work_case = #{workCase},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="workStatus != null">work_status = #{workStatus},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="updateConsultantWorkStatus">
        update psy_consultants
        set work_status = #{workStatus},
            update_time = sysdate()
        where id = #{id}
    </update>

    <update id="updateConsultantAuditStatus">
        update psy_consultants
        set audit_status = #{auditStatus},
            update_time = sysdate()
        where id = #{id}
    </update>

    <delete id="deleteConsultantById" parameterType="Long">
        delete from psy_consultants where id = #{id}
    </delete>

    <delete id="deleteConsultantByIds" parameterType="Long">
        delete from psy_consultants where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询所有咨询师简单信息（仅id、name和头像） -->
    <select id="selectAllSimple" resultType="com.xihuan.common.core.domain.vo.ConsultantSimpleVO">
        SELECT id, name, image_url as avatar
        FROM psy_consultants
        ORDER BY id ASC
    </select>

    <select id="selectByOptionIds" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        INNER JOIN psy_match_consultant_option co ON c.id = co.consultant_id
        WHERE co.option_id IN
        <foreach collection="optionIds" item="optionId" open="(" separator="," close=")">
            #{optionId}
        </foreach>
        AND c.audit_status = '1'
        AND c.work_status = '0'
        AND c.del_flag = '0'
    </select>

    <!-- 根据咨询领域ID筛选咨询师 -->
    <select id="selectByExpertiseIds" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        INNER JOIN psy_consultant_expertise ce ON c.id = ce.consultant_id
        WHERE ce.expertise_id IN
        <foreach collection="expertiseIds" item="expertiseId" open="(" separator="," close=")">
            #{expertiseId}
        </foreach>
        AND c.audit_status = '1'
        AND c.work_status = '0'
        AND c.del_flag = '0'
    </select>

    <!-- 根据选项ID和咨询领域ID筛选咨询师 -->
    <select id="selectByOptionAndExpertiseIds" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        INNER JOIN psy_match_consultant_option co ON c.id = co.consultant_id
        INNER JOIN psy_consultant_expertise ce ON c.id = ce.consultant_id
        WHERE co.option_id IN
        <foreach collection="optionIds" item="optionId" open="(" separator="," close=")">
            #{optionId}
        </foreach>
        AND ce.expertise_id IN
        <foreach collection="expertiseIds" item="expertiseId" open="(" separator="," close=")">
            #{expertiseId}
        </foreach>
        AND c.audit_status = '1'
        AND c.work_status = '0'
        AND c.del_flag = '0'
    </select>

    <!-- 根据性别和价格范围筛选咨询师 -->
    <select id="selectConsultantsByGenderAndPrice" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        <where>
            c.audit_status = '1'
            AND c.work_status = '0'
            AND c.del_flag = '0'
            <if test="gender != null">
                AND c.gender = #{gender}
            </if>
            <if test="priceRange != null">
                <choose>
                    <when test="priceRange == 1"> <!-- 低价格范围 -->
                        AND c.price &lt;= 300
                    </when>
                    <when test="priceRange == 2"> <!-- 中价格范围 -->
                        AND c.price > 300 AND c.price &lt;= 500
                    </when>
                    <when test="priceRange == 3"> <!-- 高价格范围 -->
                        AND c.price > 500
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据性别、价格范围和选项ID筛选咨询师 -->
    <select id="selectByGenderPriceAndOption" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        INNER JOIN psy_match_consultant_option co ON c.id = co.consultant_id
        <where>
            co.option_id IN
            <foreach collection="optionIds" item="optionId" open="(" separator="," close=")">
                #{optionId}
            </foreach>
            AND c.audit_status = '1'
            AND c.work_status = '0'
            AND c.del_flag = '0'
            <if test="gender != null">
                AND c.gender = #{gender}
            </if>
            <if test="priceRange != null">
                <choose>
                    <when test="priceRange == 1"> <!-- 低价格范围 -->
                        AND c.price &lt;= 300
                    </when>
                    <when test="priceRange == 2"> <!-- 中价格范围 -->
                        AND c.price > 300 AND c.price &lt;= 500
                    </when>
                    <when test="priceRange == 3"> <!-- 高价格范围 -->
                        AND c.price > 500
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据性别、价格范围和咨询领域ID筛选咨询师 -->
    <select id="selectByGenderPriceAndExpertise" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        INNER JOIN psy_consultant_expertise ce ON c.id = ce.consultant_id
        <where>
            ce.expertise_id IN
            <foreach collection="expertiseIds" item="expertiseId" open="(" separator="," close=")">
                #{expertiseId}
            </foreach>
            AND c.audit_status = '1'
            AND c.work_status = '0'
            AND c.del_flag = '0'
            <if test="gender != null">
                AND c.gender = #{gender}
            </if>
            <if test="priceRange != null">
                <choose>
                    <when test="priceRange == 1"> <!-- 低价格范围 -->
                        AND c.price &lt;= 300
                    </when>
                    <when test="priceRange == 2"> <!-- 中价格范围 -->
                        AND c.price > 300 AND c.price &lt;= 500
                    </when>
                    <when test="priceRange == 3"> <!-- 高价格范围 -->
                        AND c.price > 500
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据性别、价格范围、选项ID和咨询领域ID筛选咨询师 -->
    <select id="selectByGenderPriceOptionAndExpertise" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        INNER JOIN psy_match_consultant_option co ON c.id = co.consultant_id
        INNER JOIN psy_consultant_expertise ce ON c.id = ce.consultant_id
        <where>
            co.option_id IN
            <foreach collection="optionIds" item="optionId" open="(" separator="," close=")">
                #{optionId}
            </foreach>
            AND ce.expertise_id IN
            <foreach collection="expertiseIds" item="expertiseId" open="(" separator="," close=")">
                #{expertiseId}
            </foreach>
            AND c.audit_status = '1'
            AND c.work_status = '0'
            AND c.del_flag = '0'
            <if test="gender != null">
                AND c.gender = #{gender}
            </if>
            <if test="priceRange != null">
                <choose>
                    <when test="priceRange == 1"> <!-- 低价格范围 -->
                        AND c.price &lt;= 300
                    </when>
                    <when test="priceRange == 2"> <!-- 中价格范围 -->
                        AND c.price > 300 AND c.price &lt;= 500
                    </when>
                    <when test="priceRange == 3"> <!-- 高价格范围 -->
                        AND c.price > 500
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据选项ID列表和已筛选的咨询师ID列表查询咨询师 -->
    <select id="selectByOptionIdsAndConsultantIds" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        INNER JOIN psy_match_consultant_option co ON c.id = co.consultant_id
        WHERE co.option_id IN
        <foreach collection="optionIds" item="optionId" open="(" separator="," close=")">
            #{optionId}
        </foreach>
        AND c.id IN
        <foreach collection="consultantIds" item="consultantId" open="(" separator="," close=")">
            #{consultantId}
        </foreach>
        AND c.audit_status = '1'
        AND c.work_status = '0'
        AND c.del_flag = '0'
        GROUP BY c.id
        HAVING COUNT(DISTINCT co.option_id) >= #{optionCount}
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据专业领域ID列表和已筛选的咨询师ID列表查询咨询师 -->
    <select id="selectByExpertiseIdsAndConsultantIds" resultMap="ConsultantDetailResult">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        INNER JOIN psy_consultant_expertise ce ON c.id = ce.consultant_id
        WHERE ce.expertise_id IN
        <foreach collection="expertiseIds" item="expertiseId" open="(" separator="," close=")">
            #{expertiseId}
        </foreach>
        AND c.id IN
        <foreach collection="consultantIds" item="consultantId" open="(" separator="," close=")">
            #{consultantId}
        </foreach>
        AND c.audit_status = '1'
        AND c.work_status = '0'
        AND c.del_flag = '0'
        GROUP BY c.id
        HAVING COUNT(DISTINCT ce.expertise_id) >= #{expertiseCount}
        ORDER BY c.create_time DESC
    </select>

</mapper> 