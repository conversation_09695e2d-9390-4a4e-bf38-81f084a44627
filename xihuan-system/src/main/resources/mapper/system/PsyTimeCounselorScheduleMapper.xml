<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTimeCounselorScheduleMapper">
    
    <resultMap type="PsyTimeCounselorSchedule" id="PsyTimeCounselorScheduleResult">
        <result property="id"               column="id"               />
        <result property="counselorId"      column="counselor_id"     />
        <result property="scheduleDate"     column="schedule_date"    />
        <result property="startTime"        column="start_time"       />
        <result property="endTime"          column="end_time"         />
        <result property="centerId"         column="center_id"        />
        <result property="isWorking"        column="is_working"       />
        <result property="isTemplate"       column="is_template"      />
        <result property="templateId"       column="template_id"      />
        <result property="remark"           column="remark"           />
        <result property="delFlag"          column="del_flag"         />
        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
    </resultMap>

    <sql id="selectPsyTimeCounselorScheduleVo">
        select id, counselor_id, schedule_date, start_time, end_time, center_id, 
               is_working, is_template, template_id, remark, del_flag,
               create_by, create_time, update_by, update_time
        from psy_time_counselor_schedule
    </sql>

    <select id="selectScheduleList" parameterType="PsyTimeCounselorSchedule" resultMap="PsyTimeCounselorScheduleResult">
        <include refid="selectPsyTimeCounselorScheduleVo"/>
        <where>  
            del_flag = 0
            <if test="counselorId != null"> and counselor_id = #{counselorId}</if>
            <if test="scheduleDate != null"> and schedule_date = #{scheduleDate}</if>
            <if test="centerId != null"> and center_id = #{centerId}</if>
            <if test="isWorking != null"> and is_working = #{isWorking}</if>
            <if test="isTemplate != null"> and is_template = #{isTemplate}</if>
            <if test="templateId != null"> and template_id = #{templateId}</if>
        </where>
        order by schedule_date desc, counselor_id
    </select>
    
    <select id="selectScheduleById" parameterType="Long" resultMap="PsyTimeCounselorScheduleResult">
        <include refid="selectPsyTimeCounselorScheduleVo"/>
        where id = #{id}
    </select>
    
    <select id="selectScheduleByCounselorAndDate" resultMap="PsyTimeCounselorScheduleResult">
        <include refid="selectPsyTimeCounselorScheduleVo"/>
        where counselor_id = #{counselorId} and schedule_date = #{scheduleDate} and del_flag = 0
        limit 1
    </select>
    
    <select id="selectSchedulesByDateRange" resultMap="PsyTimeCounselorScheduleResult">
        <include refid="selectPsyTimeCounselorScheduleVo"/>
        where counselor_id = #{counselorId} 
          and schedule_date &gt;= #{startDate} 
          and schedule_date &lt;= #{endDate}
          and del_flag = 0
        order by schedule_date
    </select>
    
    <select id="selectSchedulesByDate" resultMap="PsyTimeCounselorScheduleResult">
        <include refid="selectPsyTimeCounselorScheduleVo"/>
        where schedule_date = #{scheduleDate} and del_flag = 0
        <if test="centerId != null"> and center_id = #{centerId}</if>
        order by counselor_id
    </select>
        
    <insert id="insertSchedule" parameterType="PsyTimeCounselorSchedule" useGeneratedKeys="true" keyProperty="id">
        insert into psy_time_counselor_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="counselorId != null">counselor_id,</if>
            <if test="scheduleDate != null">schedule_date,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="centerId != null">center_id,</if>
            <if test="isWorking != null">is_working,</if>
            <if test="isTemplate != null">is_template,</if>
            <if test="templateId != null">template_id,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="counselorId != null">#{counselorId},</if>
            <if test="scheduleDate != null">#{scheduleDate},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="centerId != null">#{centerId},</if>
            <if test="isWorking != null">#{isWorking},</if>
            <if test="isTemplate != null">#{isTemplate},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertSchedules">
        insert into psy_time_counselor_schedule (counselor_id, schedule_date, start_time, end_time, center_id,
                                                  is_working, is_template, template_id, create_time)
        values
        <foreach collection="schedules" item="schedule" separator=",">
            (#{schedule.counselorId}, #{schedule.scheduleDate}, #{schedule.startTime}, #{schedule.endTime},
             #{schedule.centerId}, #{schedule.isWorking}, #{schedule.isTemplate}, #{schedule.templateId},
             #{schedule.createTime})
        </foreach>
    </insert>

    <insert id="batchInsertSchedulesIgnoreDuplicate">
        insert ignore into psy_time_counselor_schedule (counselor_id, schedule_date, start_time, end_time, center_id,
                                                         is_working, is_template, template_id, del_flag, create_time)
        values
        <foreach collection="schedules" item="schedule" separator=",">
            (#{schedule.counselorId}, #{schedule.scheduleDate}, #{schedule.startTime}, #{schedule.endTime},
             #{schedule.centerId}, #{schedule.isWorking}, #{schedule.isTemplate}, #{schedule.templateId},
             #{schedule.delFlag}, #{schedule.createTime})
        </foreach>
    </insert>

    <update id="updateSchedule" parameterType="PsyTimeCounselorSchedule">
        update psy_time_counselor_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="counselorId != null">counselor_id = #{counselorId},</if>
            <if test="scheduleDate != null">schedule_date = #{scheduleDate},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="centerId != null">center_id = #{centerId},</if>
            <if test="isWorking != null">is_working = #{isWorking},</if>
            <if test="isTemplate != null">is_template = #{isTemplate},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduleById" parameterType="Long">
        update psy_time_counselor_schedule set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteScheduleByIds" parameterType="String">
        update psy_time_counselor_schedule set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSchedulesByDateRange">
        update psy_time_counselor_schedule set del_flag = 1 
        where counselor_id = #{counselorId} 
          and schedule_date &gt;= #{startDate} 
          and schedule_date &lt;= #{endDate}
    </delete>
    
    <select id="checkScheduleExists" resultType="int">
        select count(1) from psy_time_counselor_schedule
        where counselor_id = #{counselorId} and schedule_date = #{scheduleDate} and del_flag = 0
    </select>

    <!-- 清理重复的排班记录，保留ID最小的记录 -->
    <delete id="cleanupDuplicateSchedules">
        DELETE s1 FROM psy_time_counselor_schedule s1
        INNER JOIN psy_time_counselor_schedule s2
        WHERE s1.counselor_id = s2.counselor_id
          AND s1.schedule_date = s2.schedule_date
          AND s1.id > s2.id
          AND s1.del_flag = 0
          AND s2.del_flag = 0
    </delete>
</mapper>
