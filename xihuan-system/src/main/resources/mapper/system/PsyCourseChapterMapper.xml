<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyCourseChapterMapper">

    <!-- 基础章节字段映射 -->
    <resultMap id="BaseResultMap" type="PsyCourseChapter">
        <id column="id" property="id"/>
        <result column="course_id" property="courseId"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="chapter_title" property="chapterTitle"/>
        <result column="chapter_content" property="chapterContent"/>
        <result column="content_type" property="contentType"/>
        <result column="duration" property="duration"/>
        <result column="chapter_order" property="chapterOrder"/>
        <result column="is_trial" property="isTrial"/>
        <result column="media_url" property="mediaUrl"/>
        <result column="media_file_name" property="mediaFileName"/>
        <result column="media_file_size" property="mediaFileSize"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 章节树结构映射 -->
    <resultMap id="ChapterTreeMap" type="PsyCourseChapter" extends="BaseResultMap">
        <collection property="children" ofType="PsyCourseChapter" 
                   column="{parentId=id,courseId=course_id}" 
                   select="selectChaptersByParentId"/>
    </resultMap>

    <!-- 查询章节列表 -->
    <select id="selectChapterList" parameterType="PsyCourseChapter" resultMap="BaseResultMap">
        SELECT * FROM psy_course_chapter
        <where>
            del_flag = 0
            <if test="courseId != null">
                AND course_id = #{courseId}
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            <if test="level != null">
                AND level = #{level}
            </if>
            <if test="chapterTitle != null and chapterTitle != ''">
                AND chapter_title LIKE CONCAT('%', #{chapterTitle}, '%')
            </if>
            <if test="contentType != null">
                AND content_type = #{contentType}
            </if>
            <if test="isTrial != null">
                AND is_trial = #{isTrial}
            </if>
        </where>
        ORDER BY level ASC, chapter_order ASC
    </select>

    <!-- 根据ID查询章节 -->
    <select id="selectChapterById" resultMap="BaseResultMap">
        SELECT * FROM psy_course_chapter WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据课程ID查询章节列表 -->
    <select id="selectChaptersByCourseId" resultMap="BaseResultMap">
        SELECT * FROM psy_course_chapter 
        WHERE course_id = #{courseId} AND del_flag = 0
        ORDER BY level ASC, chapter_order ASC
    </select>

    <!-- 根据课程ID查询章节树结构 -->
    <select id="selectChapterTreeByCourseId" resultMap="ChapterTreeMap">
        SELECT * FROM psy_course_chapter 
        WHERE course_id = #{courseId} AND parent_id = 0 AND del_flag = 0
        ORDER BY chapter_order ASC
    </select>

    <!-- 根据父章节ID查询子章节列表 -->
    <select id="selectChaptersByParentId" resultMap="BaseResultMap">
        SELECT * FROM psy_course_chapter 
        WHERE parent_id = #{parentId} AND del_flag = 0
        ORDER BY chapter_order ASC
    </select>

    <!-- 新增章节 -->
    <insert id="insertChapter" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_course_chapter (
            course_id, parent_id, level, chapter_title, chapter_content,
            content_type, duration, chapter_order, is_trial, media_url,
            media_file_name, media_file_size, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{courseId}, #{parentId}, #{level}, #{chapterTitle}, #{chapterContent},
            #{contentType}, #{duration}, #{chapterOrder}, #{isTrial}, #{mediaUrl},
            #{mediaFileName}, #{mediaFileSize}, #{delFlag},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <!-- 修改章节 -->
    <update id="updateChapter" parameterType="PsyCourseChapter">
        UPDATE psy_course_chapter
        <set>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="level != null">level = #{level},</if>
            <if test="chapterTitle != null">chapter_title = #{chapterTitle},</if>
            <if test="chapterContent != null">chapter_content = #{chapterContent},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="chapterOrder != null">chapter_order = #{chapterOrder},</if>
            <if test="isTrial != null">is_trial = #{isTrial},</if>
            <if test="mediaUrl != null">media_url = #{mediaUrl},</if>
            <if test="mediaFileName != null">media_file_name = #{mediaFileName},</if>
            <if test="mediaFileSize != null">media_file_size = #{mediaFileSize},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除章节 -->
    <update id="deleteChapterById">
        UPDATE psy_course_chapter SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除章节 -->
    <update id="deleteChapterByIds">
        UPDATE psy_course_chapter SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据课程ID删除章节 -->
    <update id="deleteChapterByCourseId">
        UPDATE psy_course_chapter SET del_flag = 1 WHERE course_id = #{courseId}
    </update>

    <!-- 批量根据课程ID删除章节 -->
    <update id="deleteChapterByCourseIds">
        UPDATE psy_course_chapter SET del_flag = 1
        WHERE course_id IN
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </update>

    <!-- 查询课程的章节数量 -->
    <select id="selectChapterCount" resultType="int">
        SELECT COUNT(*) FROM psy_course_chapter 
        WHERE course_id = #{courseId} AND del_flag = 0
    </select>

    <!-- 查询课程的试听章节数量 -->
    <select id="selectTrialChapterCount" resultType="int">
        SELECT COUNT(*) FROM psy_course_chapter 
        WHERE course_id = #{courseId} AND is_trial = 1 AND del_flag = 0
    </select>

    <!-- 查询课程的总时长 -->
    <select id="selectTotalDuration" resultType="int">
        SELECT COALESCE(SUM(duration), 0) FROM psy_course_chapter 
        WHERE course_id = #{courseId} AND del_flag = 0
    </select>

    <!-- 更新章节排序 -->
    <update id="updateChapterOrder">
        UPDATE psy_course_chapter
        SET chapter_order = #{chapterOrder}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 获取所有章节（仅返回id、父id、名称） -->
    <select id="selectAllChapters" resultType="PsyCourseChapter">
        SELECT
            id,
            parent_id AS parentId,
            chapter_title AS chapterTitle
        FROM psy_course_chapter
        WHERE del_flag = 0
        ORDER BY level ASC, chapter_order ASC
    </select>

</mapper>
