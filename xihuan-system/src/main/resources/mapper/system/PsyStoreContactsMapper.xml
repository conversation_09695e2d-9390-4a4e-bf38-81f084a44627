<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyStoreContactsMapper">
    
    <resultMap type="PsyStoreContacts" id="PsyStoreContactsResult">
        <id     property="id"       column="id"        />
        <result property="storeId"  column="store_id"  />
        <result property="phone"    column="phone"     />
        <result property="contactType" column="contact_type"/>
        <result property="createBy" column="create_by"  />
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"  />
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPsyStoreContactsVo">
        select id, store_id, phone, contact_type, create_by, create_time, update_by, update_time
        from psy_store_contacts
    </sql>

    <select id="selectPsyStoreContactsList" parameterType="PsyStoreContacts" resultMap="PsyStoreContactsResult">
        <include refid="selectPsyStoreContactsVo"/>
        <where>
            <if test="storeId != null "> and store_id = #{storeId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="contactType != null  and contactType != ''"> and contact_type = #{contactType}</if>
        </where>
    </select>
    
    <select id="selectPsyStoreContactsById" parameterType="Long" resultMap="PsyStoreContactsResult">
        <include refid="selectPsyStoreContactsVo"/>
        where id = #{id}
    </select>
    
    <select id="selectPsyStoreContactsByStoreId" parameterType="Long" resultMap="PsyStoreContactsResult">
        <include refid="selectPsyStoreContactsVo"/>
        where store_id = #{storeId}
    </select>
        
    <insert id="insertPsyStoreContacts" parameterType="PsyStoreContacts" useGeneratedKeys="true" keyProperty="id">
        insert into psy_store_contacts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="phone != null">phone,</if>
            <if test="contactType != null">contact_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="contactType != null">#{contactType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePsyStoreContacts" parameterType="PsyStoreContacts">
        update psy_store_contacts
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="contactType != null">contact_type = #{contactType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePsyStoreContactsById" parameterType="Long">
        delete from psy_store_contacts where id = #{id}
    </delete>

    <delete id="deletePsyStoreContactsByIds" parameterType="String">
        delete from psy_store_contacts where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deletePsyStoreContactsByStoreId" parameterType="Long">
        delete from psy_store_contacts where store_id = #{storeId}
    </delete>
</mapper> 