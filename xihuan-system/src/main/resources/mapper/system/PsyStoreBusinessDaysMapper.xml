<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyStoreBusinessDaysMapper">
    
    <resultMap type="PsyStoreBusinessDays" id="PsyStoreBusinessDaysResult">
        <id     property="id"       column="id"        />
        <result property="storeId"  column="store_id"  />
        <result property="monday"   column="monday"    />
        <result property="tuesday"  column="tuesday"   />
        <result property="wednesday" column="wednesday"/>
        <result property="thursday" column="thursday"  />
        <result property="friday"   column="friday"    />
        <result property="saturday" column="saturday"  />
        <result property="sunday"   column="sunday"    />
    </resultMap>

    <sql id="selectPsyStoreBusinessDaysVo">
        select id, store_id, monday, tuesday, wednesday, thursday, friday, saturday, sunday
        from psy_store_business_days
    </sql>

    <select id="selectPsyStoreBusinessDaysList" parameterType="PsyStoreBusinessDays" resultMap="PsyStoreBusinessDaysResult">
        <include refid="selectPsyStoreBusinessDaysVo"/>
        <where>
            <if test="storeId != null "> and store_id = #{storeId}</if>
        </where>
    </select>
    
    <select id="selectPsyStoreBusinessDaysById" parameterType="Long" resultMap="PsyStoreBusinessDaysResult">
        <include refid="selectPsyStoreBusinessDaysVo"/>
        where id = #{id}
    </select>
    
    <select id="selectPsyStoreBusinessDaysByStoreId" parameterType="Long" resultMap="PsyStoreBusinessDaysResult">
        <include refid="selectPsyStoreBusinessDaysVo"/>
        where store_id = #{storeId}
    </select>
        
    <insert id="insertPsyStoreBusinessDays" parameterType="PsyStoreBusinessDays" useGeneratedKeys="true" keyProperty="id">
        insert into psy_store_business_days
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="monday != null">monday,</if>
            <if test="tuesday != null">tuesday,</if>
            <if test="wednesday != null">wednesday,</if>
            <if test="thursday != null">thursday,</if>
            <if test="friday != null">friday,</if>
            <if test="saturday != null">saturday,</if>
            <if test="sunday != null">sunday,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="monday != null">#{monday},</if>
            <if test="tuesday != null">#{tuesday},</if>
            <if test="wednesday != null">#{wednesday},</if>
            <if test="thursday != null">#{thursday},</if>
            <if test="friday != null">#{friday},</if>
            <if test="saturday != null">#{saturday},</if>
            <if test="sunday != null">#{sunday},</if>
        </trim>
    </insert>

    <update id="updatePsyStoreBusinessDays" parameterType="PsyStoreBusinessDays">
        update psy_store_business_days
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="monday != null">monday = #{monday},</if>
            <if test="tuesday != null">tuesday = #{tuesday},</if>
            <if test="wednesday != null">wednesday = #{wednesday},</if>
            <if test="thursday != null">thursday = #{thursday},</if>
            <if test="friday != null">friday = #{friday},</if>
            <if test="saturday != null">saturday = #{saturday},</if>
            <if test="sunday != null">sunday = #{sunday},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePsyStoreBusinessDaysById" parameterType="Long">
        delete from psy_store_business_days where id = #{id}
    </delete>

    <delete id="deletePsyStoreBusinessDaysByStoreId" parameterType="Long">
        delete from psy_store_business_days where store_id = #{storeId}
    </delete>
</mapper> 