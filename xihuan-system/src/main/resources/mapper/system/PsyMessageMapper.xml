<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyMessageMapper">
    
    <resultMap type="PsyMessage" id="PsyMessageResult">
        <id     property="messageId"      column="message_id"      />
        <result property="senderId"       column="sender_id"       />
        <result property="receiverId"     column="receiver_id"     />
        <result property="conversationId" column="conversation_id" />
        <result property="content"        column="content"         />
        <result property="messageType"    column="message_type"    />
        <result property="fileUrl"        column="file_url"        />
        <result property="sendTime"       column="send_time"       />
        <result property="isWithdrawn"    column="is_withdrawn"    />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
    </resultMap>
    
    <sql id="selectPsyMessageVo">
        select message_id, sender_id, receiver_id, conversation_id, content, message_type, file_url, 
               send_time, is_withdrawn, create_by, create_time, update_by, update_time, remark
        from psy_message
    </sql>
    
    <insert id="insertMessage" parameterType="PsyMessage" useGeneratedKeys="true" keyProperty="messageId">
        insert into psy_message (
            <if test="senderId != null">sender_id,</if>
            <if test="receiverId != null">receiver_id,</if>
            <if test="conversationId != null">conversation_id,</if>
            <if test="content != null">content,</if>
            <if test="messageType != null">message_type,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="isWithdrawn != null">is_withdrawn,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        ) values (
            <if test="senderId != null">#{senderId},</if>
            <if test="receiverId != null">#{receiverId},</if>
            <if test="conversationId != null">#{conversationId},</if>
            <if test="content != null">#{content},</if>
            <if test="messageType != null">#{messageType},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="isWithdrawn != null">#{isWithdrawn},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        )
    </insert>
    
    <select id="selectMessageById" parameterType="Long" resultMap="PsyMessageResult">
        <include refid="selectPsyMessageVo"/>
        where message_id = #{messageId}
    </select>
    
    <select id="selectMessagesByConversationId" resultMap="PsyMessageResult">
        <include refid="selectPsyMessageVo"/>
        where conversation_id = #{conversationId}
        order by send_time desc
    </select>
    
    <update id="updateMessage" parameterType="PsyMessage">
        update psy_message
        <set>
            <if test="content != null">content = #{content},</if>
            <if test="messageType != null">message_type = #{messageType},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="isWithdrawn != null">is_withdrawn = #{isWithdrawn},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where message_id = #{messageId}
    </update>
    
    <update id="markMessageAsRead">
        update psy_message_status
        set is_read = '1', read_time = sysdate()
        where message_id = #{messageId} and user_id = #{userId}
    </update>
    
    <update id="markAllMessagesAsRead">
        update psy_message_status ms
        inner join psy_message m on ms.message_id = m.message_id
        set ms.is_read = '1', ms.read_time = sysdate()
        where m.conversation_id = #{conversationId} and ms.user_id = #{userId} and ms.is_read = '0'
    </update>
    
</mapper> 