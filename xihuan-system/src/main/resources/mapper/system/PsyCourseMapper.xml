<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyCourseMapper">

    <!-- 基础课程字段映射 -->
    <resultMap id="BaseResultMap" type="PsyCourse">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="summary" property="summary"/>
        <result column="price" property="price"/>
        <result column="sales_count" property="salesCount"/>
        <result column="chapter_count" property="chapterCount"/>
        <result column="trial_chapter_count" property="trialChapterCount"/>
        <result column="instructor_id" property="instructorId"/>
        <result column="cover_image" property="coverImage"/>
        <result column="difficulty_level" property="difficultyLevel"/>
        <result column="duration_total" property="durationTotal"/>
        <result column="view_count" property="viewCount"/>
        <result column="rating_avg" property="ratingAvg"/>
        <result column="rating_count" property="ratingCount"/>
        <result column="is_free" property="isFree"/>
        <result column="tags" property="tags"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 课程列表映射（包含讲师和分类信息） -->
    <resultMap id="CourseListMap" type="PsyCourse" extends="BaseResultMap">
        <association property="instructor" javaType="PsyCourseInstructor">
            <id column="instructor_id" property="id"/>
            <result column="instructor_name" property="name"/>
            <result column="instructor_title" property="title"/>
            <result column="instructor_avatar" property="avatar"/>
        </association>
        <collection property="categories" ofType="PsyCategory">
            <id column="rel_category_id" property="categoryId"/>
            <result column="rel_category_name" property="categoryName"/>
            <result column="rel_parent_id" property="parentId"/>
        </collection>
    </resultMap>

    <!-- 课程详情映射（包含讲师、分类等信息） -->
    <resultMap id="CourseWithDetailsMap" type="PsyCourse" extends="BaseResultMap">
        <association property="instructor" javaType="PsyCourseInstructor">
            <id column="instructor_id" property="id"/>
            <result column="instructor_name" property="name"/>
            <result column="instructor_title" property="title"/>
            <result column="instructor_qualifications" property="qualifications"/>
            <result column="instructor_avatar" property="avatar"/>
        </association>
        <collection property="chapters" ofType="PsyCourseChapter">
            <id column="chapter_id" property="id"/>
            <result column="chapter_title" property="chapterTitle"/>
            <result column="chapter_content" property="chapterContent"/>
            <result column="content_type" property="contentType"/>
            <result column="duration" property="duration"/>
            <result column="chapter_order" property="chapterOrder"/>
            <result column="is_trial" property="isTrial"/>
            <result column="media_url" property="mediaUrl"/>
            <result column="media_file_name" property="mediaFileName"/>
            <result column="media_file_size" property="mediaFileSize"/>
            <result column="chapter_level" property="level"/>
            <result column="chapter_parent_id" property="parentId"/>
        </collection>
        <collection property="categories" ofType="PsyCategory">
            <id column="rel_category_id" property="categoryId"/>
            <result column="rel_category_name" property="categoryName"/>
            <result column="rel_parent_id" property="parentId"/>
            <result column="rel_order_num" property="orderNum"/>
            <result column="rel_category_status" property="status"/>
        </collection>
    </resultMap>

    <!-- 查询课程列表 -->
    <select id="selectCourseList" parameterType="PsyCourse" resultMap="CourseListMap">
        SELECT DISTINCT
            c.id,
            c.title,
            c.summary,
            c.price,
            c.sales_count,
            c.chapter_count,
            c.trial_chapter_count,
            c.instructor_id,
            c.cover_image,
            c.difficulty_level,
            c.duration_total,
            c.view_count,
            c.rating_avg,
            c.rating_count,
            c.is_free,
            c.tags,
            c.status,
            c.del_flag,
            c.create_by,
            c.create_time,
            c.update_by,
            c.update_time,
            c.remark,
            i.name AS instructor_name,
            i.title AS instructor_title,
            i.avatar AS instructor_avatar,
            cat.category_id AS rel_category_id,
            cat.category_name AS rel_category_name,
            cat.parent_id AS rel_parent_id
        FROM psy_course c
        LEFT JOIN psy_course_instructor i ON c.instructor_id = i.id
        LEFT JOIN psy_course_category_rel ccr ON c.id = ccr.course_id
        LEFT JOIN psy_category cat ON ccr.category_id = cat.category_id
        <where>
            c.del_flag = 0
            <if test="title != null and title != ''">
                AND c.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="isFree != null">
                AND c.is_free = #{isFree}
            </if>
            <if test="difficultyLevel != null">
                AND c.difficulty_level = #{difficultyLevel}
            </if>
            <if test="instructorId != null">
                AND c.instructor_id = #{instructorId}
            </if>
            <if test="categoryIds != null and categoryIds.size() > 0">
                AND ccr.category_id IN
                <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="params.beginPrice != null and params.endPrice != null">
                AND c.price BETWEEN #{params.beginPrice} AND #{params.endPrice}
            </if>
        </where>
        ORDER BY c.id DESC
    </select>

    <!-- 查询课程详情 -->
    <select id="selectCourseWithDetails" resultMap="CourseWithDetailsMap">
        SELECT
            c.*,
            i.name AS instructor_name,
            i.title AS instructor_title,
            i.qualifications AS instructor_qualifications,
            i.avatar AS instructor_avatar,
            ch.id AS chapter_id,
            ch.chapter_title,
            ch.chapter_content,
            ch.content_type,
            ch.duration,
            ch.chapter_order,
            ch.is_trial,
            ch.media_url,
            ch.media_file_name,
            ch.media_file_size,
            ch.level AS chapter_level,
            ch.parent_id AS chapter_parent_id,
            rel_cat.category_id AS rel_category_id,
            rel_cat.category_name AS rel_category_name,
            rel_cat.parent_id AS rel_parent_id,
            rel_cat.order_num AS rel_order_num,
            rel_cat.status AS rel_category_status
        FROM psy_course c
        LEFT JOIN psy_course_instructor i ON c.instructor_id = i.id AND i.del_flag = 0
        LEFT JOIN psy_course_chapter ch ON c.id = ch.course_id AND ch.del_flag = 0
        LEFT JOIN psy_course_category_rel ccr ON c.id = ccr.course_id
        LEFT JOIN psy_category rel_cat ON ccr.category_id = rel_cat.category_id
        WHERE c.id = #{id} AND c.del_flag = 0
        ORDER BY ch.level ASC, ch.chapter_order ASC
    </select>

    <!-- 根据ID查询课程 -->
    <select id="selectCourseById" resultMap="BaseResultMap">
        SELECT * FROM psy_course WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据分类ID查询课程列表 -->
    <select id="selectCoursesByCategoryId" resultMap="CourseListMap">
        SELECT DISTINCT
            c.*,
            i.name AS instructor_name,
            i.title AS instructor_title,
            i.avatar AS instructor_avatar,
            cat.category_id AS rel_category_id,
            cat.category_name AS rel_category_name,
            cat.parent_id AS rel_parent_id
        FROM psy_course c
        LEFT JOIN psy_course_instructor i ON c.instructor_id = i.id
        INNER JOIN psy_course_category_rel ccr ON c.id = ccr.course_id
        LEFT JOIN psy_category cat ON ccr.category_id = cat.category_id
        WHERE ccr.category_id = #{categoryId} AND c.del_flag = 0 AND c.status = 1
        ORDER BY c.id DESC
    </select>

    <!-- 根据讲师ID查询课程列表 -->
    <select id="selectCoursesByInstructorId" resultMap="CourseListMap">
        SELECT DISTINCT
            c.*,
            i.name AS instructor_name,
            i.title AS instructor_title,
            i.avatar AS instructor_avatar,
            cat.category_id AS rel_category_id,
            cat.category_name AS rel_category_name,
            cat.parent_id AS rel_parent_id
        FROM psy_course c
        LEFT JOIN psy_course_instructor i ON c.instructor_id = i.id
        LEFT JOIN psy_course_category_rel ccr ON c.id = ccr.course_id
        LEFT JOIN psy_category cat ON ccr.category_id = cat.category_id
        WHERE c.instructor_id = #{instructorId} AND c.del_flag = 0
        ORDER BY c.id DESC
    </select>

    <!-- 新增课程 -->
    <insert id="insertCourse" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_course (
            title, summary, price, sales_count, chapter_count, trial_chapter_count,
            instructor_id, cover_image, difficulty_level, duration_total,
            view_count, rating_avg, rating_count, is_free, tags, status, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{title}, #{summary}, #{price}, #{salesCount}, #{chapterCount}, #{trialChapterCount},
            #{instructorId}, #{coverImage}, #{difficultyLevel}, #{durationTotal},
            #{viewCount}, #{ratingAvg}, #{ratingCount}, #{isFree}, #{tags}, #{status}, #{delFlag},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <!-- 修改课程 -->
    <update id="updateCourse" parameterType="PsyCourse">
        UPDATE psy_course
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="price != null">price = #{price},</if>
            <if test="salesCount != null">sales_count = #{salesCount},</if>
            <if test="chapterCount != null">chapter_count = #{chapterCount},</if>
            <if test="trialChapterCount != null">trial_chapter_count = #{trialChapterCount},</if>
            <if test="instructorId != null">instructor_id = #{instructorId},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="difficultyLevel != null">difficulty_level = #{difficultyLevel},</if>
            <if test="durationTotal != null">duration_total = #{durationTotal},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="ratingAvg != null">rating_avg = #{ratingAvg},</if>
            <if test="ratingCount != null">rating_count = #{ratingCount},</if>
            <if test="isFree != null">is_free = #{isFree},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除课程 -->
    <update id="deleteCourseById">
        UPDATE psy_course SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除课程 -->
    <update id="deleteCourseByIds">
        UPDATE psy_course SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量插入课程分类关联 -->
    <insert id="batchInsertCourseCategories">
        INSERT INTO psy_course_category_rel (course_id, category_id) VALUES
        <foreach collection="categoryIds" item="categoryId" separator=",">
            (#{courseId}, #{categoryId})
        </foreach>
    </insert>

    <!-- 删除课程分类关联 -->
    <delete id="deleteCourseCategories">
        DELETE FROM psy_course_category_rel WHERE course_id = #{courseId}
    </delete>

    <!-- 批量删除课程分类关联 -->
    <delete id="deleteCourseCategoriesByIds">
        DELETE FROM psy_course_category_rel
        WHERE course_id IN
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>

    <!-- 更新课程统计信息 -->
    <update id="updateCourseStatistics">
        UPDATE psy_course
        SET chapter_count = #{chapterCount},
            trial_chapter_count = #{trialChapterCount},
            duration_total = #{durationTotal},
            update_time = NOW()
        WHERE id = #{courseId}
    </update>

    <!-- 更新课程评分信息 -->
    <update id="updateCourseRating">
        UPDATE psy_course
        SET rating_avg = #{ratingAvg},
            rating_count = #{ratingCount},
            update_time = NOW()
        WHERE id = #{courseId}
    </update>

    <!-- 增加课程观看次数 -->
    <update id="incrementViewCount">
        UPDATE psy_course
        SET view_count = view_count + 1,
            update_time = NOW()
        WHERE id = #{courseId}
    </update>

    <!-- 增加课程销售数量 -->
    <update id="incrementSalesCount">
        UPDATE psy_course
        SET sales_count = sales_count + #{count},
            update_time = NOW()
        WHERE id = #{courseId}
    </update>

</mapper>
