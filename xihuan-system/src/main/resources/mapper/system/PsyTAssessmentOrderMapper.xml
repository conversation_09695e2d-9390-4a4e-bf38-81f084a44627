<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTAssessmentOrderMapper">
    
    <resultMap type="PsyTAssessmentOrder" id="PsyTAssessmentOrderResult">
        <result property="id"                column="id" />
        <result property="orderNo"           column="order_no" />
        <result property="scaleId"           column="scale_id" />
        <result property="userId"            column="user_id" />
        <result property="originalPrice"     column="original_price" />
        <result property="discountPrice"     column="discount_price" />
        <result property="paymentAmount"     column="payment_amount" />
        <result property="paymentMethod"     column="payment_method" />
        <result property="paymentTime"       column="payment_time" />
        <result property="transactionId"     column="transaction_id" />
        <result property="refundAmount"      column="refund_amount" />
        <result property="refundTime"        column="refund_time" />
        <result property="refundReason"      column="refund_reason" />
        <result property="status"            column="status" />
        <result property="delFlag"           column="del_flag" />
        <result property="createBy"          column="create_by" />
        <result property="createTime"        column="create_time" />
        <result property="updateBy"          column="update_by" />
        <result property="updateTime"        column="update_time" />
        <result property="remark"            column="remark" />
    </resultMap>

    <resultMap type="PsyTAssessmentOrder" id="PsyTAssessmentOrderWithDetailsResult" extends="PsyTAssessmentOrderResult">
        <result property="scaleName"         column="scale_name" />
        <result property="scaleCode"         column="scale_code" />
        <result property="nickName"          column="nick_name" />
        <result property="userPhone"         column="user_phone" />
        <association property="scale" javaType="com.xihuan.common.core.domain.entity.PsyTScale">
            <result property="id"            column="scale_id" />
            <result property="name"          column="scale_name" />
            <result property="code"          column="scale_code" />
            <result property="price"         column="scale_price" />
            <result property="status"        column="scale_status" />
        </association>
        <association property="user" javaType="com.xihuan.common.core.domain.entity.SysUser">
            <result property="userId"        column="user_id" />
            <result property="nickName"      column="nick_name" />
            <result property="phonenumber"   column="user_phone" />
            <result property="avatar"        column="avatar" />
        </association>
    </resultMap>

    <sql id="selectOrderVo">
        select o.id, o.order_no, o.scale_id, o.user_id, o.original_price, o.discount_price, 
               o.payment_amount, o.payment_method, o.payment_time, o.transaction_id, 
               o.refund_amount, o.refund_time, o.refund_reason, o.status, o.del_flag,
               o.create_by, o.create_time, o.update_by, o.update_time, o.remark
        from psy_t_assessment_order o
    </sql>

    <sql id="selectOrderWithDetailsVo">
        select o.id, o.order_no, o.scale_id, o.user_id, o.original_price, o.discount_price, 
               o.payment_amount, o.payment_method, o.payment_time, o.transaction_id, 
               o.refund_amount, o.refund_time, o.refund_reason, o.status, o.del_flag,
               o.create_by, o.create_time, o.update_by, o.update_time, o.remark,
               s.scale_name, s.scale_code, s.price as scale_price, s.status as scale_status,
               u.nick_name, u.phonenumber as user_phone, u.avatar
        from psy_t_assessment_order o
        left join psy_t_scale s on o.scale_id = s.id
        left join sys_user u on o.user_id = u.user_id
    </sql>

    <select id="selectOrderList" parameterType="PsyTAssessmentOrder" resultMap="PsyTAssessmentOrderResult">
        <include refid="selectOrderVo"/>
        <where>  
            <if test="orderNo != null and orderNo != ''"> and o.order_no like concat('%', #{orderNo}, '%')</if>
            <if test="scaleId != null"> and o.scale_id = #{scaleId}</if>
            <if test="userId != null"> and o.user_id = #{userId}</if>
            <if test="status != null"> and o.status = #{status}</if>
            <if test="paymentMethod != null and paymentMethod != ''"> and o.payment_method = #{paymentMethod}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(o.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(o.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            and o.del_flag = '0'
        </where>
        order by o.create_time desc
    </select>
    
    <select id="selectOrderById" parameterType="Long" resultMap="PsyTAssessmentOrderResult">
        <include refid="selectOrderVo"/>
        where o.id = #{id} and o.del_flag = '0'
    </select>

    <select id="selectOrderByOrderNo" parameterType="String" resultMap="PsyTAssessmentOrderResult">
        <include refid="selectOrderVo"/>
        where o.order_no = #{orderNo} and o.del_flag = '0'
    </select>

    <select id="selectOrderWithDetails" parameterType="Long" resultMap="PsyTAssessmentOrderWithDetailsResult">
        <include refid="selectOrderWithDetailsVo"/>
        where o.id = #{id} and o.del_flag = '0'
    </select>

    <select id="selectOrdersByUserId" parameterType="Long" resultMap="PsyTAssessmentOrderResult">
        <include refid="selectOrderVo"/>
        where o.user_id = #{userId} and o.del_flag = '0'
        order by o.create_time desc
    </select>

    <select id="selectOrdersByScaleId" parameterType="Long" resultMap="PsyTAssessmentOrderResult">
        <include refid="selectOrderVo"/>
        where o.scale_id = #{scaleId} and o.del_flag = '0'
        order by o.create_time desc
    </select>

    <select id="checkUserPurchased" resultType="int">
        select count(1) from psy_t_assessment_order 
        where user_id = #{userId} and scale_id = #{scaleId} and status = 1 and del_flag = '0'
    </select>

    <insert id="insertOrder" parameterType="PsyTAssessmentOrder" useGeneratedKeys="true" keyProperty="id">
        insert into psy_t_assessment_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="scaleId != null">scale_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="discountPrice != null">discount_price,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="transactionId != null and transactionId != ''">transaction_id,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundTime != null">refund_time,</if>
            <if test="refundReason != null and refundReason != ''">refund_reason,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="scaleId != null">#{scaleId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="discountPrice != null">#{discountPrice},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="paymentMethod != null and paymentMethod != ''">#{paymentMethod},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="transactionId != null and transactionId != ''">#{transactionId},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundTime != null">#{refundTime},</if>
            <if test="refundReason != null and refundReason != ''">#{refundReason},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateOrder" parameterType="PsyTAssessmentOrder">
        update psy_t_assessment_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="discountPrice != null">discount_price = #{discountPrice},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method = #{paymentMethod},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="transactionId != null and transactionId != ''">transaction_id = #{transactionId},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="refundReason != null and refundReason != ''">refund_reason = #{refundReason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrderById" parameterType="Long">
        update psy_t_assessment_order set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteOrderByIds" parameterType="String">
        update psy_t_assessment_order set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectOrderStats" resultType="map">
        select
            count(1) as totalOrders,
            sum(case when status = 0 then 1 else 0 end) as pendingOrders,
            sum(case when status = 1 then 1 else 0 end) as paidOrders,
            sum(case when status = 2 then 1 else 0 end) as cancelledOrders,
            sum(case when status = 3 then 1 else 0 end) as refundedOrders,
            sum(payment_amount) as totalAmount,
            avg(payment_amount) as avgAmount
        from psy_t_assessment_order
        where del_flag = '0'
    </select>

    <select id="selectUserOrderStats" parameterType="Long" resultType="map">
        select
            count(1) as totalOrders,
            sum(case when status = 1 then 1 else 0 end) as paidOrders,
            sum(case when status = 1 then payment_amount else 0 end) as totalSpent,
            avg(case when status = 1 then payment_amount else null end) as avgSpent
        from psy_t_assessment_order
        where user_id = #{userId} and del_flag = '0'
    </select>

    <select id="selectScaleOrderStats" parameterType="Long" resultType="map">
        select
            count(1) as totalOrders,
            sum(case when status = 1 then 1 else 0 end) as paidOrders,
            sum(case when status = 1 then payment_amount else 0 end) as totalRevenue,
            avg(case when status = 1 then payment_amount else null end) as avgPrice
        from psy_t_assessment_order
        where scale_id = #{scaleId} and del_flag = '0'
    </select>

    <update id="updateOrderStatus">
        update psy_t_assessment_order
        set status = #{status}, update_by = #{updateBy}, update_time = now()
        <if test="status == 1">, payment_time = now()</if>
        <if test="status == 3">, refund_time = now()</if>
        where id = #{id}
    </update>

    <update id="confirmPayment">
        update psy_t_assessment_order
        set status = 1, payment_time = now(), transaction_id = #{transactionId},
            update_by = #{updateBy}, update_time = now()
        where id = #{id}
    </update>

    <update id="refundOrder">
        update psy_t_assessment_order
        set status = 3, refund_time = now(), refund_amount = #{refundAmount},
            refund_reason = #{refundReason}, update_by = #{updateBy}, update_time = now()
        where id = #{id}
    </update>

    <select id="searchOrders" resultMap="PsyTAssessmentOrderWithDetailsResult">
        <include refid="selectOrderWithDetailsVo"/>
        <where>
            <if test="keyword != null and keyword != ''">
                and (o.order_no like concat('%', #{keyword}, '%') or s.scale_name like concat('%', #{keyword}, '%') or u.nick_name like concat('%', #{keyword}, '%'))
            </if>
            <if test="userId != null"> and o.user_id = #{userId}</if>
            <if test="scaleId != null"> and o.scale_id = #{scaleId}</if>
            <if test="status != null"> and o.status = #{status}</if>
            and o.del_flag = '0'
        </where>
        order by o.create_time desc
    </select>

    <select id="selectHotScaleRanking" resultType="map">
        select o.scale_id, s.scale_name, count(1) as order_count, sum(o.payment_amount) as total_revenue
        from psy_t_assessment_order o
        left join psy_t_scale s on o.scale_id = s.id
        where o.status = 1 and o.del_flag = '0'
        group by o.scale_id, s.scale_name
        order by order_count desc, total_revenue desc
        <if test="limit != null">limit #{limit}</if>
    </select>

    <select id="selectUserConsumptionRanking" resultType="map">
        select o.user_id, u.nick_name, count(1) as order_count, sum(o.payment_amount) as total_spent
        from psy_t_assessment_order o
        left join sys_user u on o.user_id = u.user_id
        where o.status = 1 and o.del_flag = '0'
        group by o.user_id, u.nick_name
        order by total_spent desc, order_count desc
        <if test="limit != null">limit #{limit}</if>
    </select>

    <select id="selectDailyOrderStats" resultType="map">
        select
            date_format(create_time, '%Y-%m-%d') as date,
            count(1) as order_count,
            sum(case when status = 1 then 1 else 0 end) as paid_count,
            sum(case when status = 1 then payment_amount else 0 end) as revenue
        from psy_t_assessment_order
        where del_flag = '0'
        <if test="startDate != null"> and date_format(create_time, '%Y-%m-%d') >= #{startDate}</if>
        <if test="endDate != null"> and date_format(create_time, '%Y-%m-%d') &lt;= #{endDate}</if>
        group by date_format(create_time, '%Y-%m-%d')
        order by date desc
    </select>

    <select id="selectMonthlyOrderStats" resultType="map">
        select
            date_format(create_time, '%Y-%m') as month,
            count(1) as order_count,
            sum(case when status = 1 then 1 else 0 end) as paid_count,
            sum(case when status = 1 then payment_amount else 0 end) as revenue
        from psy_t_assessment_order
        where del_flag = '0'
        <if test="startMonth != null"> and date_format(create_time, '%Y-%m') >= #{startMonth}</if>
        <if test="endMonth != null"> and date_format(create_time, '%Y-%m') &lt;= #{endMonth}</if>
        group by date_format(create_time, '%Y-%m')
        order by month desc
    </select>

    <select id="selectExpiredOrders" resultMap="PsyTAssessmentOrderResult">
        <include refid="selectOrderVo"/>
        where o.status = 0 and o.create_time &lt; date_sub(now(), interval 30 minute) and o.del_flag = '0'
    </select>

    <update id="cancelExpiredOrders">
        update psy_t_assessment_order
        set status = 2, update_time = now()
        where status = 0 and create_time &lt; date_sub(now(), interval 30 minute) and del_flag = '0'
    </update>
</mapper>
