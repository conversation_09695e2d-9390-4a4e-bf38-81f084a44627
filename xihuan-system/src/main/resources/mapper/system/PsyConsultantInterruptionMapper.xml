<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyConsultantInterruptionMapper">

    <resultMap id="BaseResultMap" type="PsyConsultantInterruption">
        <id column="id" property="id"/>
        <result column="record_id" property="recordId"/>
        <result column="interrupt_type" property="interruptType"/>
        <result column="reason" property="reason"/>
        <result column="duration" property="duration"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="InterruptionWithDetailsMap" type="PsyConsultantInterruption" extends="BaseResultMap">
        <association property="record" javaType="PsyConsultationRecord">
            <id column="record_id" property="id"/>
            <result column="record_start_time" property="startTime"/>
            <result column="record_consult_type" property="consultType"/>
            <result column="user_id" property="userId"/>
            <result column="consultant_id" property="consultantId"/>
        </association>
    </resultMap>

    <select id="selectInterruptionList" parameterType="PsyConsultantInterruption" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_interruption
        <where>
            <if test="recordId != null">AND record_id = #{recordId}</if>
            <if test="interruptType != null and interruptType != ''">AND interrupt_type = #{interruptType}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectInterruptionById" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_interruption WHERE id = #{id}
    </select>

    <select id="selectInterruptionsByRecordId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_interruption 
        WHERE record_id = #{recordId}
        ORDER BY create_time ASC
    </select>

    <insert id="insertInterruption" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_consultant_interruption (
            record_id, interrupt_type, reason, duration, create_time
        ) VALUES (
            #{recordId}, #{interruptType}, #{reason}, #{duration}, #{createTime}
        )
    </insert>

    <update id="updateInterruption" parameterType="PsyConsultantInterruption">
        UPDATE psy_consultant_interruption
        <set>
            <if test="interruptType != null">interrupt_type = #{interruptType},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="duration != null">duration = #{duration},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteInterruptionById">
        DELETE FROM psy_consultant_interruption WHERE id = #{id}
    </delete>

    <delete id="deleteInterruptionByIds">
        DELETE FROM psy_consultant_interruption
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteInterruptionByRecordId">
        DELETE FROM psy_consultant_interruption WHERE record_id = #{recordId}
    </delete>

    <select id="countInterruptionsByRecordId" resultType="int">
        SELECT COUNT(*) FROM psy_consultant_interruption WHERE record_id = #{recordId}
    </select>

    <select id="sumInterruptionDuration" resultType="int">
        SELECT COALESCE(SUM(duration), 0) FROM psy_consultant_interruption WHERE record_id = #{recordId}
    </select>

    <select id="countInterruptionsByType" resultType="int">
        SELECT COUNT(*) FROM psy_consultant_interruption 
        WHERE record_id = #{recordId} AND interrupt_type = #{interruptType}
    </select>

</mapper>
