<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyMeditationReviewMapper">

    <resultMap id="BaseResultMap" type="PsyMeditationReview">
        <id column="id" property="id"/>
        <result column="meditation_id" property="meditationId"/>
        <result column="user_id" property="userId"/>
        <result column="content" property="content"/>
        <result column="rating" property="rating"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="ReviewWithDetailsMap" type="PsyMeditationReview" extends="BaseResultMap">
        <association property="meditation" javaType="PsyMeditation">
            <id column="meditation_id" property="id"/>
            <result column="meditation_title" property="title"/>
        </association>
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
        </association>
    </resultMap>

    <select id="selectReviewList" parameterType="PsyMeditationReview" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_review
        <where>
            del_flag = 0
            <if test="meditationId != null">AND meditation_id = #{meditationId}</if>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="rating != null">AND rating = #{rating}</if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectReviewById" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_review WHERE id = #{id} AND del_flag = 0
    </select>

    <select id="selectReviewWithDetails" resultMap="ReviewWithDetailsMap">
        SELECT r.*, m.title AS meditation_title, u.user_name, u.nick_name
        FROM psy_meditation_review r
        LEFT JOIN psy_meditation m ON r.meditation_id = m.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.id = #{id} AND r.del_flag = 0
    </select>

    <select id="selectReviewsByMeditationId" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_review 
        WHERE meditation_id = #{meditationId} AND del_flag = 0
        ORDER BY id DESC
    </select>

    <select id="selectReviewsByUserId" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_review 
        WHERE user_id = #{userId} AND del_flag = 0
        ORDER BY id DESC
    </select>

    <insert id="insertReview" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_meditation_review (
            meditation_id, user_id, content, rating, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{meditationId}, #{userId}, #{content}, #{rating}, #{delFlag},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <update id="updateReview" parameterType="PsyMeditationReview">
        UPDATE psy_meditation_review
        <set>
            <if test="content != null">content = #{content},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteReviewById">
        UPDATE psy_meditation_review SET del_flag = 1 WHERE id = #{id}
    </update>

    <update id="deleteReviewByIds">
        UPDATE psy_meditation_review SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteReviewByMeditationId">
        UPDATE psy_meditation_review SET del_flag = 1 WHERE meditation_id = #{meditationId}
    </update>

    <update id="deleteReviewByMeditationIds">
        UPDATE psy_meditation_review SET del_flag = 1
        WHERE meditation_id IN
        <foreach item="meditationId" collection="array" open="(" separator="," close=")">
            #{meditationId}
        </foreach>
    </update>

    <select id="calculateAverageRating" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(rating), 0) FROM psy_meditation_review 
        WHERE meditation_id = #{meditationId} AND del_flag = 0
    </select>

    <select id="countReviewsByMeditationId" resultType="int">
        SELECT COUNT(*) FROM psy_meditation_review 
        WHERE meditation_id = #{meditationId} AND del_flag = 0
    </select>

    <select id="checkUserReviewed" resultType="int">
        SELECT COUNT(*) FROM psy_meditation_review 
        WHERE user_id = #{userId} AND meditation_id = #{meditationId} AND del_flag = 0
    </select>

</mapper>
