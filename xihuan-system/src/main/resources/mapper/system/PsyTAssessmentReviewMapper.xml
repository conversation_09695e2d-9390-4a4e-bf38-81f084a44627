<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTAssessmentReviewMapper">
    
    <resultMap type="PsyTAssessmentReview" id="PsyTAssessmentReviewResult">
        <result property="id"                column="id" />
        <result property="scaleId"           column="scale_id" />
        <result property="userId"            column="user_id" />
        <result property="recordId"          column="record_id" />
        <result property="rating"            column="rating" />
        <result property="content"           column="content" />
        <result property="isAnonymous"       column="is_anonymous" />
        <result property="status"            column="status" />
        <result property="auditRemark"       column="audit_remark" />
        <result property="auditTime"         column="audit_time" />
        <result property="auditBy"           column="audit_by" />
        <result property="likeCount"         column="like_count" />
        <result property="replyCount"        column="reply_count" />
        <result property="delFlag"           column="del_flag" />
        <result property="createBy"          column="create_by" />
        <result property="createTime"        column="create_time" />
        <result property="updateBy"          column="update_by" />
        <result property="updateTime"        column="update_time" />
        <result property="remark"            column="remark" />
    </resultMap>

    <resultMap type="PsyTAssessmentReview" id="PsyTAssessmentReviewWithDetailsResult" extends="PsyTAssessmentReviewResult">
        <result property="scaleName"         column="scale_name" />
        <result property="scaleCode"         column="scale_code" />
        <result property="nickName"          column="nick_name" />
        <result property="avatar"            column="avatar" />
        <association property="scale" javaType="PsyTScale">
            <result property="id"            column="scale_id" />
            <result property="scaleName"     column="scale_name" />
            <result property="scaleCode"     column="scale_code" />
            <result property="status"        column="scale_status" />
        </association>
        <association property="user" javaType="SysUser">
            <result property="userId"        column="user_id" />
            <result property="nickName"      column="nick_name" />
            <result property="avatar"        column="avatar" />
        </association>
    </resultMap>

    <sql id="selectReviewVo">
        select r.id, r.scale_id, r.user_id, r.record_id, r.rating, r.content,
               r.is_anonymous, r.status, r.audit_remark, r.audit_time, r.audit_by,
               r.like_count, r.reply_count, r.del_flag,
               r.create_by, r.create_time, r.update_by, r.update_time, r.remark
        from psy_t_review r
    </sql>

    <sql id="selectReviewWithDetailsVo">
        select r.id, r.scale_id, r.user_id, r.record_id, r.rating, r.content,
               r.is_anonymous, r.status, r.audit_remark, r.audit_time, r.audit_by,
               r.like_count, r.reply_count, r.del_flag,
               r.create_by, r.create_time, r.update_by, r.update_time, r.remark,
               s.name as scale_name, s.code as scale_code, s.status as scale_status,
               u.nick_name, u.avatar
        from psy_t_review r
        left join psy_t_scale s on r.scale_id = s.id
        left join sys_user u on r.user_id = u.user_id
    </sql>

    <select id="selectReviewList" parameterType="PsyTAssessmentReview" resultMap="PsyTAssessmentReviewResult">
        <include refid="selectReviewVo"/>
        <where>  
            <if test="scaleId != null"> and r.scale_id = #{scaleId}</if>
            <if test="userId != null"> and r.user_id = #{userId}</if>
            <if test="recordId != null"> and r.record_id = #{recordId}</if>
            <if test="orderId != null"> and r.order_id = #{orderId}</if>
            <if test="rating != null"> and r.rating = #{rating}</if>
            <if test="status != null"> and r.status = #{status}</if>
            <if test="isAnonymous != null"> and r.is_anonymous = #{isAnonymous}</if>
            <if test="isTop != null"> and r.is_top = #{isTop}</if>
            <if test="content != null and content != ''"> and r.content like concat('%', #{content}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(r.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(r.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            and r.del_flag = 0
        </where>
        order by r.create_time desc
    </select>
    
    <select id="selectReviewById" parameterType="Long" resultMap="PsyTAssessmentReviewResult">
        <include refid="selectReviewVo"/>
        where r.id = #{id} and r.del_flag = 0
    </select>

    <select id="selectReviewWithDetails" parameterType="Long" resultMap="PsyTAssessmentReviewWithDetailsResult">
        <include refid="selectReviewWithDetailsVo"/>
        where r.id = #{id} and r.del_flag = 0
    </select>

    <select id="selectReviewsByScaleId" parameterType="Long" resultMap="PsyTAssessmentReviewResult">
        <include refid="selectReviewVo"/>
        where r.scale_id = #{scaleId} and r.status = 1 and r.del_flag = 0
        order by r.like_count desc, r.create_time desc
    </select>

    <select id="selectReviewsByUserId" parameterType="Long" resultMap="PsyTAssessmentReviewResult">
        <include refid="selectReviewVo"/>
        where r.user_id = #{userId} and r.del_flag = 0
        order by r.create_time desc
    </select>

    <select id="selectReviewByRecordId" parameterType="Long" resultMap="PsyTAssessmentReviewResult">
        <include refid="selectReviewVo"/>
        where r.record_id = #{recordId} and r.del_flag = 0
    </select>

    <!-- 注意：数据库表中没有order_id字段，此方法暂时注释
    <select id="selectReviewByOrderId" parameterType="Long" resultMap="PsyTAssessmentReviewResult">
        <include refid="selectReviewVo"/>
        where r.order_id = #{orderId} and r.del_flag = 0
    </select>
    -->

    <select id="checkUserReviewed" resultType="int">
        select count(1) from psy_t_review
        where user_id = #{userId} and scale_id = #{scaleId}
        <if test="recordId != null"> and record_id = #{recordId}</if>
        and del_flag = 0
    </select>

    <select id="selectPendingReviews" resultMap="PsyTAssessmentReviewWithDetailsResult">
        <include refid="selectReviewWithDetailsVo"/>
        where r.status = 0 and r.del_flag = 0
        order by r.create_time asc
    </select>

    <!-- 注意：数据库表中没有is_top和top_time字段，此方法暂时注释
    <select id="selectTopReviews" parameterType="Long" resultMap="PsyTAssessmentReviewResult">
        <include refid="selectReviewVo"/>
        where r.scale_id = #{scaleId} and r.is_top = 1 and r.status = 1 and r.del_flag = 0
        order by r.top_time desc
    </select>
    -->

    <select id="selectHotReviews" resultMap="PsyTAssessmentReviewResult">
        <include refid="selectReviewVo"/>
        where r.scale_id = #{scaleId} and r.status = 1 and r.del_flag = 0
        order by r.like_count desc, r.reply_count desc, r.create_time desc
        <if test="limit != null">limit #{limit}</if>
    </select>

    <select id="selectLatestReviews" parameterType="Integer" resultMap="PsyTAssessmentReviewWithDetailsResult">
        <include refid="selectReviewWithDetailsVo"/>
        where r.status = 1 and r.del_flag = 0
        order by r.create_time desc
        <if test="limit != null">limit #{limit}</if>
    </select>

    <insert id="insertReview" parameterType="PsyTAssessmentReview" useGeneratedKeys="true" keyProperty="id">
        insert into psy_t_review
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">scale_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="recordId != null">record_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="rating != null">rating,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="isAnonymous != null">is_anonymous,</if>
            <if test="status != null">status,</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditBy != null and auditBy != ''">audit_by,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="replyCount != null">reply_count,</if>
            <if test="isTop != null">is_top,</if>
            <if test="topTime != null">top_time,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">#{scaleId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="recordId != null">#{recordId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="rating != null">#{rating},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="isAnonymous != null">#{isAnonymous},</if>
            <if test="status != null">#{status},</if>
            <if test="auditRemark != null and auditRemark != ''">#{auditRemark},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditBy != null and auditBy != ''">#{auditBy},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="replyCount != null">#{replyCount},</if>
            <if test="isTop != null">#{isTop},</if>
            <if test="topTime != null">#{topTime},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateReview" parameterType="PsyTAssessmentReview">
        update psy_t_review
        <trim prefix="SET" suffixOverrides=",">
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="recordId != null">record_id = #{recordId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="content != null">content = #{content},</if>
            <if test="isAnonymous != null">is_anonymous = #{isAnonymous},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="replyCount != null">reply_count = #{replyCount},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="topTime != null">top_time = #{topTime},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReviewById" parameterType="Long">
        update psy_t_review set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteReviewByIds" parameterType="String">
        update psy_t_review set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="auditReview">
        update psy_t_review
        set status = #{status}, audit_remark = #{auditRemark}, audit_by = #{auditBy}, audit_time = now()
        where id = #{id}
    </update>

    <update id="topReview">
        update psy_t_assessment_review 
        set is_top = #{isTop}, update_by = #{updateBy}, update_time = now()
        <if test="isTop == 1">, top_time = now()</if>
        where id = #{id}
    </update>

    <update id="likeReview" parameterType="Long">
        update psy_t_review set like_count = like_count + 1 where id = #{id}
    </update>

    <update id="unlikeReview" parameterType="Long">
        update psy_t_review set like_count = like_count - 1 where id = #{id} and like_count > 0
    </update>

    <update id="increaseReplyCount" parameterType="Long">
        update psy_t_review set reply_count = reply_count + 1 where id = #{id}
    </update>

    <update id="decreaseReplyCount" parameterType="Long">
        update psy_t_review set reply_count = reply_count - 1 where id = #{id} and reply_count > 0
    </update>

    <select id="selectScaleReviewStats" parameterType="Long" resultType="map">
        select
            count(1) as totalReviews,
            avg(rating) as avgRating,
            sum(case when rating = 5 then 1 else 0 end) as fiveStars,
            sum(case when rating = 4 then 1 else 0 end) as fourStars,
            sum(case when rating = 3 then 1 else 0 end) as threeStars,
            sum(case when rating = 2 then 1 else 0 end) as twoStars,
            sum(case when rating = 1 then 1 else 0 end) as oneStars
        from psy_t_review
        where scale_id = #{scaleId} and status = 1 and del_flag = 0
    </select>

    <select id="selectUserReviewStats" parameterType="Long" resultType="map">
        select
            count(1) as totalReviews,
            avg(rating) as avgRating,
            sum(like_count) as totalLikes,
            sum(reply_count) as totalReplies
        from psy_t_review
        where user_id = #{userId} and del_flag = 0
    </select>

    <select id="selectReviewStats" resultType="map">
        select
            count(1) as totalReviews,
            sum(case when status = 0 then 1 else 0 end) as pendingReviews,
            sum(case when status = 1 then 1 else 0 end) as approvedReviews,
            sum(case when status = 2 then 1 else 0 end) as rejectedReviews,
            avg(rating) as avgRating,
            sum(like_count) as totalLikes,
            sum(reply_count) as totalReplies
        from psy_t_review
        where del_flag = 0
    </select>

    <select id="searchReviews" resultMap="PsyTAssessmentReviewWithDetailsResult">
        <include refid="selectReviewWithDetailsVo"/>
        <where>
            <if test="keyword != null and keyword != ''">
                and (r.content like concat('%', #{keyword}, '%') or s.name like concat('%', #{keyword}, '%'))
            </if>
            <if test="scaleId != null"> and r.scale_id = #{scaleId}</if>
            <if test="userId != null"> and r.user_id = #{userId}</if>
            <if test="status != null"> and r.status = #{status}</if>
            <if test="rating != null"> and r.rating = #{rating}</if>
            and r.del_flag = 0
        </where>
        order by r.create_time desc
    </select>

    <select id="selectReviewRanking" resultType="map">
        select r.id, r.content, r.rating, r.like_count, r.reply_count, r.create_time,
               s.name as scale_name, u.nick_name
        from psy_t_review r
        left join psy_t_scale s on r.scale_id = s.id
        left join sys_user u on r.user_id = u.user_id
        where r.status = 1 and r.del_flag = 0
        <choose>
            <when test="type == 'like'">
                order by r.like_count desc
            </when>
            <when test="type == 'reply'">
                order by r.reply_count desc
            </when>
            <otherwise>
                order by r.create_time desc
            </otherwise>
        </choose>
        <if test="limit != null">limit #{limit}</if>
    </select>

    <select id="selectRatingDistribution" parameterType="Long" resultType="map">
        select
            rating,
            count(1) as count,
            round(count(1) * 100.0 / (select count(1) from psy_t_review where scale_id = #{scaleId} and status = 1 and del_flag = 0), 2) as percentage
        from psy_t_review
        where scale_id = #{scaleId} and status = 1 and del_flag = 0
        group by rating
        order by rating desc
    </select>

    <select id="checkUserLiked" resultType="int">
        select count(1) from psy_t_assessment_review_like
        where review_id = #{reviewId} and user_id = #{userId}
    </select>

    <update id="batchAuditReviews">
        update psy_t_assessment_review
        set status = #{status}, audit_remark = #{auditRemark}, audit_by = #{auditBy}, audit_time = now()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 注意：评价回复表可能不存在，此方法暂时注释
    <select id="selectReviewReplies" parameterType="Long" resultType="map">
        select id, review_id, user_id, content, create_time,
               (select nick_name from sys_user where user_id = reply.user_id) as nick_name
        from psy_t_review_reply reply
        where review_id = #{reviewId} and del_flag = 0
        order by create_time asc
    </select>
    -->
</mapper>
