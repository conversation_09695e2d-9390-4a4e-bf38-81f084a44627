<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTInterpretationMapper">

    <!-- 结果映射 -->
    <resultMap id="InterpretationResultMap" type="PsyTInterpretation">
        <id property="id" column="id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="minScore" column="min_score"/>
        <result property="maxScore" column="max_score"/>
        <result property="levelDescription" column="interpretation_text"/>
        <result property="levelName" column="level_name"/>
        <result property="color" column="level_color"/>
        <result property="dimension" column="dimension"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <!-- 注意：字段映射说明
        - interpretation_text 字段映射到 levelDescription 属性
        - 以下字段在数据库表中不存在，已删除：
        <result property="suggestions" column="suggestions"/>
        <result property="color" column="color"/>
        <result property="orderNum" column="order_num"/>
        <result property="remark" column="remark"/>
        -->
    </resultMap>

    <!-- 带量表信息的结果映射 -->
    <resultMap id="InterpretationWithScaleMap" type="PsyTInterpretation" extends="InterpretationResultMap">
        <association property="scale" javaType="PsyTScale">
            <id property="id" column="s_id"/>
            <result property="name" column="s_name"/>
            <result property="code" column="s_code"/>
        </association>
    </resultMap>

    <!-- 通用查询字段 -->
    <sql id="selectInterpretationVo">
        SELECT i.id, i.scale_id, i.min_score, i.max_score, i.interpretation_text,
               i.level_name, i.level_color, i.dimension, i.del_flag,
               i.create_by, i.create_time, i.update_by, i.update_time
        FROM psy_t_interpretation i
    </sql>

    <!-- 查询解释列表 -->
    <select id="selectInterpretationList" parameterType="PsyTInterpretation" resultMap="InterpretationResultMap">
        <include refid="selectInterpretationVo"/>
        WHERE i.del_flag = '0'
        <if test="scaleId != null">
            AND i.scale_id = #{scaleId}
        </if>
        <if test="dimension != null and dimension != ''">
            AND i.dimension = #{dimension}
        </if>
        <if test="levelName != null and levelName != ''">
            AND i.level_name LIKE CONCAT('%', #{levelName}, '%')
        </if>
        <if test="minScore != null">
            AND i.min_score >= #{minScore}
        </if>
        <if test="maxScore != null">
            AND i.max_score &lt;= #{maxScore}
        </if>
        ORDER BY i.scale_id, i.dimension, i.min_score
    </select>

    <!-- 根据ID查询解释 -->
    <select id="selectInterpretationById" parameterType="Long" resultMap="InterpretationResultMap">
        <include refid="selectInterpretationVo"/>
        WHERE i.id = #{id} AND i.del_flag = '0'
    </select>

    <!-- 根据量表ID查询解释列表 -->
    <select id="selectInterpretationsByScaleId" parameterType="Long" resultMap="InterpretationResultMap">
        <include refid="selectInterpretationVo"/>
        WHERE i.scale_id = #{scaleId} AND i.del_flag = '0'
        ORDER BY i.dimension, i.min_score
    </select>

    <!-- 根据量表ID和维度查询解释列表 -->
    <select id="selectInterpretationsByScaleAndDimension" resultMap="InterpretationResultMap">
        <include refid="selectInterpretationVo"/>
        WHERE i.scale_id = #{scaleId} AND i.del_flag = '0'
        <if test="dimension != null and dimension != ''">
            AND i.dimension = #{dimension}
        </if>
        <if test="dimension == null or dimension == ''">
            AND (i.dimension IS NULL OR i.dimension = '')
        </if>
        ORDER BY i.min_score
    </select>

    <!-- 根据分数查询匹配的解释 -->
    <select id="selectInterpretationByScore" resultMap="InterpretationResultMap">
        <include refid="selectInterpretationVo"/>
        WHERE i.scale_id = #{scaleId} AND i.del_flag = '0'
        AND #{score} >= i.min_score AND #{score}  &lt;= i.max_score
        <if test="dimension != null and dimension != ''">
            AND i.dimension = #{dimension}
        </if>
        <if test="dimension == null or dimension == ''">
            AND (i.dimension IS NULL OR i.dimension = '')
        </if>
        ORDER BY i.min_score
        LIMIT 1
    </select>

    <!-- 查询总分解释列表 -->
    <select id="selectTotalScoreInterpretations" parameterType="Long" resultMap="InterpretationResultMap">
        <include refid="selectInterpretationVo"/>
        WHERE i.scale_id = #{scaleId} AND i.del_flag = '0'
        AND (i.dimension IS NULL OR i.dimension = '' OR i.dimension = '总分')
        ORDER BY i.order_num, i.min_score
    </select>

    <!-- 查询维度解释列表 -->
    <select id="selectDimensionInterpretations" parameterType="Long" resultMap="InterpretationResultMap">
        <include refid="selectInterpretationVo"/>
        WHERE i.scale_id = #{scaleId} AND i.del_flag = '0'
        AND i.dimension IS NOT NULL AND i.dimension != '' AND i.dimension != '总分'
        ORDER BY i.dimension, i.order_num, i.min_score
    </select>

    <!-- 查询量表的所有维度 -->
    <select id="selectDimensionsByScaleId" parameterType="Long" resultType="String">
        SELECT DISTINCT i.dimension
        FROM psy_t_interpretation i
        WHERE i.scale_id = #{scaleId} AND i.del_flag = '0'
        AND i.dimension IS NOT NULL AND i.dimension != '' AND i.dimension != '总分'
        ORDER BY i.dimension
    </select>

    <!-- 新增解释 -->
    <insert id="insertInterpretation" parameterType="PsyTInterpretation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_interpretation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">scale_id,</if>
            <if test="minScore != null">min_score,</if>
            <if test="maxScore != null">max_score,</if>
            <if test="levelDescription != null">interpretation_text,</if>
            <if test="levelName != null and levelName != ''">level_name,</if>
            <if test="color != null">level_color,</if>
            <if test="dimension != null">dimension,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            create_time
            <!-- 注意：以下字段在数据库表中不存在，已删除
            <if test="levelDescription != null">level_description,</if>
            <if test="suggestions != null">suggestions,</if>
            <if test="color != null">color,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="remark != null">remark,</if>
            -->
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">#{scaleId},</if>
            <if test="minScore != null">#{minScore},</if>
            <if test="maxScore != null">#{maxScore},</if>
            <if test="levelDescription != null">#{levelDescription},</if>
            <if test="levelName != null and levelName != ''">#{levelName},</if>
            <if test="color != null">#{color},</if>
            <if test="dimension != null">#{dimension},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            NOW()
            <!-- 注意：以下字段在数据库表中不存在，已删除
            <if test="levelDescription != null">#{levelDescription},</if>
            <if test="suggestions != null">#{suggestions},</if>
            <if test="color != null">#{color},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="remark != null">#{remark},</if>
            -->
        </trim>
    </insert>

    <!-- 修改解释 -->
    <update id="updateInterpretation" parameterType="PsyTInterpretation">
        UPDATE psy_t_interpretation
        <trim prefix="SET" suffixOverrides=",">
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="minScore != null">min_score = #{minScore},</if>
            <if test="maxScore != null">max_score = #{maxScore},</if>
            <if test="levelDescription != null">interpretation_text = #{levelDescription},</if>
            <if test="levelName != null and levelName != ''">level_name = #{levelName},</if>
            <if test="color != null">level_color = #{color},</if>
            <if test="dimension != null">dimension = #{dimension},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
            <!-- 注意：以下字段在数据库表中不存在，已删除
            <if test="levelDescription != null">level_description = #{levelDescription},</if>
            <if test="suggestions != null">suggestions = #{suggestions},</if>
            <if test="color != null">color = #{color},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="remark != null">remark = #{remark},</if>
            -->
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 删除解释 -->
    <update id="deleteInterpretationById" parameterType="Long">
        UPDATE psy_t_interpretation SET del_flag = '1' WHERE id = #{id}
    </update>

    <!-- 批量删除解释 -->
    <update id="deleteInterpretationByIds" parameterType="Long">
        UPDATE psy_t_interpretation SET del_flag = '1' WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据量表ID删除所有解释 -->
    <update id="deleteInterpretationsByScaleId" parameterType="Long">
        UPDATE psy_t_interpretation SET del_flag = '1' WHERE scale_id = #{scaleId}
    </update>

    <!-- 检查分数范围是否重叠 -->
    <select id="checkScoreRangeOverlap" resultType="int">
        SELECT COUNT(1)
        FROM psy_t_interpretation
        WHERE scale_id = #{scaleId} AND del_flag = '0'
        <if test="dimension != null and dimension != ''">
            AND dimension = #{dimension}
        </if>
        <if test="dimension == null or dimension == ''">
            AND (dimension IS NULL OR dimension = '')
        </if>
        AND (
            (#{minScore} >= min_score AND #{minScore}  &lt;= max_score)
            OR (#{maxScore} >= min_score AND #{maxScore}  &lt;= max_score)
            OR (#{minScore}  &lt;= min_score AND #{maxScore} >= max_score)
        )
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量插入解释 -->
    <insert id="batchInsertInterpretations" parameterType="java.util.List">
        INSERT INTO psy_t_interpretation (
            scale_id, dimension, min_score, max_score, level_name, level_description,
            suggestions, color, order_num, del_flag, create_by, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.scaleId}, #{item.dimension}, #{item.minScore}, #{item.maxScore},
                #{item.levelName}, #{item.levelDescription}, #{item.suggestions}, #{item.color},
                #{item.orderNum}, #{item.delFlag}, #{item.createBy}, NOW()
            )
        </foreach>
    </insert>

    <!-- 查询解释统计信息 -->
    <select id="selectInterpretationStats" parameterType="Long" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN dimension IS NULL OR dimension = '' THEN 1 END) as total_score_count,
            COUNT(CASE WHEN dimension IS NOT NULL AND dimension != '' THEN 1 END) as dimension_count,
            COUNT(DISTINCT dimension) as dimension_types
        FROM psy_t_interpretation
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

    <!-- 根据等级名称查询解释 -->
    <select id="selectInterpretationByLevel" resultMap="InterpretationResultMap">
        <include refid="selectInterpretationVo"/>
        WHERE i.scale_id = #{scaleId} AND i.level_name = #{levelName} AND i.del_flag = '0'
        <if test="dimension != null and dimension != ''">
            AND i.dimension = #{dimension}
        </if>
        <if test="dimension == null or dimension == ''">
            AND (i.dimension IS NULL OR i.dimension = '')
        </if>
        LIMIT 1
    </select>

    <!-- 更新解释的显示顺序（注意：order_num字段在数据库中不存在，此方法已禁用） -->
    <update id="updateInterpretationOrder">
        <!-- UPDATE psy_t_interpretation SET order_num = #{orderNum}, update_time = NOW()
        WHERE id = #{id} -->
        UPDATE psy_t_interpretation SET update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询解释的最大显示顺序 -->
    <select id="selectMaxOrderNum" resultType="Integer">
        SELECT IFNULL(MAX(order_num), 0)
        FROM psy_t_interpretation
        WHERE scale_id = #{scaleId} AND del_flag = '0'
        <if test="dimension != null and dimension != ''">
            AND dimension = #{dimension}
        </if>
        <if test="dimension == null or dimension == ''">
            AND (dimension IS NULL OR dimension = '')
        </if>
    </select>
</mapper>
