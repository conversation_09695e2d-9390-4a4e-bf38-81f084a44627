<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTScoringConfigMapper">
    
    <resultMap type="PsyTScoringConfig" id="ScoringConfigResult">
        <result property="id"                    column="id" />
        <result property="scaleId"               column="scale_id" />
        <result property="scoringType"           column="scoring_type" />
        <result property="formulaConfig"         column="formula_config" />
        <result property="reverseItems"          column="reverse_items" />
        <result property="standardMultiplier"    column="standard_multiplier" />
        <result property="createTime"            column="create_time" />
        <result property="status"                column="status" />
        <result property="description"           column="description" />
        <result property="updateBy"              column="update_by" />
        <result property="updateTime"            column="update_time" />
        <!-- 关联查询字段 -->
        <result property="scaleCode"             column="scale_code" />
        <result property="scaleName"             column="scale_name" />
        <result property="scoringMethodDesc"     column="scoring_method_desc" />
    </resultMap>

    <sql id="selectScoringConfigVo">
        SELECT 
            sc.id, sc.scale_id, sc.scoring_type, sc.formula_config, sc.reverse_items,
            sc.standard_multiplier, sc.create_time, sc.status, sc.description,
            sc.update_by, sc.update_time,
            s.code as scale_code, s.name as scale_name, s.scoring_method as scoring_method_desc
        FROM psy_t_scoring_config sc
        LEFT JOIN psy_t_scale s ON sc.scale_id = s.id
    </sql>

    <select id="selectScoringConfigList" parameterType="PsyTScoringConfig" resultMap="ScoringConfigResult">
        <include refid="selectScoringConfigVo"/>
        <where>  
            <if test="scaleId != null">
                AND sc.scale_id = #{scaleId}
            </if>
            <if test="scoringType != null and scoringType != ''">
                AND sc.scoring_type = #{scoringType}
            </if>
            <if test="status != null">
                AND sc.status = #{status}
            </if>
            <if test="scaleCode != null and scaleCode != ''">
                AND s.code LIKE CONCAT('%', #{scaleCode}, '%')
            </if>
            <if test="scaleName != null and scaleName != ''">
                AND s.name LIKE CONCAT('%', #{scaleName}, '%')
            </if>
        </where>
        ORDER BY sc.create_time DESC
    </select>
    
    <select id="selectScoringConfigById" parameterType="Long" resultMap="ScoringConfigResult">
        <include refid="selectScoringConfigVo"/>
        WHERE sc.id = #{id}
    </select>

    <select id="selectByScaleId" parameterType="Long" resultMap="ScoringConfigResult">
        <include refid="selectScoringConfigVo"/>
        WHERE sc.scale_id = #{scaleId}
        LIMIT 1
    </select>

    <select id="selectConfiguredScaleIds" resultType="Long">
        SELECT DISTINCT scale_id FROM psy_t_scoring_config WHERE status = 1
    </select>
        
    <insert id="insertScoringConfig" parameterType="PsyTScoringConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_scoring_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">scale_id,</if>
            <if test="scoringType != null and scoringType != ''">scoring_type,</if>
            <if test="formulaConfig != null">formula_config,</if>
            <if test="reverseItems != null">reverse_items,</if>
            <if test="standardMultiplier != null">standard_multiplier,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">#{scaleId},</if>
            <if test="scoringType != null and scoringType != ''">#{scoringType},</if>
            <if test="formulaConfig != null">#{formulaConfig},</if>
            <if test="reverseItems != null">#{reverseItems},</if>
            <if test="standardMultiplier != null">#{standardMultiplier},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateScoringConfig" parameterType="PsyTScoringConfig">
        UPDATE psy_t_scoring_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="scoringType != null and scoringType != ''">scoring_type = #{scoringType},</if>
            <if test="formulaConfig != null">formula_config = #{formulaConfig},</if>
            <if test="reverseItems != null">reverse_items = #{reverseItems},</if>
            <if test="standardMultiplier != null">standard_multiplier = #{standardMultiplier},</if>
            <if test="status != null">status = #{status},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteScoringConfigById" parameterType="Long">
        DELETE FROM psy_t_scoring_config WHERE id = #{id}
    </delete>

    <delete id="deleteScoringConfigByIds" parameterType="String">
        DELETE FROM psy_t_scoring_config WHERE id IN 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteScoringConfigByScaleId" parameterType="Long">
        DELETE FROM psy_t_scoring_config WHERE scale_id = #{scaleId}
    </delete>

    <insert id="batchInsertScoringConfig" parameterType="java.util.List">
        INSERT INTO psy_t_scoring_config (scale_id, scoring_type, formula_config, reverse_items, standard_multiplier, status, description, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.scaleId}, #{item.scoringType}, #{item.formulaConfig}, #{item.reverseItems}, #{item.standardMultiplier}, #{item.status}, #{item.description}, sysdate())
        </foreach>
    </insert>

</mapper>
