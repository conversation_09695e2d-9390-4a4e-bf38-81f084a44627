<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyMessageStatusMapper">
    
    <resultMap type="PsyMessageStatus" id="PsyMessageStatusResult">
        <id     property="statusId"      column="status_id"      />
        <result property="messageId"     column="message_id"     />
        <result property="userId"        column="user_id"        />
        <result property="isRead"        column="is_read"        />
        <result property="readTime"      column="read_time"      />
    </resultMap>
    
    <sql id="selectPsyMessageStatusVo">
        select status_id, message_id, user_id, is_read, read_time
        from psy_message_status
    </sql>
    
    <insert id="insertMessageStatus" parameterType="PsyMessageStatus" useGeneratedKeys="true" keyProperty="statusId">
        insert into psy_message_status (
            <if test="messageId != null">message_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="isRead != null">is_read,</if>
            <if test="readTime != null">read_time</if>
        ) values (
            <if test="messageId != null">#{messageId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="isRead != null">#{isRead},</if>
            <if test="readTime != null">#{readTime}</if>
        )
    </insert>
    
    <insert id="batchInsertMessageStatus" parameterType="java.util.List">
        insert into psy_message_status (message_id, user_id, is_read) values 
        <foreach collection="list" item="item" separator=",">
            (#{item.messageId}, #{item.userId}, #{item.isRead})
        </foreach>
    </insert>
    
    <select id="selectMessageStatus" resultMap="PsyMessageStatusResult">
        <include refid="selectPsyMessageStatusVo"/>
        where message_id = #{messageId} and user_id = #{userId}
    </select>
    
    <update id="updateMessageStatus" parameterType="PsyMessageStatus">
        update psy_message_status
        <set>
            <if test="isRead != null">is_read = #{isRead},</if>
            <if test="readTime != null">read_time = #{readTime}</if>
        </set>
        where status_id = #{statusId}
    </update>
    
    <update id="batchUpdateMessageReadStatus">
        update psy_message_status
        <set>
            is_read = #{isRead},
            <if test="readTime != null">read_time = #{readTime}</if>
        </set>
        where message_id in
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
        and user_id = #{userId}
    </update>
    
    <select id="countUnreadMessages" parameterType="Long" resultType="int">
        select count(1)
        from psy_message_status
        where user_id = #{userId} and is_read = '0'
    </select>
    
</mapper> 