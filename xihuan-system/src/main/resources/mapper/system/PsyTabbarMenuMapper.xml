<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTabbarMenuMapper">
    
    <resultMap type="PsyTabbarMenu" id="PsyTabbarMenuResult">
        <id     property="id"               column="id"                />
        <result property="index"            column="index"            />
        <result property="name"             column="name"             />
        <result property="img"              column="img"              />
        <result property="acImg"            column="acImg"            />
        <result property="path"             column="path"             />
        <result property="permissions"      column="permissions"      />
        <result property="sortOrder"        column="sortOrder"        />
        <result property="status"           column="status"           />
        <result property="delFlag"          column="delFlag"          />
        <result property="createBy"         column="createBy"         />
        <result property="createTime"       column="createTime"       />
        <result property="updateBy"         column="updateBy"         />
        <result property="updateTime"       column="updateTime"       />
        <result property="remark"           column="remark"           />
    </resultMap>

    <sql id="selectPsyTabbarMenuVo">
        select id, 
            menu_index as 'index',
            name, 
            icon_path as img, 
            selected_icon_path as acImg, 
            page_path as path, 
            permissions, 
            sort_order as sortOrder, 
            status, 
            del_flag as delFlag, 
            create_by as createBy, 
            create_time as createTime, 
            update_by as updateBy, 
            update_time as updateTime, 
            remark
        from psy_tabbar_menu
    </sql>

    <select id="selectMenuList" parameterType="PsyTabbarMenu" resultMap="PsyTabbarMenuResult">
        <include refid="selectPsyTabbarMenuVo"/>
        <where>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            AND del_flag = '0'
        </where>
        order by sort_order
    </select>

    <select id="selectMenuById" parameterType="Long" resultMap="PsyTabbarMenuResult">
        <include refid="selectPsyTabbarMenuVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="selectMenuByPermissions" parameterType="java.util.List" resultMap="PsyTabbarMenuResult">
        <include refid="selectPsyTabbarMenuVo"/>
        <where>
            AND del_flag = '0'
            AND status = '0'
            AND (
                permissions = ''
                <if test="list != null and list.size() > 0">
                    OR
                    <foreach collection="list" item="permission" separator="OR">
                        FIND_IN_SET(#{permission}, permissions) > 0
                    </foreach>
                </if>
            )
        </where>
        order by sort_order
    </select>

    <insert id="insertMenu" parameterType="PsyTabbarMenu" useGeneratedKeys="true" keyProperty="id">
        insert into psy_tabbar_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="index != null">menu_index,</if>
            <if test="name != null">name,</if>
            <if test="img != null">icon_path,</if>
            <if test="acImg != null">selected_icon_path,</if>
            <if test="path != null">page_path,</if>
            <if test="permissions != null">permissions,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="index != null">#{index},</if>
            <if test="name != null">#{name},</if>
            <if test="img != null">#{img},</if>
            <if test="acImg != null">#{acImg},</if>
            <if test="path != null">#{path},</if>
            <if test="permissions != null">#{permissions},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMenu" parameterType="PsyTabbarMenu">
        update psy_tabbar_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="index != null">menu_index = #{index},</if>
            <if test="name != null">name = #{name},</if>
            <if test="img != null">icon_path = #{img},</if>
            <if test="acImg != null">selected_icon_path = #{acImg},</if>
            <if test="path != null">page_path = #{path},</if>
            <if test="permissions != null">permissions = #{permissions},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMenuById" parameterType="Long">
        update psy_tabbar_menu set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteMenuByIds" parameterType="Long">
        update psy_tabbar_menu set del_flag = '1' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 