<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTQuestionMapper">

    <!-- 结果映射 -->
    <resultMap id="QuestionResultMap" type="PsyTQuestion">
        <id property="id" column="id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="questionNo" column="question_no"/>
        <result property="content" column="content"/>
        <result property="questionType" column="question_type"/>
        <result property="isReverse" column="is_reverse"/>
        <result property="reverseValue" column="reverse_value"/>
        <result property="options" column="options"/>
        <result property="subscaleRef" column="subscale_ref"/>
        <result property="isRequired" column="is_required"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 包含选项的结果映射 -->
    <resultMap id="QuestionWithOptionsMap" type="PsyTQuestion" extends="QuestionResultMap">
        <collection property="optionList" ofType="PsyTQuestionOption">
            <id property="id" column="option_id"/>
            <result property="questionId" column="option_question_id"/>
            <result property="optionText" column="option_text"/>
            <result property="optionValue" column="option_value"/>
            <result property="sort" column="option_sort"/>
            <result property="status" column="option_status"/>
        </collection>
    </resultMap>

    <!-- 包含量表信息的结果映射 -->
    <resultMap id="QuestionWithScaleMap" type="PsyTQuestion" extends="QuestionResultMap">
        <association property="scale" javaType="PsyTScale">
            <id property="id" column="scale_id"/>
            <result property="name" column="scale_name"/>
            <result property="code" column="scale_code"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="selectQuestionVo">
        select id, scale_id, question_no, content, question_type,
               is_reverse, reverse_value, options, subscale_ref,
               is_required, sort, status, del_flag,
               create_by, create_time, update_by, update_time
        from psy_t_question
    </sql>

    <!-- 查询题目列表 -->
    <select id="selectQuestionList" parameterType="PsyTQuestion" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        <where>
            del_flag = '0'
            <if test="scaleId != null">
                and scale_id = #{scaleId}
            </if>
            <if test="questionNo != null">
                and question_no = #{questionNo}
            </if>
            <if test="content != null and content != ''">
                and content like concat('%', #{content}, '%')
            </if>
            <if test="questionType != null and questionType != ''">
                and question_type = #{questionType}
            </if>
            <if test="isReverse != null">
                and is_reverse = #{isReverse}
            </if>
            <if test="subscaleRef != null and subscaleRef != ''">
                and subscale_ref = #{subscaleRef}
            </if>
            <if test="isRequired != null">
                and is_required = #{isRequired}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        order by scale_id, sort, question_no
    </select>

    <!-- 根据ID查询题目 -->
    <select id="selectQuestionById" parameterType="Long" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <!-- 查询题目详情（包含选项、复合题等信息） -->
    <select id="selectQuestionWithDetails" parameterType="Long" resultMap="QuestionWithOptionsMap">
        select q.id, q.scale_id, q.question_no, q.content, q.question_type,
               q.is_reverse, q.reverse_value, q.options, q.subscale_ref,
               q.is_required, q.sort, q.status, q.del_flag,
               q.create_by, q.create_time, q.update_by, q.update_time,
               o.id as option_id, o.question_id as option_question_id, o.option_text,
               o.option_value, o.sort as option_sort,
               o.status as option_status
        from psy_t_question q
        left join psy_t_question_option o on q.id = o.question_id and o.del_flag = '0'
        where q.id = #{id} and q.del_flag = '0'
        order by o.sort
    </select>

    <!-- 根据量表ID查询题目列表 -->
    <select id="selectQuestionsByScaleId" parameterType="Long" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where scale_id = #{scaleId} and del_flag = '0' and status = 1
        order by sort, question_no
    </select>

    <!-- 根据量表ID查询题目列表（包含选项） -->
    <select id="selectQuestionsWithOptionsByScaleId" parameterType="Long" resultMap="QuestionWithOptionsMap">
        select q.id, q.scale_id, q.question_no, q.content, q.question_type,
               q.is_reverse, q.reverse_value, q.options, q.subscale_ref,
               q.is_required, q.sort, q.status, q.del_flag,
               q.create_by, q.create_time, q.update_by, q.update_time,
               o.id as option_id, o.question_id as option_question_id, o.option_text,
               o.option_value, o.sort as option_sort,
               o.status as option_status
        from psy_t_question q
        left join psy_t_question_option o on q.id = o.question_id and o.del_flag = '0'
        where q.scale_id = #{scaleId} and q.del_flag = '0' and q.status = 1
        order by q.sort, q.question_no, o.sort
    </select>

    <!-- 新增题目 -->
    <insert id="insertQuestion" parameterType="PsyTQuestion" useGeneratedKeys="true" keyProperty="id">
        insert into psy_t_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">scale_id,</if>
            <if test="questionNo != null">question_no,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="questionType != null and questionType != ''">question_type,</if>
            <if test="isReverse != null">is_reverse,</if>
            <if test="reverseValue != null">reverse_value,</if>
            <if test="options != null and options != ''">options,</if>
            <if test="subscaleRef != null and subscaleRef != ''">subscale_ref,</if>
            <if test="isRequired != null">is_required,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">#{scaleId},</if>
            <if test="questionNo != null">#{questionNo},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="questionType != null and questionType != ''">#{questionType},</if>
            <if test="isReverse != null">#{isReverse},</if>
            <if test="reverseValue != null">#{reverseValue},</if>
            <if test="options != null and options != ''">#{options},</if>
            <if test="subscaleRef != null and subscaleRef != ''">#{subscaleRef},</if>
            <if test="isRequired != null">#{isRequired},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <!-- 修改题目 -->
    <update id="updateQuestion" parameterType="PsyTQuestion">
        update psy_t_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="questionNo != null">question_no = #{questionNo},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="isReverse != null">is_reverse = #{isReverse},</if>
            <if test="reverseValue != null">reverse_value = #{reverseValue},</if>
            <if test="options != null">options = #{options},</if>
            <if test="subscaleRef != null">subscale_ref = #{subscaleRef},</if>
            <if test="isRequired != null">is_required = #{isRequired},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除题目 -->
    <update id="deleteQuestionById" parameterType="Long">
        update psy_t_question set del_flag = '1' where id = #{id}
    </update>

    <!-- 批量删除题目 -->
    <update id="deleteQuestionByIds" parameterType="String">
        update psy_t_question set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据量表ID删除题目 -->
    <update id="deleteQuestionsByScaleId" parameterType="Long">
        update psy_t_question set del_flag = '1' where scale_id = #{scaleId}
    </update>

    <!-- 查询题目序号是否存在 -->
    <select id="checkQuestionNoExists" resultType="int">
        select count(1) from psy_t_question
        where scale_id = #{scaleId} and question_no = #{questionNo} and del_flag = '0'
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
    </select>

    <!-- 获取量表下一个题目序号 -->
    <select id="getNextQuestionNo" parameterType="Long" resultType="Integer">
        select IFNULL(max(question_no), 0) + 1 from psy_t_question
        where scale_id = #{scaleId} and del_flag = '0'
    </select>

    <!-- 批量更新题目排序 -->
    <update id="batchUpdateQuestionSort" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update psy_t_question set sort = #{item.sort}
            where id = #{item.id}
        </foreach>
    </update>

    <!-- 统计量表题目数量 -->
    <select id="countQuestionsByScaleId" parameterType="Long" resultType="int">
        select count(1) from psy_t_question
        where scale_id = #{scaleId} and del_flag = '0'
    </select>

    <!-- 查询题目类型统计 -->
    <select id="selectQuestionTypeStats" parameterType="Long" resultType="java.util.Map">
        select question_type as questionType, count(1) as count
        from psy_t_question
        where scale_id = #{scaleId} and del_flag = '0'
        group by question_type
    </select>

    <!-- 查询反向计分题目 -->
    <select id="selectReverseQuestions" parameterType="Long" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where scale_id = #{scaleId} and is_reverse = 1 and del_flag = '0' and status = 1
        order by sort, question_no
    </select>

    <!-- 查询必答题目 -->
    <select id="selectRequiredQuestions" parameterType="Long" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where scale_id = #{scaleId} and is_required = 1 and del_flag = '0' and status = 1
        order by sort, question_no
    </select>

    <!-- 查询复合题目 -->
    <select id="selectCompositeQuestions" parameterType="Long" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where scale_id = #{scaleId} and question_type = 'COMPOSITE' and del_flag = '0' and status = 1
        order by sort, question_no
    </select>

    <!-- 根据分量表关联标识查询题目 -->
    <select id="selectQuestionsBySubscaleRef" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where scale_id = #{scaleId} and subscale_ref = #{subscaleRef} and del_flag = '0' and status = 1
        order by sort, question_no
    </select>

    <!-- 批量更新题目状态 -->
    <update id="batchUpdateQuestionStatus">
        update psy_t_question set status = #{status}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 复制题目到新量表 -->
    <insert id="copyQuestionsToScale">
        insert into psy_t_question (scale_id, question_no, content, question_type,
                                   is_reverse, reverse_value, options, subscale_ref,
                                   is_required, sort, status, del_flag, create_by, create_time)
        select #{targetScaleId}, question_no, content, question_type,
               is_reverse, reverse_value, options, subscale_ref,
               is_required, sort, status, '0', 'system', now()
        from psy_t_question
        where scale_id = #{sourceScaleId} and del_flag = '0'
    </insert>

    <!-- 查询题目答题统计 -->
    <select id="selectQuestionAnswerStats" parameterType="Long" resultType="java.util.Map">
        select
            count(1) as totalAnswers,
            count(distinct record_id) as totalRecords,
            avg(response_time) as avgResponseTime,
            min(response_time) as minResponseTime,
            max(response_time) as maxResponseTime
        from psy_t_answer_record
        where question_id = #{questionId}
    </select>

    <!-- 查询题目选项分布 -->
    <select id="selectQuestionOptionDistribution" parameterType="Long" resultType="java.util.Map">
        select
            o.option_text as optionText,
            o.option_value as optionValue,
            count(a.id) as selectCount,
            round(count(a.id) * 100.0 / (select count(1) from psy_t_answer_record where question_id = #{questionId}), 2) as percentage
        from psy_t_question_option o
        left join psy_t_answer_record a on o.id = a.option_id
        where o.question_id = #{questionId} and o.del_flag = '0'
        group by o.id, o.option_text, o.option_value
    </select>

    <!-- 查询题目平均答题时间 -->
    <select id="selectQuestionAvgResponseTime" parameterType="Long" resultType="Integer">
        select avg(response_time) from psy_t_answer_record
        where question_id = #{questionId} and response_time > 0
    </select>

    <!-- 查询题目难度分析 -->
    <select id="selectQuestionDifficultyAnalysis" parameterType="Long" resultType="java.util.Map">
        select
            avg(answer_score) as avgScore,
            min(answer_score) as minScore,
            max(answer_score) as maxScore,
            stddev(answer_score) as scoreStddev,
            count(1) as totalAnswers,
            count(distinct record_id) as totalRecords
        from psy_t_answer_record
        where question_id = #{questionId}
    </select>

    <!-- 搜索题目 -->
    <select id="searchQuestions" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        <where>
            del_flag = '0'
            <if test="keyword != null and keyword != ''">
                and content like concat('%', #{keyword}, '%')
            </if>
            <if test="scaleId != null">
                and scale_id = #{scaleId}
            </if>
            <if test="questionType != null and questionType != ''">
                and question_type = #{questionType}
            </if>
        </where>
        order by scale_id, sort, question_no
    </select>

    <!-- 查询题目使用情况 -->
    <select id="selectQuestionUsageInfo" parameterType="Long" resultType="java.util.Map">
        select
            q.id as questionId,
            q.content as questionContent,
            q.question_no as questionNo,
            s.name as scaleName,
            count(a.id) as answerCount,
            count(distinct a.record_id) as recordCount,
            max(a.answer_time) as lastAnswerTime
        from psy_t_question q
        left join psy_t_scale s on q.scale_id = s.id
        left join psy_t_answer_record a on q.id = a.question_id
        where q.id = #{questionId} and q.del_flag = '0'
        group by q.id, q.content, q.question_no, s.name
    </select>

    <!-- 查询量表最大排序号 -->
    <select id="selectMaxOrderNum" parameterType="Long" resultType="Integer">
        select IFNULL(max(sort), 0) from psy_t_question
        where scale_id = #{scaleId} and del_flag = '0'
    </select>

    <!-- 查询题目详情（包含选项） -->
    <select id="selectQuestionWithOptions" parameterType="Long" resultMap="QuestionWithOptionsMap">
        select q.id, q.scale_id, q.question_no, q.content, q.question_type,
               q.is_reverse, q.reverse_value, q.options, q.subscale_ref,
               q.is_required, q.sort, q.status, q.del_flag,
               q.create_by, q.create_time, q.update_by, q.update_time,
               o.id as option_id, o.question_id as option_question_id, o.option_text,
               o.option_value, o.sort as option_sort,
               o.status as option_status
        from psy_t_question q
        left join psy_t_question_option o on q.id = o.question_id and o.del_flag = '0'
        where q.id = #{id} and q.del_flag = '0'
        order by o.sort
    </select>

    <!-- 根据题号查询题目 -->
    <select id="selectQuestionByNo" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where scale_id = #{scaleId} and question_no = #{questionNo} and del_flag = '0'
    </select>

    <!-- 批量插入题目 -->
    <insert id="batchInsertQuestions" parameterType="java.util.List">
        insert into psy_t_question (scale_id, question_no, content, question_type,
                                   is_reverse, reverse_value, options, subscale_ref,
                                   is_required, sort, status, del_flag, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.scaleId}, #{item.questionNo}, #{item.content}, #{item.questionType},
             #{item.isReverse}, #{item.reverseValue}, #{item.options}, #{item.subscaleRef},
             #{item.isRequired}, #{item.sort}, #{item.status}, #{item.delFlag},
             #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 统计量表启用题目数量 -->
    <select id="countEnabledQuestionsByScaleId" parameterType="Long" resultType="int">
        select count(1) from psy_t_question
        where scale_id = #{scaleId} and status = 1 and del_flag = '0'
    </select>

    <!-- 更新题目排序 -->
    <update id="updateQuestionOrder">
        update psy_t_question set sort = #{sort}
        where id = #{id}
    </update>

    <!-- 查询题目统计信息 -->
    <select id="selectQuestionStats" parameterType="Long" resultType="java.util.Map">
        select
            count(1) as totalQuestions,
            count(case when status = 1 then 1 end) as enabledQuestions,
            count(case when status = 0 then 1 end) as disabledQuestions,
            count(case when is_required = 1 then 1 end) as requiredQuestions,
            count(case when is_reverse = 1 then 1 end) as reverseQuestions,
            count(case when question_type = 'SINGLE' then 1 end) as singleQuestions,
            count(case when question_type = 'MULTIPLE' then 1 end) as multipleQuestions,
            count(case when question_type = 'TEXT' then 1 end) as textQuestions,
            count(case when question_type = 'COMPOSITE' then 1 end) as compositeQuestions
        from psy_t_question
        where scale_id = #{scaleId} and del_flag = '0'
    </select>

    <!-- 更新题目状态 -->
    <update id="updateQuestionStatus">
        update psy_t_question set status = #{status}
        where id = #{id}
    </update>

    <!-- 根据分量表关联标识查询题目 -->
    <select id="selectQuestionsBySubscaleId" parameterType="String" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where subscale_ref = #{subscaleRef} and del_flag = '0' and status = 1
        order by sort, question_no
    </select>

    <!-- 查询可选题目 -->
    <select id="selectOptionalQuestions" parameterType="Long" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where scale_id = #{scaleId} and is_required = 0 and del_flag = '0' and status = 1
        order by sort, question_no
    </select>

    <!-- 随机查询题目 -->
    <select id="selectRandomQuestions" resultMap="QuestionResultMap">
        <include refid="selectQuestionVo"/>
        where scale_id = #{scaleId} and del_flag = '0' and status = 1
        order by rand()
        limit #{count}
    </select>

</mapper>
