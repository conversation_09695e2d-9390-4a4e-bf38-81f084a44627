<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTQuestionOptionMapper">
    
    <!-- 结果映射 -->
    <resultMap id="OptionResultMap" type="PsyTQuestionOption">
        <id property="id" column="id"/>
        <result property="questionId" column="question_id"/>
        <result property="optionText" column="option_text"/>
        <result property="optionValue" column="option_value"/>
        <result property="sort" column="sort"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <!-- 注意：以下字段在某些数据库版本中可能不存在，已注释
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        -->
    </resultMap>

    <!-- 通用查询字段 -->
    <sql id="selectOptionVo">
        SELECT o.id, o.question_id, o.option_text, o.option_value, o.sort,
               o.del_flag, o.create_by, o.create_time, o.update_by, o.update_time
        FROM psy_t_question_option o
    </sql>

    <!-- 根据ID查询题目选项 -->
    <select id="selectOptionById" parameterType="Long" resultMap="OptionResultMap">
        <include refid="selectOptionVo"/>
        WHERE o.id = #{id} AND o.del_flag = 0
    </select>

    <!-- 查询题目选项列表 -->
    <select id="selectOptionList" parameterType="PsyTQuestionOption" resultMap="OptionResultMap">
        <include refid="selectOptionVo"/>
        WHERE o.del_flag = 0
        <if test="questionId != null">
            AND o.question_id = #{questionId}
        </if>
        <if test="optionText != null and optionText != ''">
            AND o.option_text LIKE CONCAT('%', #{optionText}, '%')
        </if>
        <if test="optionValue != null">
            AND o.option_value = #{optionValue}
        </if>
        <if test="status != null">
            AND o.status = #{status}
        </if>
        ORDER BY o.question_id, o.sort
    </select>

    <!-- 根据题目ID查询选项列表 -->
    <select id="selectOptionsByQuestionId" parameterType="Long" resultMap="OptionResultMap">
        <include refid="selectOptionVo"/>
        WHERE o.question_id = #{questionId} AND o.del_flag = 0
        ORDER BY o.sort
    </select>

    <!-- 根据题目ID列表查询选项 -->
    <select id="selectOptionsByQuestionIds" parameterType="java.util.List" resultMap="OptionResultMap">
        <include refid="selectOptionVo"/>
        WHERE o.question_id IN
        <foreach collection="list" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        AND o.del_flag = 0
        ORDER BY o.question_id, o.sort
    </select>

    <!-- 新增题目选项 -->
    <insert id="insertOption" parameterType="PsyTQuestionOption" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_question_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionId != null">question_id,</if>
            <if test="optionText != null and optionText != ''">option_text,</if>
            <if test="optionValue != null">option_value,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            create_time
            <!-- 注意：以下字段在某些数据库版本中可能不存在，已注释
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            -->
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionId != null">#{questionId},</if>
            <if test="optionText != null and optionText != ''">#{optionText},</if>
            <if test="optionValue != null">#{optionValue},</if>
            <if test="sort != null">#{sort},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            NOW()
            <!-- 注意：以下字段在某些数据库版本中可能不存在，已注释
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            -->
        </trim>
    </insert>

    <!-- 修改题目选项 -->
    <update id="updateOption" parameterType="PsyTQuestionOption">
        UPDATE psy_t_question_option
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="optionText != null and optionText != ''">option_text = #{optionText},</if>
            <if test="optionValue != null">option_value = #{optionValue},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
            <!-- 注意：以下字段在某些数据库版本中可能不存在，已注释
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            -->
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 删除题目选项 -->
    <delete id="deleteOptionById" parameterType="Long">
        UPDATE psy_t_question_option SET del_flag = 1 WHERE id = #{id}
    </delete>

    <!-- 批量删除题目选项 -->
    <delete id="deleteOptionByIds" parameterType="String">
        UPDATE psy_t_question_option SET del_flag = 1 WHERE id IN 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据题目ID删除选项 -->
    <delete id="deleteOptionsByQuestionId" parameterType="Long">
        UPDATE psy_t_question_option SET del_flag = 1 WHERE question_id = #{questionId}
    </delete>

    <!-- 统计题目选项数量 -->
    <select id="countOptionsByQuestionId" parameterType="Long" resultType="int">
        SELECT COUNT(*) FROM psy_t_question_option 
        WHERE question_id = #{questionId} AND del_flag = 0
    </select>

    <!-- 获取题目的最大排序号 -->
    <select id="getMaxSortByQuestionId" parameterType="Long" resultType="int">
        SELECT COALESCE(MAX(sort), 0) FROM psy_t_question_option 
        WHERE question_id = #{questionId} AND del_flag = 0
    </select>

    <!-- 检查选项文本是否重复 -->
    <select id="checkOptionTextExists" resultType="int">
        SELECT COUNT(*) FROM psy_t_question_option 
        WHERE question_id = #{questionId} AND option_text = #{optionText} AND del_flag = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

</mapper>
