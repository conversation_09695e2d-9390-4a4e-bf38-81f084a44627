<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyConsultationRecordMapper">

    <resultMap id="BaseResultMap" type="PsyConsultationRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="consultant_id" property="consultantId"/>
        <result column="order_id" property="orderId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="actual_start_time" property="actualStartTime"/>
        <result column="actual_end_time" property="actualEndTime"/>
        <result column="duration" property="duration"/>
        <result column="consult_type" property="consultType"/>
        <result column="main_symptoms" property="mainSymptoms"/>
        <result column="consult_content" property="consultContent"/>
        <result column="consult_count" property="consultCount"/>
        <result column="user_rating" property="userRating"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="attachment" property="attachment"/>
    </resultMap>

    <resultMap id="RecordWithDetailsMap" type="PsyConsultationRecord" extends="BaseResultMap">
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
            <result column="phonenumber" property="phonenumber"/>
        </association>
        <association property="consultant" javaType="PsyConsultant">
            <id column="consultant_id" property="id"/>
            <result column="consultant_name" property="name"/>
            <result column="consultant_title" property="title"/>
            <result column="consultant_avatar" property="avatar"/>
        </association>
        <association property="order" javaType="PsyConsultantOrder">
            <id column="order_id" property="id"/>
            <result column="order_no" property="orderNo"/>
            <result column="order_status" property="status"/>
            <result column="payment_amount" property="paymentAmount"/>
        </association>
    </resultMap>

    <select id="selectRecordList" parameterType="PsyConsultationRecord" resultMap="BaseResultMap">
        SELECT * FROM psy_consultation_record
        <where>
            del_flag = '0'
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="consultantId != null">AND consultant_id = #{consultantId}</if>
            <if test="orderId != null">AND order_id = #{orderId}</if>
            <if test="consultType != null and consultType != ''">AND consult_type = #{consultType}</if>
            <if test="userRating != null">AND user_rating = #{userRating}</if>
            <if test="params.beginTime != null and params.endTime != null">
                AND start_time BETWEEN #{params.beginTime} AND #{params.endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectRecordById" resultMap="BaseResultMap">
        SELECT * FROM psy_consultation_record WHERE id = #{id} AND del_flag = '0'
    </select>

    <select id="selectRecordWithDetails" resultMap="RecordWithDetailsMap">
        SELECT 
            r.*,
            u.user_name, u.nick_name, u.phonenumber,
            c.name AS consultant_name, c.title AS consultant_title, c.avatar AS consultant_avatar,
            o.order_no, o.status AS order_status, o.payment_amount
        FROM psy_consultation_record r
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        LEFT JOIN psy_consultant c ON r.consultant_id = c.id
        LEFT JOIN psy_consultant_order o ON r.order_id = o.id
        WHERE r.id = #{id} AND r.del_flag = '0'
    </select>

    <select id="selectRecordsByUserId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultation_record 
        WHERE user_id = #{userId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <select id="selectRecordsByConsultantId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultation_record 
        WHERE consultant_id = #{consultantId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <select id="selectRecordByOrderId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultation_record 
        WHERE order_id = #{orderId} AND del_flag = '0'
    </select>

    <select id="selectRecordsByUserAndConsultant" resultMap="BaseResultMap">
        SELECT * FROM psy_consultation_record 
        WHERE user_id = #{userId} AND consultant_id = #{consultantId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <select id="selectRecordsByTimeRange" resultMap="BaseResultMap">
        SELECT * FROM psy_consultation_record 
        WHERE start_time BETWEEN #{startTime} AND #{endTime} AND del_flag = '0'
        ORDER BY start_time ASC
    </select>

    <insert id="insertRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_consultation_record (
            user_id, consultant_id, order_id, start_time, end_time, actual_start_time,
            actual_end_time, duration, consult_type, main_symptoms, consult_content,
            consult_count, user_rating, del_flag, create_time, update_time, attachment
        ) VALUES (
            #{userId}, #{consultantId}, #{orderId}, #{startTime}, #{endTime}, #{actualStartTime},
            #{actualEndTime}, #{duration}, #{consultType}, #{mainSymptoms}, #{consultContent},
            #{consultCount}, #{userRating}, #{delFlag}, #{createTime}, #{updateTime}, #{attachment}
        )
    </insert>

    <update id="updateRecord" parameterType="PsyConsultationRecord">
        UPDATE psy_consultation_record
        <set>
            <if test="actualStartTime != null">actual_start_time = #{actualStartTime},</if>
            <if test="actualEndTime != null">actual_end_time = #{actualEndTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="mainSymptoms != null">main_symptoms = #{mainSymptoms},</if>
            <if test="consultContent != null">consult_content = #{consultContent},</if>
            <if test="consultCount != null">consult_count = #{consultCount},</if>
            <if test="userRating != null">user_rating = #{userRating},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteRecordById">
        UPDATE psy_consultation_record SET del_flag = '2' WHERE id = #{id}
    </update>

    <update id="deleteRecordByIds">
        UPDATE psy_consultation_record SET del_flag = '2'
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateRecordStatus">
        UPDATE psy_consultation_record
        SET actual_start_time = #{actualStartTime},
            actual_end_time = #{actualEndTime},
            duration = #{duration},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="updateUserRating">
        UPDATE psy_consultation_record
        SET user_rating = #{userRating}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="countUserConsultations" resultType="int">
        SELECT COUNT(*) FROM psy_consultation_record 
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <select id="countConsultantConsultations" resultType="int">
        SELECT COUNT(*) FROM psy_consultation_record 
        WHERE consultant_id = #{consultantId} AND del_flag = '0'
    </select>

    <select id="sumUserConsultationDuration" resultType="int">
        SELECT COALESCE(SUM(duration), 0) FROM psy_consultation_record 
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <select id="sumConsultantConsultationDuration" resultType="int">
        SELECT COALESCE(SUM(duration), 0) FROM psy_consultation_record 
        WHERE consultant_id = #{consultantId} AND del_flag = '0'
    </select>

    <select id="calculateConsultantAvgRating" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(user_rating), 0) FROM psy_consultation_record 
        WHERE consultant_id = #{consultantId} AND user_rating IS NOT NULL AND del_flag = '0'
    </select>

</mapper>
