<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.KangniliQuestionMapper">

    <resultMap type="KangniliQuestion" id="KangniliQuestionResult">
        <id     property="id"       column="id"        />
        <result property="title"    column="title"     />
        <result property="scenario" column="scenario"  />
        <result property="dimension" column="dimension"/>
        <result property="orderNum" column="order_num" />
    </resultMap>


    <resultMap id="QuestionWithOptionsResult" type="KangniliQuestion">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="scenario" column="scenario"/>
        <result property="dimension" column="dimension"/>
        <result property="orderNum" column="order_num"/>
        <collection property="options" ofType="KangniliOption">
            <id property="id" column="opt_id"/>
            <result property="optionLabel" column="option_label"/>
            <result property="optionType" column="option_type"/>
            <result property="optionText" column="option_text"/>
            <result property="score" column="score"/>
            <result property="orderNum" column="opt_order_num"/>
        </collection>
    </resultMap>

    <sql id="selectQuestionVo">
        SELECT id, title, scenario, dimension, order_num
        FROM kangnili_question
    </sql>

    <!-- 查询列表（匹配TINYINT字段类型） -->
    <select id="selectQuestionList" parameterType="KangniliQuestion" resultMap="KangniliQuestionResult">
        <include refid="selectQuestionVo"/>
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="dimension != null and dimension != ''">
                AND dimension = #{dimension}
            </if>
        </where>
        ORDER BY order_num ASC
    </select>

    <!-- 插入（严格匹配TINYINT UNSIGNED字段） -->
    <insert id="insertQuestion" parameterType="KangniliQuestion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO kangnili_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            title,
            scenario,
            dimension,
            order_num,
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            #{title},
            #{scenario},
            #{dimension},
            #{orderNum},
        </trim>
    </insert>

    <!-- 更新（处理无符号数值字段） -->
    <update id="updateQuestion" parameterType="KangniliQuestion">
        UPDATE kangnili_question
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="scenario != null">scenario = #{scenario},</if>
            <if test="dimension != null">dimension = #{dimension},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 批量删除（解决数组参数问题） -->
    <delete id="deleteQuestionByIds" parameterType="Long">
        DELETE FROM kangnili_question
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 存在性校验（用于删除前验证） -->
    <select id="selectExistingIds" parameterType="Long" resultType="Long">
        SELECT id FROM kangnili_question
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectQuestionById" parameterType="Long" resultMap="KangniliQuestionResult">
        <include refid="selectQuestionVo"/>
        WHERE id = #{id}
    </select>


    <select id="selectQuestionWithOptions" resultMap="QuestionWithOptionsResult">
        SELECT * FROM kangnili_question WHERE id = #{id}
    </select>

    <select id="selectOptionsByQuestionId" resultType="KangniliOption">
        SELECT * FROM kangnili_option
        WHERE question_id = #{questionId}
        ORDER BY order_num ASC
    </select>

    <select id="selectQuestionListWithOptions" resultMap="QuestionWithOptionsResult">
        SELECT
        q.id,
        q.title,
        q.scenario,
        q.dimension,
        q.order_num,
        o.id as opt_id,
        o.option_type,
        o.option_text,
        o.score,
        o.order_num as opt_order_num,
        o.option_label
        FROM kangnili_question q
        LEFT JOIN kangnili_option o ON q.id = o.question_id
        <where>
            <if test="title != null and title != ''">
                AND q.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="dimension != null and dimension != ''">
                AND q.dimension = #{dimension}
            </if>
        </where>
        ORDER BY q.order_num ASC, o.order_num ASC
    </select>
</mapper>