<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTimeScheduleTemplateMapper">
    
    <resultMap type="PsyTimeScheduleTemplate" id="PsyTimeScheduleTemplateResult">
        <result property="id"               column="id"               />
        <result property="counselorId"      column="counselor_id"     />
        <result property="name"             column="name"             />
        <result property="isDefault"        column="is_default"       />
        <result property="effectiveStart"   column="effective_start"  />
        <result property="effectiveEnd"     column="effective_end"    />
        <result property="delFlag"          column="del_flag"         />
        <result property="createTime"       column="create_time"      />
    </resultMap>

    <sql id="selectPsyTimeScheduleTemplateVo">
        select id, counselor_id, name, is_default, effective_start, effective_end, 
               del_flag, create_time
        from psy_time_schedule_template
    </sql>

    <select id="selectTemplateList" parameterType="PsyTimeScheduleTemplate" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        <where>  
            del_flag = 0
            <if test="counselorId != null"> and counselor_id = #{counselorId}</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="isDefault != null"> and is_default = #{isDefault}</if>
        </where>
        order by counselor_id, is_default desc, create_time desc
    </select>
    
    <select id="selectTemplateById" parameterType="Long" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where id = #{id}
    </select>
    
    <select id="selectTemplatesByCounselorId" parameterType="Long" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where counselor_id = #{counselorId} and del_flag = 0
        order by is_default desc, create_time desc
    </select>
    
    <select id="selectDefaultTemplateByCounselorId" parameterType="Long" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where counselor_id = #{counselorId} and is_default = 1 and del_flag = 0
        limit 1
    </select>
    
    <select id="selectEffectiveTemplate" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where counselor_id = #{counselorId} and del_flag = 0
        and (effective_start is null or effective_start &lt;= #{date})
        and (effective_end is null or effective_end &gt;= #{date})
        order by is_default desc, create_time desc
        limit 1
    </select>
        
    <insert id="insertTemplate" parameterType="PsyTimeScheduleTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into psy_time_schedule_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="counselorId != null">counselor_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            effective_start,
            effective_end,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="counselorId != null">#{counselorId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            #{effectiveStart},
            #{effectiveEnd},
         </trim>
    </insert>

    <update id="updateTemplate" parameterType="PsyTimeScheduleTemplate">
        update psy_time_schedule_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="counselorId != null">counselor_id = #{counselorId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            effective_start = #{effectiveStart},
            effective_end = #{effectiveEnd},
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="setDefaultTemplate">
        update psy_time_schedule_template
        set is_default = 1, update_time = now()
        where id = #{templateId} and del_flag = 0
    </update>

    <update id="clearDefaultTemplate">
        update psy_time_schedule_template
        set is_default = 0, update_time = now()
        where counselor_id = #{counselorId} and del_flag = 0
    </update>

    <delete id="deleteTemplateById" parameterType="Long">
        update psy_time_schedule_template set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteTemplateByIds" parameterType="String">
        update psy_time_schedule_template set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkTemplateNameUnique" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where counselor_id = #{counselorId} and name = #{name} and del_flag = 0
        limit 1
    </select>
</mapper>
