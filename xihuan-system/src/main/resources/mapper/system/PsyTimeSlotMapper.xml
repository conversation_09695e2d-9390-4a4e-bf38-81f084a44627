<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTimeSlotMapper">
    
    <resultMap type="PsyTimeSlot" id="PsyTimeSlotResult">
        <result property="id"               column="id"               />
        <result property="scheduleId"       column="schedule_id"      />
        <result property="isPublic"         column="is_public"        />
        <result property="centerId"         column="center_id"        />
        <result property="counselorId"      column="counselor_id"     />
        <result property="dateKey"          column="date_key"         />
        <result property="weekDay"          column="week_day"         />
        <result property="rangeId"          column="range_id"         />
        <result property="startTime"        column="start_time"       />
        <result property="endTime"          column="end_time"         />
        <result property="status"           column="status"           />
        <result property="delFlag"          column="del_flag"         />
        <result property="parentSlotId"     column="parent_slot_id"   />
        <result property="timeGroupHash"    column="time_group_hash"  />
        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
        <result property="remark"           column="remark"           />
    </resultMap>

    <resultMap type="PsyTimeSlot" id="PsyTimeSlotWithRangeResult" extends="PsyTimeSlotResult">
        <association property="timeRange" javaType="PsyTimeRange">
            <result property="id"           column="range_id"         />
            <result property="name"         column="range_name"       />
            <result property="iconUrl"      column="range_icon_url"   />
            <result property="startHour"    column="range_start_hour" />
            <result property="endHour"      column="range_end_hour"   />
        </association>
    </resultMap>

    <sql id="selectPsyTimeSlotVo">
        select ts.id, ts.schedule_id, ts.is_public, ts.center_id, ts.counselor_id, ts.date_key, 
               ts.week_day, ts.range_id, ts.start_time, ts.end_time, ts.status, ts.del_flag,
               ts.parent_slot_id, ts.time_group_hash, ts.create_by, ts.create_time, 
               ts.update_by, ts.update_time, ts.remark
        from psy_time_slot ts
    </sql>

    <sql id="selectPsyTimeSlotWithRangeVo">
        select ts.id, ts.schedule_id, ts.is_public, ts.center_id, ts.counselor_id, ts.date_key, 
               ts.week_day, ts.range_id, ts.start_time, ts.end_time, ts.status, ts.del_flag,
               ts.parent_slot_id, ts.time_group_hash, ts.create_by, ts.create_time, 
               ts.update_by, ts.update_time, ts.remark,
               tr.name as range_name, tr.icon_url as range_icon_url, 
               tr.start_hour as range_start_hour, tr.end_hour as range_end_hour
        from psy_time_slot ts
        left join psy_time_range tr on ts.range_id = tr.id
    </sql>

    <select id="selectTimeSlotList" parameterType="PsyTimeSlot" resultMap="PsyTimeSlotWithRangeResult">
        <include refid="selectPsyTimeSlotWithRangeVo"/>
        <where>
            ts.del_flag = 0
            <if test="centerId != null"> and ts.center_id = #{centerId}</if>
            <if test="counselorId != null"> and ts.counselor_id = #{counselorId}</if>
            <if test="dateKey != null and dateKey != ''"> and ts.date_key = #{dateKey}</if>
            <if test="slotDate != null and slotDate != ''"> and ts.date_key = #{slotDate}</if>
            <if test="rangeId != null"> and ts.range_id = #{rangeId}</if>
            <if test="status != null"> and ts.status = #{status}</if>
            <if test="isPublic != null"> and ts.is_public = #{isPublic}</if>
        </where>
        order by ts.date_key, ts.start_time
    </select>
    
    <select id="selectTimeSlotById" parameterType="Long" resultMap="PsyTimeSlotWithRangeResult">
        <include refid="selectPsyTimeSlotWithRangeVo"/>
        where ts.id = #{id}
    </select>
    
    <select id="selectSlotsByCounselorAndDateRange" resultMap="PsyTimeSlotWithRangeResult">
        <include refid="selectPsyTimeSlotWithRangeVo"/>
        where ts.counselor_id = #{counselorId} 
          and ts.date_key >= #{startDate} 
          and ts.date_key &lt;= #{endDate}
          and ts.del_flag = 0
        order by ts.date_key, ts.start_time
    </select>
    
    <select id="selectAvailableSlotsByDate" resultMap="PsyTimeSlotWithRangeResult">
        <include refid="selectPsyTimeSlotWithRangeVo"/>
        where ts.date_key = #{dateKey} 
          and ts.center_id = #{centerId}
          and ts.status = 0
          and ts.del_flag = 0
        <if test="counselorId != null"> and ts.counselor_id = #{counselorId}</if>
        order by ts.start_time
    </select>
    
    <select id="selectPublicSlots" resultMap="PsyTimeSlotWithRangeResult">
        <include refid="selectPsyTimeSlotWithRangeVo"/>
        where ts.date_key >= #{startDate} 
          and ts.date_key &lt;= #{endDate}
          and ts.center_id = #{centerId}
          and ts.is_public = 1
          and ts.del_flag = 0
        order by ts.date_key, ts.start_time
    </select>
        
    <insert id="insertTimeSlot" parameterType="PsyTimeSlot" useGeneratedKeys="true" keyProperty="id">
        insert into psy_time_slot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scheduleId != null">schedule_id,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="centerId != null">center_id,</if>
            <if test="counselorId != null">counselor_id,</if>
            <if test="dateKey != null and dateKey != ''">date_key,</if>
            <if test="weekDay != null">week_day,</if>
            <if test="rangeId != null">range_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="parentSlotId != null">parent_slot_id,</if>
            <if test="timeGroupHash != null">time_group_hash,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scheduleId != null">#{scheduleId},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="centerId != null">#{centerId},</if>
            <if test="counselorId != null">#{counselorId},</if>
            <if test="dateKey != null and dateKey != ''">#{dateKey},</if>
            <if test="weekDay != null">#{weekDay},</if>
            <if test="rangeId != null">#{rangeId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="parentSlotId != null">#{parentSlotId},</if>
            <if test="timeGroupHash != null">#{timeGroupHash},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertTimeSlots">
        insert ignore into psy_time_slot (schedule_id, is_public, center_id, counselor_id, date_key, week_day,
                                          range_id, start_time, end_time, status, del_flag, parent_slot_id,
                                          time_group_hash, create_time)
        values
        <foreach collection="timeSlots" item="slot" separator=",">
            (#{slot.scheduleId}, #{slot.isPublic}, #{slot.centerId}, #{slot.counselorId}, #{slot.dateKey},
             #{slot.weekDay}, #{slot.rangeId}, #{slot.startTime}, #{slot.endTime}, #{slot.status},
             #{slot.delFlag}, #{slot.parentSlotId}, #{slot.timeGroupHash}, #{slot.createTime})
        </foreach>
    </insert>

    <update id="updateTimeSlot" parameterType="PsyTimeSlot">
        update psy_time_slot
        <trim prefix="SET" suffixOverrides=",">
            <if test="scheduleId != null">schedule_id = #{scheduleId},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="centerId != null">center_id = #{centerId},</if>
            <if test="counselorId != null">counselor_id = #{counselorId},</if>
            <if test="dateKey != null and dateKey != ''">date_key = #{dateKey},</if>
            <if test="weekDay != null">week_day = #{weekDay},</if>
            <if test="rangeId != null">range_id = #{rangeId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="parentSlotId != null">parent_slot_id = #{parentSlotId},</if>
            <if test="timeGroupHash != null">time_group_hash = #{timeGroupHash},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdateSlotStatus">
        update psy_time_slot set status = #{status}, update_time = now()
        where id in
        <foreach collection="slotIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteTimeSlotById" parameterType="Long">
        update psy_time_slot set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteTimeSlotByIds" parameterType="String">
        update psy_time_slot set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSlotsByDateRange">
        update psy_time_slot set del_flag = 1
        where date_key >= #{startDate} and date_key &lt;= #{endDate}
        <if test="counselorId != null"> and counselor_id = #{counselorId}</if>
    </delete>

    <delete id="deleteSlotsByDateRangeAndCounselor">
        update psy_time_slot set del_flag = 1
        where counselor_id = #{counselorId}
          and date_key >= #{startDate}
          and date_key &lt;= #{endDate}
          and del_flag = 0
    </delete>

    <select id="checkSlotConflict" resultType="int">
        select count(1) from psy_time_slot
        where counselor_id = #{counselorId}
          and date_key = #{dateKey}
          and del_flag = 0
          and ((start_time &gt;= #{startTime} and start_time &lt; #{endTime})
               or (end_time &gt; #{startTime} and end_time &lt;= #{endTime})
               or (start_time &lt;= #{startTime} and end_time &gt;= #{endTime}))
        <if test="excludeId != null">and id != #{excludeId}</if>
    </select>
</mapper>
