<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyConsultantCenterConfigMapper">
    
    <resultMap type="PsyConsultantCenterConfig" id="PsyConsultantCenterConfigResult">
        <result property="id"                    column="id" />
        <result property="consultantId"          column="consultant_id" />
        <result property="centerId"              column="center_id" />
        <result property="arrivalTimeHours"      column="arrival_time_hours" />
        <result property="enableArrivalFilter"   column="enable_arrival_filter" />
        <result property="status"                column="status" />
        <result property="delFlag"               column="del_flag" />
        <result property="createBy"              column="create_by" />
        <result property="createTime"            column="create_time" />
        <result property="updateBy"              column="update_by" />
        <result property="updateTime"            column="update_time" />
        <result property="remark"                column="remark" />
    </resultMap>

    <sql id="selectPsyConsultantCenterConfigVo">
        select id, consultant_id, center_id, arrival_time_hours, enable_arrival_filter, status, create_by, create_time, update_by, update_time, remark
        from psy_consultant_center_config
    </sql>

    <select id="selectConfigList" parameterType="PsyConsultantCenterConfig" resultMap="PsyConsultantCenterConfigResult">
        <include refid="selectPsyConsultantCenterConfigVo"/>
        <where>  
            <if test="consultantId != null">and consultant_id = #{consultantId}</if>
            <if test="centerId != null">and center_id = #{centerId}</if>
            <if test="enableArrivalFilter != null">and enable_arrival_filter = #{enableArrivalFilter}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        order by consultant_id, center_id
    </select>
    
    <select id="selectConfigById" parameterType="Long" resultMap="PsyConsultantCenterConfigResult">
        <include refid="selectPsyConsultantCenterConfigVo"/>
        where id = #{id}
    </select>
    
    <select id="selectConfigByConsultantAndCenter" resultMap="PsyConsultantCenterConfigResult">
        <include refid="selectPsyConsultantCenterConfigVo"/>
        where consultant_id = #{consultantId} and center_id = #{centerId} and status = 1
        limit 1
    </select>
    
    <select id="selectConfigsByConsultantId" parameterType="Long" resultMap="PsyConsultantCenterConfigResult">
        <include refid="selectPsyConsultantCenterConfigVo"/>
        where consultant_id = #{consultantId} and status = 1
        order by center_id
    </select>

    <insert id="insertConfig" parameterType="PsyConsultantCenterConfig" useGeneratedKeys="true" keyProperty="id">
        insert into psy_consultant_center_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="consultantId != null">consultant_id,</if>
            <if test="centerId != null">center_id,</if>
            <if test="arrivalTimeHours != null">arrival_time_hours,</if>
            <if test="enableArrivalFilter != null">enable_arrival_filter,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="consultantId != null">#{consultantId},</if>
            <if test="centerId != null">#{centerId},</if>
            <if test="arrivalTimeHours != null">#{arrivalTimeHours},</if>
            <if test="enableArrivalFilter != null">#{enableArrivalFilter},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateConfig" parameterType="PsyConsultantCenterConfig">
        update psy_consultant_center_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="arrivalTimeHours != null">arrival_time_hours = #{arrivalTimeHours},</if>
            <if test="enableArrivalFilter != null">enable_arrival_filter = #{enableArrivalFilter},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteConfigById" parameterType="Long">
        delete from psy_consultant_center_config where id = #{id}
    </delete>

    <delete id="deleteConfigByIds" parameterType="String">
        delete from psy_consultant_center_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
