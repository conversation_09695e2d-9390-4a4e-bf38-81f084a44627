<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyImageResourceMapper">
    <!-- 查询图片资源 -->
    <select id="selectPsyImageResourceById" resultType="com.xihuan.common.core.domain.entity.PsyImageResource">
        SELECT 
            id,
            image_url as imageUrl,
            image_name as imageName,
            sort_num as sortNum,
            enable_status as enableStatus,
            create_by as createBy,
            create_time as createTime,
            update_by as updateBy,
            update_time as updateTime,
            del_flag as delFlag,
            remark,
            display_mode as displayMode
        FROM psy_image_resource 
        WHERE id = #{id}
    </select>

    <!-- 查询图片资源列表 -->
    <select id="selectPsyImageResourceList" resultType="com.xihuan.common.core.domain.entity.PsyImageResource">
        SELECT 
            id,
            image_url as imageUrl,
            image_name as imageName,
            sort_num as sortNum,
            enable_status as enableStatus,
            create_by as createBy,
            create_time as createTime,
            update_by as updateBy,
            update_time as updateTime,
            del_flag as delFlag,
            remark,
            display_mode as displayMode
        FROM psy_image_resource
        <where>
            <if test="imageName != null and imageName != ''">AND image_name LIKE CONCAT('%', #{imageName}, '%')</if>
            <if test="enableStatus != null and enableStatus != ''">AND enable_status = #{enableStatus}</if>
            <if test="delFlag != null and delFlag != ''">AND del_flag = #{delFlag}</if>
        </where>
        ORDER BY sort_num ASC, id DESC
    </select>

    <!-- 新增图片资源 -->
    <insert id="insertPsyImageResource" parameterType="com.xihuan.common.core.domain.entity.PsyImageResource">
        INSERT INTO psy_image_resource (
            image_url, image_name, sort_num, enable_status, remark,
            display_mode
        ) VALUES (
            #{imageUrl}, #{imageName}, #{sortNum}, #{enableStatus}, #{remark},
            #{displayMode}
        )
    </insert>

    <!-- 修改图片资源 -->
    <update id="updatePsyImageResource" parameterType="com.xihuan.common.core.domain.entity.PsyImageResource">
        UPDATE psy_image_resource
        <set>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="imageName != null">image_name = #{imageName},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
            <if test="enableStatus != null">enable_status = #{enableStatus},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW(),
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="displayMode != null">display_mode = #{displayMode}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除图片资源 -->
    <delete id="deletePsyImageResourceById" parameterType="long">
        DELETE FROM psy_image_resource WHERE id = #{id}
    </delete>

    <!-- 批量删除图片资源 -->
    <delete id="deletePsyImageResourceByIds" parameterType="long">
        DELETE FROM psy_image_resource WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 