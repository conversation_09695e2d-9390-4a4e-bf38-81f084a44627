<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyCourseReviewMapper">

    <resultMap id="BaseResultMap" type="PsyCourseReview">
        <id column="id" property="id"/>
        <result column="course_id" property="courseId"/>
        <result column="user_id" property="userId"/>
        <result column="content" property="content"/>
        <result column="rating" property="rating"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="ReviewWithDetailsMap" type="PsyCourseReview" extends="BaseResultMap">
        <association property="course" javaType="PsyCourse">
            <id column="course_id" property="id"/>
            <result column="course_title" property="title"/>
        </association>
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
        </association>
    </resultMap>

    <select id="selectReviewList" parameterType="PsyCourseReview" resultMap="BaseResultMap">
        SELECT * FROM psy_course_review
        <where>
            del_flag = 0
            <if test="courseId != null">AND course_id = #{courseId}</if>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="rating != null">AND rating = #{rating}</if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectReviewById" resultMap="BaseResultMap">
        SELECT * FROM psy_course_review WHERE id = #{id} AND del_flag = 0
    </select>

    <select id="selectReviewWithDetails" resultMap="ReviewWithDetailsMap">
        SELECT r.*, c.title AS course_title, u.user_name, u.nick_name
        FROM psy_course_review r
        LEFT JOIN psy_course c ON r.course_id = c.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.id = #{id} AND r.del_flag = 0
    </select>

    <select id="selectReviewsByCourseId" resultMap="BaseResultMap">
        SELECT * FROM psy_course_review 
        WHERE course_id = #{courseId} AND del_flag = 0
        ORDER BY id DESC
    </select>

    <select id="selectReviewsByUserId" resultMap="BaseResultMap">
        SELECT * FROM psy_course_review 
        WHERE user_id = #{userId} AND del_flag = 0
        ORDER BY id DESC
    </select>

    <insert id="insertReview" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_course_review (
            course_id, user_id, content, rating, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{courseId}, #{userId}, #{content}, #{rating}, #{delFlag},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <update id="updateReview" parameterType="PsyCourseReview">
        UPDATE psy_course_review
        <set>
            <if test="content != null">content = #{content},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteReviewById">
        UPDATE psy_course_review SET del_flag = 1 WHERE id = #{id}
    </update>

    <update id="deleteReviewByIds">
        UPDATE psy_course_review SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteReviewByCourseId">
        UPDATE psy_course_review SET del_flag = 1 WHERE course_id = #{courseId}
    </update>

    <update id="deleteReviewByCourseIds">
        UPDATE psy_course_review SET del_flag = 1
        WHERE course_id IN
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </update>

    <select id="calculateAverageRating" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(rating), 0) FROM psy_course_review 
        WHERE course_id = #{courseId} AND del_flag = 0
    </select>

    <select id="countReviewsByCourseId" resultType="int">
        SELECT COUNT(*) FROM psy_course_review 
        WHERE course_id = #{courseId} AND del_flag = 0
    </select>

    <select id="checkUserReviewed" resultType="int">
        SELECT COUNT(*) FROM psy_course_review 
        WHERE user_id = #{userId} AND course_id = #{courseId} AND del_flag = 0
    </select>

</mapper>
