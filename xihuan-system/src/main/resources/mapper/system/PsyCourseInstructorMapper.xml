<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyCourseInstructorMapper">

    <!-- 基础讲师字段映射 -->
    <resultMap id="BaseResultMap" type="PsyCourseInstructor">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="title" property="title"/>
        <result column="qualifications" property="qualifications"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 讲师详情映射（包含头像图片） -->
    <resultMap id="InstructorWithDetailsMap" type="PsyCourseInstructor" extends="BaseResultMap">
        <collection property="avatarImages" ofType="PsyCourseImageResource">
            <id column="image_id" property="id"/>
            <result column="image_url" property="imageUrl"/>
            <result column="image_order" property="imageOrder"/>
            <result column="is_main" property="isMain"/>
        </collection>
    </resultMap>

    <!-- 查询讲师列表 -->
    <select id="selectInstructorList" parameterType="PsyCourseInstructor" resultMap="BaseResultMap">
        SELECT * FROM psy_course_instructor
        <where>
            del_flag = 0
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 根据ID查询讲师 -->
    <select id="selectInstructorById" resultMap="BaseResultMap">
        SELECT * FROM psy_course_instructor WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询讲师详情（包含头像图片） -->
    <select id="selectInstructorWithDetails" resultMap="InstructorWithDetailsMap">
        SELECT 
            i.*,
            img.id AS image_id,
            img.image_url,
            img.image_order,
            img.is_main
        FROM psy_course_instructor i
        LEFT JOIN psy_course_image_resource img ON i.id = img.resource_id 
            AND img.resource_type = 2 AND img.del_flag = 0
        WHERE i.id = #{id} AND i.del_flag = 0
        ORDER BY img.image_order ASC
    </select>

    <!-- 新增讲师 -->
    <insert id="insertInstructor" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_course_instructor (
            name, title, qualifications, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{name}, #{title}, #{qualifications}, #{delFlag},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <!-- 修改讲师 -->
    <update id="updateInstructor" parameterType="PsyCourseInstructor">
        UPDATE psy_course_instructor
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="title != null">title = #{title},</if>
            <if test="qualifications != null">qualifications = #{qualifications},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除讲师 -->
    <update id="deleteInstructorById">
        UPDATE psy_course_instructor SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除讲师 -->
    <update id="deleteInstructorByIds">
        UPDATE psy_course_instructor SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询所有讲师简单信息 -->
    <select id="selectAllSimpleList" resultMap="BaseResultMap">
        SELECT id, name FROM psy_course_instructor WHERE del_flag = 0 ORDER BY id DESC
    </select>

</mapper>
