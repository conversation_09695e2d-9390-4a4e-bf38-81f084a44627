<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTAnswerRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="AnswerRecordResultMap" type="PsyTAnswerRecord">
        <id property="id" column="id"/>
        <result property="recordId" column="record_id"/>
        <result property="questionId" column="question_id"/>
        <result property="optionId" column="option_id"/>
        <result property="answerContent" column="answer_content"/>
        <result property="answerScore" column="answer_score"/>
        <result property="answerTime" column="answer_time"/>
        <result property="responseTime" column="response_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 带详情的结果映射 -->
    <resultMap id="AnswerRecordWithDetailsMap" type="PsyTAnswerRecord" extends="AnswerRecordResultMap">
        <association property="question" javaType="PsyTQuestion">
            <id property="id" column="q_id"/>
            <result property="questionNo" column="q_question_no"/>
            <result property="content" column="q_content"/>
            <result property="questionType" column="q_question_type"/>
            <result property="subscaleRef" column="q_subscale_ref"/>
        </association>
        <association property="option" javaType="PsyTQuestionOption">
            <id property="id" column="o_id"/>
            <result property="optionText" column="o_option_text"/>
            <result property="optionValue" column="o_option_value"/>
            <result property="sort" column="o_sort"/>
        </association>
    </resultMap>

    <!-- 通用查询字段 -->
    <sql id="selectPsyTAnswerRecordVo">
        SELECT a.id, a.record_id, a.question_id, a.option_id, a.answer_content,
               a.answer_score, a.answer_time, a.response_time, a.del_flag,
               a.create_by, a.create_time, a.update_by, a.update_time
        FROM psy_t_answer_record a
    </sql>

    <!-- 查询答案记录列表 -->
    <select id="selectAnswerRecordList" parameterType="PsyTAnswerRecord" resultMap="AnswerRecordResultMap">
        <include refid="selectPsyTAnswerRecordVo"/>
        WHERE a.del_flag = 0
        <if test="recordId != null">
            AND a.record_id = #{recordId}
        </if>
        <if test="questionId != null">
            AND a.question_id = #{questionId}
        </if>
        <if test="optionId != null">
            AND a.option_id = #{optionId}
        </if>
        ORDER BY a.record_id, a.question_id
    </select>

    <!-- 根据ID查询答案记录 -->
    <select id="selectAnswerRecordById" parameterType="Long" resultMap="AnswerRecordResultMap">
        <include refid="selectPsyTAnswerRecordVo"/>
        WHERE a.id = #{id} AND a.del_flag = 0
    </select>

    <!-- 查询答案记录详情 -->
    <select id="selectAnswerRecordWithDetails" parameterType="Long" resultMap="AnswerRecordWithDetailsMap">
        SELECT a.*,
               q.id as q_id, q.question_no as q_question_no, q.content as q_content,
               q.question_type as q_question_type, q.subscale_ref as q_subscale_ref,
               o.id as o_id, o.option_text as o_option_text, o.option_value as o_option_value, o.sort as o_sort
        FROM psy_t_answer_record a
        LEFT JOIN psy_t_question q ON a.question_id = q.id
        LEFT JOIN psy_t_question_option o ON a.option_id = o.id
        WHERE a.id = #{id} AND a.del_flag = 0
    </select>

    <!-- 根据测评记录ID查询答案列表 -->
    <select id="selectAnswersByRecordId" parameterType="Long" resultMap="AnswerRecordResultMap">
        <include refid="selectPsyTAnswerRecordVo"/>
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
        ORDER BY a.question_id
    </select>

    <!-- 根据测评记录ID查询答案列表（包含题目、选项信息） -->
    <select id="selectAnswersWithDetailsByRecordId" parameterType="Long" resultMap="AnswerRecordWithDetailsMap">
        SELECT a.*,
               q.id as q_id, q.question_no as q_question_no, q.content as q_content,
               q.question_type as q_question_type, q.subscale_ref as q_subscale_ref,
               o.id as o_id, o.option_text as o_option_text, o.option_value as o_option_value, o.sort as o_sort
        FROM psy_t_answer_record a
        LEFT JOIN psy_t_question q ON a.question_id = q.id
        LEFT JOIN psy_t_question_option o ON a.option_id = o.id
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
        ORDER BY q.question_no
    </select>

    <!-- 根据题目ID查询答案列表 -->
    <select id="selectAnswersByQuestionId" parameterType="Long" resultMap="AnswerRecordResultMap">
        <include refid="selectPsyTAnswerRecordVo"/>
        WHERE a.question_id = #{questionId} AND a.del_flag = 0
        ORDER BY a.record_id
    </select>

    <!-- 新增答案记录 -->
    <insert id="insertAnswerRecord" parameterType="PsyTAnswerRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_answer_record (
            record_id, question_id, option_id, answer_content, answer_score, 
            answer_time, response_time, del_flag, create_by, create_time
        ) VALUES (
            #{recordId}, #{questionId}, #{optionId}, #{answerContent}, #{answerScore}, 
            #{answerTime}, #{responseTime}, #{delFlag}, #{createBy}, #{createTime}
        )
    </insert>

    <!-- 修改答案记录 -->
    <update id="updateAnswerRecord" parameterType="PsyTAnswerRecord">
        UPDATE psy_t_answer_record
        <set>
            <if test="recordId != null">record_id = #{recordId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="optionId != null">option_id = #{optionId},</if>
            <if test="answerContent != null">answer_content = #{answerContent},</if>
            <if test="answerScore != null">answer_score = #{answerScore},</if>
            <if test="answerTime != null">answer_time = #{answerTime},</if>
            <if test="responseTime != null">response_time = #{responseTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除答案记录 -->
    <update id="deleteAnswerRecordById" parameterType="Long">
        UPDATE psy_t_answer_record SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除答案记录 -->
    <update id="deleteAnswerRecordByIds" parameterType="Long">
        UPDATE psy_t_answer_record SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据测评记录ID删除答案 -->
    <update id="deleteAnswersByRecordId" parameterType="Long">
        UPDATE psy_t_answer_record SET del_flag = 1 WHERE record_id = #{recordId}
    </update>

    <!-- 查询用户在指定题目的答案 -->
    <select id="selectAnswerByRecordAndQuestion" resultMap="AnswerRecordResultMap">
        <include refid="selectPsyTAnswerRecordVo"/>
        WHERE a.record_id = #{recordId} AND a.question_id = #{questionId} AND a.del_flag = 0
        LIMIT 1
    </select>

    <!-- 批量插入答案记录 -->
    <insert id="batchInsertAnswerRecords" parameterType="java.util.List">
        INSERT INTO psy_t_answer_record (
            record_id, question_id, option_id, answer_content, answer_score, 
            answer_time, response_time, del_flag, create_by, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.recordId}, #{item.questionId}, #{item.optionId}, #{item.answerContent}, #{item.answerScore}, 
                #{item.answerTime}, #{item.responseTime}, #{item.delFlag}, #{item.createBy}, #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 统计测评记录的答案数量 -->
    <select id="countAnswersByRecordId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_answer_record WHERE record_id = #{recordId} AND del_flag = 0
    </select>

    <!-- 计算测评记录的总分（已移动到文件末尾，避免重复） -->

    <!-- 计算维度得分（已移动到文件末尾，避免重复） -->

    <!-- 查询答案统计信息 -->
    <select id="selectAnswerStats" parameterType="Long" resultType="java.util.Map">
        SELECT 
            q.subscale_ref as dimension,
            COUNT(a.id) as answer_count,
            SUM(a.answer_score) as total_score,
            AVG(a.answer_score) as avg_score,
            AVG(a.response_time) as avg_response_time
        FROM psy_t_answer_record a
        JOIN psy_t_question q ON a.question_id = q.id
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
        GROUP BY q.subscale_ref
    </select>

    <!-- 查询题目答案分布统计 -->
    <select id="selectQuestionAnswerStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            o.id as option_id,
            o.option_text,
            o.option_value,
            o.sort,
            COUNT(a.id) as answer_count,
            ROUND(COUNT(a.id) * 100.0 / NULLIF((
                SELECT COUNT(1) FROM psy_t_answer_record
                WHERE question_id = #{questionId} AND del_flag = 0
            ), 0), 2) as percentage
        FROM psy_t_question_option o
        LEFT JOIN psy_t_answer_record a ON o.id = a.option_id AND a.del_flag = 0 AND a.question_id = #{questionId}
        WHERE o.question_id = #{questionId} AND o.del_flag = 0
        GROUP BY o.id, o.option_text, o.option_value, o.sort
        ORDER BY o.sort
    </select>

    <!-- 查询用户答题进度 -->
    <select id="selectAnswerProgress" parameterType="Long" resultType="java.util.Map">
        SELECT
            ar.id as record_id,
            ar.scale_id,
            ar.user_id,
            ar.start_time,
            ar.status,
            -- 计算实际已答题数
            COUNT(a.id) as actual_answered_count,
            COUNT(a.id) as answered_questions,
            -- 计算总题数
            (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = ar.scale_id AND q.del_flag = 0) as total_questions,
            -- 计算进度百分比
            ROUND(COUNT(a.id) * 100.0 / NULLIF((SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = ar.scale_id AND q.del_flag = 0), 0), 2) as actual_progress,
            ROUND(COUNT(a.id) * 100.0 / NULLIF((SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = ar.scale_id AND q.del_flag = 0), 0), 2) as progress,
            -- 计算剩余题数
            ((SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = ar.scale_id AND q.del_flag = 0) - COUNT(a.id)) as remaining_questions,
            -- 计算当前题目序号（下一个未答题目）
            COALESCE((SELECT MIN(q.question_no)
                     FROM psy_t_question q
                     WHERE q.scale_id = ar.scale_id AND q.del_flag = 0
                     AND NOT EXISTS (SELECT 1 FROM psy_t_answer_record a2
                                   WHERE a2.record_id = ar.id AND a2.question_id = q.id AND a2.del_flag = 0)),
                    (SELECT MAX(q.question_no) + 1 FROM psy_t_question q WHERE q.scale_id = ar.scale_id AND q.del_flag = 0)) as current_question_no,
            -- 状态文本
            CASE
                WHEN ar.status = 1 THEN '已完成'
                WHEN ar.status = 0 THEN '进行中'
                WHEN ar.status = 2 THEN '已暂停'
                WHEN ar.status = 3 THEN '已取消'
                ELSE '未知状态'
            END as status_text
        FROM psy_t_assessment_record ar
        LEFT JOIN psy_t_answer_record a ON ar.id = a.record_id AND a.del_flag = 0
        WHERE ar.id = #{recordId} AND ar.del_flag = 0
        GROUP BY ar.id, ar.scale_id, ar.user_id, ar.start_time, ar.status
    </select>

    <!-- 查询答题历史记录 -->
    <select id="selectAnswerHistoryByRecordId" parameterType="Long" resultType="java.util.Map">
        SELECT
            a.id as answer_id,
            a.question_id,
            a.option_id,
            a.answer_content,
            a.answer_score,
            a.response_time,
            a.create_time as answer_time,
            -- 题目信息
            q.content as question_content,
            q.question_type,
            q.subscale_ref as dimension,
            q.sort as question_sort,
            -- 选项信息（如果是选择题）
            o.option_text as option_content,
            o.option_value,
            o.sort as option_sort,
            -- 计算题目序号（在该量表中的位置）
            (SELECT COUNT(*) + 1 FROM psy_t_question q2
             WHERE q2.scale_id = q.scale_id AND q2.sort &lt; q.sort AND q2.del_flag = 0) as question_no
        FROM psy_t_answer_record a
        LEFT JOIN psy_t_question q ON a.question_id = q.id
        LEFT JOIN psy_t_question_option o ON a.option_id = o.id
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
        ORDER BY q.sort, a.create_time
    </select>

    <!-- 查询答题时长统计 -->
    <select id="selectAnswerDurationStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(a.id) as total_answers,
            SUM(a.response_time) as total_response_time,
            AVG(a.response_time) as avg_response_time,
            MIN(a.response_time) as min_response_time,
            MAX(a.response_time) as max_response_time,
            ROUND(STDDEV(a.response_time), 2) as std_response_time
        FROM psy_t_answer_record a
        WHERE a.record_id = #{recordId} AND a.del_flag = 0 AND a.response_time IS NOT NULL
    </select>

    <!-- 查询下一题信息 -->
    <select id="getNextQuestion" parameterType="Long" resultType="java.util.Map">
        SELECT
            q.id as question_id,
            q.question_no,
            q.content,
            q.question_type,
            q.is_required,
            q.sort,
            CASE WHEN a.id IS NOT NULL THEN 1 ELSE 0 END as is_answered
        FROM psy_t_question q
        LEFT JOIN psy_t_answer_record a ON q.id = a.question_id AND a.record_id = #{recordId} AND a.del_flag = 0
        WHERE q.scale_id = (SELECT scale_id FROM psy_t_assessment_record WHERE id = #{recordId})
        AND q.del_flag = 0 AND q.status = 1
        AND a.id IS NULL
        ORDER BY q.sort, q.question_no
        LIMIT 1
    </select>

    <!-- 查询上一题信息 -->
    <select id="getPreviousQuestion" parameterType="Long" resultType="java.util.Map">
        SELECT
            q.id as question_id,
            q.question_no,
            q.content,
            q.question_type,
            q.is_required,
            q.sort,
            a.answer_content,
            a.option_id,
            a.answer_score
        FROM psy_t_question q
        INNER JOIN psy_t_answer_record a ON q.id = a.question_id AND a.record_id = #{recordId} AND a.del_flag = 0
        WHERE q.scale_id = (SELECT scale_id FROM psy_t_assessment_record WHERE id = #{recordId})
        AND q.del_flag = 0 AND q.status = 1
        ORDER BY q.sort DESC, q.question_no DESC
        LIMIT 1
    </select>

    <!-- 验证答案有效性 -->
    <select id="validateAnswer" resultType="java.util.Map">
        SELECT
            q.id as question_id,
            q.question_type,
            q.is_required,
            o.id as option_id,
            o.option_value,
            CASE
                WHEN q.question_type = 'SINGLE_CHOICE' AND #{optionId} IS NOT NULL THEN 1
                WHEN q.question_type = 'MULTIPLE_CHOICE' AND #{optionId} IS NOT NULL THEN 1
                WHEN q.question_type = 'TEXT' AND #{answerContent} IS NOT NULL AND LENGTH(TRIM(#{answerContent})) > 0 THEN 1
                ELSE 0
            END as is_valid
        FROM psy_t_question q
        LEFT JOIN psy_t_question_option o ON q.id = o.question_id AND o.id = #{optionId} AND o.del_flag = 0
        WHERE q.id = #{questionId} AND q.del_flag = 0
    </select>

    <!-- 计算答案分数 -->
    <select id="calculateAnswerScore" resultType="java.math.BigDecimal">
        SELECT
            CASE
                WHEN q.question_type = 'TEXT' THEN 0
                WHEN o.option_value IS NOT NULL THEN o.option_value
                ELSE 0
            END as score
        FROM psy_t_question q
        LEFT JOIN psy_t_question_option o ON q.id = o.question_id AND o.id = #{optionId} AND o.del_flag = 0
        WHERE q.id = #{questionId} AND q.del_flag = 0
    </select>

    <!-- 检查答题是否完成 -->
    <select id="isAnswerCompleted" parameterType="Long" resultType="boolean">
        SELECT
            CASE
                WHEN COUNT(DISTINCT q.id) = COUNT(DISTINCT a.question_id) THEN 1
                ELSE 0
            END as is_completed
        FROM psy_t_question q
        LEFT JOIN psy_t_answer_record a ON q.id = a.question_id AND a.record_id = #{recordId} AND a.del_flag = 0
        WHERE q.scale_id = (SELECT scale_id FROM psy_t_assessment_record WHERE id = #{recordId})
        AND q.del_flag = 0 AND q.status = 1 AND q.is_required = 1
    </select>

    <!-- 计算总分 -->
    <select id="calculateTotalScore" parameterType="Long" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(a.answer_score), 0) as total_score
        FROM psy_t_answer_record a
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
    </select>

    <!-- 计算维度分数 -->
    <select id="calculateDimensionScores" parameterType="Long" resultType="java.util.Map">
        SELECT
            q.subscale_ref as dimension,
            SUM(a.answer_score) as dimension_score,
            COUNT(a.id) as question_count,
            AVG(a.answer_score) as avg_score
        FROM psy_t_answer_record a
        INNER JOIN psy_t_question q ON a.question_id = q.id
        WHERE a.record_id = #{recordId} AND a.del_flag = 0 AND q.subscale_ref IS NOT NULL
        GROUP BY q.subscale_ref
    </select>

    <!-- 计算单个维度得分 -->
    <select id="calculateDimensionScore" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(a.answer_score), 0) as dimension_score
        FROM psy_t_answer_record a
        INNER JOIN psy_t_question q ON a.question_id = q.id
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
        AND q.subscale_ref = #{dimension}
    </select>

    <!-- 查询题目答案分布（与selectQuestionAnswerStats重复，已删除） -->

    <!-- 查询答题时长统计（与selectAnswerDurationStats重复，已删除） -->

    <!-- 查询题目平均答题时间 -->
    <select id="selectQuestionAvgResponseTime" parameterType="Long" resultType="Integer">
        SELECT ROUND(AVG(a.response_time)) as avg_time
        FROM psy_t_answer_record a
        WHERE a.question_id = #{questionId} AND a.del_flag = 0 AND a.response_time IS NOT NULL
    </select>

    <!-- 查询用户答题历史 -->
    <select id="selectUserAnswerHistory" resultMap="AnswerRecordResultMap">
        SELECT a.id, a.record_id, a.question_id, a.option_id, a.answer_content,
               a.answer_score, a.answer_time, a.response_time, a.del_flag,
               a.create_by, a.create_time, a.update_by, a.update_time
        FROM psy_t_answer_record a
        INNER JOIN psy_t_assessment_record ar ON a.record_id = ar.id
        WHERE ar.user_id = #{userId} AND a.question_id = #{questionId} AND a.del_flag = 0
        ORDER BY a.create_time DESC
    </select>

    <!-- 查询答题正确率统计 -->
    <select id="selectAnswerAccuracyStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(a.id) as total_answers,
            COUNT(CASE WHEN a.answer_score > 0 THEN 1 END) as correct_answers,
            ROUND(COUNT(CASE WHEN a.answer_score > 0 THEN 1 END) * 100.0 / COUNT(a.id), 2) as accuracy_rate,
            AVG(a.answer_score) as avg_score,
            MAX(a.answer_score) as max_score,
            MIN(a.answer_score) as min_score
        FROM psy_t_answer_record a
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
    </select>

    <!-- 查询答题模式分析 -->
    <select id="selectAnswerPatternAnalysis" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT a.record_id) as test_count,
            AVG(a.answer_score) as avg_score,
            AVG(a.response_time) as avg_response_time,
            COUNT(CASE WHEN a.answer_score >= (
                SELECT AVG(answer_score) FROM psy_t_answer_record
                WHERE question_id = a.question_id AND del_flag = 0
            ) THEN 1 END) as above_avg_count,
            COUNT(a.id) as total_answers
        FROM psy_t_answer_record a
        INNER JOIN psy_t_assessment_record ar ON a.record_id = ar.id
        WHERE ar.user_id = #{userId} AND ar.scale_id = #{scaleId} AND a.del_flag = 0
    </select>

    <!-- 保存答案（用于Service层调用） -->
    <insert id="saveAnswer" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_answer_record (
            record_id, question_id, option_id, answer_content,
            answer_score, response_time, answer_time, create_by, create_time
        ) VALUES (
            #{recordId}, #{questionId}, #{optionId}, #{answerContent},
            #{answerScore}, #{responseTime}, NOW(), #{createBy}, NOW()
        )
        ON DUPLICATE KEY UPDATE
            option_id = VALUES(option_id),
            answer_content = VALUES(answer_content),
            answer_score = VALUES(answer_score),
            response_time = VALUES(response_time),
            answer_time = VALUES(answer_time),
            update_time = NOW()
    </insert>

</mapper>
