<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTAssessmentRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="RecordResultMap" type="PsyTAssessmentRecord">
        <id property="id" column="id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="userId" column="user_id"/>
        <result property="sessionId" column="session_id"/>
        <result property="startTime" column="start_time"/>
        <result property="completionTime" column="completion_time"/>
        <result property="totalScore" column="total_score"/>
        <result property="resultLevel" column="result_level"/>
        <result property="resultDescription" column="result_description"/>
        <result property="suggestions" column="suggestions"/>
        <result property="status" column="status"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="userAgent" column="user_agent"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <!-- 扩展字段 -->
        <result property="scaleName" column="scale_name"/>
        <result property="scaleCode" column="scale_code"/>
        <result property="nickName" column="nick_name"/>
        <!-- 计算字段 -->
        <result property="duration" column="duration"/>
        <result property="currentQuestionNo" column="current_question_no"/>
        <result property="answeredQuestions" column="answered_questions"/>
        <result property="totalQuestions" column="total_questions"/>
        <result property="progress" column="progress"/>
        <!-- 新增字段 -->
        <result property="dimensionScores" column="dimension_scores"/>
        <result property="standardScore" column="standard_score"/>
        <result property="reportGenerated" column="report_generated"/>
        <result property="reportContent" column="report_content"/>
    </resultMap>

    <!-- 详细结果映射 -->
    <resultMap id="RecordDetailMap" type="PsyTAssessmentRecord" extends="RecordResultMap">
        <association property="scale" javaType="PsyTScale">
            <id property="id" column="s_id"/>
            <result property="name" column="s_name"/>
            <result property="code" column="s_code"/>
            <result property="description" column="s_description"/>
            <result property="questionCount" column="s_question_count"/>
        </association>
        <association property="user" javaType="SysUser">
            <id property="userId" column="u_user_id"/>
            <result property="userName" column="u_user_name"/>
            <result property="nickName" column="u_nick_name"/>
            <result property="phonenumber" column="u_phonenumber"/>
            <result property="email" column="u_email"/>
        </association>
        <collection property="answers" ofType="PsyTAnswerRecord" column="id" select="com.xihuan.system.mapper.PsyTAnswerRecordMapper.selectAnswersByRecordId"/>
    </resultMap>

    <!-- 查询测评记录列表 -->
    <select id="selectAssessmentRecordList" parameterType="PsyTAssessmentRecord" resultMap="RecordResultMap">
        SELECT
            r.id, r.scale_id, r.user_id, r.session_id, r.start_time, r.completion_time,
            r.total_score, r.result_level, r.result_description, r.suggestions,
            r.status, r.ip_address, r.user_agent, r.del_flag,
            r.create_by, r.create_time, r.update_by, r.update_time,
            r.dimension_scores, r.standard_score, r.report_generated, r.report_content,
            s.name as scale_name, s.code as scale_code, u.nick_name,
            -- 计算字段
            CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
                 THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time)
                 ELSE NULL END as duration,
            -- 答题进度相关字段（从答题记录计算）
            (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions,
            (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions,
            CASE WHEN (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) > 0
                 THEN ROUND((SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) * 100.0 /
                           (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0), 2)
                 ELSE 0 END as progress,
            -- 当前题目序号（简化版，避免使用可能不存在的字段）
            1 as current_question_no
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.del_flag = '0'
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        <if test="userId != null">
            AND r.user_id = #{userId}
        </if>
        <if test="status != null">
            AND r.status = #{status}
        </if>
        <if test="sessionId != null and sessionId != ''">
            AND r.session_id = #{sessionId}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''">
            AND date_format(r.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据ID查询测评记录 -->
    <select id="selectAssessmentRecordById" parameterType="Long" resultMap="RecordResultMap">
        SELECT
            r.id, r.scale_id, r.user_id, r.session_id, r.start_time, r.completion_time,
            r.total_score, r.result_level, r.result_description, r.suggestions,
            r.status, r.ip_address, r.user_agent, r.del_flag,
            r.create_by, r.create_time, r.update_by, r.update_time,
            r.dimension_scores, r.standard_score, r.report_generated, r.report_content,
            s.name as scale_name, s.code as scale_code, u.nick_name,
            -- 计算字段
            CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
                 THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time)
                 ELSE NULL END as duration,
            (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions,
            (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions,
            CASE WHEN (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) > 0
                 THEN ROUND((SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) * 100.0 /
                           (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0), 2)
                 ELSE 0 END as progress,
            1 as current_question_no
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.id = #{id} AND r.del_flag = '0'
    </select>

    <!-- 根据会话ID查询测评记录 -->
    <select id="selectRecordBySessionId" parameterType="String" resultMap="RecordResultMap">
        SELECT
            r.id, r.scale_id, r.user_id, r.session_id, r.start_time, r.completion_time,
            r.total_score, r.result_level, r.result_description, r.suggestions,
            r.status, r.ip_address, r.user_agent, r.del_flag,
            r.create_by, r.create_time, r.update_by, r.update_time,
            r.dimension_scores, r.standard_score, r.report_generated, r.report_content,
            s.name as scale_name, s.code as scale_code, u.nick_name,
            -- 计算字段
            CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
                 THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time)
                 ELSE NULL END as duration,
            (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions,
            (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions,
            CASE WHEN (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) > 0
                 THEN ROUND((SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) * 100.0 /
                           (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0), 2)
                 ELSE 0 END as progress,
            1 as current_question_no
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.session_id = #{sessionId} AND r.del_flag = '0'
    </select>

    <!-- 简化查询测评记录（基础字段） -->
    <select id="selectAssessmentRecordSimple" parameterType="Long" resultType="PsyTAssessmentRecord">
        SELECT
            id, scale_id, user_id, session_id, start_time, completion_time,
            total_score, result_level, result_description, suggestions,
            status, ip_address, user_agent, del_flag,
            create_by, create_time, update_by, update_time,
            dimension_scores, standard_score, report_generated, report_content
        FROM psy_t_assessment_record
        WHERE id = #{id} AND del_flag = '0'
    </select>

    <!-- 查询测评记录详情 -->
    <select id="selectRecordWithDetails" parameterType="Long" resultMap="RecordDetailMap">
        SELECT
            r.id, r.scale_id, r.user_id, r.session_id, r.start_time, r.completion_time,
            r.total_score, r.result_level, r.result_description, r.suggestions,
            r.status, r.ip_address, r.user_agent, r.del_flag,
            r.create_by, r.create_time, r.update_by, r.update_time,
            s.id as s_id, s.name as s_name, s.code as s_code, s.description as s_description,
            s.question_count as s_question_count,
            u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.phonenumber as u_phonenumber, u.email as u_email
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.id = #{id} AND r.del_flag = '0'
    </select>

    <!-- 根据用户ID查询测评记录 -->
    <select id="selectRecordsByUserId" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE user_id = #{userId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 根据量表ID查询测评记录 -->
    <select id="selectRecordsByScaleId" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE scale_id = #{scaleId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户在指定量表的测评记录 -->
    <select id="selectRecordsByUserAndScale" resultMap="RecordResultMap">
        SELECT r.*, s.name as scale_name, s.code as scale_code
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.user_id = #{userId} AND r.scale_id = #{scaleId} AND r.del_flag = '0'
        ORDER BY r.create_time DESC
    </select>

    <!-- 新增测评记录 -->
    <insert id="insertAssessmentRecord" parameterType="PsyTAssessmentRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_assessment_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">scale_id,</if>
            <if test="userId != null">user_id,</if>
            session_id,
            <if test="startTime != null">start_time,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="resultLevel != null and resultLevel != ''">result_level,</if>
            <if test="resultDescription != null and resultDescription != ''">result_description,</if>
            <if test="suggestions != null and suggestions != ''">suggestions,</if>
            <!-- 注意：这些字段在数据库表中不存在，已注释掉
            <if test="currentQuestionNo != null">current_question_no,</if>
            <if test="answeredQuestions != null">answered_questions,</if>
            <if test="totalQuestions != null">total_questions,</if>
            <if test="progress != null">progress,</if>
            <if test="duration != null">duration,</if>
            -->
            <if test="status != null">status,</if>
            <if test="ipAddress != null and ipAddress != ''">ip_address,</if>
            <if test="userAgent != null and userAgent != ''">user_agent,</if>
            <if test="enterpriseId != null">enterprise_id,</if>
            <!--<if test="assessmentNo != null and assessmentNo != ''">assessment_no,</if>-->
            <!-- 注意：这些字段在数据库表中不存在，已注释掉
            <if test="subscaleScores != null and subscaleScores != ''">subscale_scores,</if>
            <if test="maxScore != null">max_score,</if>
            <if test="scorePercentage != null">score_percentage,</if>
            <if test="remark != null">remark,</if>
            -->
            <if test="dimensionScores != null">dimension_scores,</if>
            <if test="standardScore != null">standard_score,</if>
            <if test="reportGenerated != null">report_generated,</if>
            <if test="reportContent != null">report_content,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">#{scaleId},</if>
            <if test="userId != null">#{userId},</if>
            #{sessionId},
            <if test="startTime != null">#{startTime},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="resultLevel != null and resultLevel != ''">#{resultLevel},</if>
            <if test="resultDescription != null and resultDescription != ''">#{resultDescription},</if>
            <if test="suggestions != null and suggestions != ''">#{suggestions},</if>
            <!-- 注意：这些字段在数据库表中不存在，已注释掉
            <if test="currentQuestionNo != null">#{currentQuestionNo},</if>
            <if test="answeredQuestions != null">#{answeredQuestions},</if>
            <if test="totalQuestions != null">#{totalQuestions},</if>
            <if test="progress != null">#{progress},</if>
            <if test="duration != null">#{duration},</if>
            -->
            <if test="status != null">#{status},</if>
            <if test="ipAddress != null and ipAddress != ''">#{ipAddress},</if>
            <if test="userAgent != null and userAgent != ''">#{userAgent},</if>
            <!-- 注意：这些字段在数据库表中不存在，已注释掉
            <if test="enterpriseId != null">#{enterpriseId},</if>
            <if test="assessmentNo != null and assessmentNo != ''">#{assessmentNo},</if>
            <if test="subscaleScores != null and subscaleScores != ''">#{subscaleScores},</if>
            <if test="maxScore != null">#{maxScore},</if>
            <if test="scorePercentage != null">#{scorePercentage},</if>
            <if test="remark != null">#{remark},</if>
            -->
            <if test="dimensionScores != null">#{dimensionScores},</if>
            <if test="standardScore != null">#{standardScore},</if>
            <if test="reportGenerated != null">#{reportGenerated},</if>
            <if test="reportContent != null">#{reportContent},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <!-- 修改测评记录 -->
    <update id="updateAssessmentRecord" parameterType="PsyTAssessmentRecord">
        UPDATE psy_t_assessment_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="resultLevel != null">result_level = #{resultLevel},</if>
            <if test="resultDescription != null">result_description = #{resultDescription},</if>
            <if test="suggestions != null">suggestions = #{suggestions},</if>
            <if test="dimensionScores != null">dimension_scores = #{dimensionScores},</if>
            <if test="standardScore != null">standard_score = #{standardScore},</if>
            <if test="reportGenerated != null">report_generated = #{reportGenerated},</if>
            <if test="reportContent != null">report_content = #{reportContent},</if>
            <!-- 注意：这些字段在数据库表中不存在，已注释掉
            <if test="currentQuestionNo != null">current_question_no = #{currentQuestionNo},</if>
            <if test="answeredQuestions != null">answered_questions = #{answeredQuestions},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="subscaleScores != null">subscale_scores = #{subscaleScores},</if>
            <if test="maxScore != null">max_score = #{maxScore},</if>
            <if test="scorePercentage != null">score_percentage = #{scorePercentage},</if>
            -->
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <!-- <if test="remark != null">remark = #{remark},</if> 字段不存在 -->
            update_time = sysdate()
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 删除测评记录 -->
    <delete id="deleteAssessmentRecordById" parameterType="Long">
        UPDATE psy_t_assessment_record SET del_flag = '1' WHERE id = #{id}
    </delete>

    <!-- 批量删除测评记录 -->
    <delete id="deleteAssessmentRecordByIds" parameterType="String">
        UPDATE psy_t_assessment_record SET del_flag = '1' WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询进行中的测评记录 -->
    <select id="selectIncompleteRecordsByUserId" parameterType="Long" resultMap="RecordResultMap">
        SELECT r.*, s.name as scale_name, s.code as scale_code
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.status = 0 AND r.del_flag = '0' AND r.user_id = #{userId}
        ORDER BY r.create_time DESC
    </select>

    <!-- 查询已完成的测评记录 -->
    <select id="selectCompletedRecordsByUserId" parameterType="Long" resultMap="RecordResultMap">
        SELECT r.*, s.name as scale_name, s.code as scale_code
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.status = 1 AND r.del_flag = '0' AND r.user_id = #{userId}
        ORDER BY r.completion_time DESC
    </select>

    <!-- 查询已放弃的测评记录 -->
    <select id="selectAbandonedRecords" parameterType="Long" resultMap="RecordResultMap">
        SELECT r.*, s.name as scale_name, s.code as scale_code
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.status = 2 AND r.del_flag = '0' AND r.user_id = #{userId}
        ORDER BY r.update_time DESC
    </select>

    <!-- 统计用户测评记录数量 -->
    <select id="countRecordsByUserId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_assessment_record 
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <!-- 统计量表测评记录数量 -->
    <select id="countRecordsByScaleId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_assessment_record 
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

    <!-- 搜索测评记录 -->
    <select id="searchRecords" resultMap="RecordResultMap">
        SELECT r.* FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.del_flag = '0'
        <if test="keyword != null and keyword != ''">
            AND (
                s.name LIKE CONCAT('%', #{keyword}, '%')
                OR u.user_name LIKE CONCAT('%', #{keyword}, '%')
                OR u.nick_name LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="userId != null">
            AND r.user_id = #{userId}
        </if>
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        <if test="status != null">
            AND r.status = #{status}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 更新测评记录状态 -->
    <update id="updateRecordStatus">
        UPDATE psy_t_assessment_record 
        SET status = #{status}, update_time = sysdate()
        WHERE id = #{id}
    </update>

    <!-- 批量更新测评记录状态 -->
    <update id="batchUpdateRecordStatus">
        UPDATE psy_t_assessment_record 
        SET status = #{status}, update_time = sysdate()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询超时未完成的测评记录 -->
    <select id="selectTimeoutRecords" parameterType="Integer" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE status = 1 AND del_flag = '0'
        AND TIMESTAMPDIFF(HOUR, start_time, NOW()) > #{timeoutHours}
        ORDER BY start_time ASC
    </select>

    <!-- 清理过期的测评记录 -->
    <delete id="cleanExpiredRecords" parameterType="Integer">
        UPDATE psy_t_assessment_record SET del_flag = '1'
        WHERE status IN (0, 2) AND del_flag = '0'
        AND TIMESTAMPDIFF(DAY, create_time, NOW()) > #{expireDays}
    </delete>

    <!-- 查询用户测评统计 -->
    <select id="selectUserRecordStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalCount,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inProgressCount,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandonedCount,
            IFNULL(AVG(CASE WHEN status = 1 THEN total_score END), 0) as avgScore,
            IFNULL(MAX(CASE WHEN status = 1 THEN total_score END), 0) as maxScore,
            IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration,
            COUNT(DISTINCT scale_id) as scaleCount
        FROM psy_t_assessment_record
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <!-- 查询量表测评统计 -->
    <select id="selectScaleRecordStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalCount,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inProgressCount,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandonedCount,
            IFNULL(AVG(CASE WHEN status = 1 THEN total_score END), 0) as avgScore,
            IFNULL(MAX(CASE WHEN status = 1 THEN total_score END), 0) as maxScore,
            IFNULL(MIN(CASE WHEN status = 1 THEN total_score END), 0) as minScore,
            IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration,
            COUNT(DISTINCT user_id) as userCount,
            ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as completionRate
        FROM psy_t_assessment_record
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

    <!-- 查询测评趋势统计 -->
    <select id="selectRecordTrendStats" parameterType="Integer" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m-%d') as date,
            COUNT(*) as totalCount,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inProgressCount,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandonedCount
        FROM psy_t_assessment_record
        WHERE del_flag = '0' AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d')
        ORDER BY date DESC
    </select>

    <!-- 查询测评完成率统计 -->
    <select id="selectCompletionRateStats" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalCount,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
            ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as completionRate,
            IFNULL(AVG(CASE WHEN status = 1 THEN duration END), 0) as avgDuration
        FROM psy_t_assessment_record
        WHERE del_flag = '0'
        <if test="scaleId != null">
            AND scale_id = #{scaleId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND create_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND create_time &lt;= #{endDate}
        </if>
    </select>

    <!-- 查询测评时长统计 -->
    <select id="selectDurationStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
            IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration,
            IFNULL(MAX(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as maxDuration,
            IFNULL(MIN(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as minDuration,
            COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                       AND TIMESTAMPDIFF(SECOND, start_time, completion_time) &lt; 300 THEN 1 END) as fastCount,
            COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                       AND TIMESTAMPDIFF(SECOND, start_time, completion_time) BETWEEN 300 AND 1800 THEN 1 END) as normalCount,
            COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                       AND TIMESTAMPDIFF(SECOND, start_time, completion_time) > 1800 THEN 1 END) as slowCount
        FROM psy_t_assessment_record
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

    <!-- 查询测评分数分布 -->
    <select id="selectScoreDistribution" parameterType="Long" resultType="java.util.Map">
        SELECT
            CASE
                WHEN total_score &lt; 20 THEN '0-20'
                WHEN total_score &lt; 40 THEN '20-40'
                WHEN total_score &lt; 60 THEN '40-60'
                WHEN total_score &lt; 80 THEN '60-80'
                ELSE '80-100'
            END as scoreRange,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM psy_t_assessment_record WHERE scale_id = #{scaleId} AND status = 1 AND del_flag = '0'), 2) as percentage
        FROM psy_t_assessment_record
        WHERE scale_id = #{scaleId} AND status = 1 AND del_flag = '0'
        GROUP BY scoreRange
        ORDER BY scoreRange
    </select>

    <!-- 查询用户最近测评记录 -->
    <select id="selectRecentRecordsByUserId" resultMap="RecordResultMap">
        SELECT r.*, s.name as scale_name, s.code as scale_code
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.user_id = #{userId} AND r.del_flag = '0'
        ORDER BY r.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询热门测评记录 -->
    <select id="selectHotRecords" resultMap="RecordResultMap">
        SELECT r.*, s.name as scale_name, s.code as scale_code, COUNT(*) as record_count
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.del_flag = '0' AND r.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY r.scale_id
        ORDER BY record_count DESC
        LIMIT #{limit}
    </select>

    <!-- 查询测评记录排行榜 -->
    <select id="selectRecordRanking" resultType="java.util.Map">
        SELECT
            r.user_id,
            u.nick_name,
            r.total_score,
            CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
                 THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time)
                 ELSE NULL END as duration,
            r.completion_time,
            RANK() OVER (ORDER BY r.total_score DESC,
                        CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
                             THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time)
                             ELSE 999999 END ASC) as ranking
        FROM psy_t_assessment_record r
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.scale_id = #{scaleId} AND r.status = 1 AND r.del_flag = '0'
        ORDER BY ranking
        LIMIT #{limit}
    </select>

    <!-- 查询用户测评进度 -->
    <select id="selectUserTestProgress" resultType="java.util.Map">
        SELECT
            r.id,
            r.scale_id,
            s.name as scale_name,
            r.start_time,
            TIMESTAMPDIFF(SECOND, r.start_time, NOW()) as elapsed_time,
            r.status,
            -- 计算当前题目序号
            COALESCE((SELECT MIN(q.question_no)
                     FROM psy_t_question q
                     WHERE q.scale_id = r.scale_id AND q.del_flag = 0
                     AND NOT EXISTS (SELECT 1 FROM psy_t_answer_record a
                                   WHERE a.record_id = r.id AND a.question_id = q.id AND a.del_flag = 0)),
                    (SELECT MAX(q.question_no) + 1 FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0)) as current_question_no,
            -- 计算总题数
            (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions,
            -- 计算已答题数
            (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions,
            -- 计算进度百分比
            CASE WHEN (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) > 0
                 THEN ROUND((SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) * 100.0 /
                           (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0), 2)
                 ELSE 0 END as progress
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.user_id = #{userId} AND r.scale_id = #{scaleId} AND r.status = 0 AND r.del_flag = '0'
        ORDER BY r.create_time DESC
        LIMIT 1
    </select>

    <!-- 查询测评记录详情（包含答题记录） -->
    <select id="selectRecordWithAnswers" parameterType="Long" resultMap="RecordDetailMap">
        SELECT r.*,
               s.id as s_id, s.name as s_name, s.code as s_code, s.description as s_description,
               s.question_count as s_question_count,
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.phonenumber as u_phonenumber, u.email as u_email
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.id = #{recordId} AND r.del_flag = '0'
    </select>

    <!-- 查询测评统计信息 -->
    <select id="selectAssessmentStats" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalRecords,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedRecords,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inProgressRecords,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandonedRecords,
            COUNT(DISTINCT user_id) as totalUsers,
            COUNT(DISTINCT scale_id) as totalScales,
            ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as completionRate,
            IFNULL(AVG(CASE WHEN status = 1 THEN total_score END), 0) as avgScore,
            IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration
        FROM psy_t_assessment_record
        WHERE del_flag = '0'
    </select>

    <!-- 查询用户测评统计 -->
    <select id="selectUserAssessmentStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalRecords,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedRecords,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inProgressRecords,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandonedRecords,
            COUNT(DISTINCT scale_id) as scaleCount,
            ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as completionRate,
            IFNULL(AVG(CASE WHEN status = 1 THEN total_score END), 0) as avgScore,
            IFNULL(MAX(CASE WHEN status = 1 THEN total_score END), 0) as maxScore,
            IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration,
            (SELECT COUNT(*) FROM psy_t_assessment_record WHERE user_id = #{userId} AND status = 1 AND del_flag = '0' AND DATE(completion_time) = CURDATE()) as todayCompleted
        FROM psy_t_assessment_record
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <!-- 查询量表测评统计 -->
    <select id="selectScaleAssessmentStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalRecords,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedRecords,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inProgressRecords,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandonedRecords,
            COUNT(DISTINCT user_id) as userCount,
            ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as completionRate,
            IFNULL(AVG(CASE WHEN status = 1 THEN total_score END), 0) as avgScore,
            IFNULL(MAX(CASE WHEN status = 1 THEN total_score END), 0) as maxScore,
            IFNULL(MIN(CASE WHEN status = 1 THEN total_score END), 0) as minScore,
            IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration,
            (SELECT COUNT(*) FROM psy_t_assessment_record WHERE scale_id = #{scaleId} AND status = 1 AND del_flag = '0' AND DATE(completion_time) = CURDATE()) as todayCompleted
        FROM psy_t_assessment_record
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

    <!-- 查询企业测评统计（注意：enterprise_id字段在当前表结构中不存在，此方法需要根据实际业务需求调整） -->
    <select id="selectEnterpriseAssessmentStats" parameterType="Long" resultType="java.util.Map">
        <!-- 由于enterprise_id字段不存在，暂时返回空结果，需要根据实际业务逻辑调整 -->
        SELECT
            0 as totalRecords,
            0 as completedRecords,
            0 as inProgressRecords,
            0 as abandonedRecords,
            0 as userCount,
            0 as scaleCount,
            0.00 as completionRate,
            0.00 as avgScore,
            0.00 as avgDuration,
            0 as todayCompleted
        WHERE 1 = 0
        <!--
        如果需要实现企业统计功能，可能需要：
        1. 在psy_t_assessment_record表中添加enterprise_id字段
        2. 或者通过用户表关联企业信息
        3. 或者通过其他业务逻辑实现企业统计

        示例实现（如果通过用户表关联）：
        SELECT
            COUNT(*) as totalRecords,
            COUNT(CASE WHEN r.status = 1 THEN 1 END) as completedRecords,
            COUNT(CASE WHEN r.status = 0 THEN 1 END) as inProgressRecords,
            COUNT(CASE WHEN r.status = 2 THEN 1 END) as abandonedRecords,
            COUNT(DISTINCT r.user_id) as userCount,
            COUNT(DISTINCT r.scale_id) as scaleCount,
            ROUND(COUNT(CASE WHEN r.status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as completionRate,
            IFNULL(AVG(CASE WHEN r.status = 1 THEN r.total_score END), 0) as avgScore,
            IFNULL(AVG(CASE WHEN r.status = 1 AND r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time) END), 0) as avgDuration,
            (SELECT COUNT(*) FROM psy_t_assessment_record r2
             INNER JOIN sys_user u2 ON r2.user_id = u2.user_id
             WHERE u2.enterprise_id = #{enterpriseId} AND r2.status = 1 AND r2.del_flag = '0'
             AND DATE(r2.completion_time) = CURDATE()) as todayCompleted
        FROM psy_t_assessment_record r
        INNER JOIN sys_user u ON r.user_id = u.user_id
        WHERE u.enterprise_id = #{enterpriseId} AND r.del_flag = '0'
        -->
    </select>

    <!-- 查询测评趋势 -->
    <select id="selectAssessmentTrend" parameterType="Integer" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m-%d') as date,
            COUNT(*) as totalCount,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inProgressCount,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandonedCount,
            COUNT(DISTINCT user_id) as userCount
        FROM psy_t_assessment_record
        WHERE del_flag = '0' AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d')
        ORDER BY date DESC
    </select>

    <!-- 查询热门量表排行 -->
    <select id="selectHotScaleRanking" parameterType="Integer" resultType="java.util.Map">
        SELECT
            s.id as scale_id,
            s.name as scale_name,
            s.code as scale_code,
            COUNT(r.id) as record_count,
            COUNT(CASE WHEN r.status = 1 THEN 1 END) as completed_count,
            ROUND(COUNT(CASE WHEN r.status = 1 THEN 1 END) * 100.0 / COUNT(r.id), 2) as completion_rate,
            IFNULL(AVG(CASE WHEN r.status = 1 THEN r.total_score END), 0) as avg_score
        FROM psy_t_scale s
        LEFT JOIN psy_t_assessment_record r ON s.id = r.scale_id AND r.del_flag = '0'
        WHERE s.del_flag = '0' AND r.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY s.id, s.name, s.code
        ORDER BY record_count DESC
        LIMIT #{limit}
    </select>

    <!-- 更新测评状态 -->
    <update id="updateAssessmentStatus">
        UPDATE psy_t_assessment_record
        SET status = #{status}, update_time = sysdate()
        WHERE id = #{recordId}
    </update>

    <!-- 批量更新测评状态 -->
    <update id="batchUpdateAssessmentStatus">
        UPDATE psy_t_assessment_record
        SET status = #{status}, update_time = sysdate()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询测评时长分析 -->
    <select id="selectDurationAnalysis" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
            IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration,
            IFNULL(MAX(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as maxDuration,
            IFNULL(MIN(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as minDuration,
            COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                       AND TIMESTAMPDIFF(SECOND, start_time, completion_time) &lt; 300 THEN 1 END) as veryFastCount,
            COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                       AND TIMESTAMPDIFF(SECOND, start_time, completion_time) BETWEEN 300 AND 600 THEN 1 END) as fastCount,
            COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                       AND TIMESTAMPDIFF(SECOND, start_time, completion_time) BETWEEN 600 AND 1200 THEN 1 END) as normalCount,
            COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                       AND TIMESTAMPDIFF(SECOND, start_time, completion_time) BETWEEN 1200 AND 1800 THEN 1 END) as slowCount,
            COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                       AND TIMESTAMPDIFF(SECOND, start_time, completion_time) > 1800 THEN 1 END) as verySlowCount,
            ROUND(COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                             AND TIMESTAMPDIFF(SECOND, start_time, completion_time) &lt; 300 THEN 1 END) * 100.0 /
                  NULLIF(COUNT(CASE WHEN status = 1 THEN 1 END), 0), 2) as veryFastRate,
            ROUND(COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                             AND TIMESTAMPDIFF(SECOND, start_time, completion_time) BETWEEN 300 AND 600 THEN 1 END) * 100.0 /
                  NULLIF(COUNT(CASE WHEN status = 1 THEN 1 END), 0), 2) as fastRate,
            ROUND(COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                             AND TIMESTAMPDIFF(SECOND, start_time, completion_time) BETWEEN 600 AND 1200 THEN 1 END) * 100.0 /
                  NULLIF(COUNT(CASE WHEN status = 1 THEN 1 END), 0), 2) as normalRate,
            ROUND(COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                             AND TIMESTAMPDIFF(SECOND, start_time, completion_time) BETWEEN 1200 AND 1800 THEN 1 END) * 100.0 /
                  NULLIF(COUNT(CASE WHEN status = 1 THEN 1 END), 0), 2) as slowRate,
            ROUND(COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                             AND TIMESTAMPDIFF(SECOND, start_time, completion_time) > 1800 THEN 1 END) * 100.0 /
                  NULLIF(COUNT(CASE WHEN status = 1 THEN 1 END), 0), 2) as verySlowRate
        FROM psy_t_assessment_record
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

    <!-- 查询测评完成率分析 -->
    <select id="selectCompletionRateAnalysis" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalCount,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inProgressCount,
            COUNT(CASE WHEN status = 2 THEN 1 END) as abandonedCount,
            ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as completionRate,
            ROUND(COUNT(CASE WHEN status = 0 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as inProgressRate,
            ROUND(COUNT(CASE WHEN status = 2 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as abandonedRate,
            IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL
                           THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgCompletionTime,
            COUNT(DISTINCT user_id) as uniqueUsers,
            (SELECT COUNT(*) FROM psy_t_assessment_record WHERE scale_id = #{scaleId} AND status = 1 AND del_flag = '0' AND DATE(completion_time) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) as weeklyCompleted,
            (SELECT COUNT(*) FROM psy_t_assessment_record WHERE scale_id = #{scaleId} AND status = 1 AND del_flag = '0' AND DATE(completion_time) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as monthlyCompleted
        FROM psy_t_assessment_record
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

    <!-- 查询异常测评记录 -->
    <select id="selectAbnormalRecords" resultMap="RecordResultMap">
        SELECT
            r.id, r.scale_id, r.user_id, r.session_id, r.start_time, r.completion_time,
            r.total_score, r.result_level, r.result_description, r.suggestions,
            r.status, r.ip_address, r.user_agent, r.del_flag,
            r.create_by, r.create_time, r.update_by, r.update_time,
            s.name as scale_name, s.code as scale_code, u.nick_name,
            -- 计算字段
            CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
                 THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time)
                 ELSE NULL END as duration,
            (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions,
            (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions,
            CASE WHEN (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) > 0
                 THEN ROUND((SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) * 100.0 /
                           (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0), 2)
                 ELSE 0 END as progress,
            COALESCE((SELECT MIN(q.question_no)
                     FROM psy_t_question q
                     WHERE q.scale_id = r.scale_id AND q.del_flag = 0
                     AND NOT EXISTS (SELECT 1 FROM psy_t_answer_record a
                                   WHERE a.record_id = r.id AND a.question_id = q.id AND a.del_flag = 0)),
                    (SELECT MAX(q.question_no) + 1 FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0)) as current_question_no
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.del_flag = '0' AND (
            (r.status = 1 AND r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
             AND TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time) &lt; 60) OR  -- 完成时间过短
            (r.status = 1 AND r.completion_time IS NOT NULL AND r.start_time IS NOT NULL
             AND TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time) > 7200) OR  -- 完成时间过长
            (r.status = 0 AND TIMESTAMPDIFF(HOUR, r.start_time, NOW()) > 24) OR  -- 进行中时间过长
            (r.status = 1 AND r.total_score IS NULL) OR  -- 完成但无分数
            (r.status = 1 AND (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0)
             &lt; (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) * 0.5)  -- 答题数量异常
        )
        ORDER BY r.create_time DESC
    </select>

</mapper>
