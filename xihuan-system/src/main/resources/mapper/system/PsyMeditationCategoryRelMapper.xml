<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyMeditationCategoryRelMapper">

    <resultMap id="BaseResultMap" type="PsyMeditationCategoryRel">
        <result column="meditation_id" property="meditationId"/>
        <result column="category_id" property="categoryId"/>
    </resultMap>

    <resultMap id="RelWithDetailsMap" type="PsyMeditationCategoryRel" extends="BaseResultMap">
        <association property="meditation" javaType="PsyMeditation">
            <id column="meditation_id" property="id"/>
            <result column="meditation_title" property="title"/>
            <result column="meditation_status" property="status"/>
        </association>
        <association property="category" javaType="PsyCategory">
            <id column="category_id" property="categoryId"/>
            <result column="category_name" property="categoryName"/>
            <result column="parent_id" property="parentId"/>
        </association>
    </resultMap>

    <select id="selectRelList" parameterType="PsyMeditationCategoryRel" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_category_rel
        <where>
            <if test="meditationId != null">AND meditation_id = #{meditationId}</if>
            <if test="categoryId != null">AND category_id = #{categoryId}</if>
        </where>
        ORDER BY meditation_id, category_id
    </select>

    <select id="selectCategoryIdsByMeditationId" resultType="Long">
        SELECT category_id FROM psy_meditation_category_rel WHERE meditation_id = #{meditationId}
    </select>

    <select id="selectMeditationIdsByCategoryId" resultType="Long">
        SELECT meditation_id FROM psy_meditation_category_rel WHERE category_id = #{categoryId}
    </select>

    <insert id="insertRel" parameterType="PsyMeditationCategoryRel">
        INSERT INTO psy_meditation_category_rel (meditation_id, category_id)
        VALUES (#{meditationId}, #{categoryId})
    </insert>

    <insert id="batchInsertRel">
        INSERT INTO psy_meditation_category_rel (meditation_id, category_id) VALUES
        <foreach collection="categoryIds" item="categoryId" separator=",">
            (#{meditationId}, #{categoryId})
        </foreach>
    </insert>

    <delete id="deleteRel">
        DELETE FROM psy_meditation_category_rel 
        WHERE meditation_id = #{meditationId} AND category_id = #{categoryId}
    </delete>

    <delete id="deleteRelByMeditationId">
        DELETE FROM psy_meditation_category_rel WHERE meditation_id = #{meditationId}
    </delete>

    <delete id="deleteRelByMeditationIds">
        DELETE FROM psy_meditation_category_rel
        WHERE meditation_id IN
        <foreach item="meditationId" collection="array" open="(" separator="," close=")">
            #{meditationId}
        </foreach>
    </delete>

    <delete id="deleteRelByCategoryId">
        DELETE FROM psy_meditation_category_rel WHERE category_id = #{categoryId}
    </delete>

    <delete id="deleteRelByCategoryIds">
        DELETE FROM psy_meditation_category_rel
        WHERE category_id IN
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="checkRelExists" resultType="int">
        SELECT COUNT(*) FROM psy_meditation_category_rel 
        WHERE meditation_id = #{meditationId} AND category_id = #{categoryId}
    </select>

</mapper>
