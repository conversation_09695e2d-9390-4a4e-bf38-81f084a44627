<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsySearchSuggestionMapper">
    
    <resultMap type="PsySearchSuggestion" id="PsySearchSuggestionResult">
        <result property="id"              column="id"                />
        <result property="keyword"         column="keyword"           />
        <result property="suggestionType"  column="suggestion_type"   />
        <result property="searchCount"     column="search_count"      />
        <result property="priority"        column="priority"          />
        <result property="status"          column="status"            />
        <result property="createTime"      column="create_time"       />
        <result property="updateTime"      column="update_time"       />
    </resultMap>

    <sql id="selectPsySearchSuggestionVo">
        select id, keyword, suggestion_type, search_count, priority, status, create_time, update_time from psy_search_suggestion
    </sql>

    <select id="selectSearchSuggestionList" parameterType="PsySearchSuggestion" resultMap="PsySearchSuggestionResult">
        <include refid="selectPsySearchSuggestionVo"/>
        <where>  
            <if test="keyword != null  and keyword != ''"> and keyword like concat('%', #{keyword}, '%')</if>
            <if test="suggestionType != null  and suggestionType != ''"> and suggestion_type = #{suggestionType}</if>
            <if test="searchCount != null "> and search_count = #{searchCount}</if>
            <if test="priority != null "> and priority = #{priority}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by priority desc, search_count desc
    </select>
    
    <select id="selectSearchSuggestionById" parameterType="Long" resultMap="PsySearchSuggestionResult">
        <include refid="selectPsySearchSuggestionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSearchSuggestion" parameterType="PsySearchSuggestion" useGeneratedKeys="true" keyProperty="id">
        insert into psy_search_suggestion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">keyword,</if>
            <if test="suggestionType != null and suggestionType != ''">suggestion_type,</if>
            <if test="searchCount != null">search_count,</if>
            <if test="priority != null">priority,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">#{keyword},</if>
            <if test="suggestionType != null and suggestionType != ''">#{suggestionType},</if>
            <if test="searchCount != null">#{searchCount},</if>
            <if test="priority != null">#{priority},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSearchSuggestion" parameterType="PsySearchSuggestion">
        update psy_search_suggestion
        <trim prefix="SET" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
            <if test="suggestionType != null and suggestionType != ''">suggestion_type = #{suggestionType},</if>
            <if test="searchCount != null">search_count = #{searchCount},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSearchSuggestionById" parameterType="Long">
        delete from psy_search_suggestion where id = #{id}
    </delete>

    <delete id="deleteSearchSuggestionByIds" parameterType="String">
        delete from psy_search_suggestion where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectSuggestionsByKeyword" resultType="String">
        select keyword 
        from psy_search_suggestion 
        where keyword like concat(#{keyword}, '%') 
        and status = '0'
        order by priority desc, search_count desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    
    <update id="incrementSearchCount">
        update psy_search_suggestion 
        set search_count = search_count + 1, update_time = now()
        where keyword = #{keyword}
    </update>
    
    <select id="selectHighPrioritySuggestions" resultType="String">
        select keyword 
        from psy_search_suggestion 
        where status = '0'
        order by priority desc, search_count desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

</mapper>
