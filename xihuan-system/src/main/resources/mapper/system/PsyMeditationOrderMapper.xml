<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyMeditationOrderMapper">

    <resultMap id="BaseResultMap" type="PsyMeditationOrder">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="meditation_id" property="meditationId"/>
        <result column="user_id" property="userId"/>
        <result column="original_price" property="originalPrice"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_time" property="paymentTime"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_time" property="refundTime"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_discount" property="couponDiscount"/>
        <result column="membership_discount" property="membershipDiscount"/>
        <result column="points_used" property="pointsUsed"/>
        <result column="points_discount" property="pointsDiscount"/>
        <result column="is_membership_free" property="isMembershipFree"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="OrderWithDetailsMap" type="PsyMeditationOrder" extends="BaseResultMap">
        <association property="meditation" javaType="PsyMeditation">
            <id column="meditation_id" property="id"/>
            <result column="meditation_title" property="title"/>
            <result column="meditation_cover_image" property="coverImage"/>
            <result column="meditation_price" property="price"/>
            <result column="meditation_duration" property="duration"/>
        </association>
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
            <result column="phonenumber" property="phonenumber"/>
        </association>
    </resultMap>

    <select id="selectOrderList" parameterType="PsyMeditationOrder" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_order
        <where>
            del_flag = 0
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
            <if test="meditationId != null">
                AND meditation_id = #{meditationId}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                AND payment_method = #{paymentMethod}
            </if>
            <if test="params.beginTime != null and params.endTime != null">
                AND create_time BETWEEN #{params.beginTime} AND #{params.endTime}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectOrderById" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_order WHERE id = #{id} AND del_flag = 0
    </select>

    <select id="selectOrderByOrderNo" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_order WHERE order_no = #{orderNo} AND del_flag = 0
    </select>

    <select id="selectOrderWithDetails" resultMap="OrderWithDetailsMap">
        SELECT 
            o.*,
            m.title AS meditation_title,
            m.cover_image AS meditation_cover_image,
            m.price AS meditation_price,
            m.duration AS meditation_duration,
            u.user_name,
            u.nick_name,
            u.phonenumber
        FROM psy_meditation_order o
        LEFT JOIN psy_meditation m ON o.meditation_id = m.id
        LEFT JOIN sys_user u ON o.user_id = u.user_id
        WHERE o.id = #{id} AND o.del_flag = 0
    </select>

    <select id="selectOrdersByUserId" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_order 
        WHERE user_id = #{userId} AND del_flag = 0
        ORDER BY id DESC
    </select>

    <select id="selectOrdersByMeditationId" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation_order 
        WHERE meditation_id = #{meditationId} AND del_flag = 0
        ORDER BY id DESC
    </select>

    <insert id="insertOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_meditation_order (
            order_no, meditation_id, user_id, original_price, payment_amount,
            payment_method, payment_time, transaction_id, refund_amount, refund_time,
            cancel_time, coupon_id, coupon_discount, membership_discount,
            points_used, points_discount, is_membership_free, status, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{orderNo}, #{meditationId}, #{userId}, #{originalPrice}, #{paymentAmount},
            #{paymentMethod}, #{paymentTime}, #{transactionId}, #{refundAmount}, #{refundTime},
            #{cancelTime}, #{couponId}, #{couponDiscount}, #{membershipDiscount},
            #{pointsUsed}, #{pointsDiscount}, #{isMembershipFree}, #{status}, #{delFlag},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <update id="updateOrder" parameterType="PsyMeditationOrder">
        UPDATE psy_meditation_order
        <set>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="couponId != null">coupon_id = #{couponId},</if>
            <if test="couponDiscount != null">coupon_discount = #{couponDiscount},</if>
            <if test="membershipDiscount != null">membership_discount = #{membershipDiscount},</if>
            <if test="pointsUsed != null">points_used = #{pointsUsed},</if>
            <if test="pointsDiscount != null">points_discount = #{pointsDiscount},</if>
            <if test="isMembershipFree != null">is_membership_free = #{isMembershipFree},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteOrderById">
        UPDATE psy_meditation_order SET del_flag = 1 WHERE id = #{id}
    </update>

    <update id="deleteOrderByIds">
        UPDATE psy_meditation_order SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateOrderPaymentStatus">
        UPDATE psy_meditation_order
        SET status = #{status},
            payment_method = #{paymentMethod},
            transaction_id = #{transactionId},
            payment_time = #{paymentTime},
            update_time = NOW()
        WHERE order_no = #{orderNo}
    </update>

    <update id="updateOrderRefund">
        UPDATE psy_meditation_order
        SET refund_amount = #{refundAmount},
            refund_time = #{refundTime},
            update_time = NOW()
        WHERE order_no = #{orderNo}
    </update>

    <select id="generateOrderNo" resultType="String">
        SELECT CONCAT('MEDITATION_', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), LPAD(FLOOR(RAND() * 1000), 3, '0'))
    </select>

    <select id="checkUserPurchased" resultType="int">
        SELECT COUNT(*) FROM psy_meditation_order 
        WHERE user_id = #{userId} AND meditation_id = #{meditationId} 
        AND status IN (1, 2) AND del_flag = 0
    </select>

</mapper>
