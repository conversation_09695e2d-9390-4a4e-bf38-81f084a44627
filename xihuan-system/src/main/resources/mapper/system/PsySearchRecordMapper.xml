<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsySearchRecordMapper">
    
    <resultMap type="PsySearchRecord" id="PsySearchRecordResult">
        <result property="id"           column="id"           />
        <result property="userId"       column="user_id"      />
        <result property="keyword"      column="keyword"      />
        <result property="searchType"   column="search_type"  />
        <result property="resultCount"  column="result_count" />
        <result property="searchTime"   column="search_time"  />
        <result property="ipAddress"    column="ip_address"   />
        <result property="userAgent"    column="user_agent"   />
        <result property="delFlag"      column="del_flag"     />
    </resultMap>

    <sql id="selectPsySearchRecordVo">
        select id, user_id, keyword, search_type, result_count, search_time, ip_address, user_agent, del_flag from psy_search_record
    </sql>

    <select id="selectSearchRecordList" parameterType="PsySearchRecord" resultMap="PsySearchRecordResult">
        <include refid="selectPsySearchRecordVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="keyword != null  and keyword != ''"> and keyword like concat('%', #{keyword}, '%')</if>
            <if test="searchType != null  and searchType != ''"> and search_type = #{searchType}</if>
            <if test="resultCount != null "> and result_count = #{resultCount}</if>
            <if test="searchTime != null "> and search_time = #{searchTime}</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            and del_flag = '0'
        </where>
        order by search_time desc
    </select>
    
    <select id="selectSearchRecordById" parameterType="Long" resultMap="PsySearchRecordResult">
        <include refid="selectPsySearchRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSearchRecord" parameterType="PsySearchRecord" useGeneratedKeys="true" keyProperty="id">
        insert into psy_search_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="keyword != null and keyword != ''">keyword,</if>
            <if test="searchType != null and searchType != ''">search_type,</if>
            <if test="resultCount != null">result_count,</if>
            <if test="searchTime != null">search_time,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="keyword != null and keyword != ''">#{keyword},</if>
            <if test="searchType != null and searchType != ''">#{searchType},</if>
            <if test="resultCount != null">#{resultCount},</if>
            <if test="searchTime != null">#{searchTime},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateSearchRecord" parameterType="PsySearchRecord">
        update psy_search_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
            <if test="searchType != null and searchType != ''">search_type = #{searchType},</if>
            <if test="resultCount != null">result_count = #{resultCount},</if>
            <if test="searchTime != null">search_time = #{searchTime},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSearchRecordById" parameterType="Long">
        delete from psy_search_record where id = #{id}
    </delete>

    <delete id="deleteSearchRecordByIds" parameterType="String">
        delete from psy_search_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectUserSearchHistory" resultType="String">
        select keyword
        from psy_search_record
        where user_id = #{userId} and del_flag = '0'
        group by keyword
        order by max(search_time) desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    
    <select id="selectHotKeywords" resultType="map">
        select keyword, count(*) as search_count
        from psy_search_record 
        where del_flag = '0'
        <if test="searchType != null and searchType != '' and searchType != 'all'">
            and search_type = #{searchType}
        </if>
        and search_time >= date_sub(now(), interval 30 day)
        group by keyword
        order by search_count desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    
    <select id="countKeywordSearch" resultType="Integer">
        select count(*) 
        from psy_search_record 
        where keyword = #{keyword} 
        <if test="searchType != null and searchType != '' and searchType != 'all'">
            and search_type = #{searchType}
        </if>
        and del_flag = '0'
    </select>
    
    <delete id="cleanExpiredRecords">
        delete from psy_search_record
        where search_time &lt; date_sub(now(), interval #{days} day)
    </delete>

    <!-- 软删除用户搜索记录 -->
    <update id="softDeleteByUserId">
        update psy_search_record
        set del_flag = '1',
            update_time = now()
        where user_id = #{userId}
          and del_flag = '0'
    </update>

    <!-- 硬删除用户搜索记录 -->
    <delete id="deleteByUserId">
        delete from psy_search_record
        where user_id = #{userId}
    </delete>

</mapper>
