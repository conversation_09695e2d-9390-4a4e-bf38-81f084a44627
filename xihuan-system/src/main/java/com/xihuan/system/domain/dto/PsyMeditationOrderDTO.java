package com.xihuan.system.domain.dto;

import com.xihuan.common.core.domain.entity.PsyMeditationOrder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 冥想订单数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyMeditationOrderDTO extends PsyMeditationOrder {
    
    /** 冥想信息 */
    private MeditationInfo meditationInfo;
    
    /** 用户信息 */
    private UserInfo userInfo;
    
    /** 支付信息 */
    private PaymentInfo paymentInfo;
    
    /** 优惠信息 */
    private DiscountInfo discountInfo;
    
    /**
     * 冥想信息
     */
    @Data
    public static class MeditationInfo {
        
        /** 冥想ID */
        private Long id;
        
        /** 冥想标题 */
        private String title;
        
        /** 封面图片 */
        private String coverImage;
        
        /** 冥想价格 */
        private BigDecimal price;
        
        /** 引导师 */
        private String narrator;
        
        /** 冥想时长 */
        private Integer duration;
        
        /** 难度等级 */
        private Integer difficultyLevel;
    }
    
    /**
     * 用户信息
     */
    @Data
    public static class UserInfo {
        
        /** 用户ID */
        private Long userId;
        
        /** 用户名 */
        private String userName;
        
        /** 昵称 */
        private String nickName;
        
        /** 手机号 */
        private String phonenumber;
        
        /** 邮箱 */
        private String email;
    }
    
    /**
     * 支付信息
     */
    @Data
    public static class PaymentInfo {
        
        /** 支付方式 */
        private String paymentMethod;
        
        /** 支付时间 */
        private Date paymentTime;
        
        /** 第三方交易号 */
        private String transactionId;
        
        /** 支付状态描述 */
        private String statusDesc;
        
        /** 退款金额 */
        private BigDecimal refundAmount;
        
        /** 退款时间 */
        private Date refundTime;
        
        /** 退款原因 */
        private String refundReason;
    }
    
    /**
     * 优惠信息
     */
    @Data
    public static class DiscountInfo {
        
        /** 优惠券ID */
        private Long couponId;
        
        /** 优惠券名称 */
        private String couponName;
        
        /** 优惠券折扣金额 */
        private BigDecimal couponDiscount;
        
        /** 会员折扣金额 */
        private BigDecimal membershipDiscount;
        
        /** 使用的积分 */
        private Integer pointsUsed;
        
        /** 积分抵扣金额 */
        private BigDecimal pointsDiscount;
        
        /** 是否会员免费 */
        private Integer isMembershipFree;
        
        /** 总优惠金额 */
        private BigDecimal totalDiscount;
    }
    
    /**
     * 创建订单请求DTO
     */
    @Data
    public static class CreateOrderRequest {
        
        /** 冥想ID */
        @NotNull(message = "冥想ID不能为空")
        private Long meditationId;
        
        /** 优惠券ID */
        private Long couponId;
        
        /** 使用积分 */
        private Integer pointsUsed;
        
        /** 备注 */
        private String remark;
    }
    
    /**
     * 支付订单请求DTO
     */
    @Data
    public static class PayOrderRequest {
        
        /** 订单号 */
        @NotNull(message = "订单号不能为空")
        private String orderNo;
        
        /** 支付方式 */
        @NotNull(message = "支付方式不能为空")
        private String paymentMethod;
        
        /** 支付金额 */
        @NotNull(message = "支付金额不能为空")
        @Positive(message = "支付金额必须大于0")
        private BigDecimal paymentAmount;
        
        /** 第三方支付参数 */
        private String paymentParams;
    }
    
    /**
     * 退款请求DTO
     */
    @Data
    public static class RefundRequest {
        
        /** 订单号 */
        @NotNull(message = "订单号不能为空")
        private String orderNo;
        
        /** 退款金额 */
        @NotNull(message = "退款金额不能为空")
        @Positive(message = "退款金额必须大于0")
        private BigDecimal refundAmount;
        
        /** 退款原因 */
        @NotNull(message = "退款原因不能为空")
        private String refundReason;
    }
}
