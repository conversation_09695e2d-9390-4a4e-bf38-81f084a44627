package com.xihuan.system.domain.wechat;


import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
@EqualsAndHashCode(callSuper = true)
@Data
public class WxConsultingField extends BaseEntity {
    // 核心字段
    private int id;
    private int parentId; // 对应数据库 parent_id
    private String name;
    private Integer sort;

    // 树结构需要字段（不存数据库）
    private List<WxConsultingField> children;

}
