package com.xihuan.system.domain.dto;

import java.util.List;

public class PsyAppointmentTimeDTO {
    private String date;        // 完整日期，如 "2024年3月15日"
    private String weekDay;     // 星期几，如 "周一"
    private String shortDate;   // 短日期，如 "3月15日"
    private boolean isToday;    // 是否是今天
    private List<TimeRange> timeRanges;  // 时间段列表

    public static class TimeRange {
        private String rangeName;  // 时间段名称（上午/下午/晚上）
        private List<TimeSlot> slots;  // 具体时间槽

        public TimeRange(String rangeName, List<TimeSlot> slots) {
            this.rangeName = rangeName;
            this.slots = slots;
        }

        // Getters and Setters
        public String getRangeName() {
            return rangeName;
        }

        public void setRangeName(String rangeName) {
            this.rangeName = rangeName;
        }

        public List<TimeSlot> getSlots() {
            return slots;
        }

        public void setSlots(List<TimeSlot> slots) {
            this.slots = slots;
        }
    }

    public static class TimeSlot {
        private String time;        // 时间段，如 "09:00-10:00"
        private String status;      // 状态（0: 可预约, 1: 已预约）
        private String timeStatus;  // 状态描述（可预约/已过期）
        private String fullDate;    // 完整日期，用于前端标识

        // Getters and Setters
        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getTimeStatus() {
            return timeStatus;
        }

        public void setTimeStatus(String timeStatus) {
            this.timeStatus = timeStatus;
        }

        public String getFullDate() {
            return fullDate;
        }

        public void setFullDate(String fullDate) {
            this.fullDate = fullDate;
        }
    }

    // Getters and Setters
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getWeekDay() {
        return weekDay;
    }

    public void setWeekDay(String weekDay) {
        this.weekDay = weekDay;
    }

    public String getShortDate() {
        return shortDate;
    }

    public void setShortDate(String shortDate) {
        this.shortDate = shortDate;
    }

    public boolean getIsToday() {
        return isToday;
    }

    public void setIsToday(boolean isToday) {
        this.isToday = isToday;
    }

    public List<TimeRange> getTimeRanges() {
        return timeRanges;
    }

    public void setTimeRanges(List<TimeRange> timeRanges) {
        this.timeRanges = timeRanges;
    }
}