package com.xihuan.system.domain.dto;

import com.xihuan.common.core.domain.entity.PsyUserMeditationRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户冥想记录数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyUserMeditationRecordDTO extends PsyUserMeditationRecord {
    
    /** 冥想标题 */
    private String meditationTitle;
    
    /** 冥想时长 */
    private Integer meditationDuration;
    
    /** 引导师 */
    private String narrator;
    
    /** 冥想封面 */
    private String coverImage;
    
    /** 心情改善程度 */
    private Integer moodImprovement;
    
    /**
     * 冥想记录请求DTO
     */
    @Data
    public static class RecordRequest {
        
        /** 冥想ID */
        @NotNull(message = "冥想ID不能为空")
        private Long meditationId;
        
        /** 播放时长（秒） */
        @Min(value = 0, message = "播放时长不能小于0")
        private Integer durationPlayed;
        
        /** 是否完成 */
        private Integer isCompleted;
        
        /** 冥想前心情（1-5分） */
        @Min(value = 1, message = "心情评分最小为1")
        @Max(value = 5, message = "心情评分最大为5")
        private Integer moodBefore;
        
        /** 冥想后心情（1-5分） */
        @Min(value = 1, message = "心情评分最小为1")
        @Max(value = 5, message = "心情评分最大为5")
        private Integer moodAfter;
    }
    
    /**
     * 冥想统计DTO
     */
    @Data
    public static class MeditationStatistics {
        
        /** 总冥想时长（秒） */
        private Integer totalDuration;
        
        /** 总冥想次数 */
        private Integer totalTimes;
        
        /** 完成次数 */
        private Integer completedTimes;
        
        /** 完成率 */
        private Double completionRate;
        
        /** 平均每次时长 */
        private Integer avgDuration;
        
        /** 最近7天冥想次数 */
        private Integer recentWeekTimes;
        
        /** 最近30天冥想次数 */
        private Integer recentMonthTimes;
        
        /** 连续冥想天数 */
        private Integer consecutiveDays;
        
        /** 心情改善统计 */
        private MoodStatistics moodStats;
    }
    
    /**
     * 心情统计DTO
     */
    @Data
    public static class MoodStatistics {
        
        /** 平均冥想前心情 */
        private Double avgMoodBefore;
        
        /** 平均冥想后心情 */
        private Double avgMoodAfter;
        
        /** 平均心情改善程度 */
        private Double avgMoodImprovement;
        
        /** 心情改善次数 */
        private Integer improvementTimes;
        
        /** 心情改善率 */
        private Double improvementRate;
    }
    
    /**
     * 每日冥想记录DTO
     */
    @Data
    public static class DailyRecord {
        
        /** 日期 */
        private String date;
        
        /** 当日冥想次数 */
        private Integer times;
        
        /** 当日冥想总时长 */
        private Integer totalDuration;
        
        /** 当日完成次数 */
        private Integer completedTimes;
        
        /** 当日平均心情改善 */
        private Double avgMoodImprovement;
    }
    
    /**
     * 冥想记录列表DTO
     */
    @Data
    public static class RecordListDTO {
        
        /** 记录列表 */
        private List<PsyUserMeditationRecordDTO> records;
        
        /** 统计信息 */
        private MeditationStatistics statistics;
        
        /** 每日记录 */
        private List<DailyRecord> dailyRecords;
    }
}
