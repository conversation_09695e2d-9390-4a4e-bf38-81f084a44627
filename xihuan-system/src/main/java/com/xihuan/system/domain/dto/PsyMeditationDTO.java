package com.xihuan.system.domain.dto;

import com.xihuan.common.core.domain.entity.PsyMeditation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 冥想数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyMeditationDTO extends PsyMeditation {
    
    /** 分类信息列表 */
    private List<CategoryDTO> categoryList;
    
    /** 用户是否已购买 */
    private Boolean purchased;
    
//    /** 用户播放记录 */
//    private RecordDTO userRecord;
    
    /** 用户评价信息 */
    private ReviewDTO userReview;
    
    /**
     * 分类DTO
     */
    @Data
    public static class CategoryDTO {
        
        /** 分类ID */
        private Long categoryId;
        
        /** 分类名称 */
        private String categoryName;
        
        /** 父分类ID */
        private Long parentId;
        
        /** 显示顺序 */
        private Integer orderNum;
    }
    
    /**
     * 播放记录DTO
     */
    @Data
    public static class RecordDTO {
        
        /** 记录ID */
        private Long id;
        
        /** 播放时长（秒） */
        private Integer durationPlayed;
        
        /** 是否完成 */
        private Integer isCompleted;
        
        /** 冥想前心情 */
        private Integer moodBefore;
        
        /** 冥想后心情 */
        private Integer moodAfter;
        
        /** 创建时间 */
        private String createTime;
    }
    
    /**
     * 评价DTO
     */
    @Data
    public static class ReviewDTO {
        
        /** 评价ID */
        private Long id;
        
        /** 评价内容 */
        private String content;
        
        /** 评分 */
        private Integer rating;
        
        /** 评价时间 */
        private String createTime;
        
        /** 用户昵称 */
        private String nickName;
    }
    
    /**
     * 播放请求DTO
     */
    @Data
    public static class PlayRequest {
        
        /** 冥想ID */
        @NotNull(message = "冥想ID不能为空")
        private Long meditationId;
        
        /** 播放时长（秒） */
        private Integer durationPlayed;
        
        /** 是否完成 */
        private Integer isCompleted;
        
        /** 冥想前心情（1-5分） */
        private Integer moodBefore;
        
        /** 冥想后心情（1-5分） */
        private Integer moodAfter;
    }
    
    /**
     * 冥想统计DTO
     */
    @Data
    public static class StatisticsDTO {
        
        /** 总冥想时长（秒） */
        private Integer totalDuration;
        
        /** 总冥想次数 */
        private Integer totalTimes;
        
        /** 完成次数 */
        private Integer completedTimes;
        
        /** 平均每次时长 */
        private Integer avgDuration;
        
        /** 完成率 */
        private BigDecimal completionRate;
        
        /** 最近7天冥想次数 */
        private Integer recentWeekTimes;
        
        /** 连续冥想天数 */
        private Integer consecutiveDays;
    }
}
