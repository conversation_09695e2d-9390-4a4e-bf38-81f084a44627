package com.xihuan.system.domain.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 心理测评相关DTO
 * 
 * <AUTHOR>
 */
public class PsyAssessmentDTO {

    /**
     * 量表DTO
     */
    @Data
    public static class ScaleDTO {
        
        /** 量表ID */
        private Long id;
        
        /** 量表名称 */
        private String scaleName;
        
        /** 量表编码 */
        private String scaleCode;
        
        /** 量表描述 */
        private String description;
        
        /** 测评说明 */
        private String instruction;

        /** 测评须知 */
        private String testNotice;

        /** 测评目的 */
        private String testPurpose;

        /** 测评对象 */
        private String testObject;

        /** 测评准备 */
        private String testPreparation;

        /** 测评后的处理 */
        private String testProcessing;

        /** 注意事项 */
        private String testAttention;

        /** 测评基础理论 */
        private String testTheory;

        /** 测评应用 */
        private String testApplication;

        /** 引用文献 */
        private String referenceLiterature;

        /** 适用年龄 */
        private String applicableAge;

        /** 分类ID */
        private Long categoryId;
        
        /** 分类名称 */
        private String categoryName;
        
        /** 作者 */
        private String author;
        
        /** 版本号 */
        private String version;
        
        /** 题目数量 */
        private Integer questionCount;
        
        /** 时间限制(分钟) */
        private Integer timeLimit;
        
        /** 难度等级 */
        private Integer difficultyLevel;
        
        /** 难度描述 */
        private String difficultyDesc;
        
        /** 价格 */
        private BigDecimal price;
        
        /** 是否免费 */
        private Integer isFree;
        
        /** 价格描述 */
        private String priceDesc;
        
        /** 封面图片 */
        private String coverImage;
        
        /** 标签 */
        private String tags;
        
        /** 状态 */
        private Integer status;
        
        /** 状态描述 */
        private String statusDesc;
        
        /** 查看次数 */
        private Integer viewCount;
        
        /** 测试次数 */
        private Integer testCount;
        
        /** 平均评分 */
        private BigDecimal ratingAvg;
        
        /** 评分人数 */
        private Integer ratingCount;
        
        /** 创建时间 */
        private String createTime;
        
        /** 用户是否已购买 */
        private Boolean purchased;
        
        /** 用户测评记录 */
        private RecordDTO userRecord;
        
        /** 用户评价 */
        private ReviewDTO userReview;
        
        /** 分类列表 */
        private List<CategoryDTO> categories;
    }

    /**
     * 题目DTO
     */
    @Data
    public static class QuestionDTO {
        
        /** 题目ID */
        private Long id;
        
        /** 量表ID */
        private Long scaleId;
        
        /** 题目序号 */
        private Integer questionNo;
        
        /** 题目内容 */
        private String questionText;
        
        /** 题目类型 */
        private Integer questionType;
        
        /** 题目类型描述 */
        private String questionTypeDesc;
        
        /** 是否必答 */
        private Integer isRequired;
        
        /** 测量维度 */
        private String dimension;
        
        /** 显示顺序 */
        private Integer orderNum;
        
        /** 选项列表 */
        private List<OptionDTO> options;
        
        /** 用户答案 */
        private AnswerDTO userAnswer;
    }

    /**
     * 选项DTO
     */
    @Data
    public static class OptionDTO {
        
        /** 选项ID */
        private Long id;
        
        /** 题目ID */
        private Long questionId;
        
        /** 选项内容 */
        private String optionText;
        
        /** 选项值 */
        private String optionValue;
        
        /** 选项分值 */
        private Integer score;
        
        /** 显示顺序 */
        private Integer orderNum;
        
        /** 是否被选中 */
        private Boolean selected;
        
        /** 显示文本 */
        private String displayText;
    }

    /**
     * 测评记录DTO
     */
    @Data
    public static class RecordDTO {
        
        /** 记录ID */
        private Long id;
        
        /** 用户ID */
        private Long userId;
        
        /** 量表ID */
        private Long scaleId;
        
        /** 会话ID */
        private String sessionId;
        
        /** 开始时间 */
        private String startTime;
        
        /** 结束时间 */
        private String endTime;
        
        /** 测评时长(秒) */
        private Integer duration;
        
        /** 格式化时长 */
        private String formattedDuration;
        
        /** 总分 */
        private BigDecimal totalScore;
        
        /** 满分 */
        private BigDecimal maxScore;
        
        /** 得分率 */
        private BigDecimal percentage;
        
        /** 格式化得分率 */
        private String formattedPercentage;
        
        /** 状态 */
        private Integer status;
        
        /** 状态描述 */
        private String statusDesc;
        
        /** 结果等级 */
        private String resultLevel;
        
        /** 结果描述 */
        private String resultDescription;
        
        /** 建议 */
        private String suggestions;
        
        /** 维度得分 */
        private String dimensionScores;
        
        /** 是否匿名 */
        private Integer isAnonymous;
        
        /** 量表名称 */
        private String scaleName;
        
        /** 用户昵称 */
        private String nickName;
        
        /** 完成进度 */
        private BigDecimal progress;
        
        /** 当前题目序号 */
        private Integer currentQuestionNo;
        
        /** 剩余时间 */
        private Integer remainingTime;
    }

    /**
     * 答案DTO
     */
    @Data
    public static class AnswerDTO {
        
        /** 答案ID */
        private Long id;
        
        /** 测评记录ID */
        private Long recordId;
        
        /** 题目ID */
        private Long questionId;
        
        /** 选项ID */
        private Long optionId;
        
        /** 答案内容 */
        private String answerText;
        
        /** 得分 */
        private BigDecimal score;
        
        /** 答题时间 */
        private String answerTime;
        
        /** 答题耗时 */
        private Integer timeSpent;
        
        /** 格式化耗时 */
        private String formattedTimeSpent;
        
        /** 题目内容 */
        private String questionText;
        
        /** 选项内容 */
        private String optionText;
        
        /** 答案显示 */
        private String answerDisplay;
    }

    /**
     * 评价DTO
     */
    @Data
    public static class ReviewDTO {
        
        /** 评价ID */
        private Long id;
        
        /** 用户ID */
        private Long userId;
        
        /** 量表ID */
        private Long scaleId;
        
        /** 测评记录ID */
        private Long recordId;
        
        /** 评分 */
        private Integer rating;
        
        /** 评价内容 */
        private String content;
        
        /** 是否匿名 */
        private Integer isAnonymous;
        
        /** 审核状态 */
        private Integer status;
        
        /** 状态描述 */
        private String statusDesc;
        
        /** 点赞数 */
        private Integer likeCount;
        
        /** 回复数 */
        private Integer replyCount;
        
        /** 创建时间 */
        private String createTime;
        
        /** 用户昵称 */
        private String nickName;
        
        /** 用户头像 */
        private String avatar;
        
        /** 量表名称 */
        private String scaleName;
        
        /** 是否已点赞 */
        private Boolean liked;
        
        /** 评分星级 */
        private String ratingStars;
        
        /** 显示用户名 */
        private String displayName;
    }

    /**
     * 分类DTO
     */
    @Data
    public static class CategoryDTO {
        
        /** 分类ID */
        private Long categoryId;
        
        /** 分类名称 */
        private String categoryName;
        
        /** 父分类ID */
        private Long parentId;
        
        /** 显示顺序 */
        private Integer orderNum;
    }

    /**
     * 测评开始请求DTO
     */
    @Data
    public static class StartTestRequest {
        
        /** 量表ID */
        @NotNull(message = "量表ID不能为空")
        private Long scaleId;
        
        /** 是否匿名 */
        private Integer isAnonymous;
    }

    /**
     * 提交答案请求DTO
     */
    @Data
    public static class SubmitAnswerRequest {
        
        /** 会话ID */
        @NotBlank(message = "会话ID不能为空")
        private String sessionId;
        
        /** 题目ID */
        @NotNull(message = "题目ID不能为空")
        private Long questionId;
        
        /** 选项ID（单选/多选） */
        private Long optionId;
        
        /** 答案内容（填空题） */
        private String answerText;
        
        /** 答题耗时（秒） */
        private Integer timeSpent;
    }

    /**
     * 完成测评请求DTO
     */
    @Data
    public static class CompleteTestRequest {
        
        /** 会话ID */
        @NotBlank(message = "会话ID不能为空")
        private String sessionId;
    }

    /**
     * 评价提交请求DTO
     */
    @Data
    public static class SubmitReviewRequest {

        /** 量表ID */
        @NotNull(message = "量表ID不能为空")
        private Long scaleId;

        /** 测评记录ID */
        private Long recordId;

        /** 评分 */
        @NotNull(message = "评分不能为空")
        @Min(value = 1, message = "评分最低为1分")
        @Max(value = 5, message = "评分最高为5分")
        private Integer rating;

        /** 评价内容 */
        @Size(max = 1000, message = "评价内容不能超过1000个字符")
        private String content;

        /** 是否匿名 */
        private Integer isAnonymous;
    }

    /**
     * 订单DTO
     */
    @Data
    public static class OrderDTO {

        /** 订单ID */
        private Long id;

        /** 订单编号 */
        private String orderNo;

        /** 用户ID */
        private Long userId;

        /** 量表ID */
        private Long scaleId;

        /** 量表名称 */
        private String scaleName;

        /** 原价 */
        private java.math.BigDecimal originalPrice;

        /** 实付金额 */
        private java.math.BigDecimal actualPrice;

        /** 优惠金额 */
        private java.math.BigDecimal discountAmount;

        /** 优惠券ID */
        private Long couponId;

        /** 支付方式 */
        private String paymentMethod;

        /** 支付状态 */
        private Integer paymentStatus;

        /** 支付状态描述 */
        private String paymentStatusDesc;

        /** 订单状态 */
        private Integer orderStatus;

        /** 订单状态描述 */
        private String orderStatusDesc;

        /** 支付时间 */
        private String payTime;

        /** 过期时间 */
        private String expireTime;

        /** 退款金额 */
        private java.math.BigDecimal refundAmount;

        /** 退款时间 */
        private String refundTime;

        /** 退款原因 */
        private String refundReason;

        /** 创建时间 */
        private String createTime;

        /** 用户昵称 */
        private String nickName;

        /** 是否可以退款 */
        private Boolean canRefund;

        /** 是否可以取消 */
        private Boolean canCancel;

        /** 距离过期时间(分钟) */
        private Long timeToExpire;
    }

    /**
     * 创建订单请求DTO
     */
    @Data
    public static class CreateOrderRequest {

        /** 量表ID */
        @NotNull(message = "量表ID不能为空")
        private Long scaleId;

        /** 优惠券ID */
        private Long couponId;
    }

    /**
     * 支付订单请求DTO
     */
    @Data
    public static class PayOrderRequest {

        /** 订单编号 */
        @NotBlank(message = "订单编号不能为空")
        private String orderNo;

        /** 支付方式 */
        @NotBlank(message = "支付方式不能为空")
        private String paymentMethod;
    }

    /**
     * 退款申请请求DTO
     */
    @Data
    public static class RefundRequest {

        /** 订单编号 */
        @NotBlank(message = "订单编号不能为空")
        private String orderNo;

        /** 退款原因 */
        @NotBlank(message = "退款原因不能为空")
        @Size(max = 200, message = "退款原因不能超过200个字符")
        private String refundReason;
    }
}
