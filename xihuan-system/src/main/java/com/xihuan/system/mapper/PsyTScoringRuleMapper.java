package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTScoringRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 计分规则Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTScoringRuleMapper {
    
    /**
     * 查询计分规则列表
     * 
     * @param rule 计分规则信息
     * @return 计分规则集合
     */
    List<PsyTScoringRule> selectRuleList(PsyTScoringRule rule);

    /**
     * 根据ID查询计分规则
     * 
     * @param id 计分规则ID
     * @return 计分规则信息
     */
    PsyTScoringRule selectRuleById(Long id);

    /**
     * 根据量表ID查询计分规则列表
     * 
     * @param scaleId 量表ID
     * @return 计分规则集合
     */
    List<PsyTScoringRule> selectRulesByScaleId(Long scaleId);

    /**
     * 根据分量表ID查询计分规则列表
     * 
     * @param subscaleId 分量表ID
     * @return 计分规则集合
     */
    List<PsyTScoringRule> selectRulesBySubscaleId(Long subscaleId);

    /**
     * 查询量表总分计分规则
     * 
     * @param scaleId 量表ID
     * @return 计分规则集合
     */
    List<PsyTScoringRule> selectTotalScoreRules(Long scaleId);

    /**
     * 新增计分规则
     * 
     * @param rule 计分规则信息
     * @return 结果
     */
    int insertRule(PsyTScoringRule rule);

    /**
     * 批量新增计分规则
     * 
     * @param rules 计分规则列表
     * @return 结果
     */
    int batchInsertRules(List<PsyTScoringRule> rules);

    /**
     * 修改计分规则
     * 
     * @param rule 计分规则信息
     * @return 结果
     */
    int updateRule(PsyTScoringRule rule);

    /**
     * 删除计分规则
     * 
     * @param id 计分规则ID
     * @return 结果
     */
    int deleteRuleById(Long id);

    /**
     * 批量删除计分规则
     * 
     * @param ids 需要删除的计分规则ID
     * @return 结果
     */
    int deleteRuleByIds(Long[] ids);

    /**
     * 根据量表ID删除计分规则
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteRulesByScaleId(Long scaleId);

    /**
     * 根据分量表ID删除计分规则
     * 
     * @param subscaleId 分量表ID
     * @return 结果
     */
    int deleteRulesBySubscaleId(Long subscaleId);

    /**
     * 根据分数查找匹配的计分规则
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID（可为空）
     * @param score 分数
     * @return 计分规则
     */
    PsyTScoringRule findMatchingRule(@Param("scaleId") Long scaleId, 
                                    @Param("subscaleId") Long subscaleId, 
                                    @Param("score") BigDecimal score);

    /**
     * 查找分数对应的所有匹配规则
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID（可为空）
     * @param score 分数
     * @return 计分规则集合
     */
    List<PsyTScoringRule> findAllMatchingRules(@Param("scaleId") Long scaleId, 
                                              @Param("subscaleId") Long subscaleId, 
                                              @Param("score") BigDecimal score);

    /**
     * 检查规则范围是否重叠
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkRuleRangeOverlap(@Param("scaleId") Long scaleId, 
                             @Param("subscaleId") Long subscaleId,
                             @Param("minValue") BigDecimal minValue, 
                             @Param("maxValue") BigDecimal maxValue,
                             @Param("excludeId") Long excludeId);

    /**
     * 统计量表计分规则数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countRulesByScaleId(Long scaleId);

    /**
     * 统计分量表计分规则数量
     * 
     * @param subscaleId 分量表ID
     * @return 数量
     */
    int countRulesBySubscaleId(Long subscaleId);

    /**
     * 查询规则类型统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectRuleTypeStats(Long scaleId);

    /**
     * 查询规则使用统计
     * 
     * @param ruleId 规则ID
     * @return 统计信息
     */
    Map<String, Object> selectRuleUsageStats(Long ruleId);

    /**
     * 复制计分规则到新量表
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    int copyRulesToScale(@Param("sourceScaleId") Long sourceScaleId, 
                        @Param("targetScaleId") Long targetScaleId);

    /**
     * 复制计分规则到新分量表
     * 
     * @param sourceSubscaleId 源分量表ID
     * @param targetSubscaleId 目标分量表ID
     * @return 结果
     */
    int copyRulesToSubscale(@Param("sourceSubscaleId") Long sourceSubscaleId, 
                           @Param("targetSubscaleId") Long targetSubscaleId);

    /**
     * 查询规则覆盖范围分析
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 覆盖范围信息
     */
    Map<String, Object> selectRuleCoverageAnalysis(@Param("scaleId") Long scaleId, 
                                                  @Param("subscaleId") Long subscaleId);

    /**
     * 查询规则分数分布
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 分布信息
     */
    List<Map<String, Object>> selectRuleScoreDistribution(@Param("scaleId") Long scaleId, 
                                                          @Param("subscaleId") Long subscaleId);

    /**
     * 搜索计分规则
     * 
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @param ruleType 规则类型
     * @return 计分规则集合
     */
    List<PsyTScoringRule> searchRules(@Param("keyword") String keyword,
                                     @Param("scaleId") Long scaleId,
                                     @Param("ruleType") String ruleType);

    /**
     * 验证规则完整性
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 验证结果
     */
    Map<String, Object> validateRuleCompleteness(@Param("scaleId") Long scaleId, 
                                                 @Param("subscaleId") Long subscaleId);
}
