package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTScale;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 量表基础信息Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTScaleMapper {
    
    /**
     * 查询量表列表
     * 
     * @param scale 量表信息
     * @return 量表集合
     */
    List<PsyTScale> selectScaleList(PsyTScale scale);

    /**
     * 根据ID查询量表
     * 
     * @param id 量表ID
     * @return 量表信息
     */
    PsyTScale selectScaleById(Long id);

    /**
     * 查询量表详情（包含题目、分量表、计分规则等信息）
     * 
     * @param id 量表ID
     * @return 量表详情
     */
    PsyTScale selectScaleWithDetails(Long id);

    /**
     * 根据编码查询量表
     * 
     * @param code 量表编码
     * @return 量表信息
     */
    PsyTScale selectScaleByCode(String code);

    /**
     * 新增量表
     * 
     * @param scale 量表信息
     * @return 结果
     */
    int insertScale(PsyTScale scale);

    /**
     * 修改量表
     * 
     * @param scale 量表信息
     * @return 结果
     */
    int updateScale(PsyTScale scale);

    /**
     * 删除量表
     * 
     * @param id 量表ID
     * @return 结果
     */
    int deleteScaleById(Long id);

    /**
     * 批量删除量表
     * 
     * @param ids 需要删除的量表ID
     * @return 结果
     */
    int deleteScaleByIds(Long[] ids);

    /**
     * 根据企业ID查询量表列表
     * 
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    List<PsyTScale> selectScalesByEnterpriseId(Long enterpriseId);

    /**
     * 查询用户已购买的量表列表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    List<PsyTScale> selectPurchasedScalesByUserId(Long userId);

    /**
     * 查询热门量表列表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyTScale> selectHotScales(@Param("limit") Integer limit);

    /**
     * 查询推荐量表列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyTScale> selectRecommendScales(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询免费量表列表
     * 
     * @return 量表集合
     */
    List<PsyTScale> selectFreeScales();

    /**
     * 更新量表查看次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    int updateViewCount(Long id);

    /**
     * 更新量表搜索次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    int updateSearchCount(Long id);

    /**
     * 检查量表编码唯一性
     * 
     * @param code 量表编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkScaleCodeUnique(@Param("code") String code, @Param("excludeId") Long excludeId);

    /**
     * 检查量表是否被使用
     * 
     * @param id 量表ID
     * @return 数量
     */
    int checkScaleInUse(Long id);

    /**
     * 搜索量表
     *
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param status 状态
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    List<PsyTScale> searchScales(@Param("keyword") String keyword,
                                @Param("categoryId") Integer categoryId,
                                @Param("status") Integer status,
                                @Param("enterpriseId") Long enterpriseId);

    /**
     * 统计量表数量
     * 
     * @param scale 查询条件
     * @return 数量
     */
    int countScales(PsyTScale scale);

    /**
     * 查询量表统计信息
     * 
     * @return 统计信息
     */
    List<Map<String, Object>> selectScaleStats();

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserTestStats(Long userId);

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleTestStats(Long scaleId);

    /**
     * 查询今日热门量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyTScale> selectTodayHotScales(@Param("limit") Integer limit);

    /**
     * 查询最新量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyTScale> selectLatestScales(@Param("limit") Integer limit);

    /**
     * 查询用户收藏的量表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    List<PsyTScale> selectFavoriteScalesByUserId(Long userId);

    /**
     * 查询相似量表
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyTScale> selectSimilarScales(@Param("scaleId") Long scaleId, @Param("limit") Integer limit);

    /**
     * 查询企业可用量表
     * 
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    List<PsyTScale> selectAvailableScalesForEnterprise(Long enterpriseId);

    /**
     * 查询量表使用统计
     * 
     * @param scaleId 量表ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> selectScaleUsageStats(@Param("scaleId") Long scaleId, 
                                             @Param("startDate") String startDate, 
                                             @Param("endDate") String endDate);

    /**
     * 查询量表趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    List<Map<String, Object>> selectScaleTrendStats(@Param("days") Integer days);

    /**
     * 批量更新量表状态
     * 
     * @param ids 量表ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateScaleStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 查询量表分类统计
     * 
     * @return 统计信息
     */
    List<Map<String, Object>> selectScaleCategoryStats();

    /**
     * 查询量表评分分布
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectScaleScoreDistribution(Long scaleId);
}
