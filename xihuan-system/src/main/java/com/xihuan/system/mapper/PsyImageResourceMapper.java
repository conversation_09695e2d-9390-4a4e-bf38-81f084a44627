package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyImageResource;
import java.util.List;

/**
 * 心理咨询平台图片资源Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyImageResourceMapper {
    /**
     * 查询图片资源
     * @param id 主键ID
     * @return 图片资源
     */
    PsyImageResource selectPsyImageResourceById(Long id);

    /**
     * 查询图片资源列表
     * @param psyImageResource 查询条件
     * @return 图片资源集合
     */
    List<PsyImageResource> selectPsyImageResourceList(PsyImageResource psyImageResource);

    /**
     * 新增图片资源
     * @param psyImageResource 图片资源
     * @return 结果
     */
    int insertPsyImageResource(PsyImageResource psyImageResource);

    /**
     * 修改图片资源
     * @param psyImageResource 图片资源
     * @return 结果
     */
    int updatePsyImageResource(PsyImageResource psyImageResource);

    /**
     * 删除图片资源
     * @param id 主键ID
     * @return 结果
     */
    int deletePsyImageResourceById(Long id);

    /**
     * 批量删除图片资源
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    int deletePsyImageResourceByIds(Long[] ids);
} 