package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTimeTemplateItem;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 模板时间段详情Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyTimeTemplateItemMapper {
    
    /**
     * 查询模板明细列表
     * 
     * @param templateItem 模板明细
     * @return 模板明细集合
     */
    List<PsyTimeTemplateItem> selectTemplateItemList(PsyTimeTemplateItem templateItem);
    
    /**
     * 根据ID查询模板明细
     * 
     * @param id 模板明细主键
     * @return 模板明细
     */
    PsyTimeTemplateItem selectTemplateItemById(Long id);
    
    /**
     * 根据模板ID查询明细列表
     *
     * @param templateId 模板ID
     * @return 模板明细集合
     */
    List<PsyTimeTemplateItem> selectTemplateItemsByTemplateId(@Param("templateId") Long templateId);

    /**
     * 根据模板ID和星期查询明细
     *
     * @param templateId 模板ID
     * @param dayOfWeek 星期几(1-7)
     * @return 模板明细集合
     */
    List<PsyTimeTemplateItem> selectItemsByTemplateAndDay(
        @Param("templateId") Long templateId,
        @Param("dayOfWeek") Integer dayOfWeek
    );
    
    /**
     * 新增模板明细
     * 
     * @param templateItem 模板明细
     * @return 结果
     */
    int insertTemplateItem(PsyTimeTemplateItem templateItem);
    
    /**
     * 批量新增模板明细
     * 
     * @param templateItems 模板明细列表
     * @return 结果
     */
    int batchInsertTemplateItems(@Param("templateItems") List<PsyTimeTemplateItem> templateItems);
    
    /**
     * 修改模板明细
     * 
     * @param templateItem 模板明细
     * @return 结果
     */
    int updateTemplateItem(PsyTimeTemplateItem templateItem);
    
    /**
     * 删除模板明细
     * 
     * @param id 模板明细主键
     * @return 结果
     */
    int deleteTemplateItemById(Long id);
    
    /**
     * 根据模板ID删除所有明细
     *
     * @param templateId 模板ID
     * @return 结果
     */
    int deleteTemplateItemsByTemplateId(@Param("templateId") Long templateId);
    
    /**
     * 批量删除模板明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTemplateItemByIds(Long[] ids);
    
    /**
     * 检查时间段是否冲突
     * 
     * @param templateId 模板ID
     * @param dayOfWeek 星期几
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludeId 排除的明细ID
     * @return 冲突的明细数量
     */
    int checkTimeConflict(
        @Param("templateId") Long templateId,
        @Param("dayOfWeek") Integer dayOfWeek,
        @Param("startTime") String startTime,
        @Param("endTime") String endTime,
        @Param("excludeId") Long excludeId
    );
}
