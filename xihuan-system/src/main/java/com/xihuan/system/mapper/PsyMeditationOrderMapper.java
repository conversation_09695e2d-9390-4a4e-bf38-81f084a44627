package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyMeditationOrder;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 冥想订单表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyMeditationOrderMapper {
    
    /**
     * 查询订单列表
     * 
     * @param order 订单信息
     * @return 订单集合
     */
    List<PsyMeditationOrder> selectOrderList(PsyMeditationOrder order);

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    PsyMeditationOrder selectOrderById(Long id);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    PsyMeditationOrder selectOrderByOrderNo(String orderNo);

    /**
     * 查询订单详情（包含冥想和用户信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    PsyMeditationOrder selectOrderWithDetails(Long id);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyMeditationOrder> selectOrdersByUserId(Long userId);

    /**
     * 根据冥想ID查询订单列表
     * 
     * @param meditationId 冥想ID
     * @return 订单集合
     */
    List<PsyMeditationOrder> selectOrdersByMeditationId(Long meditationId);

    /**
     * 新增订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int insertOrder(PsyMeditationOrder order);

    /**
     * 修改订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int updateOrder(PsyMeditationOrder order);

    /**
     * 删除订单
     * 
     * @param id 订单ID
     * @return 结果
     */
    int deleteOrderById(Long id);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    int deleteOrderByIds(Long[] ids);

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单号
     * @param status 订单状态
     * @param paymentMethod 支付方式
     * @param transactionId 第三方交易号
     * @param paymentTime 支付时间
     * @return 结果
     */
    int updateOrderPaymentStatus(@Param("orderNo") String orderNo,
                                @Param("status") Integer status,
                                @Param("paymentMethod") String paymentMethod,
                                @Param("transactionId") String transactionId,
                                @Param("paymentTime") Date paymentTime);

    /**
     * 更新订单退款信息
     * 
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param refundTime 退款时间
     * @return 结果
     */
    int updateOrderRefund(@Param("orderNo") String orderNo,
                         @Param("refundAmount") BigDecimal refundAmount,
                         @Param("refundTime") Date refundTime);

    /**
     * 生成订单号
     * 
     * @return 订单号
     */
    String generateOrderNo();

    /**
     * 检查用户是否已购买冥想
     * 
     * @param userId 用户ID
     * @param meditationId 冥想ID
     * @return 已购买的订单数量
     */
    int checkUserPurchased(@Param("userId") Long userId, @Param("meditationId") Long meditationId);
}
