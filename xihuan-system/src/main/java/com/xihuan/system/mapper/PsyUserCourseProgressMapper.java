package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyUserCourseProgress;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户课程学习进度表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyUserCourseProgressMapper {
    
    /**
     * 查询学习进度列表
     * 
     * @param progress 学习进度信息
     * @return 学习进度集合
     */
    List<PsyUserCourseProgress> selectProgressList(PsyUserCourseProgress progress);

    /**
     * 根据ID查询学习进度
     * 
     * @param id 学习进度ID
     * @return 学习进度信息
     */
    PsyUserCourseProgress selectProgressById(Long id);

    /**
     * 根据用户ID和章节ID查询学习进度
     * 
     * @param userId 用户ID
     * @param chapterId 章节ID
     * @return 学习进度信息
     */
    PsyUserCourseProgress selectProgressByUserAndChapter(@Param("userId") Long userId, @Param("chapterId") Long chapterId);

    /**
     * 根据用户ID和课程ID查询学习进度列表
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 学习进度集合
     */
    List<PsyUserCourseProgress> selectProgressByUserAndCourse(@Param("userId") Long userId, @Param("courseId") Long courseId);

    /**
     * 根据用户ID查询学习进度列表
     * 
     * @param userId 用户ID
     * @return 学习进度集合
     */
    List<PsyUserCourseProgress> selectProgressByUserId(Long userId);

    /**
     * 新增学习进度
     * 
     * @param progress 学习进度信息
     * @return 结果
     */
    int insertProgress(PsyUserCourseProgress progress);

    /**
     * 修改学习进度
     * 
     * @param progress 学习进度信息
     * @return 结果
     */
    int updateProgress(PsyUserCourseProgress progress);

    /**
     * 删除学习进度
     * 
     * @param id 学习进度ID
     * @return 结果
     */
    int deleteProgressById(Long id);

    /**
     * 批量删除学习进度
     * 
     * @param ids 需要删除的学习进度ID
     * @return 结果
     */
    int deleteProgressByIds(Long[] ids);

    /**
     * 根据用户ID删除学习进度
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int deleteProgressByUserId(Long userId);

    /**
     * 根据课程ID删除学习进度
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    int deleteProgressByCourseId(Long courseId);

    /**
     * 更新学习进度
     * 
     * @param userId 用户ID
     * @param chapterId 章节ID
     * @param progressPercent 进度百分比
     * @param lastPosition 最后播放位置
     * @param studyTime 学习时长
     * @return 结果
     */
    int updateStudyProgress(@Param("userId") Long userId,
                           @Param("chapterId") Long chapterId,
                           @Param("progressPercent") BigDecimal progressPercent,
                           @Param("lastPosition") Integer lastPosition,
                           @Param("studyTime") Integer studyTime);

    /**
     * 标记章节为已完成
     * 
     * @param userId 用户ID
     * @param chapterId 章节ID
     * @return 结果
     */
    int markChapterCompleted(@Param("userId") Long userId, @Param("chapterId") Long chapterId);

    /**
     * 计算用户课程总进度
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 总进度百分比
     */
    BigDecimal calculateCourseProgress(@Param("userId") Long userId, @Param("courseId") Long courseId);

    /**
     * 统计用户已完成的章节数量
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 已完成章节数量
     */
    int countCompletedChapters(@Param("userId") Long userId, @Param("courseId") Long courseId);

    /**
     * 统计用户总学习时长
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 总学习时长（秒）
     */
    int sumStudyTime(@Param("userId") Long userId, @Param("courseId") Long courseId);
}
