package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 题目Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTQuestionMapper {
    
    /**
     * 查询题目列表
     * 
     * @param question 题目信息
     * @return 题目集合
     */
    List<PsyTQuestion> selectQuestionList(PsyTQuestion question);

    /**
     * 根据ID查询题目
     * 
     * @param id 题目ID
     * @return 题目信息
     */
    PsyTQuestion selectQuestionById(Long id);

    /**
     * 查询题目详情（包含选项、复合题等信息）
     * 
     * @param id 题目ID
     * @return 题目详情
     */
    PsyTQuestion selectQuestionWithDetails(Long id);

    /**
     * 根据量表ID查询题目列表
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    List<PsyTQuestion> selectQuestionsByScaleId(Long scaleId);

    /**
     * 根据量表ID查询题目列表（包含选项）
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    List<PsyTQuestion> selectQuestionsWithOptionsByScaleId(Long scaleId);

    /**
     * 新增题目
     * 
     * @param question 题目信息
     * @return 结果
     */
    int insertQuestion(PsyTQuestion question);

    /**
     * 修改题目
     * 
     * @param question 题目信息
     * @return 结果
     */
    int updateQuestion(PsyTQuestion question);

    /**
     * 删除题目
     * 
     * @param id 题目ID
     * @return 结果
     */
    int deleteQuestionById(Long id);

    /**
     * 批量删除题目
     * 
     * @param ids 需要删除的题目ID
     * @return 结果
     */
    int deleteQuestionByIds(Long[] ids);

    /**
     * 根据量表ID删除题目
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteQuestionsByScaleId(Long scaleId);

    /**
     * 查询题目序号是否存在
     * 
     * @param scaleId 量表ID
     * @param questionNo 题目序号
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkQuestionNoExists(@Param("scaleId") Long scaleId, 
                             @Param("questionNo") Integer questionNo, 
                             @Param("excludeId") Long excludeId);

    /**
     * 获取量表下一个题目序号
     * 
     * @param scaleId 量表ID
     * @return 下一个序号
     */
    Integer getNextQuestionNo(Long scaleId);

    /**
     * 批量更新题目排序
     * 
     * @param questions 题目列表
     * @return 结果
     */
    int batchUpdateQuestionSort(List<PsyTQuestion> questions);

    /**
     * 统计量表题目数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countQuestionsByScaleId(Long scaleId);

    /**
     * 查询题目类型统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectQuestionTypeStats(Long scaleId);

    /**
     * 查询反向计分题目
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    List<PsyTQuestion> selectReverseQuestions(Long scaleId);

    /**
     * 查询必答题目
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    List<PsyTQuestion> selectRequiredQuestions(Long scaleId);

    /**
     * 查询复合题目
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    List<PsyTQuestion> selectCompositeQuestions(Long scaleId);

    /**
     * 根据分量表关联标识查询题目
     * 
     * @param scaleId 量表ID
     * @param subscaleRef 分量表关联标识
     * @return 题目集合
     */
    List<PsyTQuestion> selectQuestionsBySubscaleRef(@Param("scaleId") Long scaleId, 
                                                   @Param("subscaleRef") String subscaleRef);

    /**
     * 批量更新题目状态
     * 
     * @param ids 题目ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateQuestionStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 复制题目到新量表
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    int copyQuestionsToScale(@Param("sourceScaleId") Long sourceScaleId, 
                            @Param("targetScaleId") Long targetScaleId);

    /**
     * 查询题目答题统计
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    Map<String, Object> selectQuestionAnswerStats(Long questionId);

    /**
     * 查询题目选项分布
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectQuestionOptionDistribution(Long questionId);

    /**
     * 查询题目平均答题时间
     * 
     * @param questionId 题目ID
     * @return 平均时间(秒)
     */
    Integer selectQuestionAvgResponseTime(Long questionId);

    /**
     * 查询题目难度分析
     * 
     * @param questionId 题目ID
     * @return 难度信息
     */
    Map<String, Object> selectQuestionDifficultyAnalysis(Long questionId);

    /**
     * 搜索题目
     * 
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @param questionType 题目类型
     * @return 题目集合
     */
    List<PsyTQuestion> searchQuestions(@Param("keyword") String keyword,
                                      @Param("scaleId") Long scaleId,
                                      @Param("questionType") String questionType);

    /**
     * 查询题目使用情况
     *
     * @param questionId 题目ID
     * @return 使用情况
     */
    Map<String, Object> selectQuestionUsageInfo(Long questionId);

    /**
     * 查询量表最大排序号
     *
     * @param scaleId 量表ID
     * @return 最大排序号
     */
    Integer selectMaxOrderNum(Long scaleId);

    /**
     * 查询题目详情（包含选项）
     *
     * @param id 题目ID
     * @return 题目详情
     */
    PsyTQuestion selectQuestionWithOptions(Long id);

    /**
     * 根据题号查询题目
     *
     * @param scaleId 量表ID
     * @param questionNo 题号
     * @return 题目信息
     */
    PsyTQuestion selectQuestionByNo(@Param("scaleId") Long scaleId, @Param("questionNo") Integer questionNo);

    /**
     * 批量插入题目
     *
     * @param questions 题目列表
     * @return 结果
     */
    int batchInsertQuestions(List<PsyTQuestion> questions);

    /**
     * 统计量表启用题目数量
     *
     * @param scaleId 量表ID
     * @return 数量
     */
    int countEnabledQuestionsByScaleId(Long scaleId);

    /**
     * 更新题目排序
     *
     * @param id 题目ID
     * @param orderNum 排序号
     * @return 结果
     */
    int updateQuestionOrder(@Param("id") Long id, @Param("orderNum") Integer orderNum);

    /**
     * 查询题目统计信息
     *
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectQuestionStats(Long scaleId);

    /**
     * 更新题目状态
     *
     * @param id 题目ID
     * @param status 状态
     * @return 结果
     */
    int updateQuestionStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据分量表ID查询题目
     *
     * @param subscaleId 分量表ID
     * @return 题目集合
     */
    List<PsyTQuestion> selectQuestionsBySubscaleId(Long subscaleId);

    /**
     * 查询可选题目
     *
     * @param scaleId 量表ID
     * @return 题目集合
     */
    List<PsyTQuestion> selectOptionalQuestions(Long scaleId);

    /**
     * 随机查询题目
     *
     * @param scaleId 量表ID
     * @param count 数量
     * @return 题目集合
     */
    List<PsyTQuestion> selectRandomQuestions(@Param("scaleId") Long scaleId, @Param("count") Integer count);
}
