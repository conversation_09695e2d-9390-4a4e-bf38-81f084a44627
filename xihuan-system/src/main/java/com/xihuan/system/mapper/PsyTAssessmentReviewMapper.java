package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTAssessmentReview;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 测评评价Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTAssessmentReviewMapper {
    
    /**
     * 查询测评评价列表
     * 
     * @param review 测评评价信息
     * @return 测评评价集合
     */
    List<PsyTAssessmentReview> selectReviewList(PsyTAssessmentReview review);

    /**
     * 根据ID查询测评评价
     * 
     * @param id 测评评价ID
     * @return 测评评价信息
     */
    PsyTAssessmentReview selectReviewById(Long id);

    /**
     * 查询评价详情（包含量表、用户等信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    PsyTAssessmentReview selectReviewWithDetails(Long id);

    /**
     * 根据量表ID查询评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectReviewsByScaleId(Long scaleId);

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectReviewsByUserId(Long userId);

    /**
     * 根据测评记录ID查询评价
     * 
     * @param recordId 测评记录ID
     * @return 评价信息
     */
    PsyTAssessmentReview selectReviewByRecordId(Long recordId);

    /**
     * 根据订单ID查询评价
     * 
     * @param orderId 订单ID
     * @return 评价信息
     */
    PsyTAssessmentReview selectReviewByOrderId(Long orderId);

    /**
     * 新增测评评价
     * 
     * @param review 测评评价信息
     * @return 结果
     */
    int insertReview(PsyTAssessmentReview review);

    /**
     * 修改测评评价
     * 
     * @param review 测评评价信息
     * @return 结果
     */
    int updateReview(PsyTAssessmentReview review);

    /**
     * 删除测评评价
     * 
     * @param id 测评评价ID
     * @return 结果
     */
    int deleteReviewById(Long id);

    /**
     * 批量删除测评评价
     * 
     * @param ids 需要删除的测评评价ID
     * @return 结果
     */
    int deleteReviewByIds(Long[] ids);

    /**
     * 检查用户是否已评价
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param recordId 测评记录ID
     * @return 评价数量
     */
    int checkUserReviewed(@Param("userId") Long userId, 
                         @Param("scaleId") Long scaleId, 
                         @Param("recordId") Long recordId);

    /**
     * 审核评价
     * 
     * @param id 评价ID
     * @param status 审核状态
     * @param auditRemark 审核意见
     * @param auditBy 审核人
     * @return 结果
     */
    int auditReview(@Param("id") Long id, 
                   @Param("status") Integer status, 
                   @Param("auditRemark") String auditRemark, 
                   @Param("auditBy") String auditBy);

    /**
     * 置顶评价
     * 
     * @param id 评价ID
     * @param isTop 是否置顶
     * @param updateBy 更新者
     * @return 结果
     */
    int topReview(@Param("id") Long id, 
                 @Param("isTop") Integer isTop, 
                 @Param("updateBy") String updateBy);

    /**
     * 点赞评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    int likeReview(Long id);

    /**
     * 取消点赞评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    int unlikeReview(Long id);

    /**
     * 增加回复数
     * 
     * @param id 评价ID
     * @return 结果
     */
    int increaseReplyCount(Long id);

    /**
     * 减少回复数
     * 
     * @param id 评价ID
     * @return 结果
     */
    int decreaseReplyCount(Long id);

    /**
     * 查询量表评价统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleReviewStats(Long scaleId);

    /**
     * 查询用户评价统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserReviewStats(Long userId);

    /**
     * 查询评价统计信息
     * 
     * @return 统计信息
     */
    List<Map<String, Object>> selectReviewStats();

    /**
     * 查询待审核评价列表
     * 
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectPendingReviews();

    /**
     * 查询置顶评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectTopReviews(Long scaleId);

    /**
     * 查询热门评价列表
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectHotReviews(@Param("scaleId") Long scaleId, 
                                               @Param("limit") Integer limit);

    /**
     * 搜索评价
     * 
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @param userId 用户ID
     * @param status 审核状态
     * @param rating 评分
     * @return 评价集合
     */
    List<PsyTAssessmentReview> searchReviews(@Param("keyword") String keyword,
                                           @Param("scaleId") Long scaleId,
                                           @Param("userId") Long userId,
                                           @Param("status") Integer status,
                                           @Param("rating") Integer rating);

    /**
     * 查询评价排行榜
     * 
     * @param type 排行类型(like:点赞数, reply:回复数)
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectReviewRanking(@Param("type") String type, 
                                                 @Param("limit") Integer limit);

    /**
     * 查询量表评分分布
     * 
     * @param scaleId 量表ID
     * @return 评分分布
     */
    List<Map<String, Object>> selectRatingDistribution(Long scaleId);

    /**
     * 查询最新评价列表
     * 
     * @param limit 限制数量
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectLatestReviews(Integer limit);

    /**
     * 查询用户是否点赞评价
     * 
     * @param reviewId 评价ID
     * @param userId 用户ID
     * @return 是否点赞
     */
    int checkUserLiked(@Param("reviewId") Long reviewId, @Param("userId") Long userId);

    /**
     * 批量审核评价
     * 
     * @param ids 评价ID数组
     * @param status 审核状态
     * @param auditRemark 审核意见
     * @param auditBy 审核人
     * @return 结果
     */
    int batchAuditReviews(@Param("ids") Long[] ids,
                         @Param("status") Integer status,
                         @Param("auditRemark") String auditRemark,
                         @Param("auditBy") String auditBy);

    /**
     * 查询评价回复列表
     * 
     * @param reviewId 评价ID
     * @return 回复集合
     */
    List<Map<String, Object>> selectReviewReplies(Long reviewId);
}
