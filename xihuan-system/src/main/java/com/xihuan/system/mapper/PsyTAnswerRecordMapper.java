package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTAnswerRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 答题记录Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTAnswerRecordMapper {
    
    /**
     * 查询答题记录列表
     *
     * @param answer 答题记录信息
     * @return 答题记录集合
     */
    List<PsyTAnswerRecord> selectAnswerRecordList(PsyTAnswerRecord answer);

    /**
     * 根据ID查询答题记录
     *
     * @param id 答题记录ID
     * @return 答题记录信息
     */
    PsyTAnswerRecord selectAnswerRecordById(Long id);

    /**
     * 查询答题记录详情（包含题目、选项信息）
     *
     * @param id 答题记录ID
     * @return 答题记录详情
     */
    PsyTAnswerRecord selectAnswerRecordWithDetails(Long id);

    /**
     * 根据测评记录ID查询答案列表（包含题目、选项信息）
     *
     * @param recordId 测评记录ID
     * @return 答案集合
     */
    List<PsyTAnswerRecord> selectAnswersWithDetailsByRecordId(Long recordId);

    /**
     * 根据测评记录ID查询答题记录列表
     * 
     * @param recordId 测评记录ID
     * @return 答题记录集合
     */
    List<PsyTAnswerRecord> selectAnswersByRecordId(Long recordId);

    /**
     * 根据题目ID查询答题记录列表
     * 
     * @param questionId 题目ID
     * @return 答题记录集合
     */
    List<PsyTAnswerRecord> selectAnswersByQuestionId(Long questionId);

    /**
     * 查询用户答题记录
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @return 答题记录信息
     */
    PsyTAnswerRecord selectAnswerByRecordAndQuestion(@Param("recordId") Long recordId, 
                                                    @Param("questionId") Long questionId);

    /**
     * 新增答题记录
     *
     * @param answer 答题记录信息
     * @return 结果
     */
    int insertAnswerRecord(PsyTAnswerRecord answer);

    /**
     * 批量新增答题记录
     *
     * @param answers 答题记录列表
     * @return 结果
     */
    int batchInsertAnswerRecords(List<PsyTAnswerRecord> answers);

    /**
     * 修改答题记录
     *
     * @param answer 答题记录信息
     * @return 结果
     */
    int updateAnswerRecord(PsyTAnswerRecord answer);

    /**
     * 删除答题记录
     *
     * @param id 答题记录ID
     * @return 结果
     */
    int deleteAnswerRecordById(Long id);

    /**
     * 批量删除答题记录
     *
     * @param ids 需要删除的答题记录ID
     * @return 结果
     */
    int deleteAnswerRecordByIds(Long[] ids);

    /**
     * 根据测评记录ID删除答题记录
     *
     * @param recordId 测评记录ID
     * @return 结果
     */
    int deleteAnswersByRecordId(Long recordId);

    /**
     * 统计测评记录答题数量
     * 
     * @param recordId 测评记录ID
     * @return 数量
     */
    int countAnswersByRecordId(Long recordId);

    /**
     * 统计题目答题数量
     * 
     * @param questionId 题目ID
     * @return 数量
     */
    int countAnswersByQuestionId(Long questionId);

    /**
     * 查询答题统计信息
     *
     * @param recordId 测评记录ID
     * @return 统计信息列表（每个维度一条记录）
     */
    List<Map<String, Object>> selectAnswerStats(Long recordId);

    /**
     * 查询题目答题分布
     * 
     * @param questionId 题目ID
     * @return 分布信息
     */
    List<Map<String, Object>> selectQuestionAnswerDistribution(Long questionId);

    /**
     * 查询用户答题进度
     *
     * @param recordId 测评记录ID
     * @return 进度信息
     */
    Map<String, Object> selectAnswerProgress(Long recordId);

    /**
     * 查询答题历史记录
     *
     * @param recordId 测评记录ID
     * @return 答题历史记录列表
     */
    List<Map<String, Object>> selectAnswerHistoryByRecordId(Long recordId);

    /**
     * 查询答题时长统计
     * 
     * @param recordId 测评记录ID
     * @return 时长统计
     */
    Map<String, Object> selectAnswerTimeStats(Long recordId);

    /**
     * 查询题目平均答题时间
     * 
     * @param questionId 题目ID
     * @return 平均时间
     */
    Integer selectQuestionAvgResponseTime(Long questionId);

    /**
     * 查询用户答题历史
     * 
     * @param userId 用户ID
     * @param questionId 题目ID
     * @return 答题历史
     */
    List<PsyTAnswerRecord> selectUserAnswerHistory(@Param("userId") Long userId, 
                                                  @Param("questionId") Long questionId);

    /**
     * 计算测评记录总分
     *
     * @param recordId 测评记录ID
     * @return 总分
     */
    java.math.BigDecimal calculateTotalScore(Long recordId);

    /**
     * 计算维度得分
     *
     * @param recordId 测评记录ID
     * @param dimension 维度名称
     * @return 维度得分
     */
    java.math.BigDecimal calculateDimensionScore(@Param("recordId") Long recordId,
                                               @Param("dimension") String dimension);

    /**
     * 查询题目答案分布统计
     *
     * @param questionId 题目ID
     * @return 分布统计
     */
    List<Map<String, Object>> selectQuestionAnswerStats(Long questionId);

    /**
     * 查询答题正确率统计
     * 
     * @param recordId 测评记录ID
     * @return 正确率统计
     */
    Map<String, Object> selectAnswerAccuracyStats(Long recordId);

    /**
     * 查询答题模式分析
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 模式分析
     */
    Map<String, Object> selectAnswerPatternAnalysis(@Param("userId") Long userId, 
                                                    @Param("scaleId") Long scaleId);
}
