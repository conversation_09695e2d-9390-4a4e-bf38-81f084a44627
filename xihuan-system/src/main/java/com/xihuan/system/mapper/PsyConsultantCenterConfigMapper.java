package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyConsultantCenterConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 咨询师-咨询中心配置Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyConsultantCenterConfigMapper {
    
    /**
     * 查询咨询师配置列表
     * 
     * @param config 咨询师配置
     * @return 咨询师配置集合
     */
    List<PsyConsultantCenterConfig> selectConfigList(PsyConsultantCenterConfig config);
    
    /**
     * 根据ID查询咨询师配置
     * 
     * @param id 咨询师配置主键
     * @return 咨询师配置
     */
    PsyConsultantCenterConfig selectConfigById(Long id);
    
    /**
     * 根据咨询师ID和中心ID查询配置
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 咨询师配置
     */
    PsyConsultantCenterConfig selectConfigByConsultantAndCenter(
        @Param("consultantId") Long consultantId, 
        @Param("centerId") Long centerId
    );
    
    /**
     * 根据咨询师ID查询所有配置
     * 
     * @param consultantId 咨询师ID
     * @return 咨询师配置集合
     */
    List<PsyConsultantCenterConfig> selectConfigsByConsultantId(@Param("consultantId") Long consultantId);
    
    /**
     * 新增咨询师配置
     * 
     * @param config 咨询师配置
     * @return 结果
     */
    int insertConfig(PsyConsultantCenterConfig config);
    
    /**
     * 修改咨询师配置
     * 
     * @param config 咨询师配置
     * @return 结果
     */
    int updateConfig(PsyConsultantCenterConfig config);
    
    /**
     * 删除咨询师配置
     * 
     * @param id 咨询师配置主键
     * @return 结果
     */
    int deleteConfigById(Long id);
    
    /**
     * 批量删除咨询师配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteConfigByIds(Long[] ids);
}
