package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyMessage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息Mapper接口
 */
public interface PsyMessageMapper {
    
    /**
     * 插入消息
     * 
     * @param message 消息信息
     * @return 结果
     */
    int insertMessage(PsyMessage message);
    
    /**
     * 根据ID查询消息
     * 
     * @param messageId 消息ID
     * @return 消息信息
     */
    PsyMessage selectMessageById(Long messageId);
    
    /**
     * 查询会话的消息列表
     * 
     * @param conversationId 会话ID
     * @return 消息列表
     */
    List<PsyMessage> selectMessagesByConversationId(@Param("conversationId") Long conversationId);
    
    /**
     * 更新消息
     * 
     * @param message 消息信息
     * @return 结果
     */
    int updateMessage(PsyMessage message);
    
    /**
     * 标记消息为已读
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 结果
     */
    int markMessageAsRead(@Param("messageId") Long messageId, @Param("userId") Long userId);
    
    /**
     * 标记会话所有消息为已读
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 结果
     */
    int markAllMessagesAsRead(@Param("conversationId") Long conversationId, @Param("userId") Long userId);
} 