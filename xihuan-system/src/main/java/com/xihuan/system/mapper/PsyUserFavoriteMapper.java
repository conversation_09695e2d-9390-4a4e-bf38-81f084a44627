package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyUserFavorite;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏数据访问层
 */
public interface PsyUserFavoriteMapper {
    /**
     * 新增收藏
     */
    int insertFavorite(PsyUserFavorite favorite);

    /**
     * 批量删除收藏
     */
    int deleteFavoriteByIds(Long[] favoriteIds);

    /**
     * 查询用户收藏列表
     */
    List<PsyUserFavorite> selectFavoriteList(PsyUserFavorite favorite);

    /**
     * 查询收藏详情
     */
    PsyUserFavorite selectFavoriteById(Long favoriteId);

    /**
     * 更新收藏信息
     */
    int updateFavorite(PsyUserFavorite favorite);

    /**
     * 检查是否已收藏
     */
    PsyUserFavorite checkFavorite(@Param("userId") Long userId, @Param("targetType") Integer targetType,
                                 @Param("targetId") Long targetId);

    /**
     * 兼容旧版本的检查方法
     */
    PsyUserFavorite checkFavoriteOld(@Param("userId") Long userId, @Param("targetType") Integer targetType,
                                 @Param("counselorId") Long counselorId, @Param("productId") Long productId);

    /**
     * 查询用户收藏列表（包含详细信息）
     */
    List<Map<String, Object>> selectFavoriteWithDetails(PsyUserFavorite favorite);

    /**
     * 更新收藏查看次数
     */
    int updateViewCount(Long favoriteId);

    /**
     * 查询用户收藏统计
     */
    Map<String, Object> selectUserFavoriteStats(Long userId);

    /**
     * 查询目标被收藏次数
     */
    int countFavoriteByTarget(@Param("targetType") Integer targetType, @Param("targetId") Long targetId);

    /**
     * 查询用户收藏的目标ID列表
     */
    List<Long> selectUserFavoriteTargetIds(@Param("userId") Long userId, @Param("targetType") Integer targetType);
}