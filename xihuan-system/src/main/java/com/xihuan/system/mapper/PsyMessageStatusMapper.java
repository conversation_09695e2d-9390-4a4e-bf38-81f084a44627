package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyMessageStatus;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 消息状态Mapper接口
 */
public interface PsyMessageStatusMapper {
    
    /**
     * 插入消息状态
     * 
     * @param status 消息状态信息
     * @return 结果
     */
    int insertMessageStatus(PsyMessageStatus status);
    
    /**
     * 批量插入消息状态
     * 
     * @param statusList 消息状态列表
     * @return 结果
     */
    int batchInsertMessageStatus(List<PsyMessageStatus> statusList);
    
    /**
     * 根据消息ID和用户ID查询消息状态
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 消息状态信息
     */
    PsyMessageStatus selectMessageStatus(@Param("messageId") Long messageId, @Param("userId") Long userId);
    
    /**
     * 更新消息状态
     * 
     * @param status 消息状态信息
     * @return 结果
     */
    int updateMessageStatus(PsyMessageStatus status);
    
    /**
     * 批量更新消息已读状态
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @param isRead 是否已读
     * @param readTime 阅读时间
     * @return 结果
     */
    int batchUpdateMessageReadStatus(@Param("messageIds") List<Long> messageIds, 
                                   @Param("userId") Long userId, 
                                   @Param("isRead") String isRead,
                                   @Param("readTime") Date readTime);
    
    /**
     * 统计用户未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int countUnreadMessages(Long userId);
} 