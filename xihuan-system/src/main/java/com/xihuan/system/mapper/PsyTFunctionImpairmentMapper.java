package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTFunctionImpairment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 功能损害评估Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTFunctionImpairmentMapper {
    
    /**
     * 查询功能损害评估列表
     * 
     * @param impairment 功能损害评估信息
     * @return 功能损害评估集合
     */
    List<PsyTFunctionImpairment> selectImpairmentList(PsyTFunctionImpairment impairment);

    /**
     * 根据ID查询功能损害评估
     * 
     * @param id 功能损害评估ID
     * @return 功能损害评估信息
     */
    PsyTFunctionImpairment selectImpairmentById(Long id);

    /**
     * 根据量表ID查询功能损害评估列表
     * 
     * @param scaleId 量表ID
     * @return 功能损害评估集合
     */
    List<PsyTFunctionImpairment> selectImpairmentsByScaleId(Long scaleId);

    /**
     * 新增功能损害评估
     * 
     * @param impairment 功能损害评估信息
     * @return 结果
     */
    int insertImpairment(PsyTFunctionImpairment impairment);

    /**
     * 批量新增功能损害评估
     * 
     * @param impairments 功能损害评估列表
     * @return 结果
     */
    int batchInsertImpairments(List<PsyTFunctionImpairment> impairments);

    /**
     * 修改功能损害评估
     * 
     * @param impairment 功能损害评估信息
     * @return 结果
     */
    int updateImpairment(PsyTFunctionImpairment impairment);

    /**
     * 删除功能损害评估
     * 
     * @param id 功能损害评估ID
     * @return 结果
     */
    int deleteImpairmentById(Long id);

    /**
     * 批量删除功能损害评估
     * 
     * @param ids 需要删除的功能损害评估ID
     * @return 结果
     */
    int deleteImpairmentByIds(Long[] ids);

    /**
     * 根据量表ID删除功能损害评估
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteImpairmentsByScaleId(Long scaleId);

    /**
     * 统计量表功能损害评估数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countImpairmentsByScaleId(Long scaleId);

    /**
     * 查询功能损害程度统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectImpairmentLevelStats(Long scaleId);

    /**
     * 查询功能损害评估使用统计
     * 
     * @param impairmentId 功能损害评估ID
     * @return 统计信息
     */
    Map<String, Object> selectImpairmentUsageStats(Long impairmentId);

    /**
     * 复制功能损害评估到新量表
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    int copyImpairmentsToScale(@Param("sourceScaleId") Long sourceScaleId, 
                              @Param("targetScaleId") Long targetScaleId);
}
