package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTAssessmentOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 测评订单Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTAssessmentOrderMapper {
    
    /**
     * 查询测评订单列表
     * 
     * @param order 测评订单信息
     * @return 测评订单集合
     */
    List<PsyTAssessmentOrder> selectOrderList(PsyTAssessmentOrder order);

    /**
     * 根据ID查询测评订单
     * 
     * @param id 测评订单ID
     * @return 测评订单信息
     */
    PsyTAssessmentOrder selectOrderById(Long id);

    /**
     * 根据订单编号查询测评订单
     * 
     * @param orderNo 订单编号
     * @return 测评订单信息
     */
    PsyTAssessmentOrder selectOrderByOrderNo(String orderNo);

    /**
     * 查询订单详情（包含量表、用户等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    PsyTAssessmentOrder selectOrderWithDetails(Long id);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectOrdersByUserId(Long userId);

    /**
     * 根据量表ID查询订单列表
     * 
     * @param scaleId 量表ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectOrdersByScaleId(Long scaleId);

    /**
     * 新增测评订单
     * 
     * @param order 测评订单信息
     * @return 结果
     */
    int insertOrder(PsyTAssessmentOrder order);

    /**
     * 修改测评订单
     * 
     * @param order 测评订单信息
     * @return 结果
     */
    int updateOrder(PsyTAssessmentOrder order);

    /**
     * 删除测评订单
     * 
     * @param id 测评订单ID
     * @return 结果
     */
    int deleteOrderById(Long id);

    /**
     * 批量删除测评订单
     * 
     * @param ids 需要删除的测评订单ID
     * @return 结果
     */
    int deleteOrderByIds(Long[] ids);

    /**
     * 检查用户是否已购买量表
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 数量
     */
    int checkUserPurchased(@Param("userId") Long userId, @Param("scaleId") Long scaleId);

    /**
     * 查询待支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectPendingOrders(Long userId);

    /**
     * 查询已支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectPaidOrders(Long userId);

    /**
     * 查询已取消订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectCancelledOrders(Long userId);

    /**
     * 查询已退款订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectRefundedOrders(Long userId);

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 状态
     * @return 结果
     */
    int updateOrderStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新订单支付信息
     * 
     * @param id 订单ID
     * @param transactionId 交易流水号
     * @param paymentMethod 支付方式
     * @return 结果
     */
    int updateOrderPaymentInfo(@Param("id") Long id, 
                              @Param("transactionId") String transactionId,
                              @Param("paymentMethod") String paymentMethod);

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @return 数量
     */
    int countOrdersByUserId(Long userId);

    /**
     * 统计量表订单数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countOrdersByScaleId(Long scaleId);

    /**
     * 查询订单统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectOrderStats();

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserOrderStats(Long userId);

    /**
     * 查询量表订单统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleOrderStats(Long scaleId);

    /**
     * 查询订单趋势统计
     * 
     * @param days 天数
     * @return 趋势统计
     */
    List<Map<String, Object>> selectOrderTrendStats(@Param("days") Integer days);

    /**
     * 查询收入统计
     * 
     * @param year 年份
     * @param month 月份
     * @return 收入统计
     */
    Map<String, Object> selectRevenueStats(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查询支付方式统计
     * 
     * @return 支付方式统计
     */
    List<Map<String, Object>> selectPaymentMethodStats();

    /**
     * 查询超时未支付订单
     * 
     * @param timeoutHours 超时小时数
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectTimeoutOrders(@Param("timeoutHours") Integer timeoutHours);

    /**
     * 自动取消超时订单
     * 
     * @param timeoutHours 超时小时数
     * @return 取消数量
     */
    int autoCancelTimeoutOrders(@Param("timeoutHours") Integer timeoutHours);

    /**
     * 搜索订单
     * 
     * @param keyword 关键词
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param status 状态
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> searchOrders(@Param("keyword") String keyword,
                                          @Param("userId") Long userId,
                                          @Param("scaleId") Long scaleId,
                                          @Param("status") Integer status);

    /**
     * 查询热销量表排行
     * 
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectHotScaleRanking(@Param("limit") Integer limit);

    /**
     * 查询用户消费排行
     *
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectUserConsumptionRanking(@Param("limit") Integer limit);

    /**
     * 查询每日订单统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    List<Map<String, Object>> selectDailyOrderStats(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询每月订单统计
     *
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 统计信息
     */
    List<Map<String, Object>> selectMonthlyOrderStats(@Param("startMonth") String startMonth, @Param("endMonth") String endMonth);

    /**
     * 查询过期订单
     *
     * @return 过期订单集合
     */
    List<PsyTAssessmentOrder> selectExpiredOrders();

    /**
     * 取消过期订单
     *
     * @return 取消数量
     */
    int cancelExpiredOrders();
}
