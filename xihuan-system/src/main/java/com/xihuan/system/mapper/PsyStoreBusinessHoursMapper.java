package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.store.PsyStoreBusinessHours;

import java.util.List;

/**
 * 门店营业时间段Mapper接口
 */
public interface PsyStoreBusinessHoursMapper {
    /**
     * 查询门店营业时间段列表
     * 
     * @param psyStoreBusinessHours 门店营业时间段信息
     * @return 门店营业时间段集合
     */
    List<PsyStoreBusinessHours> selectPsyStoreBusinessHoursList(PsyStoreBusinessHours psyStoreBusinessHours);

    /**
     * 查询门店营业时间段信息
     * 
     * @param id 营业时间段主键
     * @return 门店营业时间段信息
     */
    PsyStoreBusinessHours selectPsyStoreBusinessHoursById(Long id);

    /**
     * 根据门店ID查询营业时间段列表
     * 
     * @param storeId 门店ID
     * @return 门店营业时间段集合
     */
    List<PsyStoreBusinessHours> selectPsyStoreBusinessHoursByStoreId(Long storeId);

    /**
     * 新增门店营业时间段
     * 
     * @param psyStoreBusinessHours 门店营业时间段信息
     * @return 结果
     */
    int insertPsyStoreBusinessHours(PsyStoreBusinessHours psyStoreBusinessHours);

    /**
     * 修改门店营业时间段
     * 
     * @param psyStoreBusinessHours 门店营业时间段信息
     * @return 结果
     */
    int updatePsyStoreBusinessHours(PsyStoreBusinessHours psyStoreBusinessHours);

    /**
     * 删除门店营业时间段
     * 
     * @param id 营业时间段主键
     * @return 结果
     */
    int deletePsyStoreBusinessHoursById(Long id);

    /**
     * 批量删除门店营业时间段
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePsyStoreBusinessHoursByIds(Long[] ids);

    /**
     * 根据门店ID删除营业时间段
     * 
     * @param storeId 门店ID
     * @return 结果
     */
    int deletePsyStoreBusinessHoursByStoreId(Long storeId);
} 