package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyHotSearch;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 热门搜索Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyHotSearchMapper {
    
    /**
     * 查询热门搜索
     *
     * @param id 热门搜索主键
     * @return 热门搜索
     */
    PsyHotSearch selectHotSearchById(Long id);

    /**
     * 查询热门搜索列表
     *
     * @param hotSearch 热门搜索
     * @return 热门搜索集合
     */
    List<PsyHotSearch> selectHotSearchList(PsyHotSearch hotSearch);

    /**
     * 新增热门搜索
     *
     * @param hotSearch 热门搜索
     * @return 结果
     */
    int insertHotSearch(PsyHotSearch hotSearch);

    /**
     * 修改热门搜索
     *
     * @param hotSearch 热门搜索
     * @return 结果
     */
    int updateHotSearch(PsyHotSearch hotSearch);

    /**
     * 删除热门搜索
     *
     * @param id 热门搜索主键
     * @return 结果
     */
    int deleteHotSearchById(Long id);

    /**
     * 批量删除热门搜索
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteHotSearchByIds(Long[] ids);
    
    /**
     * 根据关键词和类型查询热门搜索
     *
     * @param keyword 关键词
     * @param searchType 搜索类型
     * @return 热门搜索
     */
    PsyHotSearch selectByKeywordAndType(@Param("keyword") String keyword, @Param("searchType") String searchType);
    
    /**
     * 增加搜索次数
     *
     * @param keyword 关键词
     * @param searchType 搜索类型
     * @return 结果
     */
    int incrementSearchCount(@Param("keyword") String keyword, @Param("searchType") String searchType);
    
    /**
     * 获取热门搜索列表（按热度排序）
     *
     * @param searchType 搜索类型
     * @param limit 限制数量
     * @return 热门搜索列表
     */
    List<PsyHotSearch> selectHotSearchByType(@Param("searchType") String searchType, @Param("limit") Integer limit);
    
    /**
     * 更新热度分数
     *
     * @param id 热门搜索ID
     * @param hotScore 热度分数
     * @return 结果
     */
    int updateHotScore(@Param("id") Long id, @Param("hotScore") java.math.BigDecimal hotScore);
}
