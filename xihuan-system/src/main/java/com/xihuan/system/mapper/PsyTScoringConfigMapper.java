package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTScoringConfig;
import java.util.List;

/**
 * 计分配置Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyTScoringConfigMapper {
    
    /**
     * 查询计分配置
     *
     * @param id 计分配置主键
     * @return 计分配置
     */
    public PsyTScoringConfig selectScoringConfigById(Long id);

    /**
     * 根据量表ID查询计分配置
     *
     * @param scaleId 量表ID
     * @return 计分配置
     */
    public PsyTScoringConfig selectByScaleId(Long scaleId);

    /**
     * 查询计分配置列表
     *
     * @param scoringConfig 计分配置
     * @return 计分配置集合
     */
    public List<PsyTScoringConfig> selectScoringConfigList(PsyTScoringConfig scoringConfig);

    /**
     * 新增计分配置
     *
     * @param scoringConfig 计分配置
     * @return 结果
     */
    public int insertScoringConfig(PsyTScoringConfig scoringConfig);

    /**
     * 修改计分配置
     *
     * @param scoringConfig 计分配置
     * @return 结果
     */
    public int updateScoringConfig(PsyTScoringConfig scoringConfig);

    /**
     * 删除计分配置
     *
     * @param id 计分配置主键
     * @return 结果
     */
    public int deleteScoringConfigById(Long id);

    /**
     * 批量删除计分配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScoringConfigByIds(Long[] ids);

    /**
     * 根据量表ID删除计分配置
     *
     * @param scaleId 量表ID
     * @return 结果
     */
    public int deleteScoringConfigByScaleId(Long scaleId);

    /**
     * 查询所有已配置的量表
     *
     * @return 量表ID列表
     */
    public List<Long> selectConfiguredScaleIds();

    /**
     * 批量插入计分配置
     *
     * @param configs 计分配置列表
     * @return 结果
     */
    public int batchInsertScoringConfig(List<PsyTScoringConfig> configs);
}
