package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTimeAppointmentSlot;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 预约-时间槽关系Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyTimeAppointmentSlotMapper {
    
    /**
     * 根据预约ID查询关联的时间槽
     * 
     * @param appointmentId 预约ID
     * @return 关联记录集合
     */
    List<PsyTimeAppointmentSlot> selectSlotsByAppointmentId(@Param("appointmentId") String appointmentId);
    
    /**
     * 根据时间槽ID查询关联的预约
     * 
     * @param slotId 时间槽ID
     * @return 关联记录
     */
    PsyTimeAppointmentSlot selectAppointmentBySlotId(@Param("slotId") Long slotId);
    
    /**
     * 新增预约-时间槽关联
     * 
     * @param appointmentSlot 关联记录
     * @return 结果
     */
    int insertAppointmentSlot(PsyTimeAppointmentSlot appointmentSlot);
    
    /**
     * 批量新增预约-时间槽关联
     * 
     * @param appointmentId 预约ID
     * @param slotIds 时间槽ID列表
     * @return 结果
     */
    int batchInsertAppointmentSlots(
        @Param("appointmentId") String appointmentId,
        @Param("slotIds") List<Long> slotIds
    );
    
    /**
     * 删除预约-时间槽关联
     * 
     * @param appointmentId 预约ID
     * @param slotId 时间槽ID
     * @return 结果
     */
    int deleteAppointmentSlot(
        @Param("appointmentId") String appointmentId,
        @Param("slotId") Long slotId
    );
    
    /**
     * 根据预约ID删除所有关联
     * 
     * @param appointmentId 预约ID
     * @return 结果
     */
    int deleteSlotsByAppointmentId(@Param("appointmentId") String appointmentId);
    
    /**
     * 根据时间槽ID删除关联
     * 
     * @param slotId 时间槽ID
     * @return 结果
     */
    int deleteAppointmentBySlotId(@Param("slotId") Long slotId);
}
