package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.consultant.PsyDictExpertise;

import java.util.List;

/**
 * 咨询领域Mapper接口
 */
public interface PsyDictExpertiseMapper {
    /**
     * 查询咨询领域
     */
    public PsyDictExpertise selectPsyDictExpertiseById(Long id);

    /**
     * 查询咨询领域列表
     */
    public List<PsyDictExpertise> selectPsyDictExpertiseList(PsyDictExpertise psyDictExpertise);

    /**
     * 新增咨询领域
     */
    public int insertPsyDictExpertise(PsyDictExpertise psyDictExpertise);

    /**
     * 修改咨询领域
     */
    public int updatePsyDictExpertise(PsyDictExpertise psyDictExpertise);

    /**
     * 删除咨询领域
     */
    public int deletePsyDictExpertiseById(Long id);

    /**
     * 批量删除咨询领域
     */
    public int deletePsyDictExpertiseByIds(Long[] ids);

    /**
     * 查询所有父级领域
     */
    public List<PsyDictExpertise> selectParentExpertises();

    /**
     * 根据父ID查询子领域
     */
    public List<PsyDictExpertise> selectChildrenByParentId(Long parentId);

    /**
     * 查询所有子节点（parent_id不为空的记录）
     */
    public List<PsyDictExpertise> selectAllChildExpertises();
} 