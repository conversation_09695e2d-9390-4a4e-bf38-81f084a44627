package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAppointment;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface PsyAppointmentMapper {
    /**
     * 插入预约时段
     */
    int insert(PsyAppointment appointment);

    /**
     * 删除指定日期之前的时段
     */
    int deleteBeforeDate(Date date);

    /**
     * 删除指定日期之后的公共时段
     */
    int deletePublicSlotsAfterDate(Date date);

    /**
     * 删除指定咨询师在指定日期之后的时段
     */
    int deleteCounselorSlotsAfterDate(@Param("counselorId") Long counselorId, @Param("date") Date date);

    /**
     * 检查时段是否存在
     */
    boolean existsSlot(@Param("counselorId") Long counselorId, 
                      @Param("date") String date, 
                      @Param("timeSlot") String timeSlot);

    /**
     * 检查某天是否已生成时段
     */
    boolean existsDaySlots(@Param("counselorId") Long counselorId, 
                          @Param("date") String date);

    /**
     * 更新时段状态
     */
    int updateSlotStatus(@Param("counselorId") Long counselorId,
                        @Param("date") String date,
                        @Param("timeSlot") String timeSlot,
                        @Param("status") String status);

    /**
     * 查询咨询师的时段
     */
    List<PsyAppointment> selectByCounselorAndDate(@Param("counselorId") Long counselorId,
                                                 @Param("startDate") Date startDate,
                                                 @Param("endDate") Date endDate);

    /**
     * 查询公共时段
     */
    List<PsyAppointment> selectPublicSlots(@Param("startDate") Date startDate,
                                          @Param("endDate") Date endDate);
}