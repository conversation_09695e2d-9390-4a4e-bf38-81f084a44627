package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTEnterpriseDepartment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 企业部门Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTEnterpriseDepartmentMapper {
    
    /**
     * 查询企业部门列表
     * 
     * @param department 企业部门信息
     * @return 企业部门集合
     */
    List<PsyTEnterpriseDepartment> selectDepartmentList(PsyTEnterpriseDepartment department);

    /**
     * 根据ID查询企业部门
     * 
     * @param id 企业部门ID
     * @return 企业部门信息
     */
    PsyTEnterpriseDepartment selectDepartmentById(Long id);

    /**
     * 查询部门详情（包含子部门、员工等信息）
     * 
     * @param id 部门ID
     * @return 部门详情
     */
    PsyTEnterpriseDepartment selectDepartmentWithDetails(Long id);

    /**
     * 根据企业ID查询部门列表
     * 
     * @param enterpriseId 企业ID
     * @return 部门集合
     */
    List<PsyTEnterpriseDepartment> selectDepartmentsByEnterpriseId(Long enterpriseId);

    /**
     * 根据父部门ID查询子部门列表
     * 
     * @param parentId 父部门ID
     * @return 部门集合
     */
    List<PsyTEnterpriseDepartment> selectDepartmentsByParentId(Long parentId);

    /**
     * 查询企业部门树
     * 
     * @param enterpriseId 企业ID
     * @return 部门树
     */
    List<PsyTEnterpriseDepartment> selectDepartmentTree(Long enterpriseId);

    /**
     * 新增企业部门
     * 
     * @param department 企业部门信息
     * @return 结果
     */
    int insertDepartment(PsyTEnterpriseDepartment department);

    /**
     * 修改企业部门
     * 
     * @param department 企业部门信息
     * @return 结果
     */
    int updateDepartment(PsyTEnterpriseDepartment department);

    /**
     * 删除企业部门
     * 
     * @param id 企业部门ID
     * @return 结果
     */
    int deleteDepartmentById(Long id);

    /**
     * 批量删除企业部门
     * 
     * @param ids 需要删除的企业部门ID
     * @return 结果
     */
    int deleteDepartmentByIds(Long[] ids);

    /**
     * 根据企业ID删除部门
     * 
     * @param enterpriseId 企业ID
     * @return 结果
     */
    int deleteDepartmentsByEnterpriseId(Long enterpriseId);

    /**
     * 检查部门编码唯一性
     * 
     * @param enterpriseId 企业ID
     * @param departmentCode 部门编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkDepartmentCodeUnique(@Param("enterpriseId") Long enterpriseId, 
                                 @Param("departmentCode") String departmentCode, 
                                 @Param("excludeId") Long excludeId);

    /**
     * 检查部门是否有子部门
     * 
     * @param id 部门ID
     * @return 数量
     */
    int checkDepartmentHasChildren(Long id);

    /**
     * 统计企业部门数量
     * 
     * @param enterpriseId 企业ID
     * @return 数量
     */
    int countDepartmentsByEnterpriseId(Long enterpriseId);

    /**
     * 统计部门员工数量
     * 
     * @param departmentId 部门ID
     * @return 数量
     */
    int countEmployeesByDepartmentId(Long departmentId);

    /**
     * 查询部门层级统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectDepartmentLevelStats(Long enterpriseId);

    /**
     * 查询部门规模统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectDepartmentSizeStats(Long enterpriseId);

    /**
     * 更新部门员工数量
     * 
     * @param id 部门ID
     * @param employeeCount 员工数量
     * @return 结果
     */
    int updateDepartmentEmployeeCount(@Param("id") Long id, @Param("employeeCount") Integer employeeCount);

    /**
     * 批量更新部门状态
     * 
     * @param ids 部门ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateDepartmentStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 搜索部门
     * 
     * @param keyword 关键词
     * @param enterpriseId 企业ID
     * @param departmentLevel 部门层级
     * @return 部门集合
     */
    List<PsyTEnterpriseDepartment> searchDepartments(@Param("keyword") String keyword,
                                                    @Param("enterpriseId") Long enterpriseId,
                                                    @Param("departmentLevel") Integer departmentLevel);

    /**
     * 查询部门路径
     * 
     * @param id 部门ID
     * @return 部门路径
     */
    String selectDepartmentPath(Long id);

    /**
     * 查询部门及其所有子部门ID
     * 
     * @param id 部门ID
     * @return 部门ID集合
     */
    List<Long> selectDepartmentAndChildrenIds(Long id);
}
