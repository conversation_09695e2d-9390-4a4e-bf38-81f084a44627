package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTEnterpriseAssessmentParticipant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 企业测评参与记录Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTEnterpriseAssessmentParticipantMapper {
    
    /**
     * 查询企业测评参与记录列表
     * 
     * @param participant 企业测评参与记录信息
     * @return 企业测评参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectParticipantList(PsyTEnterpriseAssessmentParticipant participant);

    /**
     * 根据ID查询企业测评参与记录
     * 
     * @param id 企业测评参与记录ID
     * @return 企业测评参与记录信息
     */
    PsyTEnterpriseAssessmentParticipant selectParticipantById(Long id);

    /**
     * 查询参与记录详情（包含计划、企业、用户等信息）
     * 
     * @param id 参与记录ID
     * @return 参与记录详情
     */
    PsyTEnterpriseAssessmentParticipant selectParticipantWithDetails(Long id);

    /**
     * 根据计划ID查询参与记录列表
     * 
     * @param planId 计划ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectParticipantsByPlanId(Long planId);

    /**
     * 根据企业ID查询参与记录列表
     * 
     * @param enterpriseId 企业ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectParticipantsByEnterpriseId(Long enterpriseId);

    /**
     * 根据员工ID查询参与记录列表
     * 
     * @param employeeId 员工ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectParticipantsByEmployeeId(Long employeeId);

    /**
     * 新增企业测评参与记录
     * 
     * @param participant 企业测评参与记录信息
     * @return 结果
     */
    int insertParticipant(PsyTEnterpriseAssessmentParticipant participant);

    /**
     * 批量新增企业测评参与记录
     * 
     * @param participants 参与记录列表
     * @return 结果
     */
    int batchInsertParticipants(List<PsyTEnterpriseAssessmentParticipant> participants);

    /**
     * 修改企业测评参与记录
     * 
     * @param participant 企业测评参与记录信息
     * @return 结果
     */
    int updateParticipant(PsyTEnterpriseAssessmentParticipant participant);

    /**
     * 删除企业测评参与记录
     * 
     * @param id 企业测评参与记录ID
     * @return 结果
     */
    int deleteParticipantById(Long id);

    /**
     * 批量删除企业测评参与记录
     * 
     * @param ids 需要删除的企业测评参与记录ID
     * @return 结果
     */
    int deleteParticipantByIds(Long[] ids);

    /**
     * 根据计划ID删除参与记录
     * 
     * @param planId 计划ID
     * @return 结果
     */
    int deleteParticipantsByPlanId(Long planId);

    /**
     * 查询计划未开始参与者
     * 
     * @param planId 计划ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectNotStartedParticipants(Long planId);

    /**
     * 查询计划进行中参与者
     * 
     * @param planId 计划ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectInProgressParticipants(Long planId);

    /**
     * 查询计划已完成参与者
     * 
     * @param planId 计划ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectCompletedParticipants(Long planId);

    /**
     * 查询计划已放弃参与者
     * 
     * @param planId 计划ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectAbandonedParticipants(Long planId);

    /**
     * 更新参与状态
     * 
     * @param id 参与记录ID
     * @param status 参与状态
     * @return 结果
     */
    int updateParticipationStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新提醒次数
     * 
     * @param id 参与记录ID
     * @return 结果
     */
    int incrementReminderCount(Long id);

    /**
     * 统计计划参与人数
     * 
     * @param planId 计划ID
     * @return 数量
     */
    int countParticipantsByPlanId(Long planId);

    /**
     * 统计计划完成人数
     * 
     * @param planId 计划ID
     * @return 数量
     */
    int countCompletedParticipantsByPlanId(Long planId);

    /**
     * 统计企业参与人数
     * 
     * @param enterpriseId 企业ID
     * @return 数量
     */
    int countParticipantsByEnterpriseId(Long enterpriseId);

    /**
     * 查询参与统计信息
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    Map<String, Object> selectParticipationStats(Long planId);

    /**
     * 查询企业参与统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    Map<String, Object> selectEnterpriseParticipationStats(Long enterpriseId);

    /**
     * 查询员工参与历史
     * 
     * @param employeeId 员工ID
     * @return 参与历史
     */
    List<PsyTEnterpriseAssessmentParticipant> selectEmployeeParticipationHistory(Long employeeId);

    /**
     * 查询需要提醒的参与者
     * 
     * @param planId 计划ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectParticipantsNeedReminder(Long planId);

    /**
     * 查询超时未完成的参与者
     * 
     * @param planId 计划ID
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> selectTimeoutParticipants(Long planId);

    /**
     * 搜索参与记录
     * 
     * @param keyword 关键词
     * @param planId 计划ID
     * @param enterpriseId 企业ID
     * @param status 参与状态
     * @return 参与记录集合
     */
    List<PsyTEnterpriseAssessmentParticipant> searchParticipants(@Param("keyword") String keyword,
                                                               @Param("planId") Long planId,
                                                               @Param("enterpriseId") Long enterpriseId,
                                                               @Param("status") Integer status);

    /**
     * 批量更新参与状态
     * 
     * @param ids 参与记录ID数组
     * @param status 参与状态
     * @return 结果
     */
    int batchUpdateParticipationStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 查询参与度排行
     * 
     * @param enterpriseId 企业ID
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectParticipationRanking(@Param("enterpriseId") Long enterpriseId, 
                                                         @Param("limit") Integer limit);

    /**
     * 查询部门参与统计
     * 
     * @param planId 计划ID
     * @return 部门统计
     */
    List<Map<String, Object>> selectDepartmentParticipationStats(Long planId);

    /**
     * 查询参与趋势统计
     * 
     * @param planId 计划ID
     * @param days 天数
     * @return 趋势统计
     */
    List<Map<String, Object>> selectParticipationTrendStats(@Param("planId") Long planId, 
                                                            @Param("days") Integer days);

    /**
     * 自动放弃超时参与者
     *
     * @param timeoutHours 超时小时数
     * @return 放弃数量
     */
    int autoAbandonTimeoutParticipants(@Param("timeoutHours") Integer timeoutHours);

    /**
     * 更新参与者状态
     */
    int updateParticipantStatus(@Param("participantId") Long participantId, @Param("status") Integer status);

    /**
     * 批量更新参与者状态
     */
    int batchUpdateParticipantStatus(@Param("participantIds") Long[] participantIds, @Param("status") Integer status);

    /**
     * 检查员工是否已参与计划
     *
     * @param planId 计划ID
     * @param employeeId 员工ID
     * @return 参与数量
     */
    int checkEmployeeParticipated(@Param("planId") Long planId, @Param("employeeId") Long employeeId);

    /**
     * 查询员工当前参与的计划
     *
     * @param employeeId 员工ID
     * @return 参与记录列表
     */
    List<PsyTEnterpriseAssessmentParticipant> selectEmployeeCurrentParticipations(Long employeeId);

    /**
     * 自动更新超时参与者状态
     *
     * @param planId 计划ID
     * @return 更新数量
     */
    int autoUpdateTimeoutParticipants(Long planId);

    /**
     * 查询参与者完成率分布
     *
     * @param enterpriseId 企业ID
     * @return 分布数据
     */
    List<Map<String, Object>> selectCompletionRateDistribution(Long enterpriseId);

    /**
     * 查询参与者测评时长统计
     *
     * @param planId 计划ID
     * @return 统计数据
     */
    Map<String, Object> selectDurationStats(Long planId);

    /**
     * 导出参与者数据
     *
     * @param planId 计划ID
     * @return 导出数据
     */
    List<Map<String, Object>> exportParticipantData(Long planId);

    /**
     * 查询参与者结果分布
     *
     * @param planId 计划ID
     * @return 结果分布
     */
    List<Map<String, Object>> selectResultDistribution(Long planId);

}
