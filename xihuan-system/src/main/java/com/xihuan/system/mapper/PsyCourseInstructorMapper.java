package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyCourseInstructor;

import java.util.List;

/**
 * 讲师信息表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyCourseInstructorMapper {
    
    /**
     * 查询讲师列表
     * 
     * @param instructor 讲师信息
     * @return 讲师集合
     */
    List<PsyCourseInstructor> selectInstructorList(PsyCourseInstructor instructor);

    /**
     * 根据ID查询讲师
     * 
     * @param id 讲师ID
     * @return 讲师信息
     */
    PsyCourseInstructor selectInstructorById(Long id);

    /**
     * 查询讲师详情（包含头像图片）
     * 
     * @param id 讲师ID
     * @return 讲师详情
     */
    PsyCourseInstructor selectInstructorWithDetails(Long id);

    /**
     * 新增讲师
     * 
     * @param instructor 讲师信息
     * @return 结果
     */
    int insertInstructor(PsyCourseInstructor instructor);

    /**
     * 修改讲师
     * 
     * @param instructor 讲师信息
     * @return 结果
     */
    int updateInstructor(PsyCourseInstructor instructor);

    /**
     * 删除讲师
     * 
     * @param id 讲师ID
     * @return 结果
     */
    int deleteInstructorById(Long id);

    /**
     * 批量删除讲师
     * 
     * @param ids 需要删除的讲师ID
     * @return 结果
     */
    int deleteInstructorByIds(Long[] ids);

    /**
     * 查询所有讲师简单信息（仅id和name，用于下拉框）
     * 
     * @return 讲师简单信息集合
     */
    List<PsyCourseInstructor> selectAllSimpleList();
}
