package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyFavoriteStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 收藏统计数据访问层
 */
public interface PsyFavoriteStatisticsMapper {
    
    /**
     * 查询收藏统计
     */
    PsyFavoriteStatistics selectStatsByTarget(@Param("targetType") Integer targetType, @Param("targetId") Long targetId);
    
    /**
     * 新增收藏统计
     */
    int insertStats(PsyFavoriteStatistics stats);
    
    /**
     * 更新收藏统计
     */
    int updateStats(PsyFavoriteStatistics stats);
    
    /**
     * 增加收藏数量
     */
    int incrementFavoriteCount(@Param("targetType") Integer targetType, @Param("targetId") Long targetId);
    
    /**
     * 减少收藏数量
     */
    int decrementFavoriteCount(@Param("targetType") Integer targetType, @Param("targetId") Long targetId);
    
    /**
     * 查询热门收藏排行
     */
    List<Map<String, Object>> selectHotFavorites(@Param("targetType") Integer targetType, @Param("limit") Integer limit);
    
    /**
     * 查询收藏趋势统计
     */
    List<Map<String, Object>> selectFavoriteTrend(@Param("targetType") Integer targetType, @Param("days") Integer days);
    
    /**
     * 重置每日统计
     */
    int resetDailyStats();
    
    /**
     * 重置每周统计
     */
    int resetWeeklyStats();
    
    /**
     * 重置每月统计
     */
    int resetMonthlyStats();
}
