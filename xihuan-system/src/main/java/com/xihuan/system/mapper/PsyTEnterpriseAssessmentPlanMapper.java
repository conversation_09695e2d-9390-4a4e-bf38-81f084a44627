package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTEnterpriseAssessmentPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 企业测评计划Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTEnterpriseAssessmentPlanMapper {
    
    /**
     * 查询企业测评计划列表
     * 
     * @param plan 企业测评计划信息
     * @return 企业测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectPlanList(PsyTEnterpriseAssessmentPlan plan);

    /**
     * 根据ID查询企业测评计划
     * 
     * @param id 企业测评计划ID
     * @return 企业测评计划信息
     */
    PsyTEnterpriseAssessmentPlan selectPlanById(Long id);

    /**
     * 查询计划详情（包含参与记录、企业、量表等信息）
     * 
     * @param id 计划ID
     * @return 计划详情
     */
    PsyTEnterpriseAssessmentPlan selectPlanWithDetails(Long id);

    /**
     * 根据企业ID查询测评计划列表
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectPlansByEnterpriseId(Long enterpriseId);

    /**
     * 根据量表ID查询测评计划列表
     * 
     * @param scaleId 量表ID
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectPlansByScaleId(Long scaleId);

    /**
     * 新增企业测评计划
     * 
     * @param plan 企业测评计划信息
     * @return 结果
     */
    int insertPlan(PsyTEnterpriseAssessmentPlan plan);

    /**
     * 修改企业测评计划
     * 
     * @param plan 企业测评计划信息
     * @return 结果
     */
    int updatePlan(PsyTEnterpriseAssessmentPlan plan);

    /**
     * 删除企业测评计划
     * 
     * @param id 企业测评计划ID
     * @return 结果
     */
    int deletePlanById(Long id);

    /**
     * 批量删除企业测评计划
     * 
     * @param ids 需要删除的企业测评计划ID
     * @return 结果
     */
    int deletePlanByIds(Long[] ids);

    /**
     * 查询待开始的测评计划
     * 
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectPendingPlans();

    /**
     * 查询进行中的测评计划
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectInProgressPlans(Long enterpriseId);

    /**
     * 查询已结束的测评计划
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectEndedPlans(Long enterpriseId);

    /**
     * 查询即将结束的测评计划
     * 
     * @param days 天数
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectEndingPlans(@Param("days") Integer days);

    /**
     * 更新计划状态
     * 
     * @param id 计划ID
     * @param status 状态
     * @return 结果
     */
    int updatePlanStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新计划完成率
     * 
     * @param id 计划ID
     * @return 结果
     */
    int updatePlanCompletionRate(Long id);

    /**
     * 统计企业测评计划数量
     * 
     * @param enterpriseId 企业ID
     * @return 数量
     */
    int countPlansByEnterpriseId(Long enterpriseId);

    /**
     * 统计量表测评计划数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countPlansByScaleId(Long scaleId);

    /**
     * 查询计划统计信息
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    Map<String, Object> selectPlanStats(Long planId);

    /**
     * 查询企业计划统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    Map<String, Object> selectEnterprisePlanStats(Long enterpriseId);

    /**
     * 查询计划类型统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectPlanTypeStats(Long enterpriseId);

    /**
     * 查询计划完成率统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectPlanCompletionStats(Long enterpriseId);

    /**
     * 查询计划趋势统计
     * 
     * @param enterpriseId 企业ID
     * @param days 天数
     * @return 趋势统计
     */
    List<Map<String, Object>> selectPlanTrendStats(@Param("enterpriseId") Long enterpriseId, 
                                                   @Param("days") Integer days);

    /**
     * 搜索测评计划
     * 
     * @param keyword 关键词
     * @param enterpriseId 企业ID
     * @param planType 计划类型
     * @param status 状态
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> searchPlans(@Param("keyword") String keyword,
                                                  @Param("enterpriseId") Long enterpriseId,
                                                  @Param("planType") Integer planType,
                                                  @Param("status") Integer status);

    /**
     * 批量更新计划状态
     * 
     * @param ids 计划ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdatePlanStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 自动开始到期的计划
     * 
     * @return 开始数量
     */
    int autoStartDuePlans();

    /**
     * 自动结束到期的计划
     * 
     * @return 结束数量
     */
    int autoEndExpiredPlans();

    /**
     * 查询计划参与度分析
     * 
     * @param planId 计划ID
     * @return 参与度分析
     */
    Map<String, Object> selectPlanParticipationAnalysis(Long planId);

    /**
     * 查询计划效果评估
     *
     * @param planId 计划ID
     * @return 效果评估
     */
    Map<String, Object> selectPlanEffectivenessAnalysis(Long planId);

    /**
     * 更新计划统计信息
     *
     * @param planId 计划ID
     * @return 结果
     */
    int updatePlanStatistics(Long planId);

    /**
     * 查询即将开始的计划
     *
     * @param hours 小时数
     * @return 计划列表
     */
    List<PsyTEnterpriseAssessmentPlan> selectPlansStartingSoon(Integer hours);

    /**
     * 查询即将结束的计划
     *
     * @param hours 小时数
     * @return 计划列表
     */
    List<PsyTEnterpriseAssessmentPlan> selectPlansEndingSoon(Integer hours);

    /**
     * 查询超时未完成的计划
     *
     * @return 计划列表
     */
    List<PsyTEnterpriseAssessmentPlan> selectOverduePlans();

    /**
     * 查询计划完成率趋势
     *
     * @param enterpriseId 企业ID
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> selectPlanCompletionTrend(@Param("enterpriseId") Long enterpriseId, @Param("days") Integer days);

    /**
     * 查询热门量表统计
     *
     * @param enterpriseId 企业ID
     * @return 统计数据
     */
    List<Map<String, Object>> selectPopularScaleStats(Long enterpriseId);

    /**
     * 查询部门参与统计
     *
     * @param enterpriseId 企业ID
     * @return 统计数据
     */
    List<Map<String, Object>> selectDepartmentParticipationStats(Long enterpriseId);
}
