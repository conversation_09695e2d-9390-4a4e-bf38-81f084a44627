package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTimeScheduleTemplate;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.List;

/**
 * 排班模板Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyTimeScheduleTemplateMapper {
    
    /**
     * 查询排班模板列表
     * 
     * @param template 排班模板
     * @return 排班模板集合
     */
    List<PsyTimeScheduleTemplate> selectTemplateList(PsyTimeScheduleTemplate template);
    
    /**
     * 根据ID查询排班模板
     * 
     * @param id 排班模板主键
     * @return 排班模板
     */
    PsyTimeScheduleTemplate selectTemplateById(Long id);
    
    /**
     * 根据咨询师ID查询排班模板
     * 
     * @param counselorId 咨询师ID
     * @return 排班模板集合
     */
    List<PsyTimeScheduleTemplate> selectTemplatesByCounselorId(@Param("counselorId") Long counselorId);
    
    /**
     * 查询咨询师的默认模板
     * 
     * @param counselorId 咨询师ID
     * @return 默认排班模板
     */
    PsyTimeScheduleTemplate selectDefaultTemplateByCounselorId(@Param("counselorId") Long counselorId);
    
    /**
     * 查询在指定日期有效的模板
     * 
     * @param counselorId 咨询师ID
     * @param date 日期
     * @return 有效的排班模板
     */
    PsyTimeScheduleTemplate selectEffectiveTemplate(
        @Param("counselorId") Long counselorId,
        @Param("date") LocalDate date
    );
    
    /**
     * 新增排班模板
     * 
     * @param template 排班模板
     * @return 结果
     */
    int insertTemplate(PsyTimeScheduleTemplate template);
    
    /**
     * 修改排班模板
     * 
     * @param template 排班模板
     * @return 结果
     */
    int updateTemplate(PsyTimeScheduleTemplate template);
    
    /**
     * 设置默认模板
     *
     * @param templateId 模板ID
     * @return 结果
     */
    int setDefaultTemplate(@Param("templateId") Long templateId);

    /**
     * 清除咨询师的所有默认模板标记
     *
     * @param counselorId 咨询师ID
     * @return 结果
     */
    int clearDefaultTemplate(@Param("counselorId") Long counselorId);

    /**
     * 删除排班模板
     *
     * @param id 排班模板主键
     * @return 结果
     */
    int deleteTemplateById(Long id);

    /**
     * 批量删除排班模板
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTemplateByIds(Long[] ids);

    /**
     * 检查模板名称是否唯一
     *
     * @param name 模板名称
     * @param counselorId 咨询师ID
     * @return 模板信息
     */
    PsyTimeScheduleTemplate checkTemplateNameUnique(
        @Param("name") String name,
        @Param("counselorId") Long counselorId
    );
}
