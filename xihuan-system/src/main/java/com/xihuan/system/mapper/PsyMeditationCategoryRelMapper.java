package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyMeditationCategoryRel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 冥想分类关系表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyMeditationCategoryRelMapper {
    
    /**
     * 查询冥想分类关系列表
     * 
     * @param rel 冥想分类关系信息
     * @return 冥想分类关系集合
     */
    List<PsyMeditationCategoryRel> selectRelList(PsyMeditationCategoryRel rel);

    /**
     * 根据冥想ID查询分类ID列表
     * 
     * @param meditationId 冥想ID
     * @return 分类ID集合
     */
    List<Long> selectCategoryIdsByMeditationId(Long meditationId);

    /**
     * 根据分类ID查询冥想ID列表
     * 
     * @param categoryId 分类ID
     * @return 冥想ID集合
     */
    List<Long> selectMeditationIdsByCategoryId(Long categoryId);

    /**
     * 新增冥想分类关系
     * 
     * @param rel 冥想分类关系信息
     * @return 结果
     */
    int insertRel(PsyMeditationCategoryRel rel);

    /**
     * 批量新增冥想分类关系
     * 
     * @param meditationId 冥想ID
     * @param categoryIds 分类ID列表
     * @return 结果
     */
    int batchInsertRel(@Param("meditationId") Long meditationId, @Param("categoryIds") List<Long> categoryIds);

    /**
     * 删除冥想分类关系
     * 
     * @param meditationId 冥想ID
     * @param categoryId 分类ID
     * @return 结果
     */
    int deleteRel(@Param("meditationId") Long meditationId, @Param("categoryId") Long categoryId);

    /**
     * 根据冥想ID删除冥想分类关系
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    int deleteRelByMeditationId(Long meditationId);

    /**
     * 批量根据冥想ID删除冥想分类关系
     * 
     * @param meditationIds 冥想ID数组
     * @return 结果
     */
    int deleteRelByMeditationIds(Long[] meditationIds);

    /**
     * 根据分类ID删除冥想分类关系
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    int deleteRelByCategoryId(Long categoryId);

    /**
     * 批量根据分类ID删除冥想分类关系
     * 
     * @param categoryIds 分类ID数组
     * @return 结果
     */
    int deleteRelByCategoryIds(Long[] categoryIds);

    /**
     * 检查冥想分类关系是否存在
     * 
     * @param meditationId 冥想ID
     * @param categoryId 分类ID
     * @return 关系数量
     */
    int checkRelExists(@Param("meditationId") Long meditationId, @Param("categoryId") Long categoryId);
}
