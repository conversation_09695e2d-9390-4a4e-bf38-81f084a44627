package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyCourseCategoryRel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程分类关系表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyCourseCategoryRelMapper {
    
    /**
     * 查询课程分类关系列表
     * 
     * @param rel 课程分类关系信息
     * @return 课程分类关系集合
     */
    List<PsyCourseCategoryRel> selectRelList(PsyCourseCategoryRel rel);

    /**
     * 根据课程ID查询分类ID列表
     * 
     * @param courseId 课程ID
     * @return 分类ID集合
     */
    List<Long> selectCategoryIdsByCourseId(Long courseId);

    /**
     * 根据分类ID查询课程ID列表
     * 
     * @param categoryId 分类ID
     * @return 课程ID集合
     */
    List<Long> selectCourseIdsByCategoryId(Long categoryId);

    /**
     * 新增课程分类关系
     * 
     * @param rel 课程分类关系信息
     * @return 结果
     */
    int insertRel(PsyCourseCategoryRel rel);

    /**
     * 批量新增课程分类关系
     * 
     * @param courseId 课程ID
     * @param categoryIds 分类ID列表
     * @return 结果
     */
    int batchInsertRel(@Param("courseId") Long courseId, @Param("categoryIds") List<Long> categoryIds);

    /**
     * 删除课程分类关系
     * 
     * @param courseId 课程ID
     * @param categoryId 分类ID
     * @return 结果
     */
    int deleteRel(@Param("courseId") Long courseId, @Param("categoryId") Long categoryId);

    /**
     * 根据课程ID删除课程分类关系
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    int deleteRelByCourseId(Long courseId);

    /**
     * 批量根据课程ID删除课程分类关系
     * 
     * @param courseIds 课程ID数组
     * @return 结果
     */
    int deleteRelByCourseIds(Long[] courseIds);

    /**
     * 根据分类ID删除课程分类关系
     * 
     * @param categoryId 分类ID
     * @return 结果
     */
    int deleteRelByCategoryId(Long categoryId);

    /**
     * 批量根据分类ID删除课程分类关系
     * 
     * @param categoryIds 分类ID数组
     * @return 结果
     */
    int deleteRelByCategoryIds(Long[] categoryIds);

    /**
     * 检查课程分类关系是否存在
     * 
     * @param courseId 课程ID
     * @param categoryId 分类ID
     * @return 关系数量
     */
    int checkRelExists(@Param("courseId") Long courseId, @Param("categoryId") Long categoryId);
}
