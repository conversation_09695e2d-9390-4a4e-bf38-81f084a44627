package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyMeditation;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 冥想主表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyMeditationMapper {
    
    /**
     * 查询冥想列表
     * 
     * @param meditation 冥想信息
     * @return 冥想集合
     */
    List<PsyMeditation> selectMeditationList(PsyMeditation meditation);

    /**
     * 查询冥想详情（包含分类等信息）
     * 
     * @param id 冥想ID
     * @return 冥想详情
     */
    PsyMeditation selectMeditationWithDetails(Long id);

    /**
     * 根据ID查询冥想
     * 
     * @param id 冥想ID
     * @return 冥想信息
     */
    PsyMeditation selectMeditationById(Long id);

    /**
     * 新增冥想
     * 
     * @param meditation 冥想信息
     * @return 结果
     */
    int insertMeditation(PsyMeditation meditation);

    /**
     * 修改冥想
     * 
     * @param meditation 冥想信息
     * @return 结果
     */
    int updateMeditation(PsyMeditation meditation);

    /**
     * 删除冥想
     * 
     * @param id 冥想ID
     * @return 结果
     */
    int deleteMeditationById(Long id);

    /**
     * 批量删除冥想
     * 
     * @param ids 需要删除的冥想ID
     * @return 结果
     */
    int deleteMeditationByIds(Long[] ids);

    /**
     * 根据分类ID查询冥想列表
     * 
     * @param categoryId 分类ID
     * @return 冥想集合
     */
    List<PsyMeditation> selectMeditationsByCategoryId(Long categoryId);

    /**
     * 批量插入冥想分类关联
     * 
     * @param meditationId 冥想ID
     * @param categoryIds 分类ID列表
     * @return 影响行数
     */
    int batchInsertMeditationCategories(@Param("meditationId") Long meditationId, @Param("categoryIds") List<Long> categoryIds);

    /**
     * 删除冥想的分类关联
     * 
     * @param meditationId 冥想ID
     * @return 影响行数
     */
    int deleteMeditationCategories(Long meditationId);

    /**
     * 批量删除冥想的分类关联
     * 
     * @param meditationIds 冥想ID数组
     * @return 影响行数
     */
    int deleteMeditationCategoriesByIds(Long[] meditationIds);

    /**
     * 更新冥想评分信息
     * 
     * @param meditationId 冥想ID
     * @param ratingAvg 平均评分
     * @param ratingCount 评分人数
     * @return 影响行数
     */
    int updateMeditationRating(@Param("meditationId") Long meditationId, 
                              @Param("ratingAvg") BigDecimal ratingAvg,
                              @Param("ratingCount") Integer ratingCount);

    /**
     * 增加冥想播放次数
     * 
     * @param meditationId 冥想ID
     * @return 影响行数
     */
    int incrementPlayCount(Long meditationId);
}
