package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTInterpretation;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 测评结果解释Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyTInterpretationMapper {
    
    /**
     * 查询测评结果解释
     * 
     * @param id 测评结果解释主键
     * @return 测评结果解释
     */
    public PsyTInterpretation selectInterpretationById(Long id);

    /**
     * 查询测评结果解释列表
     * 
     * @param interpretation 测评结果解释
     * @return 测评结果解释集合
     */
    public List<PsyTInterpretation> selectInterpretationList(PsyTInterpretation interpretation);

    /**
     * 新增测评结果解释
     * 
     * @param interpretation 测评结果解释
     * @return 结果
     */
    public int insertInterpretation(PsyTInterpretation interpretation);

    /**
     * 修改测评结果解释
     * 
     * @param interpretation 测评结果解释
     * @return 结果
     */
    public int updateInterpretation(PsyTInterpretation interpretation);

    /**
     * 删除测评结果解释
     * 
     * @param id 测评结果解释主键
     * @return 结果
     */
    public int deleteInterpretationById(Long id);

    /**
     * 批量删除测评结果解释
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInterpretationByIds(Long[] ids);

    /**
     * 根据量表ID查询解释列表
     * 
     * @param scaleId 量表ID
     * @return 解释列表
     */
    public List<PsyTInterpretation> selectInterpretationsByScaleId(Long scaleId);

    /**
     * 根据量表ID和维度查询解释列表
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称
     * @return 解释列表
     */
    public List<PsyTInterpretation> selectInterpretationsByScaleAndDimension(@Param("scaleId") Long scaleId, @Param("dimension") String dimension);

    /**
     * 根据分数查询匹配的解释
     * 
     * @param scaleId 量表ID
     * @param score 分数
     * @param dimension 维度名称（可为空，表示总分）
     * @return 匹配的解释
     */
    public PsyTInterpretation selectInterpretationByScore(@Param("scaleId") Long scaleId, @Param("score") BigDecimal score, @Param("dimension") String dimension);

    /**
     * 根据量表ID查询总分解释列表
     * 
     * @param scaleId 量表ID
     * @return 总分解释列表
     */
    public List<PsyTInterpretation> selectTotalScoreInterpretations(Long scaleId);

    /**
     * 根据量表ID查询维度解释列表
     * 
     * @param scaleId 量表ID
     * @return 维度解释列表
     */
    public List<PsyTInterpretation> selectDimensionInterpretations(Long scaleId);

    /**
     * 查询量表的所有维度
     * 
     * @param scaleId 量表ID
     * @return 维度列表
     */
    public List<String> selectDimensionsByScaleId(Long scaleId);

    /**
     * 检查分数范围是否重叠
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @param excludeId 排除的解释ID（用于更新时排除自己）
     * @return 重叠的解释数量
     */
    public int checkScoreRangeOverlap(@Param("scaleId") Long scaleId, @Param("dimension") String dimension, 
                                     @Param("minScore") BigDecimal minScore, @Param("maxScore") BigDecimal maxScore, 
                                     @Param("excludeId") Long excludeId);

    /**
     * 根据量表ID删除所有解释
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    public int deleteInterpretationsByScaleId(Long scaleId);

    /**
     * 批量插入解释
     * 
     * @param interpretations 解释列表
     * @return 结果
     */
    public int batchInsertInterpretations(List<PsyTInterpretation> interpretations);

    /**
     * 查询解释统计信息
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    public java.util.Map<String, Object> selectInterpretationStats(Long scaleId);

    /**
     * 根据等级名称查询解释
     * 
     * @param scaleId 量表ID
     * @param levelName 等级名称
     * @param dimension 维度名称
     * @return 解释信息
     */
    public PsyTInterpretation selectInterpretationByLevel(@Param("scaleId") Long scaleId, @Param("levelName") String levelName, @Param("dimension") String dimension);

    /**
     * 更新解释的显示顺序
     * 
     * @param id 解释ID
     * @param orderNum 显示顺序
     * @return 结果
     */
    public int updateInterpretationOrder(@Param("id") Long id, @Param("orderNum") Integer orderNum);

    /**
     * 查询解释的最大显示顺序
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称
     * @return 最大显示顺序
     */
    public Integer selectMaxOrderNum(@Param("scaleId") Long scaleId, @Param("dimension") String dimension);
}
