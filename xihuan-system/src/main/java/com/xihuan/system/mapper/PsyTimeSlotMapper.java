package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 时间槽Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyTimeSlotMapper {
    
    /**
     * 查询时间槽列表
     * 
     * @param timeSlot 时间槽
     * @return 时间槽集合
     */
    List<PsyTimeSlot> selectTimeSlotList(PsyTimeSlot timeSlot);
    
    /**
     * 根据ID查询时间槽
     * 
     * @param id 时间槽主键
     * @return 时间槽
     */
    PsyTimeSlot selectTimeSlotById(Long id);
    
    /**
     * 查询咨询师在指定日期范围内的时间槽
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 时间槽集合
     */
    List<PsyTimeSlot> selectSlotsByCounselorAndDateRange(
        @Param("counselorId") Long counselorId,
        @Param("startDate") String startDate,
        @Param("endDate") String endDate
    );
    
    /**
     * 查询指定日期的可用时间槽
     * 
     * @param dateKey 日期
     * @param centerId 咨询中心ID
     * @param counselorId 咨询师ID（可选）
     * @return 时间槽集合
     */
    List<PsyTimeSlot> selectAvailableSlotsByDate(
        @Param("dateKey") String dateKey,
        @Param("centerId") Long centerId,
        @Param("counselorId") Long counselorId
    );
    
    /**
     * 查询公开时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 时间槽集合
     */
    List<PsyTimeSlot> selectPublicSlots(
        @Param("startDate") String startDate,
        @Param("endDate") String endDate,
        @Param("centerId") Long centerId
    );
    
    /**
     * 新增时间槽
     * 
     * @param timeSlot 时间槽
     * @return 结果
     */
    int insertTimeSlot(PsyTimeSlot timeSlot);
    
    /**
     * 批量新增时间槽
     * 
     * @param timeSlots 时间槽列表
     * @return 结果
     */
    int batchInsertTimeSlots(@Param("timeSlots") List<PsyTimeSlot> timeSlots);
    
    /**
     * 修改时间槽
     * 
     * @param timeSlot 时间槽
     * @return 结果
     */
    int updateTimeSlot(PsyTimeSlot timeSlot);
    
    /**
     * 批量更新时间槽状态
     * 
     * @param slotIds 时间槽ID列表
     * @param status 新状态
     * @return 结果
     */
    int batchUpdateSlotStatus(@Param("slotIds") List<Long> slotIds, @Param("status") Integer status);
    
    /**
     * 删除时间槽
     * 
     * @param id 时间槽主键
     * @return 结果
     */
    int deleteTimeSlotById(Long id);
    
    /**
     * 批量删除时间槽
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTimeSlotByIds(Long[] ids);
    
    /**
     * 删除指定日期范围的时间槽
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param counselorId 咨询师ID（可选）
     * @return 结果
     */
    int deleteSlotsByDateRange(
        @Param("startDate") String startDate,
        @Param("endDate") String endDate,
        @Param("counselorId") Long counselorId
    );

    /**
     * 删除指定咨询师在指定日期范围的时间槽
     *
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果
     */
    int deleteSlotsByDateRangeAndCounselor(
        @Param("counselorId") Long counselorId,
        @Param("startDate") String startDate,
        @Param("endDate") String endDate
    );
    
    /**
     * 检查时间槽是否存在冲突
     * 
     * @param counselorId 咨询师ID
     * @param dateKey 日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludeId 排除的时间槽ID
     * @return 冲突的时间槽数量
     */
    int checkSlotConflict(
        @Param("counselorId") Long counselorId,
        @Param("dateKey") String dateKey,
        @Param("startTime") LocalTime startTime,
        @Param("endTime") LocalTime endTime,
        @Param("excludeId") Long excludeId
    );
}
