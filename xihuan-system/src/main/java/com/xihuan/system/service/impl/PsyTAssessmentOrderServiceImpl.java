package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTAssessmentOrder;
import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.system.mapper.PsyTAssessmentOrderMapper;
import com.xihuan.system.mapper.PsyTScaleMapper;
import com.xihuan.system.service.IPsyTAssessmentOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 测评订单Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTAssessmentOrderServiceImpl implements IPsyTAssessmentOrderService {
    
    @Autowired
    private PsyTAssessmentOrderMapper orderMapper;
    
    @Autowired
    private PsyTScaleMapper scaleMapper;

    /**
     * 查询测评订单列表
     * 
     * @param order 测评订单
     * @return 测评订单
     */
    @Override
    public List<PsyTAssessmentOrder> selectOrderList(PsyTAssessmentOrder order) {
        return orderMapper.selectOrderList(order);
    }

    /**
     * 根据测评订单ID查询详细信息
     * 
     * @param id 测评订单ID
     * @return 测评订单
     */
    @Override
    public PsyTAssessmentOrder selectOrderById(Long id) {
        return orderMapper.selectOrderById(id);
    }

    /**
     * 根据订单编号查询测评订单
     * 
     * @param orderNo 订单编号
     * @return 测评订单
     */
    @Override
    public PsyTAssessmentOrder selectOrderByOrderNo(String orderNo) {
        return orderMapper.selectOrderByOrderNo(orderNo);
    }

    /**
     * 查询订单详情（包含量表、用户等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    @Override
    public PsyTAssessmentOrder selectOrderWithDetails(Long id) {
        return orderMapper.selectOrderWithDetails(id);
    }

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    @Override
    public List<PsyTAssessmentOrder> selectOrdersByUserId(Long userId) {
        return orderMapper.selectOrdersByUserId(userId);
    }

    /**
     * 根据量表ID查询订单列表
     * 
     * @param scaleId 量表ID
     * @return 订单集合
     */
    @Override
    public List<PsyTAssessmentOrder> selectOrdersByScaleId(Long scaleId) {
        return orderMapper.selectOrdersByScaleId(scaleId);
    }

    /**
     * 新增测评订单
     * 
     * @param order 测评订单
     * @return 结果
     */
    @Override
    public int insertOrder(PsyTAssessmentOrder order) {
        order.setCreateBy(SecurityUtils.getUsername());
        order.setCreateTime(DateUtils.getNowDate());
        order.setDelFlag("0");
        return orderMapper.insertOrder(order);
    }

    /**
     * 修改测评订单
     * 
     * @param order 测评订单
     * @return 结果
     */
    @Override
    public int updateOrder(PsyTAssessmentOrder order) {
        order.setUpdateBy(SecurityUtils.getUsername());
        order.setUpdateTime(DateUtils.getNowDate());
        return orderMapper.updateOrder(order);
    }

    /**
     * 批量删除测评订单
     * 
     * @param ids 需要删除的测评订单ID
     * @return 结果
     */
    @Override
    public int deleteOrderByIds(Long[] ids) {
        return orderMapper.deleteOrderByIds(ids);
    }

    /**
     * 删除测评订单信息
     * 
     * @param id 测评订单ID
     * @return 结果
     */
    @Override
    public int deleteOrderById(Long id) {
        return orderMapper.deleteOrderById(id);
    }

    /**
     * 检查用户是否已购买量表
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 是否已购买
     */
    @Override
    public boolean checkUserPurchased(Long userId, Long scaleId) {
        int count = orderMapper.checkUserPurchased(userId, scaleId);
        return count > 0;
    }

    /**
     * 创建订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 订单信息
     */
    @Override
    @Transactional
    public PsyTAssessmentOrder createOrder(Long userId, Long scaleId) {
        // 检查用户是否已购买
        if (checkUserPurchased(userId, scaleId)) {
            throw new RuntimeException("用户已购买该量表");
        }
        
        // 获取量表信息
        PsyTScale scale = scaleMapper.selectScaleById(scaleId);
        if (scale == null) {
            throw new RuntimeException("量表不存在");
        }
        
        if (scale.getStatus() != 1) {
            throw new RuntimeException("量表未发布");
        }
        
        // 创建订单
        PsyTAssessmentOrder order = new PsyTAssessmentOrder();
        order.setOrderNo(generateOrderNo());
        order.setScaleId(scaleId);
        order.setUserId(userId);
        order.setOriginalPrice(scale.getPrice());
        order.setDiscountPrice(BigDecimal.ZERO);
        order.setPaymentAmount(scale.getPrice());
        order.setStatus(0); // 待支付
        order.setDelFlag("0");
        order.setCreateBy(userId.toString());
        order.setCreateTime(DateUtils.getNowDate());
        
        insertOrder(order);
        return order;
    }

    /**
     * 确认支付
     * 
     * @param orderNo 订单编号
     * @param transactionId 交易ID
     * @return 结果
     */
    @Override
    @Transactional
    public int confirmPayment(String orderNo, String transactionId) {
        PsyTAssessmentOrder order = selectOrderByOrderNo(orderNo);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        if (order.getStatus() != 0) {
            throw new RuntimeException("订单状态异常");
        }
        
        order.setStatus(1); // 已支付
        order.setPaymentTime(DateUtils.getNowDate());
        order.setTransactionId(transactionId);
        order.setUpdateBy("system");
        order.setUpdateTime(DateUtils.getNowDate());
        
        return updateOrder(order);
    }

    /**
     * 取消订单
     * 
     * @param id 订单ID
     * @param reason 取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelOrder(Long id, String reason) {
        PsyTAssessmentOrder order = selectOrderById(id);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        if (order.getStatus() != 0) {
            throw new RuntimeException("只能取消待支付订单");
        }
        
        order.setStatus(2); // 已取消
        order.setRemark(reason);
        order.setUpdateBy(SecurityUtils.getUsername());
        order.setUpdateTime(DateUtils.getNowDate());
        
        return updateOrder(order);
    }

    /**
     * 申请退款
     * 
     * @param id 订单ID
     * @param reason 退款原因
     * @return 结果
     */
    @Override
    @Transactional
    public int applyRefund(Long id, String reason) {
        PsyTAssessmentOrder order = selectOrderById(id);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        if (order.getStatus() != 1) {
            throw new RuntimeException("只能对已支付订单申请退款");
        }
        
        order.setRefundReason(reason);
        order.setUpdateBy(SecurityUtils.getUsername());
        order.setUpdateTime(DateUtils.getNowDate());
        
        return updateOrder(order);
    }

    /**
     * 处理退款
     * 
     * @param id 订单ID
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @return 结果
     */
    @Override
    @Transactional
    public int processRefund(Long id, BigDecimal refundAmount, String reason) {
        PsyTAssessmentOrder order = selectOrderById(id);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        if (order.getStatus() != 1) {
            throw new RuntimeException("订单状态异常");
        }
        
        order.setStatus(3); // 已退款
        order.setRefundAmount(refundAmount);
        order.setRefundTime(DateUtils.getNowDate());
        order.setRefundReason(reason);
        order.setUpdateBy(SecurityUtils.getUsername());
        order.setUpdateTime(DateUtils.getNowDate());
        
        return updateOrder(order);
    }

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateOrderStatus(Long id, Integer status) {
        PsyTAssessmentOrder order = new PsyTAssessmentOrder();
        order.setId(id);
        order.setStatus(status);
        order.setUpdateBy(SecurityUtils.getUsername());
        order.setUpdateTime(DateUtils.getNowDate());
        
        return updateOrder(order);
    }

    /**
     * 查询订单统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectOrderStats() {
        return orderMapper.selectOrderStats();
    }

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserOrderStats(Long userId) {
        return orderMapper.selectUserOrderStats(userId);
    }

    /**
     * 查询量表订单统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleOrderStats(Long scaleId) {
        return orderMapper.selectScaleOrderStats(scaleId);
    }

    /**
     * 搜索订单
     * 
     * @param keyword 关键词
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param status 状态
     * @return 订单集合
     */
    @Override
    public List<PsyTAssessmentOrder> searchOrders(String keyword, Long userId, Long scaleId, Integer status) {
        return orderMapper.searchOrders(keyword, userId, scaleId, status);
    }

    /**
     * 查询热销量表排行
     * 
     * @param limit 限制数量
     * @return 排行信息
     */
    @Override
    public List<Map<String, Object>> selectHotScaleRanking(Integer limit) {
        return orderMapper.selectHotScaleRanking(limit);
    }

    /**
     * 查询用户消费排行
     * 
     * @param limit 限制数量
     * @return 排行信息
     */
    @Override
    public List<Map<String, Object>> selectUserConsumptionRanking(Integer limit) {
        return orderMapper.selectUserConsumptionRanking(limit);
    }

    /**
     * 查询每日订单统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectDailyOrderStats(String startDate, String endDate) {
        return orderMapper.selectDailyOrderStats(startDate, endDate);
    }

    /**
     * 查询每月订单统计
     * 
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectMonthlyOrderStats(String startMonth, String endMonth) {
        return orderMapper.selectMonthlyOrderStats(startMonth, endMonth);
    }

    /**
     * 查询过期订单
     * 
     * @return 过期订单集合
     */
    @Override
    public List<PsyTAssessmentOrder> selectExpiredOrders() {
        return orderMapper.selectExpiredOrders();
    }

    /**
     * 取消过期订单
     * 
     * @return 取消数量
     */
    @Override
    @Transactional
    public int cancelExpiredOrders() {
        return orderMapper.cancelExpiredOrders();
    }

    /**
     * 生成订单编号
     *
     * @return 订单编号
     */
    @Override
    public String generateOrderNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String random = String.valueOf((int)(Math.random() * 9000) + 1000);
        return "PSY" + timestamp + random;
    }

    /**
     * 计算订单金额
     *
     * @param scaleId 量表ID
     * @param userId 用户ID
     * @return 订单金额信息
     */
    @Override
    public Map<String, Object> calculateOrderAmount(Long scaleId, Long userId) {
        Map<String, Object> result = new HashMap<>();

        // 获取量表信息
        PsyTScale scale = scaleMapper.selectScaleById(scaleId);
        if (scale == null) {
            result.put("success", false);
            result.put("message", "量表不存在");
            return result;
        }

        BigDecimal originalPrice = scale.getPrice();
        BigDecimal discountPrice = BigDecimal.ZERO;
        BigDecimal paymentAmount = originalPrice;

        // 这里可以添加优惠券、会员折扣等逻辑

        result.put("success", true);
        result.put("originalPrice", originalPrice);
        result.put("discountPrice", discountPrice);
        result.put("paymentAmount", paymentAmount);
        result.put("scaleName", scale.getName());

        return result;
    }

    /**
     * 验证订单
     *
     * @param order 订单信息
     * @return 验证结果
     */
    @Override
    public Map<String, Object> validateOrder(PsyTAssessmentOrder order) {
        Map<String, Object> result = new HashMap<>();

        // 检查量表是否存在且可用
        PsyTScale scale = scaleMapper.selectScaleById(order.getScaleId());
        if (scale == null) {
            result.put("success", false);
            result.put("message", "量表不存在");
            return result;
        }

        if (scale.getStatus() != 1) {
            result.put("success", false);
            result.put("message", "量表未发布");
            return result;
        }

        // 检查用户是否已购买
        if (checkUserPurchased(order.getUserId(), order.getScaleId())) {
            result.put("success", false);
            result.put("message", "用户已购买该量表");
            return result;
        }

        result.put("success", true);
        result.put("message", "验证通过");
        return result;
    }

    /**
     * 订单支付回调处理
     *
     * @param orderNo 订单编号
     * @param paymentData 支付数据
     * @return 处理结果
     */
    @Override
    @Transactional
    public Map<String, Object> handlePaymentCallback(String orderNo, Map<String, Object> paymentData) {
        Map<String, Object> result = new HashMap<>();

        try {
            PsyTAssessmentOrder order = selectOrderByOrderNo(orderNo);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            if (order.getStatus() != 0) {
                result.put("success", false);
                result.put("message", "订单状态异常");
                return result;
            }

            // 验证支付金额
            BigDecimal paidAmount = new BigDecimal(paymentData.get("amount").toString());
            if (paidAmount.compareTo(order.getPaymentAmount()) != 0) {
                result.put("success", false);
                result.put("message", "支付金额不匹配");
                return result;
            }

            // 更新订单状态
            String transactionId = paymentData.get("transactionId").toString();
            confirmPayment(orderNo, transactionId);

            result.put("success", true);
            result.put("message", "支付成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "处理失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 查询用户可用订单
     *
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 可用订单
     */
    @Override
    public PsyTAssessmentOrder selectAvailableOrder(Long userId, Long scaleId) {
        List<PsyTAssessmentOrder> orders = orderMapper.selectOrdersByUserId(userId);
        for (PsyTAssessmentOrder order : orders) {
            if (order.getScaleId().equals(scaleId) && order.getStatus() == 1) {
                return order;
            }
        }
        return null;
    }

    /**
     * 使用订单
     *
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int useOrder(Long orderId) {
        PsyTAssessmentOrder order = selectOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        if (order.getStatus() != 1) {
            throw new RuntimeException("订单状态异常");
        }

        // 这里可以添加使用次数限制等逻辑
        order.setUsed(true);
        order.setUseTime(DateUtils.getNowDate());
        order.setUpdateBy(SecurityUtils.getUsername());
        order.setUpdateTime(DateUtils.getNowDate());

        return updateOrder(order);
    }

    /**
     * 检查订单是否可用
     *
     * @param orderId 订单ID
     * @return 是否可用
     */
    @Override
    public boolean checkOrderAvailable(Long orderId) {
        PsyTAssessmentOrder order = selectOrderById(orderId);
        return order != null && order.getStatus() == 1;
    }

    /**
     * 获取订单支付信息
     *
     * @param orderNo 订单编号
     * @return 支付信息
     */
    @Override
    public Map<String, Object> getOrderPaymentInfo(String orderNo) {
        Map<String, Object> result = new HashMap<>();

        PsyTAssessmentOrder order = selectOrderByOrderNo(orderNo);
        if (order == null) {
            result.put("success", false);
            result.put("message", "订单不存在");
            return result;
        }

        if (order.getStatus() != 0) {
            result.put("success", false);
            result.put("message", "订单状态异常");
            return result;
        }

        result.put("success", true);
        result.put("orderNo", order.getOrderNo());
        result.put("amount", order.getPaymentAmount());
        result.put("scaleName", order.getScaleName());
        result.put("createTime", order.getCreateTime());

        return result;
    }

    /**
     * 订单完成处理
     *
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int completeOrder(Long orderId) {
        PsyTAssessmentOrder order = selectOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        if (order.getStatus() != 1) {
            throw new RuntimeException("订单状态异常");
        }

        // 标记订单完成
        order.setUpdateBy(SecurityUtils.getUsername());
        order.setUpdateTime(DateUtils.getNowDate());
        order.setRemark("订单已完成");

        return updateOrder(order);
    }

    /**
     * 获取订单列表DTO
     *
     * @param order 查询条件
     * @return 订单DTO列表
     */
    @Override
    public List<Map<String, Object>> getOrderListDTO(PsyTAssessmentOrder order) {
        List<PsyTAssessmentOrder> orders = selectOrderList(order);
        List<Map<String, Object>> result = new ArrayList<>();

        for (PsyTAssessmentOrder o : orders) {
            Map<String, Object> dto = new HashMap<>();
            dto.put("id", o.getId());
            dto.put("orderNo", o.getOrderNo());
            dto.put("scaleId", o.getScaleId());
            dto.put("scaleName", o.getScaleName());
            dto.put("originalPrice", o.getOriginalPrice());
            dto.put("paymentAmount", o.getPaymentAmount());
            dto.put("status", o.getStatus());
            dto.put("statusDesc", o.getStatusDesc());
            dto.put("createTime", o.getCreateTime());
            dto.put("paymentTime", o.getPaymentTime());

            result.add(dto);
        }

        return result;
    }

    /**
     * 获取订单详情DTO
     *
     * @param orderId 订单ID
     * @return 订单DTO
     */
    @Override
    public Map<String, Object> getOrderDTO(Long orderId) {
        PsyTAssessmentOrder order = selectOrderWithDetails(orderId);
        if (order == null) {
            return null;
        }

        Map<String, Object> dto = new HashMap<>();
        dto.put("id", order.getId());
        dto.put("orderNo", order.getOrderNo());
        dto.put("scaleId", order.getScaleId());
        dto.put("scaleName", order.getScaleName());
        dto.put("scaleCode", order.getScaleCode());
        dto.put("originalPrice", order.getOriginalPrice());
        dto.put("discountPrice", order.getDiscountPrice());
        dto.put("paymentAmount", order.getPaymentAmount());
        dto.put("paymentMethod", order.getPaymentMethod());
        dto.put("status", order.getStatus());
        dto.put("statusDesc", order.getStatusDesc());
        dto.put("createTime", order.getCreateTime());
        dto.put("paymentTime", order.getPaymentTime());
        dto.put("transactionId", order.getTransactionId());
        dto.put("refundAmount", order.getRefundAmount());
        dto.put("refundTime", order.getRefundTime());
        dto.put("refundReason", order.getRefundReason());

        return dto;
    }

    /**
     * 支付订单
     *
     * @param orderNo 订单编号
     * @param paymentMethod 支付方式
     * @return 支付结果
     */
    @Override
    @Transactional
    public Map<String, Object> payOrder(String orderNo, String paymentMethod) {
        Map<String, Object> result = new HashMap<>();

        try {
            PsyTAssessmentOrder order = selectOrderByOrderNo(orderNo);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            if (order.getStatus() != 0) {
                result.put("success", false);
                result.put("message", "订单状态异常");
                return result;
            }

            // 更新支付方式
            order.setPaymentMethod(paymentMethod);
            updateOrder(order);

            // 这里应该调用支付接口
            // 模拟支付成功
            String transactionId = "TXN" + System.currentTimeMillis();
            confirmPayment(orderNo, transactionId);

            result.put("success", true);
            result.put("message", "支付成功");
            result.put("transactionId", transactionId);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "支付失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 查询待支付订单
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    @Override
    public List<PsyTAssessmentOrder> selectPendingOrders(Long userId) {
        PsyTAssessmentOrder condition = new PsyTAssessmentOrder();
        condition.setUserId(userId);
        condition.setStatus(0); // 待支付
        return selectOrderList(condition);
    }

    /**
     * 查询已支付订单
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    @Override
    public List<PsyTAssessmentOrder> selectPaidOrders(Long userId) {
        PsyTAssessmentOrder condition = new PsyTAssessmentOrder();
        condition.setUserId(userId);
        condition.setStatus(1); // 已支付
        return selectOrderList(condition);
    }

    /**
     * 查询已完成订单
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    @Override
    public List<PsyTAssessmentOrder> selectCompletedOrders(Long userId) {
        // 这里可以根据业务需求定义完成状态
        // 暂时返回已支付订单
        return selectPaidOrders(userId);
    }

    /**
     * 查询已取消订单
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    @Override
    public List<PsyTAssessmentOrder> selectCancelledOrders(Long userId) {
        PsyTAssessmentOrder condition = new PsyTAssessmentOrder();
        condition.setUserId(userId);
        condition.setStatus(2); // 已取消
        return selectOrderList(condition);
    }
}
