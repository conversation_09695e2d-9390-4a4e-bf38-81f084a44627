package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTScale;

import java.util.List;
import java.util.Map;

/**
 * 量表Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTScaleService {
    
    /**
     * 查询量表
     * 
     * @param id 量表主键
     * @return 量表
     */
    public PsyTScale selectScaleById(Long id);

    /**
     * 查询量表列表
     * 
     * @param scale 量表
     * @return 量表集合
     */
    public List<PsyTScale> selectScaleList(PsyTScale scale);

    /**
     * 新增量表
     * 
     * @param scale 量表
     * @return 结果
     */
    public int insertScale(PsyTScale scale);

    /**
     * 修改量表
     * 
     * @param scale 量表
     * @return 结果
     */
    public int updateScale(PsyTScale scale);

    /**
     * 批量删除量表
     * 
     * @param ids 需要删除的量表主键集合
     * @return 结果
     */
    public int deleteScaleByIds(Long[] ids);

    /**
     * 删除量表信息
     * 
     * @param id 量表主键
     * @return 结果
     */
    public int deleteScaleById(Long id);

    /**
     * 根据编码查询量表
     * 
     * @param code 量表编码
     * @return 量表信息
     */
    public PsyTScale selectScaleByCode(String code);

    /**
     * 查询量表详情（包含题目、分量表等信息）
     * 
     * @param id 量表ID
     * @return 量表详情
     */
    public PsyTScale selectScaleWithDetails(Long id);

    /**
     * 查询启用的量表列表
     * 
     * @return 量表集合
     */
    public List<PsyTScale> selectEnabledScales();

    /**
     * 查询热门量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    public List<PsyTScale> selectHotScales(Integer limit);

    /**
     * 查询最新量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    public List<PsyTScale> selectLatestScales(Integer limit);

    /**
     * 根据分类查询量表
     * 
     * @param categoryId 分类ID
     * @return 量表集合
     */
    public List<PsyTScale> selectScalesByCategory(Integer categoryId);

    /**
     * 搜索量表
     * 
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param status 状态
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    public List<PsyTScale> searchScales(String keyword, Integer categoryId, Integer status, Long enterpriseId);

    /**
     * 统计量表数量
     * 
     * @param scale 查询条件
     * @return 数量
     */
    public int countScales(PsyTScale scale);

    /**
     * 查询量表统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> selectScaleStats();

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectUserTestStats(Long userId);

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    public Map<String, Object> selectScaleTestStats(Long scaleId);

    /**
     * 发布量表
     * 
     * @param id 量表ID
     * @return 结果
     */
    public int publishScale(Long id);

    /**
     * 下架量表
     * 
     * @param id 量表ID
     * @return 结果
     */
    public int offlineScale(Long id);

    /**
     * 复制量表
     * 
     * @param id 量表ID
     * @param newScaleName 新量表名称
     * @param newScaleCode 新量表编码
     * @return 结果
     */
    public int copyScale(Long id, String newScaleName, String newScaleCode);

    /**
     * 导入量表
     * 
     * @param scaleList 量表列表
     * @param isUpdateSupport 是否更新支持
     * @param operName 操作用户
     * @return 结果
     */
    public String importScale(List<PsyTScale> scaleList, Boolean isUpdateSupport, String operName);

    /**
     * 导出量表
     * 
     * @param ids 量表ID数组
     * @return 量表列表
     */
    public List<PsyTScale> exportScales(Long[] ids);

    /**
     * 增加查看次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    public int increaseViewCount(Long id);

    /**
     * 增加搜索次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    public int increaseSearchCount(Long id);

    /**
     * 验证量表配置的完整性
     * 
     * @param id 量表ID
     * @return 验证结果
     */
    public Map<String, Object> validateScaleConfig(Long id);

    /**
     * 查询用户收藏的量表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    public List<PsyTScale> selectFavoriteScalesByUserId(Long userId);

    /**
     * 查询相似量表
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 量表集合
     */
    public List<PsyTScale> selectSimilarScales(Long scaleId, Integer limit);

    /**
     * 查询企业量表
     * 
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    public List<PsyTScale> selectScalesByEnterpriseId(Long enterpriseId);

    /**
     * 更新量表状态
     * 
     * @param id 量表ID
     * @param status 状态
     * @return 结果
     */
    public int updateScaleStatus(Long id, Integer status);

    /**
     * 批量更新量表状态
     * 
     * @param ids 量表ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateScaleStatus(Long[] ids, Integer status);
}
