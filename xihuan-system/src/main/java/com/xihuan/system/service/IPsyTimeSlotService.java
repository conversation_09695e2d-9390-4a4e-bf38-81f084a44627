package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import com.xihuan.common.core.domain.dto.PsyTimeSlotDTO;
import java.time.LocalDate;
import java.util.List;

/**
 * 时间槽Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTimeSlotService {
    
    /**
     * 查询时间槽列表
     * 
     * @param timeSlot 时间槽
     * @return 时间槽集合
     */
    List<PsyTimeSlot> selectTimeSlotList(PsyTimeSlot timeSlot);
    
    /**
     * 根据ID查询时间槽
     * 
     * @param id 时间槽主键
     * @return 时间槽
     */
    PsyTimeSlot selectTimeSlotById(Long id);
    
    /**
     * 查询咨询师在指定日期范围内的时间槽
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 时间槽集合
     */
    List<PsyTimeSlot> selectSlotsByCounselorAndDateRange(Long counselorId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 查询指定日期的可用时间槽
     * 
     * @param date 日期
     * @param centerId 咨询中心ID
     * @param counselorId 咨询师ID（可选）
     * @return 时间槽集合
     */
    List<PsyTimeSlot> selectAvailableSlotsByDate(LocalDate date, Long centerId, Long counselorId);
    
    /**
     * 查询公开时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 时间槽集合
     */
    List<PsyTimeSlot> selectPublicSlots(LocalDate startDate, LocalDate endDate, Long centerId);
    
    /**
     * 获取格式化的时间槽数据（按日期和时间段分组）
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 格式化的时间槽数据
     */
    List<PsyTimeSlotDTO> getFormattedTimeSlots(Long counselorId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 新增时间槽
     * 
     * @param timeSlot 时间槽
     * @return 结果
     */
    int insertTimeSlot(PsyTimeSlot timeSlot);
    
    /**
     * 批量新增时间槽
     * 
     * @param timeSlots 时间槽列表
     * @return 结果
     */
    int batchInsertTimeSlots(List<PsyTimeSlot> timeSlots);
    
    /**
     * 修改时间槽
     * 
     * @param timeSlot 时间槽
     * @return 结果
     */
    int updateTimeSlot(PsyTimeSlot timeSlot);
    
    /**
     * 批量更新时间槽状态
     * 
     * @param slotIds 时间槽ID列表
     * @param status 新状态
     * @return 结果
     */
    int batchUpdateSlotStatus(List<Long> slotIds, Integer status);
    
    /**
     * 删除时间槽信息
     * 
     * @param id 时间槽主键
     * @return 结果
     */
    int deleteTimeSlotById(Long id);
    
    /**
     * 批量删除时间槽
     * 
     * @param ids 需要删除的时间槽主键集合
     * @return 结果
     */
    int deleteTimeSlotByIds(Long[] ids);
    
    /**
     * 为咨询师生成指定日期范围的时间槽
     *
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的时间槽数量
     */
    int generateSlotsForCounselor(Long counselorId, LocalDate startDate, LocalDate endDate);

    /**
     * 重新生成咨询师指定日期范围的时间槽（先清理再生成）
     *
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的时间槽数量
     */
    int regenerateSlotsForCounselor(Long counselorId, LocalDate startDate, LocalDate endDate);

    /**
     * 为所有咨询师重新生成指定日期范围的时间槽（先清理再生成）
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的时间槽数量
     */
    int regenerateSlotsForAllCounselors(LocalDate startDate, LocalDate endDate);
    
    /**
     * 为所有咨询师生成指定日期范围的时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的时间槽数量
     */
    int generateSlotsForAllCounselors(LocalDate startDate, LocalDate endDate);
    
    /**
     * 清理过期的时间槽
     * 
     * @param beforeDate 清理此日期之前的时间槽
     * @return 清理的时间槽数量
     */
    int cleanExpiredSlots(LocalDate beforeDate);
    
    /**
     * 更新过期时间槽的状态
     *
     * @return 更新的时间槽数量
     */
    int updateExpiredSlotStatus();

    /**
     * 更新过期时间槽的状态（支持延后过期配置）
     *
     * @return 更新的时间槽数量
     */
    int updateExpiredSlotStatusWithDelay();
}
