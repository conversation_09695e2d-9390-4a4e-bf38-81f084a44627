package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.PsyTScaleMapper;
import com.xihuan.system.service.IPsyTScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 量表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTScaleServiceImpl implements IPsyTScaleService {
    
    @Autowired
    private PsyTScaleMapper scaleMapper;

    /**
     * 查询量表
     * 
     * @param id 量表主键
     * @return 量表
     */
    @Override
    public PsyTScale selectScaleById(Long id) {
        return scaleMapper.selectScaleById(id);
    }

    /**
     * 查询量表列表
     * 
     * @param scale 量表
     * @return 量表
     */
    @Override
    public List<PsyTScale> selectScaleList(PsyTScale scale) {
        return scaleMapper.selectScaleList(scale);
    }

    /**
     * 新增量表
     * 
     * @param scale 量表
     * @return 结果
     */
    @Override
    public int insertScale(PsyTScale scale) {
        scale.setCreateBy(SecurityUtils.getUsername());
        scale.setCreateTime(DateUtils.getNowDate());
        scale.setDelFlag("0");
        scale.setStatus(1); // 默认启用
        scale.setViewCount(0);
        scale.setSearchCount(0);
        return scaleMapper.insertScale(scale);
    }

    /**
     * 修改量表
     * 
     * @param scale 量表
     * @return 结果
     */
    @Override
    public int updateScale(PsyTScale scale) {
        scale.setUpdateBy(SecurityUtils.getUsername());
        scale.setUpdateTime(DateUtils.getNowDate());
        return scaleMapper.updateScale(scale);
    }

    /**
     * 批量删除量表
     * 
     * @param ids 需要删除的量表主键
     * @return 结果
     */
    @Override
    public int deleteScaleByIds(Long[] ids) {
        return scaleMapper.deleteScaleByIds(ids);
    }

    /**
     * 删除量表信息
     * 
     * @param id 量表主键
     * @return 结果
     */
    @Override
    public int deleteScaleById(Long id) {
        return scaleMapper.deleteScaleById(id);
    }

    /**
     * 根据编码查询量表
     * 
     * @param code 量表编码
     * @return 量表信息
     */
    @Override
    public PsyTScale selectScaleByCode(String code) {
        return scaleMapper.selectScaleByCode(code);
    }

    /**
     * 查询量表详情（包含题目、分量表等信息）
     * 
     * @param id 量表ID
     * @return 量表详情
     */
    @Override
    public PsyTScale selectScaleWithDetails(Long id) {
        return scaleMapper.selectScaleWithDetails(id);
    }

    /**
     * 查询启用的量表列表
     *
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectEnabledScales() {
        PsyTScale condition = new PsyTScale();
        condition.setStatus(1); // 启用状态
        return scaleMapper.selectScaleList(condition);
    }

    /**
     * 查询热门量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectHotScales(Integer limit) {
        return scaleMapper.selectHotScales(limit);
    }

    /**
     * 查询最新量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectLatestScales(Integer limit) {
        return scaleMapper.selectLatestScales(limit);
    }

    /**
     * 根据分类查询量表
     *
     * @param categoryId 分类ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectScalesByCategory(Integer categoryId) {
        PsyTScale condition = new PsyTScale();
        condition.setCategoryId(categoryId);
        condition.setStatus(1); // 只查询启用状态的量表
        return scaleMapper.selectScaleList(condition);
    }

    /**
     * 搜索量表
     *
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param status 状态
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> searchScales(String keyword, Integer categoryId, Integer status, Long enterpriseId) {
        return scaleMapper.searchScales(keyword, categoryId, status, enterpriseId);
    }

    /**
     * 统计量表数量
     * 
     * @param scale 查询条件
     * @return 数量
     */
    @Override
    public int countScales(PsyTScale scale) {
        return scaleMapper.countScales(scale);
    }

    /**
     * 查询量表统计信息
     *
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleStats() {
        List<Map<String, Object>> statsList = scaleMapper.selectScaleStats();
        Map<String, Object> result = new HashMap<>();

        if (statsList != null && !statsList.isEmpty()) {
            // 合并统计信息
            for (Map<String, Object> stats : statsList) {
                result.putAll(stats);
            }
        }

        return result;
    }

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserTestStats(Long userId) {
        return scaleMapper.selectUserTestStats(userId);
    }

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleTestStats(Long scaleId) {
        return scaleMapper.selectScaleTestStats(scaleId);
    }

    /**
     * 发布量表
     *
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int publishScale(Long id) {
        PsyTScale scale = new PsyTScale();
        scale.setId(id);
        scale.setStatus(1);
        return scaleMapper.updateScale(scale);
    }

    /**
     * 下架量表
     *
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int offlineScale(Long id) {
        PsyTScale scale = new PsyTScale();
        scale.setId(id);
        scale.setStatus(0);
        return scaleMapper.updateScale(scale);
    }

    /**
     * 复制量表
     * 
     * @param id 量表ID
     * @param newScaleName 新量表名称
     * @param newScaleCode 新量表编码
     * @return 结果
     */
    @Override
    @Transactional
    public int copyScale(Long id, String newScaleName, String newScaleCode) {
        // 获取原量表信息
        PsyTScale originalScale = selectScaleById(id);
        if (originalScale == null) {
            return 0;
        }
        
        // 创建新量表
        PsyTScale newScale = new PsyTScale();
        newScale.setName(newScaleName);
        newScale.setCode(newScaleCode);
        newScale.setCategoryId(originalScale.getCategoryId());
        newScale.setDescription(originalScale.getDescription());
        newScale.setIntroduction(originalScale.getIntroduction());
        newScale.setTestNotice(originalScale.getTestNotice());
        newScale.setTestPurpose(originalScale.getTestPurpose());
        newScale.setTestObject(originalScale.getTestObject());
        newScale.setTestPreparation(originalScale.getTestPreparation());
        newScale.setTestProcessing(originalScale.getTestProcessing());
        newScale.setTestAttention(originalScale.getTestAttention());
        newScale.setTestTheory(originalScale.getTestTheory());
        newScale.setTestApplication(originalScale.getTestApplication());
        newScale.setReferenceLiterature(originalScale.getReferenceLiterature());
        newScale.setQuestionCount(originalScale.getQuestionCount());
        newScale.setScoringType(originalScale.getScoringType());
        newScale.setDuration(originalScale.getDuration());
        newScale.setApplicableAge(originalScale.getApplicableAge());
        newScale.setPrice(originalScale.getPrice());
        newScale.setPayMode(originalScale.getPayMode());
        newScale.setPayPhase(originalScale.getPayPhase());
        newScale.setStatus(0); // 新量表默认为草稿状态
        
        return insertScale(newScale);
    }

    /**
     * 导入量表
     * 
     * @param scaleList 量表列表
     * @param isUpdateSupport 是否更新支持
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importScale(List<PsyTScale> scaleList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(scaleList) || scaleList.size() == 0) {
            return "导入量表数据不能为空！";
        }
        
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (PsyTScale scale : scaleList) {
            try {
                // 验证是否存在这个量表
                PsyTScale existScale = selectScaleByCode(scale.getCode());
                if (StringUtils.isNull(existScale)) {
                    scale.setCreateBy(operName);
                    this.insertScale(scale);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、量表 " + scale.getName() + " 导入成功");
                } else if (isUpdateSupport) {
                    scale.setId(existScale.getId());
                    scale.setUpdateBy(operName);
                    this.updateScale(scale);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、量表 " + scale.getName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、量表 " + scale.getName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、量表 " + scale.getName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            return failureMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }

    /**
     * 导出量表
     * 
     * @param ids 量表ID数组
     * @return 量表列表
     */
    @Override
    public List<PsyTScale> exportScales(Long[] ids) {
        List<PsyTScale> scales = new ArrayList<>();
        for (Long id : ids) {
            PsyTScale scale = selectScaleById(id);
            if (scale != null) {
                scales.add(scale);
            }
        }
        return scales;
    }

    /**
     * 增加查看次数
     *
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int increaseViewCount(Long id) {
        return scaleMapper.updateViewCount(id);
    }

    /**
     * 增加搜索次数
     *
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int increaseSearchCount(Long id) {
        return scaleMapper.updateSearchCount(id);
    }

    /**
     * 验证量表配置的完整性
     * 
     * @param id 量表ID
     * @return 验证结果
     */
    @Override
    public Map<String, Object> validateScaleConfig(Long id) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        PsyTScale scale = selectScaleById(id);
        if (scale == null) {
            errors.add("量表不存在");
            result.put("valid", false);
            result.put("errors", errors);
            return result;
        }
        
        // 检查基本信息
        if (StringUtils.isEmpty(scale.getName())) {
            errors.add("量表名称不能为空");
        }
        if (StringUtils.isEmpty(scale.getCode())) {
            errors.add("量表编码不能为空");
        }
        if (scale.getCategoryId() == null) {
            warnings.add("建议设置量表分类");
        }
        if (StringUtils.isEmpty(scale.getDescription())) {
            warnings.add("建议添加量表描述");
        }
        
        // 检查题目数量
        if (scale.getQuestionCount() == null || scale.getQuestionCount() <= 0) {
            errors.add("题目数量必须大于0");
        }
        
        // 检查价格设置
        if (scale.getPayMode() != null && scale.getPayMode() == 1) {
            if (scale.getPrice() == null || scale.getPrice().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                errors.add("付费量表必须设置价格");
            }
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        
        return result;
    }

    /**
     * 查询用户收藏的量表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectFavoriteScalesByUserId(Long userId) {
        return scaleMapper.selectFavoriteScalesByUserId(userId);
    }

    /**
     * 查询相似量表
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectSimilarScales(Long scaleId, Integer limit) {
        return scaleMapper.selectSimilarScales(scaleId, limit);
    }

    /**
     * 查询企业量表
     * 
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectScalesByEnterpriseId(Long enterpriseId) {
        return scaleMapper.selectScalesByEnterpriseId(enterpriseId);
    }

    /**
     * 更新量表状态
     *
     * @param id 量表ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateScaleStatus(Long id, Integer status) {
        PsyTScale scale = new PsyTScale();
        scale.setId(id);
        scale.setStatus(status);
        return scaleMapper.updateScale(scale);
    }

    /**
     * 批量更新量表状态
     * 
     * @param ids 量表ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int batchUpdateScaleStatus(Long[] ids, Integer status) {
        return scaleMapper.batchUpdateScaleStatus(ids, status);
    }
}
