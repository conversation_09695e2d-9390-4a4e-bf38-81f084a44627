package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyMeditationReview;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyMeditationReviewMapper;
import com.xihuan.system.service.IPsyMeditationReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 冥想评价表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyMeditationReviewServiceImpl implements IPsyMeditationReviewService {
    
    @Autowired
    private PsyMeditationReviewMapper reviewMapper;

    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    @Override
    public List<PsyMeditationReview> selectReviewList(PsyMeditationReview review) {
        return reviewMapper.selectReviewList(review);
    }

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    @Override
    public PsyMeditationReview selectReviewById(Long id) {
        return reviewMapper.selectReviewById(id);
    }

    /**
     * 查询评价详情（包含冥想和用户信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    @Override
    public PsyMeditationReview selectReviewWithDetails(Long id) {
        return reviewMapper.selectReviewWithDetails(id);
    }

    /**
     * 根据冥想ID查询评价列表
     * 
     * @param meditationId 冥想ID
     * @return 评价集合
     */
    @Override
    public List<PsyMeditationReview> selectReviewsByMeditationId(Long meditationId) {
        return reviewMapper.selectReviewsByMeditationId(meditationId);
    }

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    @Override
    public List<PsyMeditationReview> selectReviewsByUserId(Long userId) {
        return reviewMapper.selectReviewsByUserId(userId);
    }

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    @Override
    public int insertReview(PsyMeditationReview review) {
        review.setCreateTime(DateUtils.getNowDate());
        review.setDelFlag(0);
        
        // 如果没有设置评分，默认为5星
        if (review.getRating() == null) {
            review.setRating(5);
        }
        
        return reviewMapper.insertReview(review);
    }

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    @Override
    public int updateReview(PsyMeditationReview review) {
        review.setUpdateTime(DateUtils.getNowDate());
        return reviewMapper.updateReview(review);
    }

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    @Override
    public int deleteReviewById(Long id) {
        return reviewMapper.deleteReviewById(id);
    }

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    @Override
    public int deleteReviewByIds(Long[] ids) {
        return reviewMapper.deleteReviewByIds(ids);
    }

    /**
     * 计算冥想平均评分
     * 
     * @param meditationId 冥想ID
     * @return 平均评分
     */
    @Override
    public BigDecimal calculateAverageRating(Long meditationId) {
        return reviewMapper.calculateAverageRating(meditationId);
    }

    /**
     * 统计冥想评价数量
     * 
     * @param meditationId 冥想ID
     * @return 评价数量
     */
    @Override
    public int countReviewsByMeditationId(Long meditationId) {
        return reviewMapper.countReviewsByMeditationId(meditationId);
    }

    /**
     * 检查用户是否已评价冥想
     * 
     * @param userId 用户ID
     * @param meditationId 冥想ID
     * @return 是否已评价
     */
    @Override
    public boolean checkUserReviewed(Long userId, Long meditationId) {
        int count = reviewMapper.checkUserReviewed(userId, meditationId);
        return count > 0;
    }
}
