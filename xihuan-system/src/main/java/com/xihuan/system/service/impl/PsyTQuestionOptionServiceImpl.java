package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTQuestionOption;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.system.mapper.PsyTQuestionOptionMapper;
import com.xihuan.system.service.IPsyTQuestionOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 题目选项Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTQuestionOptionServiceImpl implements IPsyTQuestionOptionService {
    
    @Autowired
    private PsyTQuestionOptionMapper questionOptionMapper;

    /**
     * 查询题目选项
     * 
     * @param id 题目选项主键
     * @return 题目选项
     */
    @Override
    public PsyTQuestionOption selectQuestionOptionById(Long id) {
        return questionOptionMapper.selectOptionById(id);
    }

    /**
     * 查询题目选项列表
     *
     * @param questionOption 题目选项
     * @return 题目选项
     */
    @Override
    public List<PsyTQuestionOption> selectQuestionOptionList(PsyTQuestionOption questionOption) {
        return questionOptionMapper.selectOptionList(questionOption);
    }

    /**
     * 新增题目选项
     * 
     * @param questionOption 题目选项
     * @return 结果
     */
    @Override
    public int insertQuestionOption(PsyTQuestionOption questionOption) {
        // 设置创建信息
        questionOption.setCreateBy(SecurityUtils.getUsername());
        questionOption.setCreateTime(DateUtils.getNowDate());
        questionOption.setDelFlag(com.xihuan.common.core.domain.entity.PsyTQuestionOption.DEL_FLAG_NORMAL);
        
        // 如果没有设置显示顺序，自动设置为最大值+1
        if (questionOption.getOrderNum() == null) {
            Integer maxOrder = questionOptionMapper.selectMaxOrderNum(questionOption.getQuestionId());
            questionOption.setOrderNum(maxOrder + 1);
        }

        return questionOptionMapper.insertOption(questionOption);
    }

    /**
     * 修改题目选项
     * 
     * @param questionOption 题目选项
     * @return 结果
     */
    @Override
    public int updateQuestionOption(PsyTQuestionOption questionOption) {
        questionOption.setUpdateBy(SecurityUtils.getUsername());
        questionOption.setUpdateTime(DateUtils.getNowDate());
        return questionOptionMapper.updateOption(questionOption);
    }

    /**
     * 批量删除题目选项
     * 
     * @param ids 需要删除的题目选项主键
     * @return 结果
     */
    @Override
    public int deleteQuestionOptionByIds(Long[] ids) {
        return questionOptionMapper.deleteOptionByIds(ids);
    }

    /**
     * 删除题目选项信息
     *
     * @param id 题目选项主键
     * @return 结果
     */
    @Override
    public int deleteQuestionOptionById(Long id) {
        return questionOptionMapper.deleteOptionById(id);
    }

    /**
     * 根据题目ID查询选项列表
     * 
     * @param questionId 题目ID
     * @return 选项列表
     */
    @Override
    public List<PsyTQuestionOption> selectOptionsByQuestionId(Long questionId) {
        return questionOptionMapper.selectOptionsByQuestionId(questionId);
    }

    /**
     * 根据题目ID查询启用的选项列表
     * 
     * @param questionId 题目ID
     * @return 启用的选项列表
     */
    @Override
    public List<PsyTQuestionOption> selectEnabledOptionsByQuestionId(Long questionId) {
        return questionOptionMapper.selectEnabledOptionsByQuestionId(questionId);
    }

    /**
     * 根据题目ID删除所有选项
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    @Override
    public int deleteOptionsByQuestionId(Long questionId) {
        return questionOptionMapper.deleteOptionsByQuestionId(questionId);
    }

    /**
     * 批量插入选项
     * 
     * @param options 选项列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertOptions(List<PsyTQuestionOption> options) {
        if (options == null || options.isEmpty()) {
            return 0;
        }
        
        String username = SecurityUtils.getUsername();
        Date now = DateUtils.getNowDate();
        
        for (PsyTQuestionOption option : options) {
            option.setCreateBy(username);
            option.setCreateTime(now);
            option.setDelFlag(com.xihuan.common.core.domain.entity.PsyTQuestionOption.DEL_FLAG_NORMAL);
        }
        
        return questionOptionMapper.batchInsertOptions(options);
    }

    /**
     * 根据题目ID和选项值查询选项
     * 
     * @param questionId 题目ID
     * @param optionValue 选项值
     * @return 选项信息
     */
    @Override
    public PsyTQuestionOption selectOptionByValue(Long questionId, String optionValue) {
        return questionOptionMapper.selectOptionByValue(questionId, optionValue);
    }

    /**
     * 根据题目ID和选项文本查询选项
     * 
     * @param questionId 题目ID
     * @param optionText 选项文本
     * @return 选项信息
     */
    @Override
    public PsyTQuestionOption selectOptionByText(Long questionId, String optionText) {
        return questionOptionMapper.selectOptionByText(questionId, optionText);
    }

    /**
     * 查询题目的选项数量
     * 
     * @param questionId 题目ID
     * @return 选项数量
     */
    @Override
    public int countOptionsByQuestionId(Long questionId) {
        return questionOptionMapper.countOptionsByQuestionId(questionId);
    }

    /**
     * 查询题目的启用选项数量
     * 
     * @param questionId 题目ID
     * @return 启用选项数量
     */
    @Override
    public int countEnabledOptionsByQuestionId(Long questionId) {
        return questionOptionMapper.countEnabledOptionsByQuestionId(questionId);
    }

    /**
     * 更新选项的显示顺序
     * 
     * @param id 选项ID
     * @param orderNum 显示顺序
     * @return 结果
     */
    @Override
    public int updateOptionOrder(Long id, Integer orderNum) {
        return questionOptionMapper.updateOptionOrder(id, orderNum);
    }

    /**
     * 查询选项的最大显示顺序
     * 
     * @param questionId 题目ID
     * @return 最大显示顺序
     */
    @Override
    public Integer selectMaxOrderNum(Long questionId) {
        Integer maxOrder = questionOptionMapper.selectMaxOrderNum(questionId);
        return maxOrder != null ? maxOrder : 0;
    }

    /**
     * 根据量表ID查询所有选项
     * 
     * @param scaleId 量表ID
     * @return 选项列表
     */
    @Override
    public List<PsyTQuestionOption> selectOptionsByScaleId(Long scaleId) {
        return questionOptionMapper.selectOptionsByScaleId(scaleId);
    }

    /**
     * 查询选项统计信息
     *
     * @param questionId 题目ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectOptionStats(Long questionId) {
        return questionOptionMapper.selectOptionStats(questionId);
    }

    /**
     * 根据选项标识查询选项
     * 
     * @param questionId 题目ID
     * @param optionLabel 选项标识(A、B、C、D等)
     * @return 选项信息
     */
    @Override
    public PsyTQuestionOption selectOptionByLabel(Long questionId, String optionLabel) {
        return questionOptionMapper.selectOptionByLabel(questionId, optionLabel);
    }

    /**
     * 查询正确答案选项
     * 
     * @param questionId 题目ID
     * @return 正确答案选项列表
     */
    @Override
    public List<PsyTQuestionOption> selectCorrectOptions(Long questionId) {
        return questionOptionMapper.selectCorrectOptions(questionId);
    }

    /**
     * 更新选项状态
     * 
     * @param id 选项ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateOptionStatus(Long id, Integer status) {
        return questionOptionMapper.updateOptionStatus(id, status);
    }

    /**
     * 批量更新选项状态
     * 
     * @param ids 选项ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int batchUpdateOptionStatus(Long[] ids, Integer status) {
        return questionOptionMapper.batchUpdateOptionStatus(ids, status);
    }

    /**
     * 复制题目选项
     * 
     * @param sourceQuestionId 源题目ID
     * @param targetQuestionId 目标题目ID
     * @return 结果
     */
    @Override
    public int copyOptionsToQuestion(Long sourceQuestionId, Long targetQuestionId) {
        return questionOptionMapper.copyOptionsToQuestion(sourceQuestionId, targetQuestionId);
    }

    /**
     * 验证选项配置的完整性
     * 
     * @param questionId 题目ID
     * @return 验证结果
     */
    @Override
    public Map<String, Object> validateOptionConfig(Long questionId) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 检查选项数量
        int optionCount = countEnabledOptionsByQuestionId(questionId);
        if (optionCount < 2) {
            errors.add("选项数量不能少于2个");
        } else if (optionCount > 10) {
            warnings.add("选项数量过多，建议不超过10个");
        }
        
        // 检查选项内容
        List<PsyTQuestionOption> options = selectEnabledOptionsByQuestionId(questionId);
        Set<String> optionTexts = new HashSet<>();
        Set<String> optionValues = new HashSet<>();
        
        for (PsyTQuestionOption option : options) {
            // 检查重复文本
            if (optionTexts.contains(option.getOptionText())) {
                errors.add("存在重复的选项文本：" + option.getOptionText());
            }
            optionTexts.add(option.getOptionText());
            
            // 检查重复值
            if (optionValues.contains(String.valueOf(option.getOptionValue()))) {
                errors.add("存在重复的选项值：" + option.getOptionValue());
            }
            optionValues.add(String.valueOf(option.getOptionValue()));
            
            // 检查空内容
            if (option.getOptionText() == null || option.getOptionText().trim().isEmpty()) {
                errors.add("选项文本不能为空");
            }
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        
        return result;
    }

    /**
     * 导入选项配置
     * 
     * @param questionId 题目ID
     * @param options 选项列表
     * @return 导入结果
     */
    @Override
    @Transactional
    public Map<String, Object> importOptions(Long questionId, List<PsyTQuestionOption> options) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 先删除现有选项
            deleteOptionsByQuestionId(questionId);
            
            // 设置题目ID
            for (PsyTQuestionOption option : options) {
                option.setQuestionId(questionId);
            }
            
            // 批量插入
            int count = batchInsertOptions(options);
            
            result.put("success", true);
            result.put("count", count);
            result.put("message", "成功导入 " + count + " 个选项");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "导入失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 导出选项配置
     * 
     * @param questionId 题目ID
     * @return 选项列表
     */
    @Override
    public List<PsyTQuestionOption> exportOptions(Long questionId) {
        return selectOptionsByQuestionId(questionId);
    }

    /**
     * 自动生成选项标识
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    @Override
    @Transactional
    public int generateOptionLabels(Long questionId) {
        List<PsyTQuestionOption> options = selectOptionsByQuestionId(questionId);
        
        if (options.isEmpty()) {
            return 0;
        }
        
        // 按显示顺序排序
        options.sort(Comparator.comparing(option -> option.getOrderNum() != null ? option.getOrderNum() : 0));
        
        // 生成标识 A、B、C、D...
        String[] labels = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
        
        int count = 0;
        for (int i = 0; i < options.size() && i < labels.length; i++) {
            PsyTQuestionOption option = options.get(i);
            option.setOptionLabel(labels[i]);
            updateQuestionOption(option);
            count++;
        }
        
        return count;
    }

    /**
     * 重新排序选项
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    @Override
    @Transactional
    public int reorderOptions(Long questionId) {
        List<PsyTQuestionOption> options = selectOptionsByQuestionId(questionId);
        
        if (options.isEmpty()) {
            return 0;
        }
        
        // 按当前顺序重新编号
        options.sort(Comparator.comparing(option -> option.getOrderNum() != null ? option.getOrderNum() : 0));

        int count = 0;
        for (int i = 0; i < options.size(); i++) {
            PsyTQuestionOption option = options.get(i);
            int newOrder = (i + 1) * 10; // 10, 20, 30...
            if (!Integer.valueOf(newOrder).equals(option.getOrderNum())) {
                updateOptionOrder(option.getId(), newOrder);
                count++;
            }
        }
        
        return count;
    }
}
