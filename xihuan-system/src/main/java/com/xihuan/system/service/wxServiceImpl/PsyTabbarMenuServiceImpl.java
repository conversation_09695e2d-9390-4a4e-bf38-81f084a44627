package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.entity.PsyTabbarMenu;
import com.xihuan.system.mapper.PsyTabbarMenuMapper;
import com.xihuan.system.service.wxService.IPsyTabbarMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 心理咨询移动端导航菜单Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTabbarMenuServiceImpl implements IPsyTabbarMenuService {
    @Autowired
    private PsyTabbarMenuMapper menuMapper;

    /**
     * 查询菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单
     */
    @Override
    public List<PsyTabbarMenu> selectMenuList(PsyTabbarMenu menu) {
        return menuMapper.selectMenuList(menu);
    }

    /**
     * 根据菜单ID查询菜单详情
     *
     * @param id 菜单ID
     * @return 菜单信息
     */
    @Override
    public PsyTabbarMenu selectMenuById(Long id) {
        return menuMapper.selectMenuById(id);
    }

    /**
     * 根据权限查询菜单列表
     * 
     * @param permissions 权限列表
     * @return 菜单集合
     */
    @Override
    public List<PsyTabbarMenu> selectMenuByPermissions(List<String> permissions) {
        return menuMapper.selectMenuByPermissions(permissions);
    }

    /**
     * 新增菜单
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public int insertMenu(PsyTabbarMenu menu) {
        return menuMapper.insertMenu(menu);
    }

    /**
     * 修改菜单
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public int updateMenu(PsyTabbarMenu menu) {
        return menuMapper.updateMenu(menu);
    }

    /**
     * 批量删除菜单
     * 
     * @param ids 需要删除的菜单ID
     * @return 结果
     */
    @Override
    public int deleteMenuByIds(Long[] ids) {
        return menuMapper.deleteMenuByIds(ids);
    }

    /**
     * 删除菜单信息
     * 
     * @param id 菜单ID
     * @return 结果
     */
    @Override
    public int deleteMenuById(Long id) {
        return menuMapper.deleteMenuById(id);
    }
} 