package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTAnswerRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 答题记录Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTAnswerRecordService {
    
    /**
     * 查询答题记录
     * 
     * @param id 答题记录主键
     * @return 答题记录
     */
    public PsyTAnswerRecord selectAnswerRecordById(Long id);

    /**
     * 查询答题记录列表
     * 
     * @param answerRecord 答题记录
     * @return 答题记录集合
     */
    public List<PsyTAnswerRecord> selectAnswerRecordList(PsyTAnswerRecord answerRecord);

    /**
     * 新增答题记录
     * 
     * @param answerRecord 答题记录
     * @return 结果
     */
    public int insertAnswerRecord(PsyTAnswerRecord answerRecord);

    /**
     * 修改答题记录
     * 
     * @param answerRecord 答题记录
     * @return 结果
     */
    public int updateAnswerRecord(PsyTAnswerRecord answerRecord);

    /**
     * 批量删除答题记录
     * 
     * @param ids 需要删除的答题记录主键集合
     * @return 结果
     */
    public int deleteAnswerRecordByIds(Long[] ids);

    /**
     * 删除答题记录信息
     * 
     * @param id 答题记录主键
     * @return 结果
     */
    public int deleteAnswerRecordById(Long id);

    /**
     * 根据测评记录ID查询答题记录
     * 
     * @param recordId 测评记录ID
     * @return 答题记录集合
     */
    public List<PsyTAnswerRecord> selectAnswersByRecordId(Long recordId);

    /**
     * 根据测评记录ID和题目ID查询答题记录
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @return 答题记录
     */
    public PsyTAnswerRecord selectAnswerByRecordAndQuestion(Long recordId, Long questionId);

    /**
     * 保存答题记录
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @param optionId 选项ID（可为空）
     * @param answerContent 答案内容
     * @param answerScore 答案分数
     * @param responseTime 答题时间（秒）
     * @return 结果
     */
    public int saveAnswer(Long recordId, Long questionId, Long optionId, String answerContent, BigDecimal answerScore, Integer responseTime);

    /**
     * 批量保存答题记录
     * 
     * @param answers 答题记录列表
     * @return 结果
     */
    public int batchSaveAnswers(List<PsyTAnswerRecord> answers);

    /**
     * 根据测评记录ID删除所有答题记录
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    public int deleteAnswersByRecordId(Long recordId);

    /**
     * 计算测评记录的总分
     * 
     * @param recordId 测评记录ID
     * @return 总分
     */
    public BigDecimal calculateTotalScore(Long recordId);

    /**
     * 计算测评记录的维度分数
     * 
     * @param recordId 测评记录ID
     * @return 维度分数映射
     */
    public Map<String, BigDecimal> calculateDimensionScores(Long recordId);

    /**
     * 查询答题统计信息
     * 
     * @param recordId 测评记录ID
     * @return 统计信息
     */
    public Map<String, Object> selectAnswerStats(Long recordId);

    /**
     * 查询用户答题统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectUserAnswerStats(Long userId);

    /**
     * 查询题目答题统计
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    public Map<String, Object> selectQuestionAnswerStats(Long questionId);

    /**
     * 查询选项选择统计
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    public List<Map<String, Object>> selectOptionChoiceStats(Long questionId);

    /**
     * 查询答题进度
     *
     * @param recordId 测评记录ID
     * @return 进度信息
     */
    public Map<String, Object> selectAnswerProgress(Long recordId);

    /**
     * 查询答题历史记录
     *
     * @param recordId 测评记录ID
     * @return 答题历史记录列表
     */
    public List<Map<String, Object>> selectAnswerHistoryByRecordId(Long recordId);

    /**
     * 检查是否已完成答题
     *
     * @param recordId 测评记录ID
     * @return 是否完成
     */
    public boolean isAnswerCompleted(Long recordId);

    /**
     * 获取下一题
     * 
     * @param recordId 测评记录ID
     * @return 下一题信息
     */
    public Map<String, Object> getNextQuestion(Long recordId);

    /**
     * 获取上一题
     * 
     * @param recordId 测评记录ID
     * @return 上一题信息
     */
    public Map<String, Object> getPreviousQuestion(Long recordId);

    /**
     * 验证答案的有效性
     * 
     * @param questionId 题目ID
     * @param optionId 选项ID
     * @param answerContent 答案内容
     * @return 验证结果
     */
    public Map<String, Object> validateAnswer(Long questionId, Long optionId, String answerContent);

    /**
     * 自动计算答案分数
     * 
     * @param questionId 题目ID
     * @param optionId 选项ID
     * @param answerContent 答案内容
     * @return 分数
     */
    public BigDecimal calculateAnswerScore(Long questionId, Long optionId, String answerContent);

    /**
     * 导出答题记录
     * 
     * @param recordId 测评记录ID
     * @return 答题记录列表
     */
    public List<PsyTAnswerRecord> exportAnswers(Long recordId);

    /**
     * 查询答题时间分析
     * 
     * @param recordId 测评记录ID
     * @return 时间分析
     */
    public Map<String, Object> selectAnswerTimeAnalysis(Long recordId);

    /**
     * 查询答题模式分析
     * 
     * @param recordId 测评记录ID
     * @return 模式分析
     */
    public Map<String, Object> selectAnswerPatternAnalysis(Long recordId);

    /**
     * 重新计算所有分数
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    public int recalculateScores(Long recordId);

    /**
     * 查询异常答题记录
     * 
     * @param recordId 测评记录ID
     * @return 异常记录列表
     */
    public List<PsyTAnswerRecord> selectAbnormalAnswers(Long recordId);
}
