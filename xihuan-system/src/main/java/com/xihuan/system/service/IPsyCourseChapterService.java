package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyCourseChapter;

import java.util.List;

/**
 * 课程章节内容表Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyCourseChapterService {
    
    /**
     * 查询章节列表
     * 
     * @param chapter 章节信息
     * @return 章节集合
     */
    List<PsyCourseChapter> selectChapterList(PsyCourseChapter chapter);

    /**
     * 根据ID查询章节
     * 
     * @param id 章节ID
     * @return 章节信息
     */
    PsyCourseChapter selectChapterById(Long id);

    /**
     * 根据课程ID查询章节列表
     * 
     * @param courseId 课程ID
     * @return 章节集合
     */
    List<PsyCourseChapter> selectChaptersByCourseId(Long courseId);

    /**
     * 根据课程ID查询章节树结构
     * 
     * @param courseId 课程ID
     * @return 章节树结构
     */
    List<PsyCourseChapter> selectChapterTreeByCourseId(Long courseId);

    /**
     * 新增章节
     * 
     * @param chapter 章节信息
     * @return 结果
     */
    int insertChapter(PsyCourseChapter chapter);

    /**
     * 修改章节
     * 
     * @param chapter 章节信息
     * @return 结果
     */
    int updateChapter(PsyCourseChapter chapter);

    /**
     * 删除章节
     * 
     * @param id 章节ID
     * @return 结果
     */
    int deleteChapterById(Long id);

    /**
     * 批量删除章节
     * 
     * @param ids 需要删除的章节ID
     * @return 结果
     */
    int deleteChapterByIds(Long[] ids);

    /**
     * 更新章节排序
     *
     * @param id 章节ID
     * @param chapterOrder 排序值
     * @return 结果
     */
    int updateChapterOrder(Long id, Integer chapterOrder);

    /**
     * 获取所有章节（仅返回id、父id、名称）
     *
     * @return 章节集合
     */
    List<PsyCourseChapter> selectAllChapters();
}
