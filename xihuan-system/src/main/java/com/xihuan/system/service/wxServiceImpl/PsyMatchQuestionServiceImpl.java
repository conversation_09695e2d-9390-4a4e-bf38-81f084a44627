package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.consultant.PsyDictExpertise;
import com.xihuan.common.core.domain.dto.MatchQuestionDTO;
import com.xihuan.common.core.domain.entity.PsyMatchQuestion;
import com.xihuan.common.core.domain.entity.PsyMatchQuestionOption;
import com.xihuan.system.mapper.PsyConsultantMapper;
import com.xihuan.system.mapper.PsyMatchQuestionMapper;
import com.xihuan.system.service.wxService.IPsyDictExpertiseService;
import com.xihuan.system.service.wxService.IPsyMatchQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 极速匹配问题Service实现类
 */
@Service
public class PsyMatchQuestionServiceImpl implements IPsyMatchQuestionService {
    
    @Autowired
    private PsyMatchQuestionMapper questionMapper;
    
    @Autowired
    private IPsyDictExpertiseService psyDictExpertiseService;

    @Autowired
    private PsyConsultantMapper consultantMapper;
    
    /**
     * 查询问题列表（包含选项）
     */
    @Override
    public List<PsyMatchQuestion> selectQuestionList() {
        return selectQuestionList(null);
    }

    /**
     * 查询问题列表（包含选项，支持搜索）
     */
    @Override
    public List<PsyMatchQuestion> selectQuestionList(String title) {
        // 使用简化查询避免JOIN问题，然后分步查询选项
        List<PsyMatchQuestion> questions = questionMapper.selectQuestionListSimple(title);

        // 为每个问题单独查询选项
        for (PsyMatchQuestion question : questions) {
            if (question.getId() != null && (question.getId() == 6L || question.getId() == 7L)) {
                // ID为6和7的问题使用特殊处理（从咨询领域获取选项）
                continue; // 后面的特殊处理逻辑会处理这些
            } else {
                // 查询普通问题的选项
                PsyMatchQuestion fullQuestion = questionMapper.selectById(question.getId());
                if (fullQuestion != null && fullQuestion.getOptions() != null) {
                    question.setOptions(fullQuestion.getOptions());
                }
            }
        }
        
        // 处理ID为6和7的问题
        for (PsyMatchQuestion question : questions) {
            if (question.getId() != null && (question.getId() == 6L || question.getId() == 7L)) {
                List<PsyDictExpertise> expertiseList;
                if (question.getId() == 6L) {
                    // ID为6时查询父节点
                    expertiseList = psyDictExpertiseService.selectParentExpertiseList();
                } else {
                    // ID为7时查询子节点
                    expertiseList = psyDictExpertiseService.selectChildExpertiseList();
                }
                // 将咨询领域数据转换为选项
                List<PsyMatchQuestionOption> options = expertiseList.stream()
                    .map(expertise -> {
                        PsyMatchQuestionOption option = new PsyMatchQuestionOption();
                        option.setQuestionId(question.getId());
                        option.setOptionText(expertise.getTypeName());
                        option.setValueCode(String.valueOf(expertise.getId()));
                        option.setSort(expertise.getSort());
                        option.setId(expertise.getId());
                        // 如果是子节点，添加父级信息用于显示
                        if (expertise.getParentId() != null) {
                            PsyDictExpertise parent = psyDictExpertiseService.selectPsyDictExpertiseById(expertise.getParentId());
                            if (parent != null) {
                                option.setRecommendTag("父类: " + parent.getTypeName());
                                option.setParentId(parent.getId());
                            }
                        }
                        // 查询关联的咨询师ID列表
                        option.setConsultantIds(questionMapper.selectConsultantIdsByOptionId(option.getId()));
                        return option;
                    })
                    .collect(Collectors.toList());
                question.setOptions(options);
            } else if (question.getOptions() != null) {
                // 为其他问题的选项查询关联的咨询师ID列表
                for (PsyMatchQuestionOption option : question.getOptions()) {
                    option.setConsultantIds(questionMapper.selectConsultantIdsByOptionId(option.getId()));
                }
            }
        }
        
        return questions;
    }
    
    /**
     * 新增问题
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addQuestion(PsyMatchQuestion question) {
        boolean result = questionMapper.insert(question) > 0;
        if (result && question.getOptions() != null && !question.getOptions().isEmpty()) {
            // 逐个插入选项，获取新生成的ID
            for (PsyMatchQuestionOption option : question.getOptions()) {
                // 保存咨询师ID列表
                List<Long> consultantIds = option.getConsultantIds();

                // 确保选项ID为空，让数据库生成新ID
                option.setId(null);
                option.setQuestionId(question.getId());

                // 插入选项，MyBatis会自动设置新生成的ID到option.id
                questionMapper.insertOption(option);

                // 调试：打印新生成的选项ID
                System.out.println("新插入的选项ID: " + option.getId() + ", 选项文本: " + option.getOptionText());

                // 使用新生成的选项ID关联咨询师
                if (consultantIds != null && !consultantIds.isEmpty()) {
                    System.out.println("关联咨询师到选项ID: " + option.getId() + ", 咨询师IDs: " + consultantIds);
                    questionMapper.batchInsertOptionConsultants(option.getId(), consultantIds);
                }
            }
        }
        return result;
    }
    
    /**
     * 修改问题
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestion(PsyMatchQuestion question) {
        // 如果是ID为6或7的问题，不处理选项
        if (question.getId() != null && (question.getId() == 6L || question.getId() == 7L)) {
            return questionMapper.update(question) > 0;
        }

        // 删除旧的选项及其关联
        List<PsyMatchQuestionOption> oldOptions = questionMapper.selectById(question.getId()).getOptions();
        if (oldOptions != null) {
            for (PsyMatchQuestionOption option : oldOptions) {
                questionMapper.deleteOptionConsultants(option.getId());
            }
        }
        questionMapper.deleteQuestionOptionsByQuestionId(question.getId());

        // 更新问题基本信息
        boolean updateResult = questionMapper.update(question) > 0;

        // 插入新的选项
        if (question.getOptions() != null && !question.getOptions().isEmpty()) {
            // 先插入选项，获取新的选项ID
            for (PsyMatchQuestionOption option : question.getOptions()) {
                // 保存咨询师ID列表
                List<Long> consultantIds = option.getConsultantIds();

                // 调试：打印插入前的状态
                System.out.println("插入前 - 选项文本: " + option.getOptionText() + ", 旧ID: " + option.getId() + ", 咨询师IDs: " + consultantIds);

                // 清除旧的ID，确保插入时生成新ID
                option.setId(null);
                option.setQuestionId(question.getId());

                // 插入选项，MyBatis会自动设置新生成的ID到option.id
                questionMapper.insertOption(option);

                // 调试：打印新生成的选项ID
                System.out.println("插入后 - 新选项ID: " + option.getId() + ", 选项文本: " + option.getOptionText());

                // 使用新生成的选项ID关联咨询师
                if (consultantIds != null && !consultantIds.isEmpty()) {
                    System.out.println("关联咨询师到选项ID: " + option.getId() + ", 咨询师IDs: " + consultantIds);
                    questionMapper.batchInsertOptionConsultants(option.getId(), consultantIds);
                }
            }
        }

        return updateResult;
    }
    
    /**
     * 删除问题
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQuestion(Long questionId) {
        // 删除选项关联的咨询师
        List<PsyMatchQuestionOption> options = questionMapper.selectById(questionId).getOptions();
        if (options != null) {
            for (PsyMatchQuestionOption option : options) {
                questionMapper.deleteOptionConsultants(option.getId());
            }
        }
        return questionMapper.deleteById(questionId) > 0;
    }

    /**
     * 批量删除问题
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQuestions(Long[] questionIds) {
        if (questionIds == null || questionIds.length == 0) {
            return false;
        }

        int successCount = 0;
        for (Long questionId : questionIds) {
            if (deleteQuestion(questionId)) {
                successCount++;
            }
        }

        // 如果所有删除都成功，返回true
        return successCount == questionIds.length;
    }

    /**
     * 根据ID查询问题
     */
    @Override
    public PsyMatchQuestion getQuestion(Long questionId) {
        PsyMatchQuestion question = questionMapper.selectById(questionId);
        
        // 特殊处理ID为6和7的问题
        if (questionId != null && (questionId == 6L || questionId == 7L)) {
            List<PsyDictExpertise> expertiseList;
            if (questionId == 6L) {
                // ID为6时查询父节点
                expertiseList = psyDictExpertiseService.selectParentExpertiseList();
            } else {
                // ID为7时查询子节点
                expertiseList = psyDictExpertiseService.selectChildExpertiseList();
            }
            // 将咨询领域数据转换为选项
            List<PsyMatchQuestionOption> options = expertiseList.stream()
                .map(expertise -> {
                    PsyMatchQuestionOption option = new PsyMatchQuestionOption();
                    option.setQuestionId(questionId);
                    option.setOptionText(expertise.getTypeName());
                    option.setValueCode(String.valueOf(expertise.getId()));
                    option.setSort(expertise.getSort());
                    option.setId(expertise.getId());
                    // 如果是子节点，添加父级信息用于显示
                    if (expertise.getParentId() != null) {
                        PsyDictExpertise parent = psyDictExpertiseService.selectPsyDictExpertiseById(expertise.getParentId());
                        if (parent != null) {
                            option.setRecommendTag("父类: " + parent.getTypeName());
                        }
                    }
                    // 查询关联的咨询师ID列表
                    option.setConsultantIds(questionMapper.selectConsultantIdsByOptionId(option.getId()));
                    return option;
                })
                .collect(Collectors.toList());
            question.setOptions(options);
        } else if (question != null && question.getOptions() != null) {
            // 为其他问题的选项查询关联的咨询师ID列表
            for (PsyMatchQuestionOption option : question.getOptions()) {
                option.setConsultantIds(questionMapper.selectConsultantIdsByOptionId(option.getId()));
            }
        }
        
        return question;
    }

    /**
     * 根据选项筛选匹配的咨询师
     */
    @Override
    public List<PsyConsultant> matchConsultants(List<MatchQuestionDTO> questions) {
        if (questions == null || questions.isEmpty()) {
            return consultantMapper.selectAllConsultantsWithDetails();
        }

        // 存储所有选项ID（包括性别、价格、咨询经历等）
        List<Long> allOptionIds = new ArrayList<>();
        // 存储咨询领域ID
        List<Long> expertiseIds = new ArrayList<>();
        // 性别选项ID
        Long genderOptionId = null;
        
        // 提取所有选项ID
        for (MatchQuestionDTO question : questions) {
            if (question.getQuestionId() == 5) { // 性别选择问题
                if (question.getOptionId() != null) {
                    genderOptionId = Long.valueOf(question.getOptionId().toString());
                    allOptionIds.add(genderOptionId); // 将性别选项ID添加到所有选项列表
                }
            } else if (question.getQuestionId() == 2) { // 价格范围问题
                if (question.getOptionId() != null) {
                    Long priceOptionId = Long.valueOf(question.getOptionId().toString());
                    allOptionIds.add(priceOptionId); // 将价格选项ID添加到所有选项列表
                }
            } else if (question.getQuestionId() == 4) { // 咨询经历问题
                if (question.getOptionId() != null) {
                    Long optionId = Long.valueOf(question.getOptionId().toString());
                    allOptionIds.add(optionId);
                }
            } else if (question.getQuestionId() == 6 || question.getQuestionId() == 7) { // 咨询领域问题
                if (question.getOptionId() instanceof List) {
                    List<?> ids = (List<?>) question.getOptionId();
                    if (!ids.isEmpty()) {
                        ids.forEach(id -> expertiseIds.add(Long.valueOf(id.toString())));
                    }
                } else if (question.getOptionId() != null) {
                    expertiseIds.add(Long.valueOf(question.getOptionId().toString()));
                }
            } else if (question.getOptionId() != null) { // 其他问题的选项
                // 对于其他问题，如果有选项也添加到选项列表中
                if (question.getOptionId() instanceof List) {
                    List<?> ids = (List<?>) question.getOptionId();
                    if (!ids.isEmpty()) {
                        ids.forEach(id -> allOptionIds.add(Long.valueOf(id.toString())));
                    }
                } else {
                    allOptionIds.add(Long.valueOf(question.getOptionId().toString()));
                }
            }
        }

        // 先查询是否有性别选项，如果有优先根据性别筛选
        if (genderOptionId != null) {
            // 这里只使用性别选项ID进行查询，确保性别是第一优先级
            List<PsyConsultant> consultantsByGender = consultantMapper.selectByOptionIds(
                Arrays.asList(genderOptionId), 
                1, 
                null, 
                0
            );
            
            // 如果找不到符合性别条件的咨询师，直接返回所有咨询师
            if (consultantsByGender == null || consultantsByGender.isEmpty()) {
                return consultantMapper.selectAllConsultantsWithDetails();
            }
            
            // 如果有其他选项或领域限制，进一步筛选
            if ((allOptionIds.size() > 1 || !expertiseIds.isEmpty()) && !consultantsByGender.isEmpty()) {
                // 去除性别选项ID，以便下一步筛选
                List<Long> otherOptionIds = new ArrayList<>(allOptionIds);
                otherOptionIds.remove(genderOptionId);
                
                // 获取符合性别条件的咨询师ID列表
                List<Long> genderFilteredConsultantIds = consultantsByGender.stream()
                    .map(PsyConsultant::getId)
                    .collect(Collectors.toList());
                
                // 如果还有其他选项，继续筛选
                if (!otherOptionIds.isEmpty()) {
                    // 在满足性别条件的咨询师中，继续筛选满足其他选项的咨询师
                    List<PsyConsultant> result = consultantMapper.selectByOptionIdsAndConsultantIds(
                        otherOptionIds, 
                        otherOptionIds.size(), 
                        genderFilteredConsultantIds
                    );
                    
                    // 如果有咨询领域条件，继续筛选
                    if (!expertiseIds.isEmpty() && result != null && !result.isEmpty()) {
                        List<Long> filteredConsultantIds = result.stream()
                            .map(PsyConsultant::getId)
                            .collect(Collectors.toList());
                        
                        result = consultantMapper.selectByExpertiseIdsAndConsultantIds(
                            expertiseIds, 
                            1, 
                            filteredConsultantIds
                        );
                        
                        return result != null && !result.isEmpty() ? result : consultantsByGender;
                    }
                    
                    return result != null && !result.isEmpty() ? result : consultantsByGender;
                } 
                // 如果只有咨询领域条件
                else if (!expertiseIds.isEmpty()) {
                    List<PsyConsultant> result = consultantMapper.selectByExpertiseIdsAndConsultantIds(
                        expertiseIds, 
                        1, 
                        genderFilteredConsultantIds
                    );
                    
                    return result != null && !result.isEmpty() ? result : consultantsByGender;
                }
            }
            
            // 只有性别条件时，直接返回符合性别的咨询师
            return consultantsByGender;
        }
        
        // 如果没有性别选项，但有其他选项
        if (!allOptionIds.isEmpty()) {
            List<PsyConsultant> result = consultantMapper.selectByOptionIds(
                allOptionIds, 
                allOptionIds.size(), 
                null, 
                0
            );
            
            // 如果有咨询领域条件，继续筛选
            if (!expertiseIds.isEmpty() && result != null && !result.isEmpty()) {
                List<Long> filteredConsultantIds = result.stream()
                    .map(PsyConsultant::getId)
                    .collect(Collectors.toList());
                
                result = consultantMapper.selectByExpertiseIdsAndConsultantIds(
                    expertiseIds, 
                    1, 
                    filteredConsultantIds
                );
                
                return result != null && !result.isEmpty() ? result : consultantMapper.selectAllConsultantsWithDetails();
            }
            
            return result != null && !result.isEmpty() ? result : consultantMapper.selectAllConsultantsWithDetails();
        }
        
        // 如果只有咨询领域条件
        if (!expertiseIds.isEmpty()) {
            List<PsyConsultant> result = consultantMapper.selectByExpertiseIds(
                expertiseIds, 
                1
            );
            
            return result != null && !result.isEmpty() ? result : consultantMapper.selectAllConsultantsWithDetails();
        }
        
        // 如果没有任何有效条件，返回所有咨询师
        return consultantMapper.selectAllConsultantsWithDetails();
    }

    /**
     * 更新选项关联的咨询师
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOptionConsultants(Long optionId, List<Long> consultantIds) {
        // 先删除原有关联
        questionMapper.deleteOptionConsultants(optionId);
        
        // 如果有新的关联，则添加
        if (consultantIds != null && !consultantIds.isEmpty()) {
            return questionMapper.batchInsertOptionConsultants(optionId, consultantIds) > 0;
        }
        
        return true;
    }

    /**
     * 获取选项关联的咨询师ID列表
     */
    @Override
    public List<Long> getOptionConsultants(Long optionId) {
        return questionMapper.selectConsultantIdsByOptionId(optionId);
    }
} 