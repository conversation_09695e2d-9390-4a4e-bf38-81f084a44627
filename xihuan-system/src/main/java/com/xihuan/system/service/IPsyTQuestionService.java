package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTQuestion;

import java.util.List;
import java.util.Map;

/**
 * 题目Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTQuestionService {
    
    /**
     * 查询题目
     * 
     * @param id 题目主键
     * @return 题目
     */
    public PsyTQuestion selectQuestionById(Long id);

    /**
     * 查询题目列表
     * 
     * @param question 题目
     * @return 题目集合
     */
    public List<PsyTQuestion> selectQuestionList(PsyTQuestion question);

    /**
     * 新增题目
     * 
     * @param question 题目
     * @return 结果
     */
    public int insertQuestion(PsyTQuestion question);

    /**
     * 修改题目
     * 
     * @param question 题目
     * @return 结果
     */
    public int updateQuestion(PsyTQuestion question);

    /**
     * 批量删除题目
     * 
     * @param ids 需要删除的题目主键集合
     * @return 结果
     */
    public int deleteQuestionByIds(Long[] ids);

    /**
     * 删除题目信息
     * 
     * @param id 题目主键
     * @return 结果
     */
    public int deleteQuestionById(Long id);

    /**
     * 根据量表ID查询题目列表
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    public List<PsyTQuestion> selectQuestionsByScaleId(Long scaleId);

    /**
     * 根据量表ID查询题目列表（包含选项信息）
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    public List<PsyTQuestion> selectQuestionsWithOptionsByScaleId(Long scaleId);

    /**
     * 查询题目详情（包含选项信息）
     * 
     * @param id 题目ID
     * @return 题目详情
     */
    public PsyTQuestion selectQuestionWithOptions(Long id);

    /**
     * 根据题目序号查询题目
     * 
     * @param scaleId 量表ID
     * @param questionNo 题目序号
     * @return 题目信息
     */
    public PsyTQuestion selectQuestionByNo(Long scaleId, Integer questionNo);

    /**
     * 根据量表ID删除所有题目
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    public int deleteQuestionsByScaleId(Long scaleId);

    /**
     * 批量插入题目
     * 
     * @param questions 题目列表
     * @return 结果
     */
    public int batchInsertQuestions(List<PsyTQuestion> questions);

    /**
     * 查询题目数量
     * 
     * @param scaleId 量表ID
     * @return 题目数量
     */
    public int countQuestionsByScaleId(Long scaleId);

    /**
     * 查询启用的题目数量
     * 
     * @param scaleId 量表ID
     * @return 启用题目数量
     */
    public int countEnabledQuestionsByScaleId(Long scaleId);

    /**
     * 更新题目的显示顺序
     * 
     * @param id 题目ID
     * @param orderNum 显示顺序
     * @return 结果
     */
    public int updateQuestionOrder(Long id, Integer orderNum);

    /**
     * 查询题目的最大显示顺序
     * 
     * @param scaleId 量表ID
     * @return 最大显示顺序
     */
    public Integer selectMaxOrderNum(Long scaleId);

    /**
     * 查询题目统计信息
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    public Map<String, Object> selectQuestionStats(Long scaleId);

    /**
     * 复制题目
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    public int copyQuestions(Long sourceScaleId, Long targetScaleId);

    /**
     * 导入题目
     * 
     * @param scaleId 量表ID
     * @param questions 题目列表
     * @return 导入结果
     */
    public Map<String, Object> importQuestions(Long scaleId, List<PsyTQuestion> questions);

    /**
     * 导出题目
     * 
     * @param scaleId 量表ID
     * @return 题目列表
     */
    public List<PsyTQuestion> exportQuestions(Long scaleId);

    /**
     * 验证题目配置的完整性
     * 
     * @param scaleId 量表ID
     * @return 验证结果
     */
    public Map<String, Object> validateQuestionConfig(Long scaleId);

    /**
     * 自动生成题目序号
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    public int generateQuestionNumbers(Long scaleId);

    /**
     * 重新排序题目
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    public int reorderQuestions(Long scaleId);

    /**
     * 更新题目状态
     * 
     * @param id 题目ID
     * @param status 状态
     * @return 结果
     */
    public int updateQuestionStatus(Long id, Integer status);

    /**
     * 批量更新题目状态
     * 
     * @param ids 题目ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateQuestionStatus(Long[] ids, Integer status);

    /**
     * 根据分量表查询题目
     * 
     * @param subscaleId 分量表ID
     * @return 题目集合
     */
    public List<PsyTQuestion> selectQuestionsBySubscaleId(Long subscaleId);

    /**
     * 查询必答题目
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    public List<PsyTQuestion> selectRequiredQuestions(Long scaleId);

    /**
     * 查询选答题目
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    public List<PsyTQuestion> selectOptionalQuestions(Long scaleId);

    /**
     * 随机获取题目
     * 
     * @param scaleId 量表ID
     * @param count 题目数量
     * @return 题目集合
     */
    public List<PsyTQuestion> selectRandomQuestions(Long scaleId, Integer count);
}
