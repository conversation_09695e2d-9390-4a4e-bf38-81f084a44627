package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTInterpretation;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.system.mapper.PsyTInterpretationMapper;
import com.xihuan.system.service.IPsyTInterpretationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 测评结果解释Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTInterpretationServiceImpl implements IPsyTInterpretationService {
    
    @Autowired
    private PsyTInterpretationMapper interpretationMapper;

    /**
     * 查询测评结果解释
     * 
     * @param id 测评结果解释主键
     * @return 测评结果解释
     */
    @Override
    public PsyTInterpretation selectInterpretationById(Long id) {
        return interpretationMapper.selectInterpretationById(id);
    }

    /**
     * 查询测评结果解释列表
     * 
     * @param interpretation 测评结果解释
     * @return 测评结果解释
     */
    @Override
    public List<PsyTInterpretation> selectInterpretationList(PsyTInterpretation interpretation) {
        return interpretationMapper.selectInterpretationList(interpretation);
    }

    /**
     * 新增测评结果解释
     * 
     * @param interpretation 测评结果解释
     * @return 结果
     */
    @Override
    public int insertInterpretation(PsyTInterpretation interpretation) {
        // 设置创建信息
        interpretation.setCreateBy(SecurityUtils.getUsername());
        interpretation.setCreateTime(DateUtils.getNowDate());
        interpretation.setDelFlag(PsyTInterpretation.DEL_FLAG_NORMAL);
        
        // 如果没有设置显示顺序，自动设置为最大值+1
        if (interpretation.getOrderNum() == null) {
            Integer maxOrder = interpretationMapper.selectMaxOrderNum(interpretation.getScaleId(), interpretation.getDimension());
            interpretation.setOrderNum(maxOrder + 1);
        }
        
        return interpretationMapper.insertInterpretation(interpretation);
    }

    /**
     * 修改测评结果解释
     * 
     * @param interpretation 测评结果解释
     * @return 结果
     */
    @Override
    public int updateInterpretation(PsyTInterpretation interpretation) {
        interpretation.setUpdateBy(SecurityUtils.getUsername());
        interpretation.setUpdateTime(DateUtils.getNowDate());
        return interpretationMapper.updateInterpretation(interpretation);
    }

    /**
     * 批量删除测评结果解释
     * 
     * @param ids 需要删除的测评结果解释主键
     * @return 结果
     */
    @Override
    public int deleteInterpretationByIds(Long[] ids) {
        return interpretationMapper.deleteInterpretationByIds(ids);
    }

    /**
     * 删除测评结果解释信息
     * 
     * @param id 测评结果解释主键
     * @return 结果
     */
    @Override
    public int deleteInterpretationById(Long id) {
        return interpretationMapper.deleteInterpretationById(id);
    }

    /**
     * 根据量表ID查询解释列表
     * 
     * @param scaleId 量表ID
     * @return 解释列表
     */
    @Override
    public List<PsyTInterpretation> selectInterpretationsByScaleId(Long scaleId) {
        return interpretationMapper.selectInterpretationsByScaleId(scaleId);
    }

    /**
     * 根据量表ID和维度查询解释列表
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称
     * @return 解释列表
     */
    @Override
    public List<PsyTInterpretation> selectInterpretationsByScaleAndDimension(Long scaleId, String dimension) {
        return interpretationMapper.selectInterpretationsByScaleAndDimension(scaleId, dimension);
    }

    /**
     * 根据分数查询匹配的解释
     * 
     * @param scaleId 量表ID
     * @param score 分数
     * @param dimension 维度名称（可为空，表示总分）
     * @return 匹配的解释
     */
    @Override
    public PsyTInterpretation selectInterpretationByScore(Long scaleId, BigDecimal score, String dimension) {
        return interpretationMapper.selectInterpretationByScore(scaleId, score, dimension);
    }

    /**
     * 根据量表ID查询总分解释列表
     * 
     * @param scaleId 量表ID
     * @return 总分解释列表
     */
    @Override
    public List<PsyTInterpretation> selectTotalScoreInterpretations(Long scaleId) {
        return interpretationMapper.selectTotalScoreInterpretations(scaleId);
    }

    /**
     * 根据量表ID查询维度解释列表
     * 
     * @param scaleId 量表ID
     * @return 维度解释列表
     */
    @Override
    public List<PsyTInterpretation> selectDimensionInterpretations(Long scaleId) {
        return interpretationMapper.selectDimensionInterpretations(scaleId);
    }

    /**
     * 查询量表的所有维度
     * 
     * @param scaleId 量表ID
     * @return 维度列表
     */
    @Override
    public List<String> selectDimensionsByScaleId(Long scaleId) {
        return interpretationMapper.selectDimensionsByScaleId(scaleId);
    }

    /**
     * 检查分数范围是否重叠
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @param excludeId 排除的解释ID（用于更新时排除自己）
     * @return 是否重叠
     */
    @Override
    public boolean checkScoreRangeOverlap(Long scaleId, String dimension, BigDecimal minScore, BigDecimal maxScore, Long excludeId) {
        int count = interpretationMapper.checkScoreRangeOverlap(scaleId, dimension, minScore, maxScore, excludeId);
        return count > 0;
    }

    /**
     * 根据量表ID删除所有解释
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    @Override
    public int deleteInterpretationsByScaleId(Long scaleId) {
        return interpretationMapper.deleteInterpretationsByScaleId(scaleId);
    }

    /**
     * 批量插入解释
     * 
     * @param interpretations 解释列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertInterpretations(List<PsyTInterpretation> interpretations) {
        if (interpretations == null || interpretations.isEmpty()) {
            return 0;
        }
        
        String username = SecurityUtils.getUsername();
        Date now = DateUtils.getNowDate();
        
        for (PsyTInterpretation interpretation : interpretations) {
            interpretation.setCreateBy(username);
            interpretation.setCreateTime(now);
            interpretation.setDelFlag(PsyTInterpretation.DEL_FLAG_NORMAL);
        }
        
        return interpretationMapper.batchInsertInterpretations(interpretations);
    }

    /**
     * 查询解释统计信息
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectInterpretationStats(Long scaleId) {
        return interpretationMapper.selectInterpretationStats(scaleId);
    }

    /**
     * 根据等级名称查询解释
     * 
     * @param scaleId 量表ID
     * @param levelName 等级名称
     * @param dimension 维度名称
     * @return 解释信息
     */
    @Override
    public PsyTInterpretation selectInterpretationByLevel(Long scaleId, String levelName, String dimension) {
        return interpretationMapper.selectInterpretationByLevel(scaleId, levelName, dimension);
    }

    /**
     * 更新解释的显示顺序
     * 
     * @param id 解释ID
     * @param orderNum 显示顺序
     * @return 结果
     */
    @Override
    public int updateInterpretationOrder(Long id, Integer orderNum) {
        return interpretationMapper.updateInterpretationOrder(id, orderNum);
    }

    /**
     * 查询解释的最大显示顺序
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称
     * @return 最大显示顺序
     */
    @Override
    public Integer selectMaxOrderNum(Long scaleId, String dimension) {
        Integer maxOrder = interpretationMapper.selectMaxOrderNum(scaleId, dimension);
        return maxOrder != null ? maxOrder : 0;
    }

    /**
     * 根据分数获取解释结果
     * 
     * @param scaleId 量表ID
     * @param totalScore 总分
     * @param dimensionScores 维度分数（可为空）
     * @return 解释结果
     */
    @Override
    public Map<String, Object> getInterpretationResult(Long scaleId, BigDecimal totalScore, Map<String, BigDecimal> dimensionScores) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取总分解释
        PsyTInterpretation totalInterpretation = selectInterpretationByScore(scaleId, totalScore, null);
        if (totalInterpretation != null) {
            result.put("totalInterpretation", totalInterpretation);
        }
        
        // 获取维度解释
        if (dimensionScores != null && !dimensionScores.isEmpty()) {
            Map<String, PsyTInterpretation> dimensionInterpretations = new HashMap<>();
            for (Map.Entry<String, BigDecimal> entry : dimensionScores.entrySet()) {
                String dimension = entry.getKey();
                BigDecimal score = entry.getValue();
                PsyTInterpretation interpretation = selectInterpretationByScore(scaleId, score, dimension);
                if (interpretation != null) {
                    dimensionInterpretations.put(dimension, interpretation);
                }
            }
            result.put("dimensionInterpretations", dimensionInterpretations);
        }
        
        return result;
    }

    /**
     * 验证解释配置的完整性
     * 
     * @param scaleId 量表ID
     * @return 验证结果
     */
    @Override
    public Map<String, Object> validateInterpretationConfig(Long scaleId) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 检查总分解释
        List<PsyTInterpretation> totalInterpretations = selectTotalScoreInterpretations(scaleId);
        if (totalInterpretations.isEmpty()) {
            errors.add("缺少总分解释配置");
        } else {
            // 检查分数范围是否连续
            totalInterpretations.sort(Comparator.comparing(PsyTInterpretation::getMinScore));
            for (int i = 0; i < totalInterpretations.size() - 1; i++) {
                PsyTInterpretation current = totalInterpretations.get(i);
                PsyTInterpretation next = totalInterpretations.get(i + 1);
                if (current.getMaxScore().compareTo(next.getMinScore()) < 0) {
                    warnings.add("总分解释存在分数范围空隙：" + current.getMaxScore() + " - " + next.getMinScore());
                }
            }
        }
        
        // 检查维度解释
        List<String> dimensions = selectDimensionsByScaleId(scaleId);
        for (String dimension : dimensions) {
            List<PsyTInterpretation> dimensionInterpretations = selectInterpretationsByScaleAndDimension(scaleId, dimension);
            if (dimensionInterpretations.isEmpty()) {
                warnings.add("维度 '" + dimension + "' 缺少解释配置");
            }
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        
        return result;
    }

    /**
     * 导入解释配置
     * 
     * @param scaleId 量表ID
     * @param interpretations 解释列表
     * @return 导入结果
     */
    @Override
    @Transactional
    public Map<String, Object> importInterpretations(Long scaleId, List<PsyTInterpretation> interpretations) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 先删除现有解释
            deleteInterpretationsByScaleId(scaleId);
            
            // 设置量表ID
            for (PsyTInterpretation interpretation : interpretations) {
                interpretation.setScaleId(scaleId);
            }
            
            // 批量插入
            int count = batchInsertInterpretations(interpretations);
            
            result.put("success", true);
            result.put("count", count);
            result.put("message", "成功导入 " + count + " 条解释配置");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "导入失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 导出解释配置
     * 
     * @param scaleId 量表ID
     * @return 解释列表
     */
    @Override
    public List<PsyTInterpretation> exportInterpretations(Long scaleId) {
        return selectInterpretationsByScaleId(scaleId);
    }

    /**
     * 复制解释配置
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 复制结果
     */
    @Override
    @Transactional
    public int copyInterpretations(Long sourceScaleId, Long targetScaleId) {
        // 获取源量表的解释配置
        List<PsyTInterpretation> sourceInterpretations = selectInterpretationsByScaleId(sourceScaleId);
        
        if (sourceInterpretations.isEmpty()) {
            return 0;
        }
        
        // 复制解释配置
        List<PsyTInterpretation> targetInterpretations = new ArrayList<>();
        for (PsyTInterpretation source : sourceInterpretations) {
            PsyTInterpretation target = new PsyTInterpretation();
            target.setScaleId(targetScaleId);
            target.setDimension(source.getDimension());
            target.setMinScore(source.getMinScore());
            target.setMaxScore(source.getMaxScore());
            target.setLevelName(source.getLevelName());
            target.setLevelDescription(source.getLevelDescription());
            target.setSuggestions(source.getSuggestions());
            target.setColor(source.getColor());
            target.setOrderNum(source.getOrderNum());
            targetInterpretations.add(target);
        }
        
        return batchInsertInterpretations(targetInterpretations);
    }
}
