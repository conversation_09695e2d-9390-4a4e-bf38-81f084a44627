package com.xihuan.system.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 测评报告生成服务接口
 * 
 * <AUTHOR>
 */
public interface IPsyTReportGenerationService {

    /**
     * 生成完整测评报告
     *
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    Map<String, Object> generateCompleteReport(Long recordId);

    /**
     * 生成PRCA-24报告
     *
     * @param recordId 测评记录ID
     * @param scores 分数映射
     * @return 报告内容
     */
    Map<String, Object> generatePRCA24Report(Long recordId, Map<String, BigDecimal> scores);

    /**
     * 生成STAI报告
     *
     * @param recordId 测评记录ID
     * @param scores 分数映射
     * @return 报告内容
     */
    Map<String, Object> generateSTAIReport(Long recordId, Map<String, BigDecimal> scores);

    /**
     * 生成SAD报告
     *
     * @param recordId 测评记录ID
     * @param scores 分数映射
     * @return 报告内容
     */
    Map<String, Object> generateSADReport(Long recordId, Map<String, BigDecimal> scores);

    /**
     * 生成PDQ-4+报告
     *
     * @param recordId 测评记录ID
     * @param scores 分数映射
     * @return 报告内容
     */
    Map<String, Object> generatePDQ4Report(Long recordId, Map<String, BigDecimal> scores);

    /**
     * 生成FNE报告
     *
     * @param recordId 测评记录ID
     * @param scores 分数映射
     * @return 报告内容
     */
    Map<String, Object> generateFNEReport(Long recordId, Map<String, BigDecimal> scores);

    /**
     * 生成SAS报告
     *
     * @param recordId 测评记录ID
     * @param scores 分数映射
     * @return 报告内容
     */
    Map<String, Object> generateSASReport(Long recordId, Map<String, BigDecimal> scores);

    /**
     * 生成BAI报告
     *
     * @param recordId 测评记录ID
     * @param scores 分数映射
     * @return 报告内容
     */
    Map<String, Object> generateBAIReport(Long recordId, Map<String, BigDecimal> scores);

    /**
     * 根据分数获取解释等级
     *
     * @param scaleId 量表ID
     * @param score 分数
     * @param scoreType 分数类型（RAW/STANDARD）
     * @return 解释信息
     */
    Map<String, Object> getScoreInterpretation(Long scaleId, BigDecimal score, String scoreType);

    /**
     * 生成建议和指导
     *
     * @param scaleCode 量表编码
     * @param scores 分数映射
     * @param interpretations 解释信息
     * @return 建议内容
     */
    Map<String, Object> generateRecommendations(String scaleCode, 
                                               Map<String, BigDecimal> scores, 
                                               Map<String, Object> interpretations);

    /**
     * 保存报告到数据库
     *
     * @param recordId 测评记录ID
     * @param reportContent 报告内容
     * @return 保存结果
     */
    boolean saveReportToDatabase(Long recordId, Map<String, Object> reportContent);

    /**
     * 获取已保存的报告
     *
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    Map<String, Object> getSavedReport(Long recordId);

    /**
     * 生成报告摘要
     *
     * @param reportContent 完整报告内容
     * @return 报告摘要
     */
    Map<String, Object> generateReportSummary(Map<String, Object> reportContent);

    /**
     * 验证报告完整性
     *
     * @param reportContent 报告内容
     * @return 验证结果
     */
    Map<String, Object> validateReport(Map<String, Object> reportContent);

    /**
     * 保存报告到数据库
     *
     * @param recordId 测评记录ID
     * @param reportContent 报告内容
     * @return 保存结果
     */
    boolean saveReport(Long recordId, Map<String, Object> reportContent);
}
