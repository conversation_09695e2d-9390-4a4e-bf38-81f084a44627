package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTSubscale;

import java.util.List;
import java.util.Map;

/**
 * 分量表定义Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTSubscaleService {
    
    /**
     * 查询分量表列表
     * 
     * @param subscale 分量表信息
     * @return 分量表集合
     */
    List<PsyTSubscale> selectSubscaleList(PsyTSubscale subscale);

    /**
     * 根据ID查询分量表
     * 
     * @param id 分量表ID
     * @return 分量表信息
     */
    PsyTSubscale selectSubscaleById(Long id);

    /**
     * 查询分量表详情（包含题目关系、计分规则等信息）
     * 
     * @param id 分量表ID
     * @return 分量表详情
     */
    PsyTSubscale selectSubscaleWithDetails(Long id);

    /**
     * 根据量表ID查询分量表列表
     * 
     * @param scaleId 量表ID
     * @return 分量表集合
     */
    List<PsyTSubscale> selectSubscalesByScaleId(Long scaleId);

    /**
     * 根据量表ID查询分量表列表（包含题目关系）
     * 
     * @param scaleId 量表ID
     * @return 分量表集合
     */
    List<PsyTSubscale> selectSubscalesWithQuestionsByScaleId(Long scaleId);

    /**
     * 新增分量表
     * 
     * @param subscale 分量表信息
     * @return 结果
     */
    int insertSubscale(PsyTSubscale subscale);

    /**
     * 修改分量表
     * 
     * @param subscale 分量表信息
     * @return 结果
     */
    int updateSubscale(PsyTSubscale subscale);

    /**
     * 删除分量表
     * 
     * @param ids 需要删除的分量表ID
     * @return 结果
     */
    int deleteSubscaleByIds(Long[] ids);

    /**
     * 根据量表ID删除分量表
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteSubscalesByScaleId(Long scaleId);

    /**
     * 检查分量表名称唯一性
     * 
     * @param scaleId 量表ID
     * @param name 分量表名称
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    boolean checkSubscaleNameUnique(Long scaleId, String name, Long excludeId);

    /**
     * 检查分量表缩写唯一性
     * 
     * @param scaleId 量表ID
     * @param alias 分量表缩写
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    boolean checkSubscaleAliasUnique(Long scaleId, String alias, Long excludeId);

    /**
     * 统计量表分量表数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countSubscalesByScaleId(Long scaleId);

    /**
     * 查询分量表题目数量统计
     * 
     * @param subscaleId 分量表ID
     * @return 题目数量
     */
    int countQuestionsBySubscaleId(Long subscaleId);

    /**
     * 查询分量表得分统计
     * 
     * @param subscaleId 分量表ID
     * @return 统计信息
     */
    Map<String, Object> selectSubscaleScoreStats(Long subscaleId);

    /**
     * 查询分量表使用情况
     * 
     * @param subscaleId 分量表ID
     * @return 使用情况
     */
    Map<String, Object> selectSubscaleUsageInfo(Long subscaleId);

    /**
     * 复制分量表到新量表
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    int copySubscalesToScale(Long sourceScaleId, Long targetScaleId);

    /**
     * 查询分量表得分分布
     * 
     * @param subscaleId 分量表ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectSubscaleScoreDistribution(Long subscaleId);

    /**
     * 查询分量表相关性分析
     * 
     * @param scaleId 量表ID
     * @return 相关性信息
     */
    List<Map<String, Object>> selectSubscaleCorrelationAnalysis(Long scaleId);

    /**
     * 查询分量表信度分析
     * 
     * @param subscaleId 分量表ID
     * @return 信度信息
     */
    Map<String, Object> selectSubscaleReliabilityAnalysis(Long subscaleId);

    /**
     * 搜索分量表
     * 
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @return 分量表集合
     */
    List<PsyTSubscale> searchSubscales(String keyword, Long scaleId);

    /**
     * 添加题目到分量表
     * 
     * @param subscaleId 分量表ID
     * @param questionIds 题目ID列表
     * @return 结果
     */
    int addQuestionsToSubscale(Long subscaleId, List<Long> questionIds);

    /**
     * 从分量表移除题目
     * 
     * @param subscaleId 分量表ID
     * @param questionIds 题目ID列表
     * @return 结果
     */
    int removeQuestionsFromSubscale(Long subscaleId, List<Long> questionIds);

    /**
     * 设置题目权重
     * 
     * @param subscaleId 分量表ID
     * @param questionId 题目ID
     * @param weight 权重
     * @return 结果
     */
    int setQuestionWeight(Long subscaleId, Long questionId, java.math.BigDecimal weight);

    /**
     * 批量设置题目权重
     * 
     * @param subscaleId 分量表ID
     * @param questionWeights 题目权重映射
     * @return 结果
     */
    int batchSetQuestionWeights(Long subscaleId, Map<Long, java.math.BigDecimal> questionWeights);

    /**
     * 计算分量表得分
     * 
     * @param subscaleId 分量表ID
     * @param recordId 测评记录ID
     * @return 得分
     */
    Integer calculateSubscaleScore(Long subscaleId, Long recordId);

    /**
     * 验证分量表完整性
     * 
     * @param subscaleId 分量表ID
     * @return 验证结果
     */
    Map<String, Object> validateSubscaleCompleteness(Long subscaleId);

    /**
     * 自动生成分量表
     * 
     * @param scaleId 量表ID
     * @param strategy 生成策略
     * @return 结果
     */
    int autoGenerateSubscales(Long scaleId, String strategy);

    /**
     * 优化分量表结构
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int optimizeSubscaleStructure(Long scaleId);

    /**
     * 导出分量表配置
     * 
     * @param scaleId 量表ID
     * @return 配置数据
     */
    Map<String, Object> exportSubscaleConfig(Long scaleId);

    /**
     * 导入分量表配置
     * 
     * @param scaleId 量表ID
     * @param configData 配置数据
     * @return 结果
     */
    int importSubscaleConfig(Long scaleId, Map<String, Object> configData);
}
