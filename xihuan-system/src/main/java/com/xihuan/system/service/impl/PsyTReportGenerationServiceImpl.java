package com.xihuan.system.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xihuan.common.core.domain.entity.*;
import com.xihuan.system.mapper.*;
import com.xihuan.system.service.IPsyTAdvancedScoringService;
import com.xihuan.system.service.IPsyTReportGenerationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 测评报告生成服务实现
 * 
 * <AUTHOR>
 */
@Service
public class PsyTReportGenerationServiceImpl implements IPsyTReportGenerationService {

    private static final Logger logger = LoggerFactory.getLogger(PsyTReportGenerationServiceImpl.class);

    @Autowired
    private PsyTAssessmentRecordMapper assessmentRecordMapper;

    @Autowired
    private PsyTScaleMapper scaleMapper;

    @Autowired
    private PsyTInterpretationMapper interpretationMapper;

    @Autowired
    private IPsyTAdvancedScoringService advancedScoringService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 生成完整测评报告
     */
    @Override
    @Transactional
    public Map<String, Object> generateCompleteReport(Long recordId) {
        logger.info("开始生成完整测评报告，recordId: {}", recordId);
        
        Map<String, Object> report = new HashMap<>();
        
        try {
            // 获取测评记录
            PsyTAssessmentRecord record = assessmentRecordMapper.selectAssessmentRecordById(recordId);
            if (record == null) {
                report.put("success", false);
                report.put("message", "测评记录不存在");
                return report;
            }

            // 获取量表信息
            PsyTScale scale = scaleMapper.selectScaleById(record.getScaleId());
            if (scale == null) {
                report.put("success", false);
                report.put("message", "量表信息不存在");
                return report;
            }

            // 执行高级计分
            Map<String, Object> scoringResult = advancedScoringService.executeAdvancedScoring(recordId);
            if (!(Boolean) scoringResult.get("success")) {
                report.put("success", false);
                report.put("message", "计分失败: " + scoringResult.get("message"));
                return report;
            }

            Map<String, Object> scores = (Map<String, Object>) scoringResult.get("scoringResult");
            String scaleCode = scale.getCode();

            // 基础信息
            report.put("recordId", recordId);
            report.put("scaleCode", scaleCode);
            report.put("scaleName", scale.getName());
            report.put("testDate", record.getStartTime());
            report.put("completionTime", record.getCompletionTime());
            report.put("scoringMethod", scores.get("scoringMethod"));

            // 根据量表类型生成专门的报告
            Map<String, Object> specificReport;
            switch (scaleCode) {
                case "PRCA-24":
                    specificReport = generatePRCA24Report(recordId, convertScores(scores));
                    break;
                case "STAI":
                    specificReport = generateSTAIReport(recordId, convertScores(scores));
                    break;
                case "SAD":
                    specificReport = generateSADReport(recordId, convertScores(scores));
                    break;
                case "PDQ-4+":
                    specificReport = generatePDQ4Report(recordId, convertScores(scores));
                    break;
                case "FNE":
                    specificReport = generateFNEReport(recordId, convertScores(scores));
                    break;
                case "SAS":
                    specificReport = generateSASReport(recordId, convertScores(scores));
                    break;
                case "BAI":
                    specificReport = generateBAIReport(recordId, convertScores(scores));
                    break;
                default:
                    specificReport = generateGenericReport(recordId, convertScores(scores));
                    break;
            }

            report.putAll(specificReport);
            report.put("success", true);
            report.put("generatedAt", new Date());

            // 保存报告到数据库
            saveReportToDatabase(recordId, report);

            logger.info("完整测评报告生成成功，recordId: {}, scaleCode: {}", recordId, scaleCode);

        } catch (Exception e) {
            logger.error("生成完整测评报告异常，recordId: {}", recordId, e);
            report.put("success", false);
            report.put("message", "生成报告时发生异常: " + e.getMessage());
        }

        return report;
    }

    /**
     * 生成PRCA-24报告
     */
    @Override
    public Map<String, Object> generatePRCA24Report(Long recordId, Map<String, BigDecimal> scores) {
        Map<String, Object> report = new HashMap<>();
        
        try {
            BigDecimal totalScore = scores.get("totalScore");
            Map<String, BigDecimal> dimensionScores = (Map<String, BigDecimal>) scores.get("dimensionScores");
            
            // 分数解释
            Map<String, Object> interpretation = new HashMap<>();
            interpretation.put("totalScore", totalScore);
            interpretation.put("totalScoreLevel", getScoreLevel(totalScore, 24, 120));
            
            // 维度分数解释
            Map<String, Object> dimensionInterpretations = new HashMap<>();
            if (dimensionScores != null) {
                for (Map.Entry<String, BigDecimal> entry : dimensionScores.entrySet()) {
                    String dimension = entry.getKey();
                    BigDecimal score = entry.getValue();
                    dimensionInterpretations.put(dimension, getScoreLevel(score, 24, 120));
                }
            }
            interpretation.put("dimensions", dimensionInterpretations);
            
            // 生成建议
            Map<String, Object> recommendations = generatePRCA24Recommendations(totalScore, dimensionScores);
            
            report.put("interpretation", interpretation);
            report.put("recommendations", recommendations);
            report.put("reportType", "PRCA-24");
            
        } catch (Exception e) {
            logger.error("生成PRCA-24报告异常，recordId: {}", recordId, e);
            report.put("error", e.getMessage());
        }
        
        return report;
    }

    /**
     * 生成STAI报告
     */
    @Override
    public Map<String, Object> generateSTAIReport(Long recordId, Map<String, BigDecimal> scores) {
        Map<String, Object> report = new HashMap<>();
        
        try {
            BigDecimal totalScore = scores.get("totalScore");
            
            // 分数解释
            Map<String, Object> interpretation = new HashMap<>();
            interpretation.put("totalScore", totalScore);
            interpretation.put("anxietyLevel", getAnxietyLevel(totalScore));
            
            // 生成建议
            Map<String, Object> recommendations = generateSTAIRecommendations(totalScore);
            
            report.put("interpretation", interpretation);
            report.put("recommendations", recommendations);
            report.put("reportType", "STAI");
            
        } catch (Exception e) {
            logger.error("生成STAI报告异常，recordId: {}", recordId, e);
            report.put("error", e.getMessage());
        }
        
        return report;
    }

    /**
     * 生成SAS报告 - 按照标准模板格式
     */
    @Override
    public Map<String, Object> generateSASReport(Long recordId, Map<String, BigDecimal> scores) {
        Map<String, Object> report = new HashMap<>();

        try {
            BigDecimal rawScore = scores.get("rawScore");
            BigDecimal standardScore = scores.get("standardScore");

            // 获取测评记录基本信息
            PsyTAssessmentRecord record = assessmentRecordMapper.selectAssessmentRecordById(recordId);

            // 一、基本信息
            Map<String, Object> basicInfo = new HashMap<>();
            basicInfo.put("testDate", record != null ? record.getStartTime() : new Date());
            basicInfo.put("completionTime", record != null ? record.getCompletionTime() : new Date());
            basicInfo.put("scaleName", "焦虑自评量表（SAS）");

            // 二、测评结果
            Map<String, Object> testResults = new HashMap<>();
            testResults.put("rawScore", rawScore);
            testResults.put("standardScore", standardScore);
            testResults.put("anxietyLevel", getSASAnxietyLevel(standardScore));
            testResults.put("scoreFormula", "标准分 = 原始分 × 1.25");

            // 三、结果解释
            Map<String, Object> interpretation = new HashMap<>();
            interpretation.put("standardScore", standardScore);
            interpretation.put("scoreRange", getSASScoreRange(standardScore));
            interpretation.put("levelDescription", getSASLevelDescription(standardScore));
            interpretation.put("possibleSymptoms", getSASPossibleSymptoms(standardScore));
            interpretation.put("generalComment", getSASGeneralComment(standardScore));

            // 四、建议
            Map<String, Object> recommendations = new HashMap<>();
            recommendations.put("suggestions", getSASRecommendationsByLevel(standardScore));

            // 五、免责声明
            String disclaimer = "本测评结果仅供个人心理状态初步筛查与参考，不作为临床诊断依据。如情绪困扰持续存在或加重，请及时就医或寻求专业心理咨询帮助。";

            // 组装完整报告
            report.put("basicInfo", basicInfo);
            report.put("testResults", testResults);
            report.put("interpretation", interpretation);
            report.put("recommendations", recommendations);
            report.put("disclaimer", disclaimer);
            report.put("reportType", "SAS");
            report.put("reportTitle", "焦虑自评量表（SAS）测评报告");

        } catch (Exception e) {
            logger.error("生成SAS报告异常，recordId: {}", recordId, e);
            report.put("error", e.getMessage());
        }

        return report;
    }

    /**
     * 生成BAI报告
     */
    @Override
    public Map<String, Object> generateBAIReport(Long recordId, Map<String, BigDecimal> scores) {
        Map<String, Object> report = new HashMap<>();
        
        try {
            BigDecimal rawScore = scores.get("rawScore");
            BigDecimal standardScore = scores.get("standardScore");
            
            // 分数解释
            Map<String, Object> interpretation = new HashMap<>();
            interpretation.put("rawScore", rawScore);
            interpretation.put("standardScore", standardScore);
            interpretation.put("anxietyLevel", getBAIAnxietyLevel(standardScore));
            
            // 生成建议
            Map<String, Object> recommendations = generateBAIRecommendations(standardScore);
            
            report.put("interpretation", interpretation);
            report.put("recommendations", recommendations);
            report.put("reportType", "BAI");
            
        } catch (Exception e) {
            logger.error("生成BAI报告异常，recordId: {}", recordId, e);
            report.put("error", e.getMessage());
        }
        
        return report;
    }

    /**
     * 转换分数格式
     */
    private Map<String, BigDecimal> convertScores(Map<String, Object> scores) {
        Map<String, BigDecimal> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : scores.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof BigDecimal) {
                result.put(entry.getKey(), (BigDecimal) value);
            } else if (value instanceof Number) {
                result.put(entry.getKey(), new BigDecimal(value.toString()));
            }
        }
        return result;
    }

    /**
     * 获取分数等级
     */
    private String getScoreLevel(BigDecimal score, int minScore, int maxScore) {
        if (score == null) return "未知";
        
        double percentage = score.doubleValue() / maxScore * 100;
        
        if (percentage >= 80) return "很高";
        else if (percentage >= 60) return "较高";
        else if (percentage >= 40) return "中等";
        else if (percentage >= 20) return "较低";
        else return "很低";
    }

    /**
     * 获取焦虑等级
     */
    private String getAnxietyLevel(BigDecimal score) {
        if (score == null) return "未知";
        
        int scoreValue = score.intValue();
        if (scoreValue >= 65) return "重度焦虑";
        else if (scoreValue >= 56) return "中度焦虑";
        else if (scoreValue >= 45) return "轻度焦虑";
        else return "正常";
    }

    /**
     * 获取SAS焦虑等级
     */
    private String getSASAnxietyLevel(BigDecimal standardScore) {
        if (standardScore == null) return "未知";
        
        int score = standardScore.intValue();
        if (score >= 70) return "重度焦虑";
        else if (score >= 60) return "中度焦虑";
        else if (score >= 50) return "轻度焦虑";
        else return "正常";
    }

    /**
     * 获取BAI焦虑等级
     */
    private String getBAIAnxietyLevel(BigDecimal standardScore) {
        if (standardScore == null) return "未知";

        int score = standardScore.intValue();
        if (score >= 36) return "重度焦虑";
        else if (score >= 22) return "中度焦虑";
        else if (score >= 8) return "轻度焦虑";
        else return "正常";
    }

    /**
     * 生成PRCA-24建议
     */
    private Map<String, Object> generatePRCA24Recommendations(BigDecimal totalScore, Map<String, BigDecimal> dimensionScores) {
        Map<String, Object> recommendations = new HashMap<>();

        List<String> suggestions = new ArrayList<>();

        if (totalScore != null && totalScore.intValue() > 80) {
            suggestions.add("您的沟通焦虑水平较高，建议寻求专业帮助");
            suggestions.add("可以通过放松训练和认知行为疗法来改善");
            suggestions.add("参加沟通技巧培训课程");
        } else if (totalScore != null && totalScore.intValue() > 60) {
            suggestions.add("您存在一定程度的沟通焦虑");
            suggestions.add("建议多参与社交活动，逐步提高沟通信心");
            suggestions.add("学习有效的沟通技巧");
        } else {
            suggestions.add("您的沟通焦虑水平在正常范围内");
            suggestions.add("继续保持良好的沟通习惯");
        }

        recommendations.put("suggestions", suggestions);
        recommendations.put("riskLevel", totalScore != null && totalScore.intValue() > 80 ? "高" : "中低");

        return recommendations;
    }

    /**
     * 生成STAI建议
     */
    private Map<String, Object> generateSTAIRecommendations(BigDecimal totalScore) {
        Map<String, Object> recommendations = new HashMap<>();

        List<String> suggestions = new ArrayList<>();

        if (totalScore != null && totalScore.intValue() >= 65) {
            suggestions.add("您的焦虑水平较高，建议寻求专业心理咨询");
            suggestions.add("学习放松技巧，如深呼吸、冥想等");
            suggestions.add("保持规律的作息和适量运动");
        } else if (totalScore != null && totalScore.intValue() >= 45) {
            suggestions.add("您存在一定程度的焦虑");
            suggestions.add("注意调节情绪，保持积极心态");
            suggestions.add("适当进行放松活动");
        } else {
            suggestions.add("您的焦虑水平在正常范围内");
            suggestions.add("继续保持良好的心理状态");
        }

        recommendations.put("suggestions", suggestions);
        recommendations.put("riskLevel", totalScore != null && totalScore.intValue() >= 65 ? "高" : "中低");

        return recommendations;
    }

    /**
     * 生成详细的SAS建议
     */
    private Map<String, Object> generateDetailedSASRecommendations(BigDecimal standardScore) {
        Map<String, Object> recommendations = new HashMap<>();

        List<String> immediateSuggestions = new ArrayList<>();
        List<String> shortTermSuggestions = new ArrayList<>();
        List<String> longTermSuggestions = new ArrayList<>();

        int score = standardScore != null ? standardScore.intValue() : 0;

        if (score >= 70) {
            // 重度焦虑
            immediateSuggestions.add("立即寻求专业医疗帮助，建议到精神科或心理科就诊");
            immediateSuggestions.add("避免独处，确保有家人朋友陪伴");
            immediateSuggestions.add("如有自伤或自杀想法，请立即拨打心理危机干预热线");

            shortTermSuggestions.add("配合医生进行药物治疗和心理治疗");
            shortTermSuggestions.add("学习深呼吸和渐进性肌肉放松技巧");
            shortTermSuggestions.add("建立规律的作息时间，保证充足睡眠");

            longTermSuggestions.add("定期复诊，监测症状变化");
            longTermSuggestions.add("参加焦虑症支持小组");
            longTermSuggestions.add("培养健康的应对方式和生活习惯");

            recommendations.put("riskLevel", "高");
            recommendations.put("urgency", "紧急");

        } else if (score >= 60) {
            // 中度焦虑
            immediateSuggestions.add("建议咨询心理健康专家进行评估");
            immediateSuggestions.add("开始学习焦虑管理技巧");
            immediateSuggestions.add("减少咖啡因和酒精摄入");

            shortTermSuggestions.add("考虑心理咨询或认知行为治疗");
            shortTermSuggestions.add("建立规律的运动习惯");
            shortTermSuggestions.add("学习正念冥想和放松技巧");

            longTermSuggestions.add("识别和改变负面思维模式");
            longTermSuggestions.add("建立良好的社会支持网络");
            longTermSuggestions.add("定期进行自我评估和调整");

            recommendations.put("riskLevel", "中");
            recommendations.put("urgency", "较高");

        } else if (score >= 50) {
            // 轻度焦虑
            immediateSuggestions.add("关注自己的情绪变化");
            immediateSuggestions.add("尝试放松技巧缓解紧张感");
            immediateSuggestions.add("保持规律的生活作息");

            shortTermSuggestions.add("学习压力管理技巧");
            shortTermSuggestions.add("增加体育锻炼和户外活动");
            shortTermSuggestions.add("培养兴趣爱好，丰富生活内容");

            longTermSuggestions.add("建立健康的生活方式");
            longTermSuggestions.add("提高情绪调节能力");
            longTermSuggestions.add("定期自我监测，预防症状加重");

            recommendations.put("riskLevel", "低");
            recommendations.put("urgency", "一般");

        } else {
            // 正常范围
            immediateSuggestions.add("继续保持良好的心理状态");
            immediateSuggestions.add("维持健康的生活方式");
            immediateSuggestions.add("适当关注心理健康知识");

            shortTermSuggestions.add("建立压力预防机制");
            shortTermSuggestions.add("培养多样化的兴趣爱好");
            shortTermSuggestions.add("保持良好的人际关系");

            longTermSuggestions.add("定期进行心理健康自查");
            longTermSuggestions.add("学习心理调适技能");
            longTermSuggestions.add("帮助他人，传播正能量");

            recommendations.put("riskLevel", "极低");
            recommendations.put("urgency", "无");
        }

        recommendations.put("immediateSuggestions", immediateSuggestions);
        recommendations.put("shortTermSuggestions", shortTermSuggestions);
        recommendations.put("longTermSuggestions", longTermSuggestions);

        return recommendations;
    }

    /**
     * 生成SAS建议（简化版，保持向后兼容）
     */
    private Map<String, Object> generateSASRecommendations(BigDecimal standardScore) {
        Map<String, Object> recommendations = new HashMap<>();

        List<String> suggestions = new ArrayList<>();

        if (standardScore != null && standardScore.intValue() >= 70) {
            suggestions.add("您的焦虑症状较为严重，强烈建议寻求专业医疗帮助");
            suggestions.add("可能需要药物治疗配合心理治疗");
            suggestions.add("避免过度刺激，保持平静的环境");
        } else if (standardScore != null && standardScore.intValue() >= 50) {
            suggestions.add("您存在明显的焦虑症状");
            suggestions.add("建议咨询心理健康专家");
            suggestions.add("学习应对焦虑的技巧");
        } else {
            suggestions.add("您的焦虑水平在正常范围内");
            suggestions.add("继续保持健康的生活方式");
        }

        recommendations.put("suggestions", suggestions);
        recommendations.put("riskLevel", standardScore != null && standardScore.intValue() >= 70 ? "高" : "中低");

        return recommendations;
    }

    /**
     * 生成BAI建议
     */
    private Map<String, Object> generateBAIRecommendations(BigDecimal standardScore) {
        Map<String, Object> recommendations = new HashMap<>();

        List<String> suggestions = new ArrayList<>();

        if (standardScore != null && standardScore.intValue() >= 36) {
            suggestions.add("您的焦虑症状严重，建议立即寻求专业帮助");
            suggestions.add("可能需要综合治疗方案");
            suggestions.add("密切关注身体症状变化");
        } else if (standardScore != null && standardScore.intValue() >= 8) {
            suggestions.add("您存在一定程度的焦虑症状");
            suggestions.add("建议进行心理咨询");
            suggestions.add("学习焦虑管理技巧");
        } else {
            suggestions.add("您的焦虑水平正常");
            suggestions.add("保持良好的心理健康习惯");
        }

        recommendations.put("suggestions", suggestions);
        recommendations.put("riskLevel", standardScore != null && standardScore.intValue() >= 36 ? "高" : "中低");

        return recommendations;
    }

    /**
     * 生成通用报告
     */
    private Map<String, Object> generateGenericReport(Long recordId, Map<String, BigDecimal> scores) {
        Map<String, Object> report = new HashMap<>();

        BigDecimal totalScore = scores.get("totalScore");

        Map<String, Object> interpretation = new HashMap<>();
        interpretation.put("totalScore", totalScore);
        interpretation.put("level", "需要专业解释");

        Map<String, Object> recommendations = new HashMap<>();
        recommendations.put("suggestions", Arrays.asList("请咨询专业人员进行结果解释"));

        report.put("interpretation", interpretation);
        report.put("recommendations", recommendations);
        report.put("reportType", "GENERIC");

        return report;
    }

    // 实现接口中的其他方法
    @Override
    public Map<String, Object> generateSADReport(Long recordId, Map<String, BigDecimal> scores) {
        // SAD报告生成逻辑
        return generateGenericReport(recordId, scores);
    }

    @Override
    public Map<String, Object> generatePDQ4Report(Long recordId, Map<String, BigDecimal> scores) {
        // PDQ-4+报告生成逻辑
        return generateGenericReport(recordId, scores);
    }

    @Override
    public Map<String, Object> generateFNEReport(Long recordId, Map<String, BigDecimal> scores) {
        // FNE报告生成逻辑
        return generateGenericReport(recordId, scores);
    }

    @Override
    public Map<String, Object> getScoreInterpretation(Long scaleId, BigDecimal score, String scoreType) {
        // 获取分数解释
        Map<String, Object> interpretation = new HashMap<>();
        interpretation.put("score", score);
        interpretation.put("scoreType", scoreType);
        return interpretation;
    }

    @Override
    public Map<String, Object> generateRecommendations(String scaleCode, Map<String, BigDecimal> scores, Map<String, Object> interpretations) {
        // 生成建议
        return new HashMap<>();
    }

    @Override
    public boolean saveReportToDatabase(Long recordId, Map<String, Object> reportContent) {
        try {
            // 将报告内容转换为JSON字符串
            String reportJson = objectMapper.writeValueAsString(reportContent);

            // 更新测评记录
            PsyTAssessmentRecord record = new PsyTAssessmentRecord();
            record.setId(recordId);
            record.setReportContent(reportJson);
            record.setReportGenerated(1);

            return assessmentRecordMapper.updateAssessmentRecord(record) > 0;

        } catch (Exception e) {
            logger.error("保存报告到数据库异常，recordId: {}", recordId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getSavedReport(Long recordId) {
        try {
            PsyTAssessmentRecord record = assessmentRecordMapper.selectAssessmentRecordById(recordId);
            if (record != null && record.getReportContent() != null) {
                return objectMapper.readValue(record.getReportContent(), Map.class);
            }
        } catch (Exception e) {
            logger.error("获取已保存报告异常，recordId: {}", recordId, e);
        }
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> generateReportSummary(Map<String, Object> reportContent) {
        // 生成报告摘要
        Map<String, Object> summary = new HashMap<>();
        summary.put("reportType", reportContent.get("reportType"));
        summary.put("generatedAt", reportContent.get("generatedAt"));
        return summary;
    }

    @Override
    public Map<String, Object> validateReport(Map<String, Object> reportContent) {
        // 验证报告完整性
        Map<String, Object> validation = new HashMap<>();
        validation.put("valid", true);
        return validation;
    }

    @Override
    public boolean saveReport(Long recordId, Map<String, Object> reportContent) {
        try {
            // 将报告内容转换为JSON字符串
            String reportJson = objectMapper.writeValueAsString(reportContent);

            // 更新测评记录的报告内容
            PsyTAssessmentRecord record = new PsyTAssessmentRecord();
            record.setId(recordId);
            record.setReportContent(reportJson);
            record.setReportGenerated(1);

            int result = assessmentRecordMapper.updateAssessmentRecord(record);

            logger.info("保存报告到数据库，recordId: {}, result: {}", recordId, result);
            return result > 0;

        } catch (Exception e) {
            logger.error("保存报告到数据库失败，recordId: {}", recordId, e);
            return false;
        }
    }

    // ==================== SAS报告标准模板方法 ====================

    /**
     * 获取SAS分数范围描述
     */
    private String getSASScoreRange(BigDecimal standardScore) {
        if (standardScore == null) return "无法评估";

        int score = standardScore.intValue();
        if (score >= 70) {
            return "70分及以上";
        } else if (score >= 60) {
            return "60–69分";
        } else if (score >= 50) {
            return "50–59分";
        } else {
            return "小于50分";
        }
    }

    /**
     * 获取SAS等级描述
     */
    private String getSASLevelDescription(BigDecimal standardScore) {
        if (standardScore == null) return "无法评估";

        int score = standardScore.intValue();
        if (score >= 70) {
            return "根据SAS量表标准分评分规则，受测者的标准分为 " + score + "分，落入 70分及以上 区间，提示其在最近一周内存在严重焦虑状态。该状态可能已经明显影响生活、社交与身心健康。";
        } else if (score >= 60) {
            return "根据SAS量表标准分评分规则，受测者的标准分为 " + score + "分，落入 60–69分 区间，提示其在最近一周内表现出中度焦虑。这表明焦虑情绪已经对生活产生一定影响。";
        } else if (score >= 50) {
            return "根据SAS量表标准分评分规则，受测者的标准分为 " + score + "分，落入 50–59分 区间，提示其在最近一周内存在一定程度的焦虑体验，表现为轻度焦虑。";
        } else {
            return "根据SAS量表标准分评分规则，受测者的标准分为 " + score + "分，落入 小于50分 区间，提示其在最近一周内未表现出明显的焦虑症状，情绪状态整体较为平稳。";
        }
    }

    /**
     * 获取SAS可能症状
     */
    private List<String> getSASPossibleSymptoms(BigDecimal standardScore) {
        List<String> symptoms = new ArrayList<>();

        if (standardScore == null) {
            symptoms.add("无法评估症状");
            return symptoms;
        }

        int score = standardScore.intValue();
        if (score >= 70) {
            symptoms.add("明显持续的焦虑与紧张，难以自我缓解");
            symptoms.add("严重失眠、梦魇或频繁惊醒");
            symptoms.add("躯体症状显著，如胸闷、气短、频繁出汗、胃肠紊乱");
            symptoms.add("注意力涣散、判断力下降，甚至出现社交退缩");
            symptoms.add("情绪极不稳定，可能伴随恐慌发作或抑郁情绪");
        } else if (score >= 60) {
            symptoms.add("持续性的紧张、烦躁、坐立不安");
            symptoms.add("注意力显著分散，学习或工作效率下降");
            symptoms.add("睡眠障碍（如难以入睡、早醒、噩梦）");
            symptoms.add("躯体症状加重，如心慌、胃肠不适、出汗、疲劳感强烈");
            symptoms.add("伴随反复担忧、易怒、情绪波动较大");
        } else if (score >= 50) {
            symptoms.add("紧张不安");
            symptoms.add("注意力难以集中");
            symptoms.add("容易疲劳或坐立不安");
            symptoms.add("睡眠质量下降或易惊醒");
            symptoms.add("可能伴随轻微身体症状，如心跳加快、出汗等");
        } else {
            symptoms.add("情绪稳定");
            symptoms.add("应对压力较为有效");
            symptoms.add("睡眠良好");
            symptoms.add("躯体状况正常");
        }

        return symptoms;
    }

    /**
     * 获取SAS总体评价
     */
    private String getSASGeneralComment(BigDecimal standardScore) {
        if (standardScore == null) return "无法评估";

        int score = standardScore.intValue();
        if (score >= 70) {
            return "强烈建议尽快前往心理科或精神科进行专业评估与治疗。";
        } else if (score >= 60) {
            return "建议联系专业心理咨询师开展谈话性干预，探索压力源并学习情绪管理技巧。";
        } else if (score >= 50) {
            return "尽管目前焦虑程度较轻，但仍建议关注其可能的诱因（如学业压力、人际冲突等）并进行适当的放松调节。";
        } else {
            return "虽然目前无明显焦虑表现，但建议继续维持积极健康的生活方式，注意心理状态的预防性维护。";
        }
    }

    /**
     * 根据等级获取SAS建议
     */
    private List<String> getSASRecommendationsByLevel(BigDecimal standardScore) {
        List<String> recommendations = new ArrayList<>();

        if (standardScore == null) {
            recommendations.add("无法提供建议");
            return recommendations;
        }

        int score = standardScore.intValue();
        if (score >= 70) {
            // 重度焦虑建议
            recommendations.add("立即就医评估：强烈建议尽快前往心理科或精神科进行专业评估与治疗；");
            recommendations.add("配合治疗：在专业指导下可考虑药物治疗联合心理咨询；");
            recommendations.add("减少外界刺激：暂时减少高强度任务，创造安全、安静的恢复环境；");
            recommendations.add("家庭/社交支持：建议亲友予以支持与陪伴，避免个体孤立、情绪失控；");
            recommendations.add("持续跟进：需长期关注心理恢复过程，建议建立后续跟踪机制。");
        } else if (score >= 60) {
            // 中度焦虑建议
            recommendations.add("建立规律生活：减少晚睡、避免过度使用电子产品，形成稳定的生物钟；");
            recommendations.add("增加放松练习：可通过腹式呼吸、渐进式肌肉放松、冥想等方法缓解生理紧张；");
            recommendations.add("心理疏导：建议联系专业心理咨询师开展谈话性干预，探索压力源并学习情绪管理技巧；");
            recommendations.add("必要时就医：若症状持续时间较长或显著影响工作生活，建议前往医院进行心理评估或干预。");
        } else if (score >= 50) {
            // 轻度焦虑建议
            recommendations.add("自我调节：保持规律作息、适量运动、尝试冥想或放松训练；");
            recommendations.add("情绪表达：与信任的人倾诉情绪，避免情绪压抑；");
            recommendations.add("心理支持：若持续存在焦虑情绪或影响日常功能，建议咨询专业心理工作者；");
            recommendations.add("关注变化：定期复测，动态关注焦虑水平变化，防止加重。");
        } else {
            // 正常范围建议
            recommendations.add("积极维持：保持规律生活作息，维持健康饮食与充足睡眠；");
            recommendations.add("心理预防：日常中可以尝试写日记、冥想等方式提高情绪觉察力；");
            recommendations.add("身心平衡：参与社交活动、发展兴趣爱好，增加积极情绪体验；");
            recommendations.add("定期关注：如遇突发事件或明显压力时，建议再次进行心理测评以评估变化。");
        }

        return recommendations;
    }

    // ==================== SAS报告原有辅助方法 ====================

    /**
     * 计算百分位数
     */
    private String calculatePercentile(BigDecimal score, int minScore, int maxScore) {
        if (score == null) return "未知";

        double percentage = (score.doubleValue() - minScore) / (maxScore - minScore) * 100;
        return String.format("%.1f%%", Math.max(0, Math.min(100, percentage)));
    }

    /**
     * 获取SAS等级含义
     */
    private String getSASLevelMeaning(BigDecimal standardScore) {
        if (standardScore == null) return "无法评估";

        int score = standardScore.intValue();
        if (score >= 70) {
            return "重度焦虑：存在明显的焦虑症状，严重影响日常生活和工作";
        } else if (score >= 60) {
            return "中度焦虑：有较明显的焦虑症状，对生活造成一定影响";
        } else if (score >= 50) {
            return "轻度焦虑：存在轻微的焦虑症状，基本不影响正常生活";
        } else {
            return "正常范围：焦虑水平在正常范围内，心理状态良好";
        }
    }

    /**
     * 获取SAS临床意义
     */
    private String getSASClinicalSignificance(BigDecimal standardScore) {
        if (standardScore == null) return "无法评估";

        int score = standardScore.intValue();
        if (score >= 70) {
            return "建议立即寻求专业医疗帮助，可能需要药物治疗和心理治疗相结合";
        } else if (score >= 60) {
            return "建议咨询心理健康专家，考虑心理治疗或药物干预";
        } else if (score >= 50) {
            return "建议关注心理健康，学习压力管理和放松技巧";
        } else {
            return "心理状态良好，建议保持现有的健康生活方式";
        }
    }

    /**
     * 获取主要症状
     */
    private List<String> getSASMainSymptoms(BigDecimal standardScore) {
        List<String> symptoms = new ArrayList<>();

        if (standardScore == null) {
            symptoms.add("无法评估症状");
            return symptoms;
        }

        int score = standardScore.intValue();
        if (score >= 70) {
            symptoms.add("严重的紧张不安");
            symptoms.add("明显的身体不适感");
            symptoms.add("睡眠质量严重下降");
            symptoms.add("注意力难以集中");
            symptoms.add("情绪波动剧烈");
        } else if (score >= 60) {
            symptoms.add("经常感到紧张");
            symptoms.add("偶有身体不适");
            symptoms.add("睡眠质量下降");
            symptoms.add("容易担心");
        } else if (score >= 50) {
            symptoms.add("偶尔感到紧张");
            symptoms.add("轻微的担心情绪");
            symptoms.add("睡眠偶有影响");
        } else {
            symptoms.add("情绪稳定");
            symptoms.add("睡眠质量良好");
            symptoms.add("能够有效应对压力");
        }

        return symptoms;
    }

    /**
     * 获取影响领域
     */
    private List<String> getSASImpactAreas(BigDecimal standardScore) {
        List<String> areas = new ArrayList<>();

        if (standardScore == null) return areas;

        int score = standardScore.intValue();
        if (score >= 70) {
            areas.add("工作效率严重下降");
            areas.add("人际关系受到影响");
            areas.add("日常生活能力下降");
            areas.add("身体健康受到影响");
        } else if (score >= 60) {
            areas.add("工作效率有所下降");
            areas.add("社交活动减少");
            areas.add("生活质量下降");
        } else if (score >= 50) {
            areas.add("偶尔影响工作专注度");
            areas.add("对某些社交场合感到不适");
        } else {
            areas.add("各方面功能正常");
            areas.add("能够胜任日常工作和生活");
        }

        return areas;
    }

    /**
     * 获取紧急程度
     */
    private String getUrgencyLevel(BigDecimal standardScore) {
        if (standardScore == null) return "无法评估";

        int score = standardScore.intValue();
        if (score >= 70) return "紧急";
        else if (score >= 60) return "较高";
        else if (score >= 50) return "一般";
        else return "无";
    }

    /**
     * 获取推荐行动
     */
    private List<String> getRecommendedActions(BigDecimal standardScore) {
        List<String> actions = new ArrayList<>();

        if (standardScore == null) return actions;

        int score = standardScore.intValue();
        if (score >= 70) {
            actions.add("立即预约精神科或心理科医生");
            actions.add("考虑住院治疗或密集门诊治疗");
            actions.add("通知家属，建立支持系统");
        } else if (score >= 60) {
            actions.add("预约心理咨询师或心理医生");
            actions.add("开始心理治疗（如认知行为治疗）");
            actions.add("考虑药物治疗");
        } else if (score >= 50) {
            actions.add("寻求心理咨询");
            actions.add("学习压力管理技巧");
            actions.add("增加运动和放松活动");
        } else {
            actions.add("保持现有的健康习惯");
            actions.add("定期进行心理健康自查");
        }

        return actions;
    }

    /**
     * 获取随访建议
     */
    private List<String> getFollowUpSuggestions(BigDecimal standardScore) {
        List<String> suggestions = new ArrayList<>();

        if (standardScore == null) return suggestions;

        int score = standardScore.intValue();
        if (score >= 70) {
            suggestions.add("每周复诊，密切监测症状变化");
            suggestions.add("1个月后重新评估");
            suggestions.add("建立症状日记");
        } else if (score >= 60) {
            suggestions.add("2-4周后复查");
            suggestions.add("3个月后重新评估");
            suggestions.add("定期记录情绪变化");
        } else if (score >= 50) {
            suggestions.add("3个月后重新评估");
            suggestions.add("半年后复查");
        } else {
            suggestions.add("1年后常规复查");
            suggestions.add("如有症状变化及时评估");
        }

        return suggestions;
    }

    /**
     * 获取日常管理建议
     */
    private List<String> getDailyManagementTips(BigDecimal standardScore) {
        List<String> tips = new ArrayList<>();

        tips.add("建立规律的作息时间，保证7-8小时睡眠");
        tips.add("每天进行30分钟有氧运动");
        tips.add("限制咖啡因和酒精摄入");
        tips.add("学习深呼吸和放松技巧");
        tips.add("保持健康的饮食习惯");
        tips.add("建立良好的社会支持网络");

        if (standardScore != null && standardScore.intValue() >= 60) {
            tips.add("避免过度刺激的环境");
            tips.add("学习识别焦虑触发因素");
            tips.add("建立应急应对计划");
        }

        return tips;
    }

    /**
     * 获取放松技巧
     */
    private List<String> getRelaxationTechniques() {
        List<String> techniques = new ArrayList<>();
        techniques.add("深呼吸练习：4-7-8呼吸法");
        techniques.add("渐进性肌肉放松");
        techniques.add("正念冥想");
        techniques.add("瑜伽和太极");
        techniques.add("音乐疗法");
        techniques.add("芳香疗法");
        return techniques;
    }

    /**
     * 获取运动建议
     */
    private List<String> getExerciseRecommendations() {
        List<String> exercises = new ArrayList<>();
        exercises.add("有氧运动：快走、慢跑、游泳");
        exercises.add("力量训练：适度的重量训练");
        exercises.add("柔韧性训练：瑜伽、普拉提");
        exercises.add("户外活动：登山、骑行");
        exercises.add("团体运动：增加社交互动");
        return exercises;
    }

    /**
     * 获取饮食建议
     */
    private List<String> getDietarySuggestions() {
        List<String> suggestions = new ArrayList<>();
        suggestions.add("增加富含Omega-3的食物（鱼类、坚果）");
        suggestions.add("多吃新鲜蔬菜和水果");
        suggestions.add("选择复合碳水化合物");
        suggestions.add("限制糖分和加工食品");
        suggestions.add("保持规律的用餐时间");
        suggestions.add("充足的水分摄入");
        return suggestions;
    }

    /**
     * 获取康复前景
     */
    private String getRecoveryOutlook(BigDecimal standardScore) {
        if (standardScore == null) return "无法评估";

        int score = standardScore.intValue();
        if (score >= 70) {
            return "通过专业治疗，大多数患者可以显著改善症状，恢复正常生活";
        } else if (score >= 60) {
            return "通过适当的干预措施，症状通常可以得到有效控制";
        } else if (score >= 50) {
            return "通过自我调节和适当的支持，症状很可能完全缓解";
        } else {
            return "心理状态良好，继续保持即可";
        }
    }

    /**
     * 获取预期时间框架
     */
    private String getExpectedTimeframe(BigDecimal standardScore) {
        if (standardScore == null) return "无法评估";

        int score = standardScore.intValue();
        if (score >= 70) {
            return "通常需要6-12个月的专业治疗";
        } else if (score >= 60) {
            return "通常需要3-6个月的干预";
        } else if (score >= 50) {
            return "通常在1-3个月内可以看到改善";
        } else {
            return "保持现状即可";
        }
    }

    /**
     * 获取成功因素
     */
    private List<String> getSuccessFactors() {
        List<String> factors = new ArrayList<>();
        factors.add("积极配合治疗");
        factors.add("建立良好的生活习惯");
        factors.add("保持乐观的心态");
        factors.add("获得家庭和社会支持");
        factors.add("定期监测和调整");
        factors.add("学习有效的应对技巧");
        return factors;
    }
}
