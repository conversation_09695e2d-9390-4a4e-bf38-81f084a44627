package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTEnterpriseAssessmentParticipant;

import java.util.List;
import java.util.Map;

/**
 * 企业测评参与者Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTEnterpriseAssessmentParticipantService {
    
    /**
     * 查询企业测评参与者列表
     * 
     * @param participant 企业测评参与者
     * @return 企业测评参与者集合
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectParticipantList(PsyTEnterpriseAssessmentParticipant participant);

    /**
     * 根据ID查询企业测评参与者
     * 
     * @param id 企业测评参与者ID
     * @return 企业测评参与者
     */
    public PsyTEnterpriseAssessmentParticipant selectParticipantById(Long id);

    /**
     * 查询企业测评参与者详情（包含关联信息）
     * 
     * @param id 企业测评参与者ID
     * @return 企业测评参与者详情
     */
    public PsyTEnterpriseAssessmentParticipant selectParticipantWithDetails(Long id);

    /**
     * 根据计划ID查询参与者列表
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectParticipantsByPlanId(Long planId);

    /**
     * 查询计划未开始参与者
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectNotStartedParticipants(Long planId);

    /**
     * 查询计划进行中参与者
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectInProgressParticipants(Long planId);

    /**
     * 查询计划已完成参与者
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectCompletedParticipants(Long planId);

    /**
     * 查询计划已放弃参与者
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectAbandonedParticipants(Long planId);

    /**
     * 新增企业测评参与者
     * 
     * @param participant 企业测评参与者
     * @return 结果
     */
    public int insertParticipant(PsyTEnterpriseAssessmentParticipant participant);

    /**
     * 修改企业测评参与者
     * 
     * @param participant 企业测评参与者
     * @return 结果
     */
    public int updateParticipant(PsyTEnterpriseAssessmentParticipant participant);

    /**
     * 删除企业测评参与者
     * 
     * @param ids 需要删除的企业测评参与者ID
     * @return 结果
     */
    public int deleteParticipantByIds(Long[] ids);

    /**
     * 批量新增参与者
     * 
     * @param participants 参与者列表
     * @return 结果
     */
    public int batchInsertParticipants(List<PsyTEnterpriseAssessmentParticipant> participants);

    /**
     * 更新参与者状态
     * 
     * @param participantId 参与者ID
     * @param status 状态
     * @return 结果
     */
    public int updateParticipantStatus(Long participantId, Integer status);

    /**
     * 批量更新参与者状态
     * 
     * @param participantIds 参与者ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateParticipantStatus(Long[] participantIds, Integer status);

    /**
     * 查询员工参与历史
     * 
     * @param employeeId 员工ID
     * @return 参与历史
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectEmployeeParticipationHistory(Long employeeId);

    /**
     * 查询参与统计信息
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    public Map<String, Object> selectParticipationStats(Long planId);

    /**
     * 查询部门参与统计
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    public List<Map<String, Object>> selectDepartmentParticipationStats(Long planId);

    /**
     * 查询参与趋势统计
     * 
     * @param planId 计划ID
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> selectParticipationTrendStats(Long planId, Integer days);

    /**
     * 查询参与度排行
     * 
     * @param enterpriseId 企业ID
     * @param limit 限制数量
     * @return 排行数据
     */
    public List<Map<String, Object>> selectParticipationRanking(Long enterpriseId, Integer limit);

    /**
     * 发送参与提醒
     * 
     * @param participantIds 参与者ID数组
     * @return 结果
     */
    public int sendParticipationReminder(Long[] participantIds);

    /**
     * 查询需要提醒的参与者
     * 
     * @param planId 计划ID
     * @return 参与者列表
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectParticipantsNeedReminder(Long planId);

    /**
     * 更新提醒信息
     * 
     * @param participantId 参与者ID
     * @return 结果
     */
    public int updateReminderInfo(Long participantId);

    /**
     * 检查员工是否已参与计划
     * 
     * @param planId 计划ID
     * @param employeeId 员工ID
     * @return 是否已参与
     */
    public boolean checkEmployeeParticipated(Long planId, Long employeeId);

    /**
     * 查询员工当前参与的计划
     * 
     * @param employeeId 员工ID
     * @return 参与记录列表
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectEmployeeCurrentParticipations(Long employeeId);

    /**
     * 查询超时未完成的参与者
     * 
     * @param planId 计划ID
     * @return 参与者列表
     */
    public List<PsyTEnterpriseAssessmentParticipant> selectTimeoutParticipants(Long planId);

    /**
     * 自动更新超时参与者状态
     * 
     * @param planId 计划ID
     * @return 更新数量
     */
    public int autoUpdateTimeoutParticipants(Long planId);

    /**
     * 查询参与者完成率分布
     * 
     * @param enterpriseId 企业ID
     * @return 分布数据
     */
    public List<Map<String, Object>> selectCompletionRateDistribution(Long enterpriseId);

    /**
     * 查询参与者测评时长统计
     * 
     * @param planId 计划ID
     * @return 统计数据
     */
    public Map<String, Object> selectDurationStats(Long planId);

    /**
     * 导出参与者数据
     * 
     * @param planId 计划ID
     * @return 导出数据
     */
    public List<Map<String, Object>> exportParticipantData(Long planId);

    /**
     * 查询参与者测评结果分布
     * 
     * @param planId 计划ID
     * @return 结果分布
     */
    public List<Map<String, Object>> selectResultDistribution(Long planId);
}
