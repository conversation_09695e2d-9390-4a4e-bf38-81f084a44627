package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.PsyMessage;
import com.xihuan.common.core.domain.AjaxResult;

import java.util.List;

/**
 * 消息服务接口
 */
public interface IPsyMessageService {
    
    /**
     * 发送消息
     * 
     * @param message 消息对象
     * @return 发送结果
     */
    PsyMessage sendMessage(PsyMessage message);
    
    /**
     * 管理员代替咨询师发送消息
     * 
     * @param message 消息对象
     * @param adminId 管理员ID
     * @return 发送结果
     */
    PsyMessage sendMessageAsAdmin(PsyMessage message, Long adminId);
    
    /**
     * 获取会话消息列表
     *
     * @param conversationId 会话ID
     * @return 消息列表
     */
    List<PsyMessage> getMessageList(Long conversationId);
    
    /**
     * 获取消息详情
     * 
     * @param messageId 消息ID
     * @return 消息对象
     */
    PsyMessage getMessage(Long messageId);
    
    /**
     * 撤回消息
     * 
     * @param messageId 消息ID
     * @param userId 操作用户ID
     * @return 操作结果
     */
    AjaxResult withdrawMessage(Long messageId, Long userId);
    
    /**
     * 标记消息为已读
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean markAsRead(Long messageId, Long userId);
    
    /**
     * 批量标记消息为已读
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean markAllAsRead(Long conversationId, Long userId);
} 