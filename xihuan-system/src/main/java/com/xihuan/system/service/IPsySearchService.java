package com.xihuan.system.service;

import com.xihuan.common.core.domain.dto.SearchResultDTO;
import com.xihuan.common.core.domain.entity.PsyHotSearch;
import com.xihuan.common.core.domain.entity.PsySearchRecord;
import com.xihuan.common.core.domain.entity.PsySearchSuggestion;

import java.util.List;

/**
 * 搜索服务接口
 * 
 * <AUTHOR>
 */
public interface IPsySearchService {
    
    /**
     * 全局搜索
     *
     * @param keyword 搜索关键词
     * @param searchType 搜索类型
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @param userId 用户ID（可为空）
     * @param ipAddress IP地址
     * @return 搜索结果
     */
    SearchResultDTO globalSearch(String keyword, String searchType, Integer pageNum, Integer pageSize, 
                                Long userId, String ipAddress);
    
    /**
     * 获取搜索建议
     *
     * @param keyword 关键词前缀
     * @param userId 用户ID（可为空）
     * @return 建议列表
     */
    List<String> getSearchSuggestions(String keyword, Long userId);
    
    /**
     * 获取热门搜索
     *
     * @param searchType 搜索类型
     * @param limit 限制数量
     * @return 热门搜索列表
     */
    List<PsyHotSearch> getHotSearches(String searchType, Integer limit);
    
    /**
     * 记录搜索行为
     *
     * @param keyword 搜索关键词
     * @param searchType 搜索类型
     * @param resultCount 结果数量
     * @param userId 用户ID（可为空）
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void recordSearch(String keyword, String searchType, Integer resultCount, 
                     Long userId, String ipAddress, String userAgent);
    
    /**
     * 更新热门搜索
     *
     * @param keyword 关键词
     * @param searchType 搜索类型
     */
    void updateHotSearch(String keyword, String searchType);
    
    /**
     * 获取用户搜索历史
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 搜索历史
     */
    List<String> getUserSearchHistory(Long userId, Integer limit);
    
    /**
     * 清除用户搜索历史
     *
     * @param userId 用户ID
     * @return 结果
     */
    boolean clearUserSearchHistory(Long userId);
    
    /**
     * 搜索记录管理
     */
    List<PsySearchRecord> selectSearchRecordList(PsySearchRecord searchRecord);
    int insertSearchRecord(PsySearchRecord searchRecord);
    int updateSearchRecord(PsySearchRecord searchRecord);
    int deleteSearchRecordByIds(Long[] ids);
    
    /**
     * 热门搜索管理
     */
    List<PsyHotSearch> selectHotSearchList(PsyHotSearch hotSearch);
    int insertHotSearch(PsyHotSearch hotSearch);
    int updateHotSearch(PsyHotSearch hotSearch);
    int deleteHotSearchByIds(Long[] ids);
    
    /**
     * 搜索建议管理
     */
    List<PsySearchSuggestion> selectSearchSuggestionList(PsySearchSuggestion searchSuggestion);
    int insertSearchSuggestion(PsySearchSuggestion searchSuggestion);
    int updateSearchSuggestion(PsySearchSuggestion searchSuggestion);
    int deleteSearchSuggestionByIds(Long[] ids);
}
