package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;
import com.xihuan.common.core.domain.entity.PsyTQuestionOption;
import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.core.domain.entity.PsyTScoringRule;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.system.mapper.*;
import com.xihuan.system.service.IPsyTScoringRuleService;
import com.xihuan.system.service.IPsyTAdvancedScoringService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 计分规则Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTScoringRuleServiceImpl implements IPsyTScoringRuleService {

    private static final Logger logger = LoggerFactory.getLogger(PsyTScoringRuleServiceImpl.class);

    @Autowired
    private PsyTScoringRuleMapper scoringRuleMapper;
    
    @Autowired
    private PsyTAssessmentRecordMapper assessmentRecordMapper;
    
    @Autowired
    private PsyTScaleMapper scaleMapper;
    
    @Autowired
    private PsyTQuestionMapper questionMapper;
    
    @Autowired
    private PsyTQuestionOptionMapper questionOptionMapper;

    @Autowired
    private IPsyTAdvancedScoringService advancedScoringService;

    /**
     * 查询计分规则列表
     * 
     * @param rule 计分规则信息
     * @return 计分规则集合
     */
    @Override
    public List<PsyTScoringRule> selectRuleList(PsyTScoringRule rule) {
        return scoringRuleMapper.selectRuleList(rule);
    }

    /**
     * 根据ID查询计分规则
     * 
     * @param id 计分规则ID
     * @return 计分规则信息
     */
    @Override
    public PsyTScoringRule selectRuleById(Long id) {
        return scoringRuleMapper.selectRuleById(id);
    }

    /**
     * 根据量表ID查询计分规则列表
     * 
     * @param scaleId 量表ID
     * @return 计分规则集合
     */
    @Override
    public List<PsyTScoringRule> selectRulesByScaleId(Long scaleId) {
        return scoringRuleMapper.selectRulesByScaleId(scaleId);
    }

    /**
     * 根据分量表ID查询计分规则列表
     * 
     * @param subscaleId 分量表ID
     * @return 计分规则集合
     */
    @Override
    public List<PsyTScoringRule> selectRulesBySubscaleId(Long subscaleId) {
        return scoringRuleMapper.selectRulesBySubscaleId(subscaleId);
    }

    /**
     * 查询量表总分计分规则
     * 
     * @param scaleId 量表ID
     * @return 计分规则集合
     */
    @Override
    public List<PsyTScoringRule> selectTotalScoreRules(Long scaleId) {
        return scoringRuleMapper.selectTotalScoreRules(scaleId);
    }

    /**
     * 新增计分规则
     * 
     * @param rule 计分规则信息
     * @return 结果
     */
    @Override
    public int insertRule(PsyTScoringRule rule) {
        rule.setCreateBy(SecurityUtils.getUsername());
        rule.setCreateTime(DateUtils.getNowDate());
        return scoringRuleMapper.insertRule(rule);
    }

    /**
     * 批量新增计分规则
     * 
     * @param rules 计分规则列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertRules(List<PsyTScoringRule> rules) {
        if (rules == null || rules.isEmpty()) {
            return 0;
        }
        
        String username = SecurityUtils.getUsername();
        Date now = DateUtils.getNowDate();
        
        for (PsyTScoringRule rule : rules) {
            rule.setCreateBy(username);
            rule.setCreateTime(now);
        }
        
        return scoringRuleMapper.batchInsertRules(rules);
    }

    /**
     * 修改计分规则
     * 
     * @param rule 计分规则信息
     * @return 结果
     */
    @Override
    public int updateRule(PsyTScoringRule rule) {
        rule.setUpdateBy(SecurityUtils.getUsername());
        rule.setUpdateTime(DateUtils.getNowDate());
        return scoringRuleMapper.updateRule(rule);
    }

    /**
     * 删除计分规则
     * 
     * @param ids 需要删除的计分规则ID
     * @return 结果
     */
    @Override
    public int deleteRuleByIds(Long[] ids) {
        return scoringRuleMapper.deleteRuleByIds(ids);
    }

    /**
     * 根据量表ID删除计分规则
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    @Override
    public int deleteRulesByScaleId(Long scaleId) {
        return scoringRuleMapper.deleteRulesByScaleId(scaleId);
    }

    /**
     * 根据分量表ID删除计分规则
     * 
     * @param subscaleId 分量表ID
     * @return 结果
     */
    @Override
    public int deleteRulesBySubscaleId(Long subscaleId) {
        return scoringRuleMapper.deleteRulesBySubscaleId(subscaleId);
    }

    /**
     * 根据分数查找匹配的计分规则
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID（可为空）
     * @param score 分数
     * @return 计分规则
     */
    @Override
    public PsyTScoringRule findMatchingRule(Long scaleId, Long subscaleId, BigDecimal score) {
        return scoringRuleMapper.findMatchingRule(scaleId, subscaleId, score);
    }

    /**
     * 查找分数对应的所有匹配规则
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID（可为空）
     * @param score 分数
     * @return 计分规则集合
     */
    @Override
    public List<PsyTScoringRule> findAllMatchingRules(Long scaleId, Long subscaleId, BigDecimal score) {
        return scoringRuleMapper.findAllMatchingRules(scaleId, subscaleId, score);
    }

    /**
     * 检查规则范围是否重叠
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param excludeId 排除的ID
     * @return 是否重叠
     */
    @Override
    public boolean checkRuleRangeOverlap(Long scaleId, Long subscaleId, BigDecimal minValue, BigDecimal maxValue, Long excludeId) {
        int count = scoringRuleMapper.checkRuleRangeOverlap(scaleId, subscaleId, minValue, maxValue, excludeId);
        return count > 0;
    }

    /**
     * 统计量表计分规则数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    @Override
    public int countRulesByScaleId(Long scaleId) {
        return scoringRuleMapper.countRulesByScaleId(scaleId);
    }

    /**
     * 统计分量表计分规则数量
     * 
     * @param subscaleId 分量表ID
     * @return 数量
     */
    @Override
    public int countRulesBySubscaleId(Long subscaleId) {
        return scoringRuleMapper.countRulesBySubscaleId(subscaleId);
    }

    /**
     * 查询规则类型统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectRuleTypeStats(Long scaleId) {
        return scoringRuleMapper.selectRuleTypeStats(scaleId);
    }

    /**
     * 查询规则使用统计
     * 
     * @param ruleId 规则ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectRuleUsageStats(Long ruleId) {
        return scoringRuleMapper.selectRuleUsageStats(ruleId);
    }

    /**
     * 复制计分规则到新量表
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int copyRulesToScale(Long sourceScaleId, Long targetScaleId) {
        List<PsyTScoringRule> sourceRules = selectRulesByScaleId(sourceScaleId);
        if (sourceRules.isEmpty()) {
            return 0;
        }
        
        List<PsyTScoringRule> targetRules = new ArrayList<>();
        for (PsyTScoringRule sourceRule : sourceRules) {
            PsyTScoringRule targetRule = new PsyTScoringRule();
            // 复制属性
            targetRule.setScaleId(targetScaleId);
            targetRule.setSubscaleId(sourceRule.getSubscaleId());
            targetRule.setRuleType(sourceRule.getRuleType());
            targetRule.setLabel(sourceRule.getLabel());
            targetRule.setDescription(sourceRule.getDescription());
            targetRule.setMinValue(sourceRule.getMinValue());
            targetRule.setMaxValue(sourceRule.getMaxValue());
            targetRule.setCutoffValue(sourceRule.getCutoffValue());
            targetRule.setSuggestion(sourceRule.getSuggestion());
            
            targetRules.add(targetRule);
        }
        
        return batchInsertRules(targetRules);
    }

    /**
     * 复制计分规则到新分量表
     * 
     * @param sourceSubscaleId 源分量表ID
     * @param targetSubscaleId 目标分量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int copyRulesToSubscale(Long sourceSubscaleId, Long targetSubscaleId) {
        List<PsyTScoringRule> sourceRules = selectRulesBySubscaleId(sourceSubscaleId);
        if (sourceRules.isEmpty()) {
            return 0;
        }
        
        List<PsyTScoringRule> targetRules = new ArrayList<>();
        for (PsyTScoringRule sourceRule : sourceRules) {
            PsyTScoringRule targetRule = new PsyTScoringRule();
            // 复制属性
            targetRule.setScaleId(sourceRule.getScaleId());
            targetRule.setSubscaleId(targetSubscaleId);
            targetRule.setRuleType(sourceRule.getRuleType());
            targetRule.setLabel(sourceRule.getLabel());
            targetRule.setDescription(sourceRule.getDescription());
            targetRule.setMinValue(sourceRule.getMinValue());
            targetRule.setMaxValue(sourceRule.getMaxValue());
            targetRule.setCutoffValue(sourceRule.getCutoffValue());
            targetRule.setSuggestion(sourceRule.getSuggestion());
            
            targetRules.add(targetRule);
        }
        
        return batchInsertRules(targetRules);
    }

    /**
     * 查询规则覆盖范围分析
     *
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 覆盖范围信息
     */
    @Override
    public Map<String, Object> selectRuleCoverageAnalysis(Long scaleId, Long subscaleId) {
        return scoringRuleMapper.selectRuleCoverageAnalysis(scaleId, subscaleId);
    }

    /**
     * 查询规则分数分布
     *
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 分布信息
     */
    @Override
    public List<Map<String, Object>> selectRuleScoreDistribution(Long scaleId, Long subscaleId) {
        return scoringRuleMapper.selectRuleScoreDistribution(scaleId, subscaleId);
    }

    /**
     * 搜索计分规则
     *
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @param ruleType 规则类型
     * @return 计分规则集合
     */
    @Override
    public List<PsyTScoringRule> searchRules(String keyword, Long scaleId, String ruleType) {
        return scoringRuleMapper.searchRules(keyword, scaleId, ruleType);
    }

    /**
     * 验证规则完整性
     *
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 验证结果
     */
    @Override
    public Map<String, Object> validateRuleCompleteness(Long scaleId, Long subscaleId) {
        Map<String, Object> result = new HashMap<>();

        List<PsyTScoringRule> rules;
        if (subscaleId != null) {
            rules = selectRulesBySubscaleId(subscaleId);
        } else {
            rules = selectRulesByScaleId(scaleId);
        }

        if (rules.isEmpty()) {
            result.put("complete", false);
            result.put("message", "未配置计分规则");
            return result;
        }

        // 检查分数范围覆盖
        BigDecimal minCovered = null;
        BigDecimal maxCovered = null;
        List<String> gaps = new ArrayList<>();

        rules.sort((r1, r2) -> r1.getMinValue().compareTo(r2.getMinValue()));

        for (int i = 0; i < rules.size(); i++) {
            PsyTScoringRule rule = rules.get(i);

            if (minCovered == null) {
                minCovered = rule.getMinValue();
            }

            if (i > 0) {
                PsyTScoringRule prevRule = rules.get(i - 1);
                if (prevRule.getMaxValue().compareTo(rule.getMinValue()) < 0) {
                    gaps.add(String.format("%.2f - %.2f", prevRule.getMaxValue(), rule.getMinValue()));
                }
            }

            maxCovered = rule.getMaxValue();
        }

        result.put("complete", gaps.isEmpty());
        result.put("minCovered", minCovered);
        result.put("maxCovered", maxCovered);
        result.put("gaps", gaps);
        result.put("ruleCount", rules.size());

        if (gaps.isEmpty()) {
            result.put("message", "规则配置完整");
        } else {
            result.put("message", "存在分数范围缺口：" + String.join(", ", gaps));
        }

        return result;
    }

    /**
     * 解释测评结果
     *
     * @param scaleId 量表ID
     * @param totalScore 总分
     * @param subscaleScores 分量表得分
     * @return 结果解释
     */
    @Override
    public Map<String, Object> interpretAssessmentResult(Long scaleId, BigDecimal totalScore, Map<Long, BigDecimal> subscaleScores) {
        Map<String, Object> result = new HashMap<>();

        // 解释总分
        if (totalScore != null) {
            PsyTScoringRule totalRule = findMatchingRule(scaleId, null, totalScore);
            if (totalRule != null) {
                Map<String, Object> totalInterpretation = new HashMap<>();
                totalInterpretation.put("score", totalScore);
                totalInterpretation.put("level", totalRule.getLabel());
                totalInterpretation.put("description", totalRule.getDescription());
                result.put("totalScore", totalInterpretation);
            }
        }

        // 解释分量表得分
        if (subscaleScores != null && !subscaleScores.isEmpty()) {
            Map<String, Object> subscaleInterpretations = new HashMap<>();

            for (Map.Entry<Long, BigDecimal> entry : subscaleScores.entrySet()) {
                Long subscaleId = entry.getKey();
                BigDecimal score = entry.getValue();

                PsyTScoringRule rule = findMatchingRule(scaleId, subscaleId, score);
                if (rule != null) {
                    Map<String, Object> interpretation = new HashMap<>();
                    interpretation.put("score", score);
                    interpretation.put("level", rule.getLabel());
                    interpretation.put("description", rule.getDescription());
                    subscaleInterpretations.put(subscaleId.toString(), interpretation);
                }
            }

            result.put("subscaleScores", subscaleInterpretations);
        }

        return result;
    }

    /**
     * 生成结果报告
     *
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    @Override
    public Map<String, Object> generateResultReport(Long recordId) {
        Map<String, Object> report = new HashMap<>();

        // 获取测评记录
        PsyTAssessmentRecord record = assessmentRecordMapper.selectAssessmentRecordById(recordId);
        if (record == null) {
            report.put("error", "测评记录不存在");
            return report;
        }

        // 获取量表信息
        PsyTScale scale = scaleMapper.selectScaleById(record.getScaleId());
        if (scale == null) {
            report.put("error", "量表不存在");
            return report;
        }

        // 基本信息
        report.put("recordId", recordId);
        report.put("scaleId", record.getScaleId());
        report.put("scaleName", scale.getName());
        report.put("userId", record.getUserId());
        report.put("startTime", record.getStartTime());
        report.put("endTime", record.getEndTime());
        report.put("totalScore", record.getTotalScore());

        // 解释结果
        Map<String, Object> interpretation = interpretAssessmentResult(
            record.getScaleId(),
            record.getTotalScore(),
            null // 这里需要获取分量表得分
        );
        report.put("interpretation", interpretation);

        // 生成建议
        List<String> suggestions = generateSuggestions(record);
        report.put("suggestions", suggestions);

        report.put("generateTime", new Date());

        return report;
    }

    /**
     * 生成建议
     */
    private List<String> generateSuggestions(PsyTAssessmentRecord record) {
        List<String> suggestions = new ArrayList<>();

        // 根据总分生成建议
        if (record.getTotalScore() != null) {
            PsyTScoringRule rule = findMatchingRule(record.getScaleId(), null, record.getTotalScore());
            if (rule != null && rule.getSuggestion() != null) {
                suggestions.add("根据您的测评结果：" + rule.getSuggestion());
            }
        }

        // 可以根据具体业务需求添加更多建议逻辑
        suggestions.add("建议定期进行心理健康评估");
        suggestions.add("如有疑问，请咨询专业心理咨询师");

        return suggestions;
    }

    /**
     * 自动生成计分规则
     *
     * @param scaleId 量表ID
     * @param strategy 生成策略
     * @return 结果
     */
    @Override
    @Transactional
    public int autoGenerateRules(Long scaleId, String strategy) {
        // 删除现有规则
        deleteRulesByScaleId(scaleId);

        List<PsyTScoringRule> rules = new ArrayList<>();

        if ("FIVE_LEVEL".equals(strategy)) {
            // 生成五级评分规则
            rules = generateFiveLevelRules(scaleId);
        } else if ("THREE_LEVEL".equals(strategy)) {
            // 生成三级评分规则
            rules = generateThreeLevelRules(scaleId);
        } else {
            // 默认生成五级规则
            rules = generateFiveLevelRules(scaleId);
        }

        return batchInsertRules(rules);
    }

    /**
     * 生成五级评分规则
     */
    private List<PsyTScoringRule> generateFiveLevelRules(Long scaleId) {
        List<PsyTScoringRule> rules = new ArrayList<>();

        String[] levels = {"很低", "较低", "中等", "较高", "很高"};
        String[] descriptions = {
            "得分很低，建议关注相关方面的发展",
            "得分较低，有一定的提升空间",
            "得分中等，表现正常",
            "得分较高，表现良好",
            "得分很高，表现优秀"
        };

        // 假设总分范围是0-100
        BigDecimal[] ranges = {
            new BigDecimal("0"), new BigDecimal("20"),
            new BigDecimal("20"), new BigDecimal("40"),
            new BigDecimal("40"), new BigDecimal("60"),
            new BigDecimal("60"), new BigDecimal("80"),
            new BigDecimal("80"), new BigDecimal("100")
        };

        for (int i = 0; i < levels.length; i++) {
            PsyTScoringRule rule = new PsyTScoringRule();
            rule.setScaleId(scaleId);
            rule.setRuleType("TOTAL_SCORE");
            rule.setLabel(levels[i]);
            rule.setMinValue(ranges[i * 2]);
            rule.setMaxValue(ranges[i * 2 + 1]);
            rule.setDescription(descriptions[i]);
            rule.setSuggestion(descriptions[i]);

            rules.add(rule);
        }

        return rules;
    }

    /**
     * 生成三级评分规则
     */
    private List<PsyTScoringRule> generateThreeLevelRules(Long scaleId) {
        List<PsyTScoringRule> rules = new ArrayList<>();

        String[] levels = {"低", "中", "高"};
        String[] descriptions = {
            "得分较低，建议关注",
            "得分中等，表现正常",
            "得分较高，表现良好"
        };

        // 假设总分范围是0-100
        BigDecimal[] ranges = {
            new BigDecimal("0"), new BigDecimal("33"),
            new BigDecimal("33"), new BigDecimal("67"),
            new BigDecimal("67"), new BigDecimal("100")
        };

        for (int i = 0; i < levels.length; i++) {
            PsyTScoringRule rule = new PsyTScoringRule();
            rule.setScaleId(scaleId);
            rule.setRuleType("TOTAL_SCORE");
            rule.setLabel(levels[i]);
            rule.setMinValue(ranges[i * 2]);
            rule.setMaxValue(ranges[i * 2 + 1]);
            rule.setDescription(descriptions[i]);
            rule.setSuggestion(descriptions[i]);

            rules.add(rule);
        }

        return rules;
    }

    /**
     * 优化计分规则
     *
     * @param scaleId 量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int optimizeRules(Long scaleId) {
        List<PsyTScoringRule> rules = selectRulesByScaleId(scaleId);
        if (rules.isEmpty()) {
            return 0;
        }

        // 排序规则
        rules.sort((r1, r2) -> r1.getMinValue().compareTo(r2.getMinValue()));

        // 优化规则范围，消除重叠和间隙
        for (int i = 0; i < rules.size() - 1; i++) {
            PsyTScoringRule currentRule = rules.get(i);
            PsyTScoringRule nextRule = rules.get(i + 1);

            // 如果当前规则的最大值小于下一个规则的最小值，调整范围
            if (currentRule.getMaxValue().compareTo(nextRule.getMinValue()) < 0) {
                currentRule.setMaxValue(nextRule.getMinValue());
            }
            // 如果有重叠，调整下一个规则的最小值
            else if (currentRule.getMaxValue().compareTo(nextRule.getMinValue()) > 0) {
                nextRule.setMinValue(currentRule.getMaxValue());
            }
        }

        // 更新规则
        int updateCount = 0;
        for (PsyTScoringRule rule : rules) {
            updateCount += updateRule(rule);
        }

        return updateCount;
    }

    /**
     * 导出计分规则
     *
     * @param scaleId 量表ID
     * @return 规则数据
     */
    @Override
    public Map<String, Object> exportRules(Long scaleId) {
        Map<String, Object> exportData = new HashMap<>();

        List<PsyTScoringRule> rules = selectRulesByScaleId(scaleId);
        exportData.put("rules", rules);
        exportData.put("scaleId", scaleId);
        exportData.put("exportTime", new Date());
        exportData.put("ruleCount", rules.size());

        return exportData;
    }

    /**
     * 导入计分规则
     *
     * @param scaleId 量表ID
     * @param ruleData 规则数据
     * @return 结果
     */
    @Override
    @Transactional
    public int importRules(Long scaleId, Map<String, Object> ruleData) {
        if (ruleData == null || !ruleData.containsKey("rules")) {
            throw new RuntimeException("规则数据格式错误");
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rulesData = (List<Map<String, Object>>) ruleData.get("rules");

        List<PsyTScoringRule> rules = new ArrayList<>();
        for (Map<String, Object> ruleMap : rulesData) {
            PsyTScoringRule rule = new PsyTScoringRule();
            rule.setScaleId(scaleId);
            rule.setRuleType((String) ruleMap.get("ruleType"));
            rule.setLabel((String) ruleMap.get("label"));
            rule.setDescription((String) ruleMap.get("description"));

            if (ruleMap.get("minValue") != null) {
                rule.setMinValue(new BigDecimal(ruleMap.get("minValue").toString()));
            }
            if (ruleMap.get("maxValue") != null) {
                rule.setMaxValue(new BigDecimal(ruleMap.get("maxValue").toString()));
            }
            if (ruleMap.get("cutoffValue") != null) {
                rule.setCutoffValue(new BigDecimal(ruleMap.get("cutoffValue").toString()));
            }

            rule.setSuggestion((String) ruleMap.get("suggestion"));

            rules.add(rule);
        }

        return batchInsertRules(rules);
    }

    /**
     * 计算常模分数
     *
     * @param scaleId 量表ID
     * @param rawScore 原始分数
     * @return 常模分数
     */
    @Override
    public BigDecimal calculateNormScore(Long scaleId, BigDecimal rawScore) {
        // 这里可以实现具体的常模转换算法
        // 简单示例：假设常模分数 = (原始分数 / 最大分数) * 100

        // 获取量表的最大分数（这里需要根据实际业务逻辑计算）
        BigDecimal maxScore = new BigDecimal("100"); // 假设最大分数为100

        if (rawScore.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        if (rawScore.compareTo(maxScore) >= 0) {
            return new BigDecimal("100");
        }

        return rawScore.divide(maxScore, 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 计算百分位数
     *
     * @param scaleId 量表ID
     * @param score 分数
     * @return 百分位数
     */
    @Override
    public BigDecimal calculatePercentile(Long scaleId, BigDecimal score) {
        // 这里可以实现具体的百分位数计算算法
        // 需要基于历史数据进行计算

        // 简单示例：基于正态分布假设
        BigDecimal mean = new BigDecimal("50"); // 假设平均分为50
        BigDecimal stdDev = new BigDecimal("15"); // 假设标准差为15

        // 计算Z分数
        BigDecimal zScore = score.subtract(mean).divide(stdDev, 4, RoundingMode.HALF_UP);

        // 这里应该使用正态分布累积函数，简化处理
        if (zScore.compareTo(new BigDecimal("-2")) <= 0) {
            return new BigDecimal("2.5");
        } else if (zScore.compareTo(new BigDecimal("-1")) <= 0) {
            return new BigDecimal("16");
        } else if (zScore.compareTo(new BigDecimal("0")) <= 0) {
            return new BigDecimal("50");
        } else if (zScore.compareTo(new BigDecimal("1")) <= 0) {
            return new BigDecimal("84");
        } else {
            return new BigDecimal("97.5");
        }
    }

    /**
     * 获取分数等级
     *
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @param score 分数
     * @return 等级信息
     */
    @Override
    public Map<String, Object> getScoreLevel(Long scaleId, Long subscaleId, BigDecimal score) {
        Map<String, Object> result = new HashMap<>();

        PsyTScoringRule rule = findMatchingRule(scaleId, subscaleId, score);
        if (rule != null) {
            result.put("level", rule.getLabel());
            result.put("description", rule.getDescription());
            result.put("minValue", rule.getMinValue());
            result.put("maxValue", rule.getMaxValue());
            result.put("ruleType", rule.getRuleType());
        } else {
            result.put("level", "未知");
            result.put("description", "未找到匹配的评分规则");
        }

        result.put("score", score);
        result.put("scaleId", scaleId);
        result.put("subscaleId", subscaleId);

        return result;
    }

    /**
     * 计算题目分数
     *
     * @param questionId 题目ID
     * @param optionId 选项ID
     * @param answerContent 答案内容
     * @return 分数
     */
    @Override
    public BigDecimal calculateQuestionScore(Long questionId, Long optionId, String answerContent) {
        BigDecimal score = BigDecimal.ZERO;

        try {
            // 如果有选项ID，获取选项分数
            if (optionId != null) {
                PsyTQuestionOption option = questionOptionMapper.selectOptionById(optionId);
                logger.info("计算分数 - questionId: {}, optionId: {}, answerContent: {}", questionId, optionId, answerContent);

                if (option != null) {
                    logger.info("选项信息 - optionText: {}, optionValue: {}", option.getOptionText(), option.getOptionValue());

                    if (option.getOptionValue() != null) {
                        // 直接使用Integer值转换为BigDecimal
                        score = new BigDecimal(option.getOptionValue());
                        logger.info("从选项获得分数: {}", score);
                    } else {
                        logger.warn("选项值为空，尝试根据选项内容推断分数");
                        // 如果选项值为空，尝试根据选项内容推断分数
                        if (option.getOptionText() != null) {
                            score = inferScoreFromContent(option.getOptionText());
                            logger.info("根据选项内容推断分数: content={}, score={}", option.getOptionText(), score);
                        }
                    }
                } else {
                    logger.warn("找不到选项: optionId={}", optionId);
                }
            }

            // 如果没有选项分数，尝试解析答案内容
            if (score.compareTo(BigDecimal.ZERO) == 0 && answerContent != null && !answerContent.trim().isEmpty()) {
                try {
                    score = new BigDecimal(answerContent.trim());
                } catch (NumberFormatException e) {
                    // 如果答案内容不是数字，可以根据具体业务逻辑处理
                    // 例如：是/否题目，"是"=1分，"否"=0分
                    if ("是".equals(answerContent) || "true".equalsIgnoreCase(answerContent) || "1".equals(answerContent)) {
                        score = BigDecimal.ONE;
                    } else if ("否".equals(answerContent) || "false".equalsIgnoreCase(answerContent) || "0".equals(answerContent)) {
                        score = BigDecimal.ZERO;
                    }
                }
            }

        } catch (Exception e) {
            // 记录错误日志，但不抛出异常，返回0分
            logger.error("计算分数时发生异常: questionId={}, optionId={}, answerContent={}",
                questionId, optionId, answerContent, e);
            score = BigDecimal.ZERO;
        }

        logger.info("最终计算分数: questionId={}, optionId={}, score={}", questionId, optionId, score);
        return score;
    }

    /**
     * 根据选项内容推断分数（用于选项值为空的情况）
     */
    private BigDecimal inferScoreFromContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }

        String trimmedContent = content.trim();

        // 李克特量表常见选项
        if (trimmedContent.contains("非常同意") || trimmedContent.contains("完全同意")) {
            return new BigDecimal("5");
        } else if (trimmedContent.contains("同意") || trimmedContent.contains("比较同意")) {
            return new BigDecimal("4");
        } else if (trimmedContent.contains("中立") || trimmedContent.contains("一般") || trimmedContent.contains("不确定")) {
            return new BigDecimal("3");
        } else if (trimmedContent.contains("不同意") || trimmedContent.contains("比较不同意")) {
            return new BigDecimal("2");
        } else if (trimmedContent.contains("非常不同意") || trimmedContent.contains("完全不同意")) {
            return new BigDecimal("1");
        }

        // 频率量表
        if (trimmedContent.contains("总是") || trimmedContent.contains("经常")) {
            return new BigDecimal("5");
        } else if (trimmedContent.contains("有时") || trimmedContent.contains("偶尔")) {
            return new BigDecimal("3");
        } else if (trimmedContent.contains("从不") || trimmedContent.contains("很少")) {
            return new BigDecimal("1");
        }

        // 数字选项
        try {
            return new BigDecimal(trimmedContent);
        } catch (NumberFormatException e) {
            // 不是数字，返回0
        }

        return BigDecimal.ZERO;
    }

    /**
     * 高级计分 - 支持复杂计分逻辑
     */
    @Override
    public Map<String, Object> calculateAdvancedScore(Long recordId) {
        return advancedScoringService.executeAdvancedScoring(recordId);
    }

    /**
     * 计算反向计分
     */
    @Override
    public BigDecimal calculateReverseScore(BigDecimal originalScore, Integer maxValue) {
        return advancedScoringService.calculateReverseScore(originalScore, maxValue);
    }

    /**
     * 计算标准分
     */
    @Override
    public BigDecimal calculateStandardScore(BigDecimal rawScore, BigDecimal multiplier) {
        return advancedScoringService.calculateStandardScore(rawScore, multiplier);
    }

    /**
     * 计算维度分数
     */
    @Override
    public Map<String, BigDecimal> calculateDimensionScores(Long recordId, Long scaleId) {
        try {
            // 获取量表信息
            PsyTScale scale = scaleMapper.selectScaleById(scaleId);
            if (scale != null && scale.getCode() != null) {
                return advancedScoringService.calculateDimensionScores(recordId, scale.getCode());
            }
        } catch (Exception e) {
            logger.error("计算维度分数异常，recordId: {}, scaleId: {}", recordId, scaleId, e);
        }
        return new HashMap<>();
    }
}
