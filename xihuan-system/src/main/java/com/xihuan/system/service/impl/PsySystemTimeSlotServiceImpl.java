package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.spring.SpringUtils;
import com.xihuan.system.mapper.PsySystemTimeSlotMapper;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统公共时间槽Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsySystemTimeSlotServiceImpl implements IPsySystemTimeSlotService {

    private static final Logger logger = LoggerFactory.getLogger(PsyTimeCounselorScheduleServiceImpl.class);
    
    @Autowired
    private PsySystemTimeSlotMapper systemTimeSlotMapper;
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;
    
    @Autowired
    private IPsyTimeRangeService timeRangeService;

    /**
     * 查询系统时间槽列表
     */
    @Override
    public List<PsySystemTimeSlot> selectSystemTimeSlotList(PsySystemTimeSlot systemTimeSlot) {
        return systemTimeSlotMapper.selectSystemTimeSlotList(systemTimeSlot);
    }

    /**
     * 根据ID查询系统时间槽
     */
    @Override
    public PsySystemTimeSlot selectSystemTimeSlotById(Long id) {
        return systemTimeSlotMapper.selectSystemTimeSlotById(id);
    }

    /**
     * 查询指定日期范围的系统时间槽
     */
    @Override
    public List<PsySystemTimeSlot> selectSlotsByDateRange(LocalDate startDate, LocalDate endDate, Long centerId) {
        return systemTimeSlotMapper.selectSlotsByDateRange(startDate.toString(), endDate.toString(), centerId);
    }

    /**
     * 查询指定日期的系统时间槽
     */
    @Override
    public List<PsySystemTimeSlot> selectSlotsByDate(LocalDate date, Long centerId) {
        return systemTimeSlotMapper.selectSlotsByDate(date.toString(), centerId);
    }

    /**
     * 查询有可用咨询师的时间槽
     */
    @Override
    public List<PsySystemTimeSlot> selectAvailableSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        return systemTimeSlotMapper.selectAvailableSlots(startDate.toString(), endDate.toString(), centerId);
    }

    /**
     * 获取格式化的系统时间槽数据（按日期和时间段分组）
     */
    @Override
    public Map<String, Object> getFormattedSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        List<PsySystemTimeSlot> slots = selectSlotsByDateRange(startDate, endDate, centerId);
        
        Map<String, Object> result = new HashMap<>();

        // 按日期分组
        Map<String, List<PsySystemTimeSlot>> slotsByDate = slots.stream()
            .collect(Collectors.groupingBy(PsySystemTimeSlot::getDateKey));

        List<Map<String, Object>> dateList = new ArrayList<>();
        int totalSlots = 0;
        int availableSlots = 0;
        
        for (Map.Entry<String, List<PsySystemTimeSlot>> entry : slotsByDate.entrySet()) {
            String dateKey = entry.getKey();
            List<PsySystemTimeSlot> daySlots = entry.getValue();
            
            Map<String, Object> dayData = buildDayData(dateKey, daySlots);
            dateList.add(dayData);
            
            totalSlots += daySlots.size();
            availableSlots += (int) daySlots.stream().filter(slot -> slot.getHasAvailable()).count();
        }

        // 按日期排序
        dateList.sort((a, b) -> ((String) a.get("date")).compareTo((String) b.get("date")));
        
        result.put("dates", dateList);
        result.put("totalDays", dateList.size());
        result.put("totalSlots", totalSlots);
        result.put("availableSlots", availableSlots);
        
        return result;
    }

    /**
     * 新增系统时间槽
     */
    @Override
    public int insertSystemTimeSlot(PsySystemTimeSlot systemTimeSlot) {
        systemTimeSlot.setCreateTime(DateUtils.getNowDate());
        return systemTimeSlotMapper.insertSystemTimeSlot(systemTimeSlot);
    }

    /**
     * 批量新增系统时间槽
     */
    @Override
    @Transactional
    public int batchInsertSystemTimeSlots(List<PsySystemTimeSlot> systemTimeSlots) {
        if (CollectionUtils.isEmpty(systemTimeSlots)) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        systemTimeSlots.forEach(slot -> slot.setCreateTime(now));
        
        return systemTimeSlotMapper.batchInsertSystemTimeSlots(systemTimeSlots);
    }

    /**
     * 修改系统时间槽
     */
    @Override
    public int updateSystemTimeSlot(PsySystemTimeSlot systemTimeSlot) {
        systemTimeSlot.setUpdateTime(DateUtils.getNowDate());
        return systemTimeSlotMapper.updateSystemTimeSlot(systemTimeSlot);
    }

    /**
     * 删除系统时间槽信息
     */
    @Override
    public int deleteSystemTimeSlotById(Long id) {
        return systemTimeSlotMapper.deleteSystemTimeSlotById(id);
    }

    /**
     * 批量删除系统时间槽
     */
    @Override
    public int deleteSystemTimeSlotByIds(Long[] ids) {
        return systemTimeSlotMapper.deleteSystemTimeSlotByIds(ids);
    }

    /**
     * 生成指定日期范围的系统时间槽
     */
    @Override
    @Transactional
    public int generateSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        logger.info("开始生成系统时间槽，日期范围：{} 到 {}，咨询中心：{}", startDate, endDate, centerId);

        List<PsySystemTimeSlot> systemSlots = new ArrayList<>();

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            List<PsySystemTimeSlot> daySlots = generateSystemTimeSlotsForDate(currentDate, centerId);
            systemSlots.addAll(daySlots);
            currentDate = currentDate.plusDays(1);
        }

        if (!systemSlots.isEmpty()) {
            // 过滤掉已存在的时间槽，避免重复插入
            List<PsySystemTimeSlot> newSlots = filterExistingSlots(systemSlots);
            if (!newSlots.isEmpty()) {
                int result = batchInsertSystemTimeSlots(newSlots);
                logger.info("成功生成 {} 个系统时间槽（过滤掉 {} 个已存在的）",
                    result, systemSlots.size() - newSlots.size());
                return result;
            } else {
                logger.info("所有时间槽都已存在，无需生成新的系统时间槽");
                return 0;
            }
        }

        logger.info("没有找到可用的咨询师时间槽，无法生成系统时间槽");
        return 0;
    }

    /**
     * 更新系统时间槽的可用性统计
     */
    @Override
    @Transactional
    public int updateAvailabilityStats(LocalDate date, Long centerId) {
        return systemTimeSlotMapper.batchUpdateAvailabilityStats(date.toString(), centerId);
    }

    /**
     * 清理过期的系统时间槽
     */
    @Override
    @Transactional
    public int cleanExpiredSystemSlots(LocalDate beforeDate, Long centerId) {
        return systemTimeSlotMapper.deleteSlotsByDateRange("1900-01-01", beforeDate.toString(), centerId);
    }

    /**
     * 重新生成系统时间槽（先清理再生成）
     */
    @Override
    @Transactional
    public int regenerateSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        // 先删除指定日期范围的系统时间槽
        systemTimeSlotMapper.deleteSlotsByDateRange(startDate.toString(), endDate.toString(), centerId);
        
        // 重新生成
        return generateSystemTimeSlots(startDate, endDate, centerId);
    }

    /**
     * 为指定日期生成系统时间槽
     * 直接基于 psy_time_range 表生成，不依赖咨询师时间槽
     */
    private List<PsySystemTimeSlot> generateSystemTimeSlotsForDate(LocalDate date, Long centerId) {
        List<PsySystemTimeSlot> systemSlots = new ArrayList<>();

        try {
            // 获取所有有效的系统时间段
            List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();

            logger.info("为日期 {} 查询系统时间段，找到 {} 个", date, timeRanges != null ? timeRanges.size() : 0);

            if (CollectionUtils.isEmpty(timeRanges)) {
                logger.warn("系统中没有配置时间段，无法生成系统时间槽");
                // 尝试初始化默认时间段
                int initResult = timeRangeService.initDefaultTimeRanges();
                if (initResult > 0) {
                    logger.info("成功初始化 {} 个默认时间段", initResult);
                    timeRanges = timeRangeService.selectAllActiveTimeRanges();
                } else {
                    return systemSlots;
                }
            }

            // 打印时间段详情并验证数据完整性
            logger.info("开始为日期 {} 生成系统时间槽，时间段配置如下：", date);
            for (PsyTimeRange range : timeRanges) {
                logger.info("时间段：ID={}, 名称={}, {}:00-{}:00, 状态={}",
                    range.getId(), range.getName(), range.getStartHour(), range.getEndHour(), range.getDelFlag());

                // 验证时间段配置的合理性
                if (range.getStartHour() == null || range.getEndHour() == null) {
                    logger.warn("时间段 {} 配置不完整，跳过", range.getId());
                    continue;
                }
                if (range.getStartHour() >= range.getEndHour()) {
                    logger.warn("时间段 {} 开始时间 {} 不能大于等于结束时间 {}",
                        range.getId(), range.getStartHour(), range.getEndHour());
                    continue;
                }
            }

            // 为每个时间段生成系统时间槽
            int totalGenerated = 0;
            for (PsyTimeRange timeRange : timeRanges) {
                // 跳过无效的时间段
                if (timeRange.getStartHour() == null || timeRange.getEndHour() == null ||
                    timeRange.getStartHour() >= timeRange.getEndHour()) {
                    continue;
                }

                List<PsySystemTimeSlot> rangeSlots = generateSystemSlotsFromTimeRange(date, timeRange, centerId);
                systemSlots.addAll(rangeSlots);
                totalGenerated += rangeSlots.size();

                logger.debug("时间段 {}:00-{}:00 生成了 {} 个时间槽",
                    timeRange.getStartHour(), timeRange.getEndHour(), rangeSlots.size());
            }

            logger.info("为日期 {} 总共生成了 {} 个系统时间槽", date, totalGenerated);

        } catch (Exception e) {
            logger.error("为日期 {} 生成系统时间槽失败: {}", date, e.getMessage());
        }

        return systemSlots;
    }

    /**
     * 根据时间段生成系统时间槽
     * 优化版本：确保时间槽连续性，改进边界处理
     */
    private List<PsySystemTimeSlot> generateSystemSlotsFromTimeRange(LocalDate date, PsyTimeRange timeRange, Long centerId) {
        List<PsySystemTimeSlot> slots = new ArrayList<>();

        try {
            // 将小时转换为具体时间
            LocalTime startTime = LocalTime.of(timeRange.getStartHour(), 0);
            LocalTime endTime = LocalTime.of(timeRange.getEndHour(), 0);

            logger.debug("为时间段 {}:00-{}:00 生成时间槽", timeRange.getStartHour(), timeRange.getEndHour());

            // 每15分钟生成一个系统时间槽
            LocalTime current = startTime;
            int slotCount = 0;

            while (current.isBefore(endTime)) {
                LocalTime slotEnd = current.plusMinutes(15);

                // 如果时间槽结束时间超过时间段结束时间，调整为时间段结束时间
                if (slotEnd.isAfter(endTime)) {
                    // 如果剩余时间不足15分钟但大于等于5分钟，仍然生成一个时间槽
                    if (current.plusMinutes(5).isBefore(endTime) || current.plusMinutes(5).equals(endTime)) {
                        slotEnd = endTime;
                    } else {
                        break; // 剩余时间太少，不生成时间槽
                    }
                }

                PsySystemTimeSlot systemSlot = createSystemTimeSlotFromRange(date, current, slotEnd, timeRange, centerId);
                if (systemSlot != null) {
                    slots.add(systemSlot);
                    slotCount++;
                    logger.trace("生成时间槽：{}-{}", current, slotEnd);
                }

                current = slotEnd;
            }

            logger.debug("时间段 {}:00-{}:00 成功生成 {} 个时间槽",
                timeRange.getStartHour(), timeRange.getEndHour(), slotCount);

        } catch (Exception e) {
            logger.error("为时间段 {}:00-{}:00 生成时间槽时发生错误: {}",
                timeRange.getStartHour(), timeRange.getEndHour(), e.getMessage(), e);
        }

        return slots;
    }

    /**
     * 创建系统时间槽（基于时间段）
     * 增强版本：添加数据验证和错误处理
     */
    private PsySystemTimeSlot createSystemTimeSlotFromRange(LocalDate date, LocalTime startTime, LocalTime endTime,
                                                           PsyTimeRange timeRange, Long centerId) {
        try {
            // 数据验证
            if (date == null || startTime == null || endTime == null || timeRange == null) {
                logger.warn("创建系统时间槽时参数不完整：date={}, startTime={}, endTime={}, timeRange={}",
                    date, startTime, endTime, timeRange != null ? timeRange.getId() : null);
                return null;
            }

            if (!startTime.isBefore(endTime)) {
                logger.warn("创建系统时间槽时时间范围无效：startTime={}, endTime={}", startTime, endTime);
                return null;
            }
            PsySystemTimeSlot systemSlot = new PsySystemTimeSlot();

            systemSlot.setCenterId(centerId);
            systemSlot.setDateKey(date.toString());
            systemSlot.setWeekDay(getWeekDay(date));
            systemSlot.setStartTime(startTime);
            systemSlot.setEndTime(endTime);
            systemSlot.setRangeId(timeRange.getId());

            // 暂时设置默认值，后续可以通过定时任务更新统计信息
            systemSlot.setAvailableCounselors(0); // 初始为0，后续统计更新
            systemSlot.setTotalCounselors(0);
            systemSlot.setHasAvailable(false); // 初始为false，有咨询师时间槽时会更新为true

            systemSlot.setDelFlag(0);
            systemSlot.setStatus(0); // 默认可用状态

            return systemSlot;

        } catch (Exception e) {
            logger.error("创建系统时间槽时发生错误：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建系统时间槽（原有方法，保持兼容性）
     */
    private PsySystemTimeSlot createSystemTimeSlot(LocalDate date, LocalTime startTime,
                                                   List<PsyTimeSlot> timeSlots, Long centerId) {
        PsySystemTimeSlot systemSlot = new PsySystemTimeSlot();
        
        systemSlot.setCenterId(centerId);
        systemSlot.setDateKey(date.toString());
        systemSlot.setWeekDay(getWeekDay(date));
        systemSlot.setStartTime(startTime);
        systemSlot.setEndTime(startTime.plusMinutes(15));
        
        // 统计可用咨询师数量
        long availableCount = timeSlots.stream()
            .filter(slot -> slot.getStatus() == 0) // 可用状态
            .count();
        
        systemSlot.setAvailableCounselors((int) availableCount);
        systemSlot.setTotalCounselors(timeSlots.size());
        systemSlot.setHasAvailable(availableCount > 0);
        
        // 设置时间段ID
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeByHour(startTime.getHour());
        if (timeRange != null) {
            systemSlot.setRangeId(timeRange.getId());
        }
        
        systemSlot.setDelFlag(0);
        systemSlot.setStatus(0); // 默认可用状态

        return systemSlot;
    }

    /**
     * 构建日期数据
     */
    private Map<String, Object> buildDayData(String dateKey, List<PsySystemTimeSlot> daySlots) {
        Map<String, Object> dayData = new HashMap<>();
        LocalDate date = LocalDate.parse(dateKey);
        
        dayData.put("date", dateKey);
        dayData.put("weekDay", getWeekDay(date));
        dayData.put("isToday", date.equals(LocalDate.now()));
        
        // 按时间段分组
        Map<Long, List<PsySystemTimeSlot>> slotsByRange = daySlots.stream()
            .collect(Collectors.groupingBy(PsySystemTimeSlot::getRangeId));
        
        List<Map<String, Object>> timeRanges = new ArrayList<>();
        for (Map.Entry<Long, List<PsySystemTimeSlot>> entry : slotsByRange.entrySet()) {
            Map<String, Object> rangeData = buildTimeRangeData(entry.getKey(), entry.getValue());
            timeRanges.add(rangeData);
        }
        
        // 按时间段开始时间排序
        timeRanges.sort((a, b) -> {
            Integer startHourA = (Integer) a.get("startHour");
            Integer startHourB = (Integer) b.get("startHour");
            return startHourA.compareTo(startHourB);
        });
        
        dayData.put("timeRanges", timeRanges);
        dayData.put("totalSlots", daySlots.size());
        dayData.put("availableSlots", (int) daySlots.stream().filter(slot -> slot.getHasAvailable()).count());
        
        return dayData;
    }

    /**
     * 构建时间段数据
     */
    private Map<String, Object> buildTimeRangeData(Long rangeId, List<PsySystemTimeSlot> rangeSlots) {
        Map<String, Object> rangeData = new HashMap<>();
        
        // 获取时间段信息
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeById(rangeId);
        if (timeRange != null) {
            rangeData.put("rangeName", timeRange.getName());
            rangeData.put("iconUrl", timeRange.getIconUrl());
            rangeData.put("startHour", timeRange.getStartHour());
            rangeData.put("endHour", timeRange.getEndHour());
        }
        
        // 转换时间槽数据
        List<Map<String, Object>> slots = rangeSlots.stream()
            .map(this::convertToSlotData)
            .sorted((a, b) -> ((LocalTime) a.get("startTime")).compareTo((LocalTime) b.get("startTime")))
            .collect(Collectors.toList());
        
        rangeData.put("slots", slots);
        rangeData.put("totalSlots", rangeSlots.size());
        rangeData.put("availableSlots", (int) rangeSlots.stream().filter(slot -> slot.getHasAvailable()).count());
        
        return rangeData;
    }

    /**
     * 转换为时间槽数据
     */
    private Map<String, Object> convertToSlotData(PsySystemTimeSlot slot) {
        Map<String, Object> slotData = new HashMap<>();
        slotData.put("slotId", slot.getId());
        slotData.put("startTime", slot.getStartTime());
        slotData.put("endTime", slot.getEndTime());
        slotData.put("timeDisplay", slot.getStartTime() + "-" + slot.getEndTime());
        slotData.put("availableCounselors", slot.getAvailableCounselors());
        slotData.put("totalCounselors", slot.getTotalCounselors());
        slotData.put("hasAvailable", slot.getHasAvailable());
        slotData.put("status", slot.getStatus());
        slotData.put("statusText", getStatusText(slot.getStatus()));
        slotData.put("availabilityText", slot.getAvailableCounselors() + "/" + slot.getTotalCounselors() + "可用");

        return slotData;
    }

    /**
     * 获取星期几
     */
    private String getWeekDay(LocalDate date) {
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        return weekDays[date.getDayOfWeek().getValue() - 1];
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "可用";
            case 2:
                return "已过期";
            default:
                return "未知";
        }
    }

    /**
     * 批量更新系统时间槽状态
     */
    @Override
    @Transactional
    public int batchUpdateSlotStatus(List<Long> slotIds, Integer status) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return 0;
        }
        return systemTimeSlotMapper.batchUpdateSlotStatus(slotIds, status);
    }

    /**
     * 更新过期的系统时间槽状态
     */
    @Override
    @Transactional
    public int updateExpiredSlotStatus(Long centerId, Integer delayHours) {
        return systemTimeSlotMapper.updateExpiredSlotStatus(centerId, delayHours);
    }

    /**
     * 更新过期的系统时间槽状态（支持延后配置）
     */
    @Override
    @Transactional
    public int updateExpiredSlotStatusWithDelay(Long centerId) {
        // 获取延后配置
        boolean isDelayEnabled = isDelayExpirationEnabled();
        int delayHours = isDelayEnabled ? getDelayExpirationHours() : 0;

        return updateExpiredSlotStatus(centerId, delayHours);
    }

    /**
     * 检查是否启用延后过期功能
     */
    private boolean isDelayExpirationEnabled() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
            return "true".equalsIgnoreCase(configValue) || "1".equals(configValue);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取延后过期的小时数
     */
    private int getDelayExpirationHours() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
            return Integer.parseInt(configValue);
        } catch (Exception e) {
            return 2; // 默认2小时
        }
    }

    /**
     * 过滤掉已存在的时间槽，避免重复插入
     */
    private List<PsySystemTimeSlot> filterExistingSlots(List<PsySystemTimeSlot> systemSlots) {
        if (CollectionUtils.isEmpty(systemSlots)) {
            return new ArrayList<>();
        }

        List<PsySystemTimeSlot> newSlots = new ArrayList<>();

        for (PsySystemTimeSlot slot : systemSlots) {
            try {
                // 检查是否已存在相同的时间槽
                boolean exists = systemTimeSlotMapper.checkSystemSlotExists(
                    slot.getCenterId(), slot.getDateKey(), String.valueOf(slot.getStartTime())) > 0;

                if (!exists) {
                    newSlots.add(slot);
                } else {
                    logger.debug("时间槽已存在，跳过：中心={}, 日期={}, 时间={}",
                        slot.getCenterId(), slot.getDateKey(), slot.getStartTime());
                }
            } catch (Exception e) {
                logger.warn("检查时间槽是否存在时发生错误，跳过该时间槽：{}", e.getMessage());
            }
        }

        return newSlots;
    }

    /**
     * 检查和修复时间段配置的连续性
     * 确保时间段覆盖完整的工作时间且无空隙
     */
    @Override
    @Transactional
    public int checkAndFixTimeRangeContinuity() {
        logger.info("开始检查时间段配置的连续性");

        List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();
        if (CollectionUtils.isEmpty(timeRanges)) {
            logger.warn("没有找到有效的时间段配置，尝试初始化默认时间段");
            return timeRangeService.initDefaultTimeRanges();
        }

        // 按开始时间排序
        timeRanges.sort((a, b) -> a.getStartHour().compareTo(b.getStartHour()));

        List<String> issues = new ArrayList<>();
        List<PsyTimeRange> missingRanges = new ArrayList<>();

        // 检查时间段连续性
        for (int i = 0; i < timeRanges.size() - 1; i++) {
            PsyTimeRange current = timeRanges.get(i);
            PsyTimeRange next = timeRanges.get(i + 1);

            if (current.getEndHour() < next.getStartHour()) {
                // 发现空隙
                int gapStart = current.getEndHour();
                int gapEnd = next.getStartHour();
                issues.add(String.format("时间段空隙：%d:00-%d:00", gapStart, gapEnd));

                // 创建填补空隙的时间段
                for (int hour = gapStart; hour < gapEnd; hour++) {
                    PsyTimeRange fillRange = new PsyTimeRange();
                    fillRange.setName(String.format("补充时段%d", hour));
                    fillRange.setStartHour(hour);
                    fillRange.setEndHour(hour + 1);
                    fillRange.setDelFlag(0);
                    missingRanges.add(fillRange);
                }
            } else if (current.getEndHour() > next.getStartHour()) {
                // 发现重叠
                issues.add(String.format("时间段重叠：%d:00-%d:00 与 %d:00-%d:00",
                    current.getStartHour(), current.getEndHour(),
                    next.getStartHour(), next.getEndHour()));
            }
        }

        if (!issues.isEmpty()) {
            logger.warn("发现时间段配置问题：{}", String.join(", ", issues));
        }

        // 插入缺失的时间段
        int addedCount = 0;
        for (PsyTimeRange missingRange : missingRanges) {
            try {
                timeRangeService.insertTimeRange(missingRange);
                addedCount++;
                logger.info("添加缺失时间段：{}:00-{}:00",
                    missingRange.getStartHour(), missingRange.getEndHour());
            } catch (Exception e) {
                logger.error("添加时间段失败：{}:00-{}:00, 错误：{}",
                    missingRange.getStartHour(), missingRange.getEndHour(), e.getMessage());
            }
        }

        if (addedCount > 0) {
            logger.info("时间段连续性检查完成，添加了 {} 个缺失的时间段", addedCount);
        } else {
            logger.info("时间段配置检查完成，无需修复");
        }

        return addedCount;
    }
}
