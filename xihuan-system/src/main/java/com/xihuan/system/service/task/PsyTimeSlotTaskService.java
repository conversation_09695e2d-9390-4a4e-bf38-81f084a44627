package com.xihuan.system.service.task;

import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;
import com.xihuan.common.core.domain.vo.ConsultantSimpleVO;
import com.xihuan.common.utils.spring.SpringUtils;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.IPsyTimeCounselorScheduleService;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.service.ISysConfigService;
import com.xihuan.system.service.wxService.PsyCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 时间槽定时任务服务
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeSlotTaskService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyTimeSlotTaskService.class);
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;

    @Autowired
    private IPsySystemTimeSlotService systemTimeSlotService;

    /**
     * 生成未来时间槽
     * 供Quartz定时任务调用
     */
    public void generateFutureTimeSlots() {
        logger.info("开始执行定时任务：生成未来时间槽");

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);
            IPsyTimeCounselorScheduleService scheduleService = SpringUtils.getBean(IPsyTimeCounselorScheduleService.class);

            LocalDate startDate = LocalDate.now(); // 从今天开始
            LocalDate endDate = startDate.plusDays(6); // 生成7天的时间槽

            // 预检查：诊断系统状态
            logger.info("=== 开始系统诊断 ===");
            boolean systemReady = performSystemDiagnostics();
            if (!systemReady) {
                logger.error("系统诊断失败，无法继续生成时间槽");
                return;
            }
            logger.info("=== 系统诊断完成，状态正常 ===");

            // 第一步：修复现有排班记录的时间（从18:00更新为21:00）
            logger.info("第一步：修复现有排班记录的时间");
            int fixedCount = fixExistingScheduleTimes();
            logger.info("修复了 {} 条排班记录的时间", fixedCount);

            // 第二步：确保所有咨询师都有未来7天的排班记录
            logger.info("第二步：确保所有咨询师都有未来7天的排班记录");
            int scheduleCount = scheduleService.ensureAllCounselorsFutureSchedule(7, 1L);
            logger.info("为咨询师生成了 {} 条排班记录", scheduleCount);

            // 第三步：重新生成咨询师时间槽（先清理再生成，避免重复）
            logger.info("第三步：重新生成咨询师时间槽");
            int count = timeSlotService.regenerateSlotsForAllCounselors(startDate, endDate);
            logger.info("生成咨询师时间槽结果：{} 个", count);

            // 第四步：重新生成系统公共时间槽（先清理再生成，避免重复）
            logger.info("第四步：重新生成系统公共时间槽");
            int systemSlotCount = systemTimeSlotService.regenerateSystemTimeSlots(startDate, endDate, 1L);
            logger.info("生成系统时间槽结果：{} 个", systemSlotCount);

            // 验证生成结果
            if (count == 0 && systemSlotCount == 0) {
                logger.warn("警告：没有生成任何时间槽，请检查系统配置");
                performDetailedDiagnostics();
            }

            logger.info("定时任务完成：修复 {} 条排班记录，生成 {} 条新排班记录，{} 个咨询师时间槽和 {} 个系统时间槽，日期范围：{} 到 {}",
                fixedCount, scheduleCount, count, systemSlotCount, startDate, endDate);
        } catch (Exception e) {
            logger.error("生成未来时间槽定时任务执行失败", e);
        }
    }

    /**
     * 清理过期时间槽
     * 供Quartz定时任务调用
     */
    public void cleanExpiredTimeSlots() {
        logger.info("开始执行定时任务：清理过期时间槽");

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate beforeDate = LocalDate.now().minusDays(7);
            int count = timeSlotService.cleanExpiredSlots(beforeDate);

            // 清理过期的系统时间槽
            int systemSlotCount = systemTimeSlotService.cleanExpiredSystemSlots(beforeDate, 1L);

            logger.info("定时任务完成：成功清理 {} 个过期咨询师时间槽和 {} 个过期系统时间槽，清理日期：{} 之前",
                count, systemSlotCount, beforeDate);
        } catch (Exception e) {
            logger.error("清理过期时间槽定时任务执行失败", e);
        }
    }

    /**
     * 更新过期时间槽状态
     * 供Quartz定时任务调用
     * 注意：建议使用 updateExpiredSlotStatusWithDelay() 方法，支持延后配置
     */
    public void updateExpiredSlotStatus() {
        logger.info("开始执行定时任务：更新过期时间槽状态（基础版本）");

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);

            // 使用支持延后配置的版本
            int count = timeSlotService.updateExpiredSlotStatusWithDelay();

            if (count > 0) {
                logger.info("定时任务完成：成功更新 {} 个过期时间槽状态", count);
            } else {
                logger.debug("定时任务完成：没有需要更新的过期时间槽");
            }
        } catch (Exception e) {
            logger.error("更新过期时间槽状态定时任务执行失败", e);
        }
    }

    /**
     * 更新过期时间槽状态（支持延后过期配置）
     * 供Quartz定时任务调用
     */
    public void updateExpiredSlotStatusWithDelay() {
        logger.info("开始执行定时任务：更新过期时间槽状态（支持延后配置）");

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);

            int count = timeSlotService.updateExpiredSlotStatusWithDelay();

            if (count > 0) {
                logger.info("定时任务完成：成功更新 {} 个过期时间槽状态（已考虑延后配置）", count);
            }
        } catch (Exception e) {
            logger.error("更新过期时间槽状态（延后配置）定时任务执行失败", e);
        }
    }

    /**
     * 检查是否启用延后过期功能
     *
     * @return true-启用延后2小时，false-不延后
     */
    public boolean isDelayExpirationEnabled() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
            return "true".equalsIgnoreCase(configValue) || "1".equals(configValue);
        } catch (Exception e) {
            logger.warn("获取延后过期配置失败，使用默认值：false", e);
            return false;
        }
    }

    /**
     * 获取延后过期的小时数
     *
     * @return 延后小时数，默认2小时
     */
    public int getDelayExpirationHours() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
            return Integer.parseInt(configValue);
        } catch (Exception e) {
            logger.warn("获取延后过期小时数配置失败，使用默认值：2小时", e);
            return 2;
        }
    }

    /**
     * 更新系统时间槽可用性统计
     * 供Quartz定时任务调用
     */
    public void updateSystemSlotAvailability() {
        logger.info("开始执行定时任务：更新系统时间槽可用性统计");

        try {
            // 动态获取服务Bean
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate today = LocalDate.now();
            LocalDate tomorrow = today.plusDays(1);

            // 更新今天和明天的系统时间槽统计
            int todayCount = systemTimeSlotService.updateAvailabilityStats(today, 1L);
            int tomorrowCount = systemTimeSlotService.updateAvailabilityStats(tomorrow, 1L);

            // 同时更新系统时间槽的过期状态
            int expiredCount = systemTimeSlotService.updateExpiredSlotStatusWithDelay(1L);

            logger.info("定时任务完成：更新了今天 {} 个和明天 {} 个系统时间槽的可用性统计，更新了 {} 个过期状态",
                todayCount, tomorrowCount, expiredCount);
        } catch (Exception e) {
            logger.error("更新系统时间槽可用性统计定时任务执行失败", e);
        }
    }

    /**
     * 生成系统公共时间槽
     * 供Quartz定时任务调用
     */
    public void generateSystemTimeSlots() {
        logger.info("开始执行定时任务：生成系统公共时间槽");

        try {
            // 动态获取服务Bean
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate startDate = LocalDate.now(); // 从今天开始
            LocalDate endDate = startDate.plusDays(6); // 生成7天的时间槽

            int count = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, 1L);

            logger.info("定时任务完成：成功生成 {} 个系统时间槽，日期范围：{} 到 {}", count, startDate, endDate);
        } catch (Exception e) {
            logger.error("生成系统公共时间槽定时任务执行失败", e);
        }
    }

    /**
     * 更新系统公共时间槽过期状态
     * 供Quartz定时任务调用
     */
    public void updateSystemSlotExpiredStatus() {
        logger.info("开始执行定时任务：更新系统公共时间槽过期状态");

        try {
            // 动态获取服务Bean
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            int count = systemTimeSlotService.updateExpiredSlotStatusWithDelay(1L);

            logger.info("定时任务完成：成功更新 {} 个系统公共时间槽的过期状态", count);
        } catch (Exception e) {
            logger.error("更新系统公共时间槽过期状态定时任务执行失败", e);
        }
    }

    /**
     * 清理过期的系统时间槽
     * 供Quartz定时任务调用
     */
    public void cleanExpiredSystemSlots() {
        logger.info("开始执行定时任务：清理过期系统时间槽");

        try {
            // 动态获取服务Bean
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate beforeDate = LocalDate.now().minusDays(7);
            int count = systemTimeSlotService.cleanExpiredSystemSlots(beforeDate, 1L);

            logger.info("定时任务完成：成功清理 {} 个过期系统时间槽，清理日期：{} 之前", count, beforeDate);
        } catch (Exception e) {
            logger.error("清理过期系统时间槽定时任务执行失败", e);
        }
    }

    /**
     * 手动触发生成时间槽（用于测试或紧急情况）
     *
     * @param days 生成未来多少天的时间槽
     * @return 生成的时间槽数量
     */
    public int manualGenerateTimeSlots(int days) {
        logger.info("手动触发生成时间槽，天数：{}", days);

        try {
            // 动态获取服务Bean（手动触发时也可能存在注入问题）
            IPsyTimeSlotService timeSlotService = this.timeSlotService != null ?
                this.timeSlotService : SpringUtils.getBean(IPsyTimeSlotService.class);
            IPsyTimeCounselorScheduleService scheduleService = SpringUtils.getBean(IPsyTimeCounselorScheduleService.class);

            LocalDate startDate = LocalDate.now().plusDays(1);
            LocalDate endDate = startDate.plusDays(days - 1);

            // 先确保排班记录存在
            logger.info("确保所有咨询师都有未来 {} 天的排班记录", days);
            int scheduleCount = scheduleService.ensureAllCounselorsFutureSchedule(days, 1L);
            logger.info("生成了 {} 条排班记录", scheduleCount);

            // 再生成时间槽
            int count = timeSlotService.generateSlotsForAllCounselors(startDate, endDate);

            logger.info("手动生成时间槽完成：生成 {} 条排班记录，{} 个时间槽", scheduleCount, count);
            return count;
        } catch (Exception e) {
            logger.error("手动生成时间槽失败", e);
            return 0;
        }
    }

    /**
     * 手动触发清理过期时间槽
     *
     * @param days 清理多少天前的时间槽
     * @return 清理的时间槽数量
     */
    public int manualCleanExpiredSlots(int days) {
        logger.info("手动触发清理过期时间槽，天数：{}", days);

        try {
            // 动态获取服务Bean（手动触发时也可能存在注入问题）
            IPsyTimeSlotService timeSlotService = this.timeSlotService != null ?
                this.timeSlotService : SpringUtils.getBean(IPsyTimeSlotService.class);

            LocalDate beforeDate = LocalDate.now().minusDays(days);
            int count = timeSlotService.cleanExpiredSlots(beforeDate);

            logger.info("手动清理过期时间槽完成：成功清理 {} 个时间槽", count);
            return count;
        } catch (Exception e) {
            logger.error("手动清理过期时间槽失败", e);
            return 0;
        }
    }

    /**
     * 获取定时任务状态信息
     *
     * @return 状态信息
     */
    public String getTaskStatus() {
        return "时间槽定时任务服务运行正常";
    }

    /**
     * 执行系统诊断
     * 检查生成时间槽所需的基础数据是否完整
     *
     * @return 是否通过诊断
     */
    private boolean performSystemDiagnostics() {
        try {
            // 获取服务Bean
            IPsyTimeRangeService timeRangeService = SpringUtils.getBean(IPsyTimeRangeService.class);
            PsyCategoryService.PsyConsultantService consultantService = SpringUtils.getBean(PsyCategoryService.PsyConsultantService.class);

            // 1. 检查时间段配置
            List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();
            logger.info("诊断：系统时间段配置数量 = {}", timeRanges != null ? timeRanges.size() : 0);

            if (timeRanges == null || timeRanges.isEmpty()) {
                logger.warn("诊断：系统时间段配置为空，尝试初始化默认时间段");
                int initResult = timeRangeService.initDefaultTimeRanges();
                logger.info("诊断：初始化时间段结果 = {} 个", initResult);

                if (initResult == 0) {
                    logger.error("诊断失败：无法初始化时间段配置");
                    return false;
                }

                // 重新查询
                timeRanges = timeRangeService.selectAllActiveTimeRanges();
                logger.info("诊断：重新查询时间段配置数量 = {}", timeRanges != null ? timeRanges.size() : 0);
            }

            // 2. 检查咨询师数据
            List<ConsultantSimpleVO> consultants = consultantService.listAllSimple();
            logger.info("诊断：系统咨询师数量 = {}", consultants != null ? consultants.size() : 0);

            if (consultants == null || consultants.isEmpty()) {
                logger.error("诊断失败：系统中没有咨询师数据");
                return false;
            }

            // 3. 输出详细信息
            logger.info("诊断：时间段详情：");
            if (timeRanges != null) {
                for (PsyTimeRange range : timeRanges) {
                    logger.info("  - {} ({}:00-{}:00)", range.getName(), range.getStartHour(), range.getEndHour());
                }
            }

            logger.info("诊断：咨询师详情：");
            if (consultants != null) {
                for (ConsultantSimpleVO consultant : consultants) {
                    logger.info("  - ID:{}, 姓名:{}", consultant.getId(), consultant.getName());
                }
            }

            return true;

        } catch (Exception e) {
            logger.error("系统诊断过程中发生异常", e);
            return false;
        }
    }

    /**
     * 执行详细诊断
     * 当时间槽生成失败时，进行更深入的问题分析
     */
    private void performDetailedDiagnostics() {
        try {
            logger.info("=== 开始详细诊断 ===");

            // 获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);
            IPsyTimeCounselorScheduleService scheduleService = SpringUtils.getBean(IPsyTimeCounselorScheduleService.class);
            PsyCategoryService.PsyConsultantService consultantService = SpringUtils.getBean(PsyCategoryService.PsyConsultantService.class);

            LocalDate today = LocalDate.now();
            LocalDate tomorrow = today.plusDays(1);

            // 1. 检查排班记录
            List<ConsultantSimpleVO> consultants = consultantService.listAllSimple();
            if (consultants != null && !consultants.isEmpty()) {
                logger.info("详细诊断：检查前3个咨询师的排班记录");
                for (int i = 0; i < Math.min(3, consultants.size()); i++) {
                    ConsultantSimpleVO consultant = consultants.get(i);

                    // 检查明天的排班
                    List<PsyTimeCounselorSchedule> schedules = scheduleService.selectSchedulesByDateRange(
                        consultant.getId(), tomorrow, tomorrow);

                    logger.info("  咨询师 {} (ID:{}) 明天的排班记录数量: {}",
                        consultant.getName(), consultant.getId(), schedules != null ? schedules.size() : 0);

                    if (schedules != null && !schedules.isEmpty()) {
                        PsyTimeCounselorSchedule schedule = schedules.get(0);
                        logger.info("    排班详情: {}:00-{}:00, 工作状态:{}",
                            schedule.getStartTime(), schedule.getEndTime(), schedule.getIsWorking());
                    }
                }
            }

            // 2. 尝试手动为单个咨询师生成时间槽
            if (consultants != null && !consultants.isEmpty()) {
                ConsultantSimpleVO firstConsultant = consultants.get(0);
                logger.info("详细诊断：尝试为咨询师 {} 手动生成明天的时间槽", firstConsultant.getName());

                int testSlots = timeSlotService.generateSlotsForCounselor(firstConsultant.getId(), tomorrow, tomorrow);
                logger.info("详细诊断：手动生成结果 = {} 个时间槽", testSlots);
            }

            logger.info("=== 详细诊断完成 ===");

        } catch (Exception e) {
            logger.error("详细诊断过程中发生异常", e);
        }
    }

    /**
     * 修复现有排班记录的时间
     * 将结束时间从18:00更新为21:00
     *
     * @return 修复的记录数量
     */
    private int fixExistingScheduleTimes() {
        try {
            // 获取服务Bean
            IPsyTimeCounselorScheduleService scheduleService = SpringUtils.getBean(IPsyTimeCounselorScheduleService.class);

            // 查询所有结束时间为18:00的未来排班记录
            LocalDate today = LocalDate.now();
            List<ConsultantSimpleVO> consultants = SpringUtils.getBean(PsyCategoryService.PsyConsultantService.class).listAllSimple();

            int fixedCount = 0;

            if (consultants != null) {
                for (ConsultantSimpleVO consultant : consultants) {
                    // 查询该咨询师未来7天的排班记录
                    LocalDate endDate = today.plusDays(7);
                    List<PsyTimeCounselorSchedule> schedules = scheduleService.selectSchedulesByDateRange(
                        consultant.getId(), today, endDate);

                    if (schedules != null) {
                        for (PsyTimeCounselorSchedule schedule : schedules) {
                            // 检查是否需要修复（结束时间为18:00）
                            if (schedule.getEndTime() != null &&
                                schedule.getEndTime().getHour() == 18 &&
                                schedule.getEndTime().getMinute() == 0) {

                                // 更新结束时间为21:00
                                schedule.setEndTime(LocalTime.of(21, 0));
                                schedule.setUpdateTime(new java.util.Date());

                                String originalRemark = schedule.getRemark();
                                schedule.setRemark((originalRemark != null ? originalRemark : "") +
                                    " [系统自动修复：将结束时间从18:00更新为21:00]");

                                int result = scheduleService.updateSchedule(schedule);
                                if (result > 0) {
                                    fixedCount++;
                                    logger.debug("修复排班记录：咨询师ID={}, 日期={}, 时间={}:00-21:00",
                                        consultant.getId(), schedule.getScheduleDate(),
                                        schedule.getStartTime().getHour());
                                }
                            }
                        }
                    }
                }
            }

            logger.info("排班记录时间修复完成，共修复 {} 条记录", fixedCount);
            return fixedCount;

        } catch (Exception e) {
            logger.error("修复排班记录时间失败", e);
            return 0;
        }
    }

    /**
     * 手动测试过期时间槽状态更新
     * 用于调试和验证功能
     *
     * @return 测试结果
     */
    public Map<String, Object> testUpdateExpiredSlotStatus() {
        logger.info("手动测试：更新过期时间槽状态");

        Map<String, Object> result = new HashMap<>();

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);

            // 测试基础版本
            int basicCount = timeSlotService.updateExpiredSlotStatus();
            result.put("basicVersionCount", basicCount);

            // 测试延后配置版本
            int delayCount = timeSlotService.updateExpiredSlotStatusWithDelay();
            result.put("delayVersionCount", delayCount);

            // 获取延后配置信息
            boolean isDelayEnabled = isDelayExpirationEnabled();
            int delayHours = getDelayExpirationHours();
            result.put("delayEnabled", isDelayEnabled);
            result.put("delayHours", delayHours);

            // 计算过期时间
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            java.time.LocalDateTime cutoffTime = isDelayEnabled ? now.minusHours(delayHours) : now;
            result.put("currentTime", now.toString());
            result.put("cutoffTime", cutoffTime.toString());

            result.put("success", true);
            result.put("message", "测试完成");

            logger.info("手动测试完成：基础版本更新 {} 个，延后版本更新 {} 个", basicCount, delayCount);

        } catch (Exception e) {
            logger.error("手动测试过期时间槽状态更新失败", e);
            result.put("success", false);
            result.put("message", "测试失败：" + e.getMessage());
        }

        return result;
    }

}
