package com.xihuan.system.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 配置化计分服务接口
 * 支持通过数据库配置实现灵活的计分逻辑
 * 
 * <AUTHOR>
 */
public interface IPsyTConfigurableScoringService {

    /**
     * 执行配置化计分
     * 根据数据库中的配置自动选择计分方法
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> executeConfigurableScoring(Long recordId);

    /**
     * 简单求和计分
     *
     * @param recordId 测评记录ID
     * @param scaleConfig 量表配置
     * @return 计分结果
     */
    Map<String, Object> calculateSimpleSum(Long recordId, Map<String, Object> scaleConfig);

    /**
     * 反向计分
     *
     * @param recordId 测评记录ID
     * @param scaleConfig 量表配置
     * @return 计分结果
     */
    Map<String, Object> calculateReverseScoring(Long recordId, Map<String, Object> scaleConfig);

    /**
     * 公式计分
     *
     * @param recordId 测评记录ID
     * @param scaleConfig 量表配置
     * @return 计分结果
     */
    Map<String, Object> calculateFormulaScoring(Long recordId, Map<String, Object> scaleConfig);

    /**
     * 标准分转换
     *
     * @param recordId 测评记录ID
     * @param scaleConfig 量表配置
     * @return 计分结果
     */
    Map<String, Object> calculateStandardScore(Long recordId, Map<String, Object> scaleConfig);

    /**
     * 特殊计分（二选一等）
     *
     * @param recordId 测评记录ID
     * @param scaleConfig 量表配置
     * @return 计分结果
     */
    Map<String, Object> calculateSpecialScoring(Long recordId, Map<String, Object> scaleConfig);

    /**
     * 获取量表计分配置
     *
     * @param scaleId 量表ID
     * @return 配置信息
     */
    Map<String, Object> getScaleConfig(Long scaleId);

    /**
     * 保存量表计分配置
     *
     * @param scaleId 量表ID
     * @param config 配置信息
     * @return 保存结果
     */
    boolean saveScaleConfig(Long scaleId, Map<String, Object> config);

    /**
     * 验证计分配置
     *
     * @param config 配置信息
     * @return 验证结果
     */
    Map<String, Object> validateConfig(Map<String, Object> config);

    /**
     * 获取支持的计分方法列表
     *
     * @return 计分方法列表
     */
    Map<String, String> getSupportedScoringMethods();

    /**
     * 根据配置生成计分规则说明
     *
     * @param scaleId 量表ID
     * @return 规则说明
     */
    String generateScoringRuleDescription(Long scaleId);

    /**
     * 测试计分配置
     *
     * @param scaleId 量表ID
     * @param testData 测试数据
     * @return 测试结果
     */
    Map<String, Object> testScoringConfig(Long scaleId, Map<String, Object> testData);
}
