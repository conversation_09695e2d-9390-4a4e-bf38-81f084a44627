package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.entity.PsyMessageStatus;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyMessageStatusMapper;
import com.xihuan.system.service.wxService.IPsyMessageStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 消息状态服务实现类
 */
@Service
public class PsyMessageStatusServiceImpl implements IPsyMessageStatusService {
    @Autowired
    private PsyMessageStatusMapper messageStatusMapper;

    /**
     * 创建消息状态
     */
    @Override
    @Transactional
    public PsyMessageStatus createMessageStatus(Long messageId, Long userId) {
        // 检查是否已存在
        PsyMessageStatus existStatus = messageStatusMapper.selectMessageStatus(messageId, userId);
        if (existStatus != null) {
            return existStatus;
        }
        
        // 创建新状态
        PsyMessageStatus status = new PsyMessageStatus();
        status.setMessageId(messageId);
        status.setUserId(userId);
        status.setIsRead("0"); // 默认未读
        
        messageStatusMapper.insertMessageStatus(status);
//        log.info("创建消息状态成功，状态ID：{}", status.getStatusId());
        
        return status;
    }

    /**
     * 批量创建消息状态
     */
    @Override
    @Transactional
    public boolean batchCreateMessageStatus(Long messageId, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return false;
        }
        
        List<PsyMessageStatus> statusList = new ArrayList<>();
        for (Long userId : userIds) {
            PsyMessageStatus status = new PsyMessageStatus();
            status.setMessageId(messageId);
            status.setUserId(userId);
            status.setIsRead("0"); // 默认未读
            statusList.add(status);
        }
        
        int rows = messageStatusMapper.batchInsertMessageStatus(statusList);
        return rows > 0;
    }

    /**
     * 更新消息已读状态
     */
    @Override
    @Transactional
    public boolean updateMessageReadStatus(Long messageId, Long userId, boolean isRead) {
        PsyMessageStatus status = messageStatusMapper.selectMessageStatus(messageId, userId);
        
        if (status == null) {
            // 状态不存在，创建新状态
            status = new PsyMessageStatus();
            status.setMessageId(messageId);
            status.setUserId(userId);
            status.setIsRead(isRead ? "1" : "0");
            if (isRead) {
                status.setReadTime(DateUtils.getNowDate());
            }
            
            int rows = messageStatusMapper.insertMessageStatus(status);
            return rows > 0;
        } else {
            // 更新已有状态
            status.setIsRead(isRead ? "1" : "0");
            if (isRead) {
                status.setReadTime(DateUtils.getNowDate());
            }
            
            int rows = messageStatusMapper.updateMessageStatus(status);
            return rows > 0;
        }
    }

    /**
     * 批量更新消息已读状态
     */
    @Override
    @Transactional
    public boolean batchUpdateMessageReadStatus(List<Long> messageIds, Long userId, boolean isRead) {
        if (messageIds == null || messageIds.isEmpty()) {
            return false;
        }
        
        int rows = messageStatusMapper.batchUpdateMessageReadStatus(messageIds, userId, isRead ? "1" : "0", isRead ? DateUtils.getNowDate() : null);
        return rows > 0;
    }

    /**
     * 获取消息状态
     */
    @Override
    public PsyMessageStatus getMessageStatus(Long messageId, Long userId) {
        return messageStatusMapper.selectMessageStatus(messageId, userId);
    }

    /**
     * 获取用户未读消息数量
     */
    @Override
    public int getUnreadCount(Long userId) {
        return messageStatusMapper.countUnreadMessages(userId);
    }
} 