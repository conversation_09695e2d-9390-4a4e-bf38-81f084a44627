package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTimeScheduleTemplate;
import com.xihuan.common.core.domain.entity.PsyTimeTemplateItem;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.PsyTimeScheduleTemplateMapper;
import com.xihuan.system.mapper.PsyTimeTemplateItemMapper;
import com.xihuan.system.service.IPsyTimeScheduleTemplateService;
import com.xihuan.system.service.IPsyTimeTemplateItemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 排班模板Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeScheduleTemplateServiceImpl implements IPsyTimeScheduleTemplateService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyTimeScheduleTemplateServiceImpl.class);
    
    @Autowired
    private PsyTimeScheduleTemplateMapper templateMapper;
    
    @Autowired
    private PsyTimeTemplateItemMapper templateItemMapper;
    
    @Autowired
    private IPsyTimeTemplateItemService templateItemService;

    /**
     * 查询排班模板列表
     * 
     * @param template 排班模板
     * @return 排班模板集合
     */
    @Override
    public List<PsyTimeScheduleTemplate> selectTemplateList(PsyTimeScheduleTemplate template) {
        List<PsyTimeScheduleTemplate> templates = templateMapper.selectTemplateList(template);
        // 加载模板明细
        for (PsyTimeScheduleTemplate t : templates) {
            List<PsyTimeTemplateItem> items = templateItemService.selectTemplateItemsByTemplateId(t.getId());
            t.setTemplateItems(items);
        }
        return templates;
    }

    /**
     * 根据ID查询排班模板
     * 
     * @param id 排班模板主键
     * @return 排班模板
     */
    @Override
    public PsyTimeScheduleTemplate selectTemplateById(Long id) {
        PsyTimeScheduleTemplate template = templateMapper.selectTemplateById(id);
        if (template != null) {
            // 加载模板明细
            List<PsyTimeTemplateItem> items = templateItemService.selectTemplateItemsByTemplateId(id);
            template.setTemplateItems(items);
        }
        return template;
    }

    /**
     * 根据咨询师ID查询排班模板
     * 
     * @param counselorId 咨询师ID
     * @return 排班模板集合
     */
    @Override
    public List<PsyTimeScheduleTemplate> selectTemplatesByCounselorId(Long counselorId) {
        List<PsyTimeScheduleTemplate> templates = templateMapper.selectTemplatesByCounselorId(counselorId);
        // 加载模板明细
        for (PsyTimeScheduleTemplate template : templates) {
            List<PsyTimeTemplateItem> items = templateItemService.selectTemplateItemsByTemplateId(template.getId());
            template.setTemplateItems(items);
        }
        return templates;
    }

    /**
     * 查询咨询师的默认模板
     * 
     * @param counselorId 咨询师ID
     * @return 默认排班模板
     */
    @Override
    public PsyTimeScheduleTemplate selectDefaultTemplateByCounselorId(Long counselorId) {
        PsyTimeScheduleTemplate template = templateMapper.selectDefaultTemplateByCounselorId(counselorId);
        if (template != null) {
            // 加载模板明细
            List<PsyTimeTemplateItem> items = templateItemService.selectTemplateItemsByTemplateId(template.getId());
            template.setTemplateItems(items);
        }
        return template;
    }

    /**
     * 查询在指定日期有效的模板
     * 
     * @param counselorId 咨询师ID
     * @param date 日期
     * @return 有效的排班模板
     */
    @Override
    public PsyTimeScheduleTemplate selectEffectiveTemplate(Long counselorId, LocalDate date) {
        PsyTimeScheduleTemplate template = templateMapper.selectEffectiveTemplate(counselorId, date);
        if (template != null) {
            // 加载模板明细
            List<PsyTimeTemplateItem> items = templateItemService.selectTemplateItemsByTemplateId(template.getId());
            template.setTemplateItems(items);
        }
        return template;
    }

    /**
     * 新增排班模板
     * 
     * @param template 排班模板
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTemplate(PsyTimeScheduleTemplate template) {
        // 如果设置为默认模板，先取消其他默认模板
        if (template.getIsDefault() != null && template.getIsDefault() == 1) {
            templateMapper.clearDefaultTemplate(template.getCounselorId());
        }
        
        int result = templateMapper.insertTemplate(template);
        
        // 保存模板明细
        if (result > 0 && template.getTemplateItems() != null && !template.getTemplateItems().isEmpty()) {
            for (PsyTimeTemplateItem item : template.getTemplateItems()) {
                item.setTemplateId(template.getId());
                templateItemService.insertTemplateItem(item);
            }
        }
        
        return result;
    }

    /**
     * 修改排班模板
     * 
     * @param template 排班模板
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTemplate(PsyTimeScheduleTemplate template) {
        // 如果设置为默认模板，先取消其他默认模板
        if (template.getIsDefault() != null && template.getIsDefault() == 1) {
            templateMapper.clearDefaultTemplate(template.getCounselorId());
        }
        
        int result = templateMapper.updateTemplate(template);
        
        // 更新模板明细
        if (result > 0 && template.getTemplateItems() != null) {
            // 先删除原有明细
            templateItemService.deleteTemplateItemsByTemplateId(template.getId());
            
            // 重新插入明细
            for (PsyTimeTemplateItem item : template.getTemplateItems()) {
                item.setTemplateId(template.getId());
                templateItemService.insertTemplateItem(item);
            }
        }
        
        return result;
    }

    /**
     * 设置默认模板
     * 
     * @param counselorId 咨询师ID
     * @param templateId 模板ID
     * @return 结果
     */
    @Override
    @Transactional
    public int setDefaultTemplate(Long counselorId, Long templateId) {
        // 先取消所有默认模板
        templateMapper.clearDefaultTemplate(counselorId);
        
        // 设置新的默认模板
        return templateMapper.setDefaultTemplate(templateId);
    }

    /**
     * 批量删除排班模板
     * 
     * @param ids 需要删除的排班模板主键集合
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTemplateByIds(Long[] ids) {
        // 删除模板明细
        for (Long id : ids) {
            templateItemService.deleteTemplateItemsByTemplateId(id);
        }
        
        // 删除模板
        return templateMapper.deleteTemplateByIds(ids);
    }

    /**
     * 删除排班模板信息
     * 
     * @param id 排班模板主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTemplateById(Long id) {
        // 删除模板明细
        templateItemService.deleteTemplateItemsByTemplateId(id);
        
        // 删除模板
        return templateMapper.deleteTemplateById(id);
    }

    /**
     * 校验模板名称是否唯一
     * 
     * @param template 排班模板信息
     * @return 结果
     */
    @Override
    public String checkTemplateNameUnique(PsyTimeScheduleTemplate template) {
        Long templateId = StringUtils.isNull(template.getId()) ? -1L : template.getId();
        PsyTimeScheduleTemplate info = templateMapper.checkTemplateNameUnique(template.getName(), template.getCounselorId());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != templateId.longValue()) {
            return "1"; // 不唯一
        }
        return "0"; // 唯一
    }

    /**
     * 为咨询师创建默认排班模板
     * 
     * @param counselorId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 结果
     */
    @Override
    @Transactional
    public int createDefaultTemplate(Long counselorId, Long centerId) {
        // 创建默认模板
        PsyTimeScheduleTemplate template = new PsyTimeScheduleTemplate();
        template.setCounselorId(counselorId);
        template.setName("默认排班模板");
        template.setIsDefault(1);
        template.setDelFlag(0);
        
        int result = templateMapper.insertTemplate(template);
        
        if (result > 0) {
            // 创建默认模板明细（周一到周五 9:00-18:00）
            List<PsyTimeTemplateItem> items = new ArrayList<>();
            for (int dayOfWeek = 1; dayOfWeek <= 5; dayOfWeek++) {
                PsyTimeTemplateItem item = new PsyTimeTemplateItem();
                item.setTemplateId(template.getId());
                item.setDayOfWeek(dayOfWeek);
                item.setStartTime(LocalTime.of(9, 0));
                item.setEndTime(LocalTime.of(18, 0));
                item.setCenterId(centerId);
                item.setDelFlag(0);
                items.add(item);
            }
            
            // 批量插入明细
            for (PsyTimeTemplateItem item : items) {
                templateItemService.insertTemplateItem(item);
            }
        }
        
        return result;
    }

    /**
     * 复制排班模板
     * 
     * @param templateId 源模板ID
     * @param newName 新模板名称
     * @return 新模板ID
     */
    @Override
    @Transactional
    public Long copyTemplate(Long templateId, String newName) {
        // 查询源模板
        PsyTimeScheduleTemplate sourceTemplate = selectTemplateById(templateId);
        if (sourceTemplate == null) {
            throw new RuntimeException("源模板不存在");
        }
        
        // 创建新模板
        PsyTimeScheduleTemplate newTemplate = new PsyTimeScheduleTemplate();
        newTemplate.setCounselorId(sourceTemplate.getCounselorId());
        newTemplate.setName(newName);
        newTemplate.setIsDefault(0); // 复制的模板不设为默认
        newTemplate.setEffectiveStart(sourceTemplate.getEffectiveStart());
        newTemplate.setEffectiveEnd(sourceTemplate.getEffectiveEnd());
        newTemplate.setDelFlag(0);
        newTemplate.setRemark("复制自：" + sourceTemplate.getName());
        
        int result = templateMapper.insertTemplate(newTemplate);
        
        if (result > 0 && sourceTemplate.getTemplateItems() != null) {
            // 复制模板明细
            for (PsyTimeTemplateItem sourceItem : sourceTemplate.getTemplateItems()) {
                PsyTimeTemplateItem newItem = new PsyTimeTemplateItem();
                newItem.setTemplateId(newTemplate.getId());
                newItem.setDayOfWeek(sourceItem.getDayOfWeek());
                newItem.setStartTime(sourceItem.getStartTime());
                newItem.setEndTime(sourceItem.getEndTime());
                newItem.setCenterId(sourceItem.getCenterId());
                newItem.setDelFlag(0);
                templateItemService.insertTemplateItem(newItem);
            }
        }
        
        return newTemplate.getId();
    }
}
