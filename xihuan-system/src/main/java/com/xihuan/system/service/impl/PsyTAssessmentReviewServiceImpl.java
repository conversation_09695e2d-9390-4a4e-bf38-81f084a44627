package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;
import com.xihuan.common.core.domain.entity.PsyTAssessmentReview;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.system.mapper.PsyTAssessmentOrderMapper;
import com.xihuan.system.mapper.PsyTAssessmentRecordMapper;
import com.xihuan.system.mapper.PsyTAssessmentReviewMapper;
import com.xihuan.system.service.IPsyTAssessmentReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测评评价Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTAssessmentReviewServiceImpl implements IPsyTAssessmentReviewService {
    
    @Autowired
    private PsyTAssessmentReviewMapper reviewMapper;
    
    @Autowired
    private PsyTAssessmentOrderMapper orderMapper;
    
    @Autowired
    private PsyTAssessmentRecordMapper recordMapper;

    /**
     * 查询测评评价列表
     * 
     * @param review 测评评价
     * @return 测评评价
     */
    @Override
    public List<PsyTAssessmentReview> selectReviewList(PsyTAssessmentReview review) {
        return reviewMapper.selectReviewList(review);
    }

    /**
     * 根据测评评价ID查询详细信息
     * 
     * @param id 测评评价ID
     * @return 测评评价
     */
    @Override
    public PsyTAssessmentReview selectReviewById(Long id) {
        return reviewMapper.selectReviewById(id);
    }

    /**
     * 查询评价详情（包含量表、用户等信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    @Override
    public PsyTAssessmentReview selectReviewWithDetails(Long id) {
        return reviewMapper.selectReviewWithDetails(id);
    }

    /**
     * 根据量表ID查询评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    @Override
    public List<PsyTAssessmentReview> selectReviewsByScaleId(Long scaleId) {
        return reviewMapper.selectReviewsByScaleId(scaleId);
    }

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    @Override
    public List<PsyTAssessmentReview> selectReviewsByUserId(Long userId) {
        return reviewMapper.selectReviewsByUserId(userId);
    }

    /**
     * 根据测评记录ID查询评价
     * 
     * @param recordId 测评记录ID
     * @return 评价信息
     */
    @Override
    public PsyTAssessmentReview selectReviewByRecordId(Long recordId) {
        return reviewMapper.selectReviewByRecordId(recordId);
    }

    /**
     * 根据订单ID查询评价
     * 
     * @param orderId 订单ID
     * @return 评价信息
     */
    @Override
    public PsyTAssessmentReview selectReviewByOrderId(Long orderId) {
        return reviewMapper.selectReviewByOrderId(orderId);
    }

    /**
     * 新增测评评价
     * 
     * @param review 测评评价
     * @return 结果
     */
    @Override
    public int insertReview(PsyTAssessmentReview review) {
        review.setCreateBy(SecurityUtils.getUsername());
        review.setCreateTime(DateUtils.getNowDate());
        review.setDelFlag("0");
        
        // 设置默认值
        if (review.getLikeCount() == null) {
            review.setLikeCount(0);
        }
        if (review.getReplyCount() == null) {
            review.setReplyCount(0);
        }
        if (review.getIsTop() == null) {
            review.setIsTop(0);
        }
        if (review.getStatus() == null) {
            review.setStatus(0); // 待审核
        }
        
        return reviewMapper.insertReview(review);
    }

    /**
     * 修改测评评价
     * 
     * @param review 测评评价
     * @return 结果
     */
    @Override
    public int updateReview(PsyTAssessmentReview review) {
        review.setUpdateBy(SecurityUtils.getUsername());
        review.setUpdateTime(DateUtils.getNowDate());
        return reviewMapper.updateReview(review);
    }

    /**
     * 批量删除测评评价
     * 
     * @param ids 需要删除的测评评价ID
     * @return 结果
     */
    @Override
    public int deleteReviewByIds(Long[] ids) {
        return reviewMapper.deleteReviewByIds(ids);
    }

    /**
     * 删除测评评价信息
     * 
     * @param id 测评评价ID
     * @return 结果
     */
    @Override
    public int deleteReviewById(Long id) {
        return reviewMapper.deleteReviewById(id);
    }

    /**
     * 检查用户是否已评价
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param recordId 测评记录ID
     * @return 是否已评价
     */
    @Override
    public boolean checkUserReviewed(Long userId, Long scaleId, Long recordId) {
        int count = reviewMapper.checkUserReviewed(userId, scaleId, recordId);
        return count > 0;
    }

    /**
     * 审核评价
     * 
     * @param id 评价ID
     * @param status 审核状态
     * @param auditRemark 审核意见
     * @return 结果
     */
    @Override
    @Transactional
    public int auditReview(Long id, Integer status, String auditRemark) {
        String auditBy = SecurityUtils.getUsername();
        return reviewMapper.auditReview(id, status, auditRemark, auditBy);
    }

    /**
     * 批量审核评价
     * 
     * @param ids 评价ID数组
     * @param status 审核状态
     * @param auditRemark 审核意见
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAuditReviews(Long[] ids, Integer status, String auditRemark) {
        String auditBy = SecurityUtils.getUsername();
        return reviewMapper.batchAuditReviews(ids, status, auditRemark, auditBy);
    }

    /**
     * 置顶评价
     * 
     * @param id 评价ID
     * @param isTop 是否置顶
     * @return 结果
     */
    @Override
    @Transactional
    public int topReview(Long id, Integer isTop) {
        String updateBy = SecurityUtils.getUsername();
        return reviewMapper.topReview(id, isTop, updateBy);
    }

    /**
     * 点赞评价
     * 
     * @param id 评价ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int likeReview(Long id, Long userId) {
        // 检查是否已点赞
        if (checkUserLiked(id, userId)) {
            throw new RuntimeException("已经点赞过了");
        }
        
        // 增加点赞数
        int result = reviewMapper.likeReview(id);
        
        // 记录点赞关系（这里需要创建点赞关系表）
        // insertUserLike(id, userId);
        
        return result;
    }

    /**
     * 取消点赞评价
     * 
     * @param id 评价ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int unlikeReview(Long id, Long userId) {
        // 检查是否已点赞
        if (!checkUserLiked(id, userId)) {
            throw new RuntimeException("还未点赞");
        }
        
        // 减少点赞数
        int result = reviewMapper.unlikeReview(id);
        
        // 删除点赞关系
        // deleteUserLike(id, userId);
        
        return result;
    }

    /**
     * 增加回复数
     * 
     * @param id 评价ID
     * @return 结果
     */
    @Override
    public int increaseReplyCount(Long id) {
        return reviewMapper.increaseReplyCount(id);
    }

    /**
     * 减少回复数
     * 
     * @param id 评价ID
     * @return 结果
     */
    @Override
    public int decreaseReplyCount(Long id) {
        return reviewMapper.decreaseReplyCount(id);
    }

    /**
     * 查询量表评价统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleReviewStats(Long scaleId) {
        return reviewMapper.selectScaleReviewStats(scaleId);
    }

    /**
     * 查询用户评价统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserReviewStats(Long userId) {
        return reviewMapper.selectUserReviewStats(userId);
    }

    /**
     * 查询评价统计信息
     * 
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectReviewStats() {
        return reviewMapper.selectReviewStats();
    }

    /**
     * 查询待审核评价列表
     * 
     * @return 评价集合
     */
    @Override
    public List<PsyTAssessmentReview> selectPendingReviews() {
        return reviewMapper.selectPendingReviews();
    }

    /**
     * 查询置顶评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    @Override
    public List<PsyTAssessmentReview> selectTopReviews(Long scaleId) {
        return reviewMapper.selectTopReviews(scaleId);
    }

    /**
     * 查询热门评价列表
     *
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 评价集合
     */
    @Override
    public List<PsyTAssessmentReview> selectHotReviews(Long scaleId, Integer limit) {
        return reviewMapper.selectHotReviews(scaleId, limit);
    }

    /**
     * 搜索评价
     *
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @param userId 用户ID
     * @param status 审核状态
     * @param rating 评分
     * @return 评价集合
     */
    @Override
    public List<PsyTAssessmentReview> searchReviews(String keyword, Long scaleId, Long userId, Integer status, Integer rating) {
        return reviewMapper.searchReviews(keyword, scaleId, userId, status, rating);
    }

    /**
     * 查询评价排行榜
     *
     * @param type 排行类型(like:点赞数, reply:回复数)
     * @param limit 限制数量
     * @return 排行信息
     */
    @Override
    public List<Map<String, Object>> selectReviewRanking(String type, Integer limit) {
        return reviewMapper.selectReviewRanking(type, limit);
    }

    /**
     * 查询量表评分分布
     *
     * @param scaleId 量表ID
     * @return 评分分布
     */
    @Override
    public List<Map<String, Object>> selectRatingDistribution(Long scaleId) {
        return reviewMapper.selectRatingDistribution(scaleId);
    }

    /**
     * 查询最新评价列表
     *
     * @param limit 限制数量
     * @return 评价集合
     */
    @Override
    public List<PsyTAssessmentReview> selectLatestReviews(Integer limit) {
        return reviewMapper.selectLatestReviews(limit);
    }

    /**
     * 查询用户是否点赞评价
     *
     * @param reviewId 评价ID
     * @param userId 用户ID
     * @return 是否点赞
     */
    @Override
    public boolean checkUserLiked(Long reviewId, Long userId) {
        int count = reviewMapper.checkUserLiked(reviewId, userId);
        return count > 0;
    }

    /**
     * 查询评价回复列表
     *
     * @param reviewId 评价ID
     * @return 回复集合
     */
    @Override
    public List<Map<String, Object>> selectReviewReplies(Long reviewId) {
        return reviewMapper.selectReviewReplies(reviewId);
    }

    /**
     * 创建评价
     *
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param recordId 测评记录ID
     * @param orderId 订单ID
     * @param rating 评分
     * @param content 评价内容
     * @param isAnonymous 是否匿名
     * @return 结果
     */
    @Override
    @Transactional
    public int createReview(Long userId, Long scaleId, Long recordId, Long orderId, Integer rating, String content, Integer isAnonymous) {
        // 验证评价权限
        Map<String, Object> validation = validateReviewPermission(userId, scaleId, recordId);
        if (!(Boolean) validation.get("success")) {
            throw new RuntimeException(validation.get("message").toString());
        }

        // 处理敏感词
        String processedContent = processSensitiveWords(content);

        // 创建评价对象
        PsyTAssessmentReview review = new PsyTAssessmentReview();
        review.setUserId(userId);
        review.setScaleId(scaleId);
        review.setRecordId(recordId);
        review.setOrderId(orderId);
        review.setRating(rating);
        review.setContent(processedContent);
        review.setIsAnonymous(isAnonymous);
        review.setStatus(0); // 待审核
        review.setLikeCount(0);
        review.setReplyCount(0);
        review.setIsTop(0);

        int result = insertReview(review);

        // 自动审核
        Map<String, Object> autoAuditResult = autoAuditReview(review);
        if ((Boolean) autoAuditResult.get("autoPass")) {
            auditReview(review.getId(), 1, "自动审核通过");
        }

        return result;
    }

    /**
     * 验证评价权限
     *
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param recordId 测评记录ID
     * @return 验证结果
     */
    @Override
    public Map<String, Object> validateReviewPermission(Long userId, Long scaleId, Long recordId) {
        Map<String, Object> result = new HashMap<>();

        // 检查是否已评价
        if (checkUserReviewed(userId, scaleId, recordId)) {
            result.put("success", false);
            result.put("message", "已经评价过了");
            return result;
        }

        // 检查测评记录是否存在且属于该用户
        PsyTAssessmentRecord record = recordMapper.selectAssessmentRecordById(recordId);
        if (record == null) {
            result.put("success", false);
            result.put("message", "测评记录不存在");
            return result;
        }

        if (!record.getUserId().equals(userId)) {
            result.put("success", false);
            result.put("message", "无权评价此测评记录");
            return result;
        }

        // 检查测评是否已完成
        if (record.getStatus() != 2) { // 假设2表示已完成
            result.put("success", false);
            result.put("message", "测评未完成，无法评价");
            return result;
        }

        result.put("success", true);
        result.put("message", "验证通过");
        return result;
    }

    /**
     * 获取评价统计摘要
     *
     * @param scaleId 量表ID
     * @return 统计摘要
     */
    @Override
    public Map<String, Object> getReviewSummary(Long scaleId) {
        Map<String, Object> stats = selectScaleReviewStats(scaleId);
        List<Map<String, Object>> distribution = selectRatingDistribution(scaleId);

        Map<String, Object> summary = new HashMap<>();
        summary.put("stats", stats);
        summary.put("distribution", distribution);

        return summary;
    }

    /**
     * 处理评价敏感词
     *
     * @param content 评价内容
     * @return 处理后的内容
     */
    @Override
    public String processSensitiveWords(String content) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        // 这里可以接入敏感词过滤服务
        // 简单示例：替换一些常见敏感词
        String[] sensitiveWords = {"垃圾", "骗子", "差评"};
        String processedContent = content;

        for (String word : sensitiveWords) {
            if (processedContent.contains(word)) {
                processedContent = processedContent.replace(word, "***");
            }
        }

        return processedContent;
    }

    /**
     * 自动审核评价
     *
     * @param review 评价信息
     * @return 审核结果
     */
    @Override
    public Map<String, Object> autoAuditReview(PsyTAssessmentReview review) {
        Map<String, Object> result = new HashMap<>();

        boolean autoPass = true;
        String reason = "";

        // 检查评价内容长度
        if (review.getContent() != null && review.getContent().length() < 10) {
            autoPass = false;
            reason = "评价内容过短";
        }

        // 检查是否包含敏感词
        if (review.getContent() != null && review.getContent().contains("***")) {
            autoPass = false;
            reason = "包含敏感词";
        }

        // 检查评分是否合理
        if (review.getRating() == null || review.getRating() < 1 || review.getRating() > 5) {
            autoPass = false;
            reason = "评分不合理";
        }

        result.put("autoPass", autoPass);
        result.put("reason", reason);

        return result;
    }

    /**
     * 发送评价通知
     *
     * @param reviewId 评价ID
     * @return 结果
     */
    @Override
    public int sendReviewNotification(Long reviewId) {
        // 这里可以实现发送通知的逻辑
        // 比如通知量表作者有新评价
        return 1;
    }

    /**
     * 导出评价数据
     *
     * @param scaleId 量表ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 导出数据
     */
    @Override
    public List<Map<String, Object>> exportReviewData(Long scaleId, String startDate, String endDate) {
        // 构建查询条件
        PsyTAssessmentReview condition = new PsyTAssessmentReview();
        condition.setScaleId(scaleId);
        condition.setStatus(1); // 只导出审核通过的评价

        // 设置时间范围
        Map<String, Object> params = new HashMap<>();
        if (startDate != null) {
            params.put("beginTime", startDate);
        }
        if (endDate != null) {
            params.put("endTime", endDate);
        }
        condition.setParams(params);

        List<PsyTAssessmentReview> reviews = selectReviewList(condition);

        // 转换为导出格式
        List<Map<String, Object>> exportData = new ArrayList<>();
        for (PsyTAssessmentReview review : reviews) {
            Map<String, Object> data = new HashMap<>();
            data.put("id", review.getId());
            data.put("rating", review.getRating());
            data.put("content", review.getContent());
            data.put("isAnonymous", review.getIsAnonymous() == 1 ? "是" : "否");
            data.put("likeCount", review.getLikeCount());
            data.put("replyCount", review.getReplyCount());
            data.put("createTime", review.getCreateTime());
            data.put("displayName", review.getDisplayName());

            exportData.add(data);
        }

        return exportData;
    }
}
