package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;
import com.xihuan.common.core.domain.entity.PsyTimeScheduleTemplate;
import com.xihuan.common.core.domain.entity.PsyTimeTemplateItem;
import com.xihuan.common.core.domain.vo.ConsultantSimpleVO;
import com.xihuan.system.mapper.PsyTimeCounselorScheduleMapper;
import com.xihuan.system.service.IPsyTimeCounselorScheduleService;
import com.xihuan.system.service.IPsyTimeScheduleTemplateService;
import com.xihuan.system.service.wxService.PsyCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 咨询师排班Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeCounselorScheduleServiceImpl implements IPsyTimeCounselorScheduleService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyTimeCounselorScheduleServiceImpl.class);
    
    @Autowired
    private PsyTimeCounselorScheduleMapper scheduleMapper;

    @Autowired
    private PsyCategoryService.PsyConsultantService consultantService;

    @Autowired
    private IPsyTimeScheduleTemplateService templateService;

    /**
     * 查询咨询师排班列表
     * 
     * @param schedule 咨询师排班
     * @return 咨询师排班
     */
    @Override
    public List<PsyTimeCounselorSchedule> selectScheduleList(PsyTimeCounselorSchedule schedule) {
        return scheduleMapper.selectScheduleList(schedule);
    }

    /**
     * 根据ID查询咨询师排班
     * 
     * @param id 咨询师排班主键
     * @return 咨询师排班
     */
    @Override
    public PsyTimeCounselorSchedule selectScheduleById(Long id) {
        return scheduleMapper.selectScheduleById(id);
    }

    /**
     * 根据咨询师和日期查询排班
     * 
     * @param counselorId 咨询师ID
     * @param scheduleDate 排班日期
     * @return 排班记录
     */
    @Override
    public PsyTimeCounselorSchedule selectScheduleByCounselorAndDate(Long counselorId, LocalDate scheduleDate) {
        return scheduleMapper.selectScheduleByCounselorAndDate(counselorId, scheduleDate);
    }

    /**
     * 查询咨询师在日期范围内的排班
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 排班记录集合
     */
    @Override
    public List<PsyTimeCounselorSchedule> selectSchedulesByDateRange(Long counselorId, LocalDate startDate, LocalDate endDate) {
        return scheduleMapper.selectSchedulesByDateRange(counselorId, startDate, endDate);
    }

    /**
     * 查询指定日期的所有咨询师排班
     * 
     * @param scheduleDate 排班日期
     * @param centerId 咨询中心ID
     * @return 排班记录集合
     */
    @Override
    public List<PsyTimeCounselorSchedule> selectSchedulesByDate(LocalDate scheduleDate, Long centerId) {
        return scheduleMapper.selectSchedulesByDate(scheduleDate, centerId);
    }

    /**
     * 新增咨询师排班
     * 
     * @param schedule 咨询师排班
     * @return 结果
     */
    @Override
    public int insertSchedule(PsyTimeCounselorSchedule schedule) {
        return scheduleMapper.insertSchedule(schedule);
    }

    /**
     * 批量新增咨询师排班
     *
     * @param schedules 咨询师排班列表
     * @return 结果
     */
    @Override
    public int batchInsertSchedules(List<PsyTimeCounselorSchedule> schedules) {
        if (schedules == null || schedules.isEmpty()) {
            return 0;
        }

        try {
            return scheduleMapper.batchInsertSchedules(schedules);
        } catch (Exception e) {
            // 如果批量插入失败（可能是重复键异常），尝试使用INSERT IGNORE
            if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                logger.warn("批量插入排班记录时发现重复数据，尝试使用INSERT IGNORE: {}", e.getMessage());
                try {
                    return scheduleMapper.batchInsertSchedulesIgnoreDuplicate(schedules);
                } catch (Exception e2) {
                    logger.warn("INSERT IGNORE也失败，尝试逐个插入: {}", e2.getMessage());
                    return batchInsertSchedulesOneByOne(schedules);
                }
            }
            throw e;
        }
    }

    /**
     * 逐个插入排班记录，跳过重复的记录
     *
     * @param schedules 咨询师排班列表
     * @return 成功插入的记录数
     */
    private int batchInsertSchedulesOneByOne(List<PsyTimeCounselorSchedule> schedules) {
        int successCount = 0;
        for (PsyTimeCounselorSchedule schedule : schedules) {
            try {
                // 再次检查是否存在重复记录
                if (!checkScheduleExists(schedule.getCounselorId(), schedule.getScheduleDate())) {
                    int result = scheduleMapper.insertSchedule(schedule);
                    if (result > 0) {
                        successCount++;
                    }
                } else {
                    logger.debug("跳过重复的排班记录: 咨询师ID={}, 日期={}",
                               schedule.getCounselorId(), schedule.getScheduleDate());
                }
            } catch (Exception e) {
                if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                    logger.debug("跳过重复的排班记录: 咨询师ID={}, 日期={}, 错误: {}",
                               schedule.getCounselorId(), schedule.getScheduleDate(), e.getMessage());
                } else {
                    logger.error("插入排班记录失败: 咨询师ID={}, 日期={}, 错误: {}",
                               schedule.getCounselorId(), schedule.getScheduleDate(), e.getMessage());
                }
            }
        }
        return successCount;
    }

    /**
     * 批量新增咨询师排班（忽略重复记录）
     *
     * @param schedules 咨询师排班列表
     * @return 结果
     */
    @Override
    public int batchInsertSchedulesIgnoreDuplicate(List<PsyTimeCounselorSchedule> schedules) {
        if (schedules == null || schedules.isEmpty()) {
            return 0;
        }
        return scheduleMapper.batchInsertSchedulesIgnoreDuplicate(schedules);
    }

    /**
     * 修改咨询师排班
     * 
     * @param schedule 咨询师排班
     * @return 结果
     */
    @Override
    public int updateSchedule(PsyTimeCounselorSchedule schedule) {
        return scheduleMapper.updateSchedule(schedule);
    }

    /**
     * 删除咨询师排班信息
     * 
     * @param id 咨询师排班主键
     * @return 结果
     */
    @Override
    public int deleteScheduleById(Long id) {
        return scheduleMapper.deleteScheduleById(id);
    }

    /**
     * 批量删除咨询师排班
     * 
     * @param ids 需要删除的咨询师排班主键集合
     * @return 结果
     */
    @Override
    public int deleteScheduleByIds(Long[] ids) {
        return scheduleMapper.deleteScheduleByIds(ids);
    }

    /**
     * 删除指定日期范围的排班
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果
     */
    @Override
    public int deleteSchedulesByDateRange(Long counselorId, LocalDate startDate, LocalDate endDate) {
        return scheduleMapper.deleteSchedulesByDateRange(counselorId, startDate, endDate);
    }

    /**
     * 检查排班是否存在
     * 
     * @param counselorId 咨询师ID
     * @param scheduleDate 排班日期
     * @return 是否存在
     */
    @Override
    public boolean checkScheduleExists(Long counselorId, LocalDate scheduleDate) {
        int count = scheduleMapper.checkScheduleExists(counselorId, scheduleDate);
        return count > 0;
    }

    /**
     * 为咨询师生成默认排班（工作日 9:00-18:00）
     *
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 生成的排班数量
     */
    @Override
    public int generateDefaultSchedule(Long counselorId, LocalDate startDate, LocalDate endDate, Long centerId) {
        if (counselorId == null || startDate == null || endDate == null) {
            logger.warn("生成排班失败：参数不能为空");
            return 0;
        }

        if (startDate.isAfter(endDate)) {
            logger.warn("生成排班失败：开始日期不能晚于结束日期");
            return 0;
        }

        List<PsyTimeCounselorSchedule> schedules = new ArrayList<>();

        // 先查询已存在的排班记录，避免重复检查
        List<PsyTimeCounselorSchedule> existingSchedules =
            scheduleMapper.selectSchedulesByDateRange(counselorId, startDate, endDate);

        // 将已存在的日期放入Set中，提高查询效率
        Set<LocalDate> existingDates = existingSchedules.stream()
            .map(PsyTimeCounselorSchedule::getScheduleDate)
            .collect(java.util.stream.Collectors.toSet());

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            // 检查是否已存在排班
            if (!existingDates.contains(currentDate)) {
                PsyTimeCounselorSchedule schedule = new PsyTimeCounselorSchedule();
                schedule.setCounselorId(counselorId);
                schedule.setScheduleDate(currentDate);
                schedule.setCenterId(centerId);

                // 判断是否为工作日
                DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
//                if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
                    // 工作日：9:00-21:00，包含晚上时间段
                    schedule.setStartTime(LocalTime.of(9, 0));
                    schedule.setEndTime(LocalTime.of(21, 0));
                    schedule.setIsWorking(1);
//                } else {
//                    // 周末：休假
//                    schedule.setIsWorking(0);
//                }

                schedule.setIsTemplate(1); // 标记为模板生成
                schedule.setDelFlag(0); // 设置删除标志
                schedule.setCreateTime(new java.util.Date()); // 设置创建时间
                schedules.add(schedule);
            } else {
                logger.debug("跳过已存在的排班日期: 咨询师ID={}, 日期={}", counselorId, currentDate);
            }

            currentDate = currentDate.plusDays(1);
        }

        if (!schedules.isEmpty()) {
            int result = batchInsertSchedules(schedules);
            logger.info("为咨询师 {} 生成了 {} 条排班记录", counselorId, result);
            return result;
        } else {
            logger.info("咨询师 {} 在指定日期范围内已存在排班记录，无需生成新记录", counselorId);
        }

        return 0;
    }

    /**
     * 为所有咨询师生成默认排班
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 生成的排班数量
     */
    @Override
    public int generateAllCounselorSchedule(LocalDate startDate, LocalDate endDate, Long centerId) {
        if (startDate == null || endDate == null) {
            logger.warn("生成排班失败：日期参数不能为空");
            return 0;
        }

        if (startDate.isAfter(endDate)) {
            logger.warn("生成排班失败：开始日期不能晚于结束日期");
            return 0;
        }

        // 获取所有启用状态的咨询师
        List<ConsultantSimpleVO> consultants = consultantService.listAllSimple();

        if (consultants == null || consultants.isEmpty()) {
            logger.warn("没有找到可用的咨询师，无法生成排班");
            return 0;
        }

        int totalCount = 0;
        int failedCount = 0;

        for (ConsultantSimpleVO consultant : consultants) {
            try {
                int count = generateDefaultSchedule(consultant.getId(), startDate, endDate, centerId);
                totalCount += count;
            } catch (Exception e) {
                failedCount++;
                logger.error("为咨询师 {} 生成排班时发生错误: {}", consultant.getId(), e.getMessage(), e);
            }
        }

        if (failedCount > 0) {
            logger.warn("为 {} 个咨询师中的 {} 个生成排班失败", consultants.size(), failedCount);
        }

        logger.info("为 {} 个咨询师生成了 {} 条排班记录", consultants.size() - failedCount, totalCount);
        return totalCount;
    }

    /**
     * 清理重复的排班记录
     *
     * @return 清理的记录数量
     */
    @Override
    public int cleanupDuplicateSchedules() {
        try {
            int count = scheduleMapper.cleanupDuplicateSchedules();
            if (count > 0) {
                logger.info("清理了 {} 条重复的排班记录", count);
            } else {
                logger.info("没有发现重复的排班记录");
            }
            return count;
        } catch (Exception e) {
            logger.error("清理重复排班记录时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 确保咨询师有未来指定天数的排班记录
     * 如果没有则自动生成默认排班
     *
     * @param counselorId 咨询师ID
     * @param days 未来天数
     * @param centerId 咨询中心ID
     * @return 生成的排班数量
     */
    @Override
    public int ensureFutureSchedule(Long counselorId, int days, Long centerId) {
        if (counselorId == null || days <= 0) {
            logger.warn("确保未来排班失败：参数无效");
            return 0;
        }

        LocalDate startDate = LocalDate.now().plusDays(1); // 从明天开始
        LocalDate endDate = startDate.plusDays(days - 1);

        try {
            // 查询该咨询师在指定日期范围内的排班记录
            List<PsyTimeCounselorSchedule> existingSchedules =
                scheduleMapper.selectSchedulesByDateRange(counselorId, startDate, endDate);

            // 找出缺失的日期
            Set<LocalDate> existingDates = existingSchedules.stream()
                .map(PsyTimeCounselorSchedule::getScheduleDate)
                .collect(java.util.stream.Collectors.toSet());

            List<PsyTimeCounselorSchedule> schedulesToCreate = new ArrayList<>();
            LocalDate currentDate = startDate;

            while (!currentDate.isAfter(endDate)) {
                    // 智能生成排班：优先使用咨询师的排班模板，否则使用默认设置
                    PsyTimeCounselorSchedule schedule = createScheduleForDate(counselorId, currentDate, centerId);
                    if (schedule != null) {
                        schedulesToCreate.add(schedule);
                    }
                currentDate = currentDate.plusDays(1);
            }

            if (!schedulesToCreate.isEmpty()) {
                int result = batchInsertSchedules(schedulesToCreate);
                logger.info("为咨询师 {} 智能生成了 {} 条未来排班记录", counselorId, result);
                return result;
            } else {
                logger.debug("咨询师 {} 已有完整的未来 {} 天排班记录", counselorId, days);
                return 0;
            }

        } catch (Exception e) {
            logger.error("确保咨询师 {} 未来排班失败", counselorId, e);
            return 0;
        }
    }

    /**
     * 为指定日期创建排班记录
     * 优先使用咨询师的排班模板，否则使用系统默认设置
     */
    private PsyTimeCounselorSchedule createScheduleForDate(Long counselorId, LocalDate date, Long centerId) {
        try {
            PsyTimeCounselorSchedule schedule = new PsyTimeCounselorSchedule();
            schedule.setCounselorId(counselorId);
            schedule.setScheduleDate(date);
            schedule.setCenterId(centerId != null ? centerId : 1L);
            schedule.setDelFlag(0);
            schedule.setCreateTime(new java.util.Date());

            // 尝试获取咨询师的排班模板
            PsyTimeScheduleTemplate template = getEffectiveTemplateForDate(counselorId, date);

            if (template != null) {
                // 使用咨询师的自定义模板
                applyTemplateToSchedule(schedule, template, date);
                schedule.setIsTemplate(1);
                schedule.setTemplateId(template.getId());
                schedule.setRemark("基于咨询师模板自动生成的排班");
                logger.debug("为咨询师 {} 在 {} 使用自定义模板生成排班", counselorId, date);
            } else {
                // 使用系统默认设置
                applyDefaultScheduleSettings(schedule);
                schedule.setIsTemplate(1);
                schedule.setRemark("系统自动生成的默认排班");
                logger.debug("为咨询师 {} 在 {} 使用默认设置生成排班", counselorId, date);
            }

            return schedule;

        } catch (Exception e) {
            logger.error("为咨询师 {} 在日期 {} 创建排班失败: {}", counselorId, date, e.getMessage());
            return null;
        }
    }

    /**
     * 获取指定日期有效的排班模板
     */
    private PsyTimeScheduleTemplate getEffectiveTemplateForDate(Long counselorId, LocalDate date) {
        try {
            // 首先尝试获取在指定日期有效的模板
            PsyTimeScheduleTemplate effectiveTemplate = templateService.selectEffectiveTemplate(counselorId, date);
            if (effectiveTemplate != null) {
                logger.debug("为咨询师 {} 在 {} 找到有效模板: {}", counselorId, date, effectiveTemplate.getName());
                return effectiveTemplate;
            }

            // 如果没有找到有效模板，尝试获取默认模板
            PsyTimeScheduleTemplate defaultTemplate = templateService.selectDefaultTemplateByCounselorId(counselorId);
            if (defaultTemplate != null) {
                logger.debug("为咨询师 {} 在 {} 使用默认模板: {}", counselorId, date, defaultTemplate.getName());
                return defaultTemplate;
            }

            logger.debug("咨询师 {} 在 {} 没有找到任何排班模板，将使用系统默认设置", counselorId, date);
            return null;

        } catch (Exception e) {
            logger.warn("获取咨询师 {} 在 {} 的排班模板失败: {}", counselorId, date, e.getMessage());
            return null;
        }
    }

    /**
     * 将模板应用到排班记录
     */
    private void applyTemplateToSchedule(PsyTimeCounselorSchedule schedule, PsyTimeScheduleTemplate template, LocalDate date) {
        try {
            if (template == null || template.getTemplateItems() == null || template.getTemplateItems().isEmpty()) {
                logger.warn("模板为空或没有模板明细，使用默认设置");
                applyDefaultScheduleSettings(schedule);
                return;
            }

            // 获取当前日期是星期几 (1=周一, 7=周日)
            int dayOfWeek = date.getDayOfWeek().getValue();

            // 查找匹配当前星期的模板明细
            List<PsyTimeTemplateItem> dayItems = template.getTemplateItems().stream()
                .filter(item -> item.getDayOfWeek() != null && item.getDayOfWeek().equals(dayOfWeek))
                .filter(item -> item.getDelFlag() == null || item.getDelFlag() == 0)
                .sorted((a, b) -> {
                    // 按开始时间排序
                    if (a.getStartTime() == null) return 1;
                    if (b.getStartTime() == null) return -1;
                    return a.getStartTime().compareTo(b.getStartTime());
                })
                .collect(java.util.stream.Collectors.toList());

            if (dayItems.isEmpty()) {
                logger.debug("模板中没有找到星期 {} 的排班设置，使用默认设置", dayOfWeek);
                applyDefaultScheduleSettings(schedule);
                return;
            }

            // 计算工作时间范围
            LocalTime earliestStart = dayItems.stream()
                .map(PsyTimeTemplateItem::getStartTime)
                .filter(java.util.Objects::nonNull)
                .min(LocalTime::compareTo)
                .orElse(LocalTime.of(9, 0));

            LocalTime latestEnd = dayItems.stream()
                .map(PsyTimeTemplateItem::getEndTime)
                .filter(java.util.Objects::nonNull)
                .max(LocalTime::compareTo)
                .orElse(LocalTime.of(21, 0));

            // 应用模板设置
            schedule.setStartTime(earliestStart);
            schedule.setEndTime(latestEnd);
            schedule.setIsWorking(1); // 有模板明细说明是工作日

            // 设置咨询中心ID（优先使用模板中的设置）
            Long centerId = dayItems.stream()
                .map(PsyTimeTemplateItem::getCenterId)
                .filter(java.util.Objects::nonNull)
                .findFirst()
                .orElse(schedule.getCenterId());
            if (centerId != null) {
                schedule.setCenterId(centerId);
            }

            logger.debug("成功应用模板到排班：咨询师 {} 在 {} 的工作时间为 {} - {}",
                schedule.getCounselorId(), date, earliestStart, latestEnd);

        } catch (Exception e) {
            logger.error("应用模板到排班失败，使用默认设置: {}", e.getMessage(), e);
            applyDefaultScheduleSettings(schedule);
        }
    }

    /**
     * 应用默认排班设置
     */
    private void applyDefaultScheduleSettings(PsyTimeCounselorSchedule schedule) {
        try {
            // 获取系统配置的默认工作时间，如果没有配置则使用硬编码默认值
            LocalTime defaultStartTime = getSystemDefaultStartTime();
            LocalTime defaultEndTime = getSystemDefaultEndTime();

            schedule.setStartTime(defaultStartTime);
            schedule.setEndTime(defaultEndTime);
            schedule.setIsWorking(1); // 默认工作状态

            logger.debug("应用默认排班设置：{} - {}", defaultStartTime, defaultEndTime);

        } catch (Exception e) {
            logger.error("应用默认排班设置失败，使用硬编码默认值: {}", e.getMessage());
            // 兜底方案：硬编码默认值
            schedule.setStartTime(LocalTime.of(9, 0));
            schedule.setEndTime(LocalTime.of(21, 0));
            schedule.setIsWorking(1);
        }
    }

    /**
     * 获取系统默认开始时间
     */
    private LocalTime getSystemDefaultStartTime() {
        try {
            // 可以从系统配置中读取，这里先使用硬编码
            // String configValue = configService.selectConfigByKey("schedule.default.start.time");
            // if (StringUtils.isNotEmpty(configValue)) {
            //     return LocalTime.parse(configValue);
            // }
            return LocalTime.of(9, 0); // 默认9:00开始
        } catch (Exception e) {
            logger.warn("获取系统默认开始时间失败，使用硬编码默认值: {}", e.getMessage());
            return LocalTime.of(9, 0);
        }
    }

    /**
     * 获取系统默认结束时间
     */
    private LocalTime getSystemDefaultEndTime() {
        try {
            // 可以从系统配置中读取，这里先使用硬编码
            // String configValue = configService.selectConfigByKey("schedule.default.end.time");
            // if (StringUtils.isNotEmpty(configValue)) {
            //     return LocalTime.parse(configValue);
            // }
            return LocalTime.of(21, 0); // 默认21:00结束，包含晚上时间段
        } catch (Exception e) {
            logger.warn("获取系统默认结束时间失败，使用硬编码默认值: {}", e.getMessage());
            return LocalTime.of(21, 0);
        }
    }

    /**
     * 确保所有咨询师都有未来指定天数的排班记录
     *
     * @param days 未来天数
     * @param centerId 咨询中心ID
     * @return 生成的排班数量
     */
    @Override
    public int ensureAllCounselorsFutureSchedule(int days, Long centerId) {
        if (days <= 0) {
            logger.warn("确保所有咨询师未来排班失败：天数参数无效");
            return 0;
        }

        try {
            // 获取所有启用状态的咨询师
            List<ConsultantSimpleVO> consultants = consultantService.listAllSimple();

            if (consultants == null || consultants.isEmpty()) {
                logger.warn("没有找到可用的咨询师，无法确保未来排班");
                return 0;
            }

            int totalCount = 0;
            int failedCount = 0;

            for (ConsultantSimpleVO consultant : consultants) {
                try {
                    int count = ensureFutureSchedule(consultant.getId(), days, centerId);
                    totalCount += count;
                } catch (Exception e) {
                    failedCount++;
                    logger.error("确保咨询师 {} 未来排班时发生错误: {}", consultant.getId(), e.getMessage(), e);
                }
            }

            if (failedCount > 0) {
                logger.warn("为 {} 个咨询师中的 {} 个确保未来排班失败", consultants.size(), failedCount);
            }

            logger.info("为 {} 个咨询师确保了未来 {} 天的排班，共生成 {} 条新记录",
                consultants.size() - failedCount, days, totalCount);
            return totalCount;

        } catch (Exception e) {
            logger.error("确保所有咨询师未来排班失败", e);
            return 0;
        }
    }
}
