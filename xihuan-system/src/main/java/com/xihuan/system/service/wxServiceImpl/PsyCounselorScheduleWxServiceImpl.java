package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.dto.PsyCounselorScheduleDTO;
import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;
import com.xihuan.common.core.domain.entity.PsyTimeScheduleTemplate;
import com.xihuan.system.mapper.PsyTimeCounselorScheduleMapper;
import com.xihuan.system.service.IPsyTimeCounselorScheduleService;
import com.xihuan.system.service.IPsyTimeScheduleTemplateService;
import com.xihuan.system.service.wxService.IPsyCounselorScheduleWxService;
import com.xihuan.system.service.wxService.PsyCategoryService.PsyConsultantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 咨询师排期小程序端Service实现
 * 
 * <AUTHOR>
 */
@Service
public class PsyCounselorScheduleWxServiceImpl implements IPsyCounselorScheduleWxService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyCounselorScheduleWxServiceImpl.class);
    
    @Autowired
    private IPsyTimeCounselorScheduleService scheduleService;
    
    @Autowired
    private PsyTimeCounselorScheduleMapper scheduleMapper;
    
    @Autowired
    private IPsyTimeScheduleTemplateService templateService;
    
    @Autowired
    private PsyConsultantService consultantService;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy年M月d日");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final String[] WEEK_DAYS = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

    @Override
    public List<PsyCounselorScheduleDTO> getCounselorScheduleList(Long counselorId, LocalDate startDate, LocalDate endDate, Long centerId) {
        List<PsyTimeCounselorSchedule> schedules;
        
        if (counselorId != null && startDate != null && endDate != null) {
            schedules = scheduleService.selectSchedulesByDateRange(counselorId, startDate, endDate);
        } else {
            PsyTimeCounselorSchedule querySchedule = new PsyTimeCounselorSchedule();
            querySchedule.setCounselorId(counselorId);
            querySchedule.setCenterId(centerId);
            schedules = scheduleService.selectScheduleList(querySchedule);
        }
        
        return schedules.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public PsyCounselorScheduleDTO getScheduleDetail(Long scheduleId) {
        PsyTimeCounselorSchedule schedule = scheduleService.selectScheduleById(scheduleId);
        if (schedule == null) {
            return null;
        }
        return convertToDTO(schedule);
    }

    @Override
    public PsyCounselorScheduleDTO.ScheduleCalendarDTO getScheduleCalendar(Long counselorId, int year, int month) {
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        
        List<PsyTimeCounselorSchedule> schedules = scheduleService.selectSchedulesByDateRange(counselorId, startDate, endDate);
        Map<LocalDate, PsyTimeCounselorSchedule> scheduleMap = schedules.stream()
                .collect(Collectors.toMap(PsyTimeCounselorSchedule::getScheduleDate, s -> s));
        
        PsyCounselorScheduleDTO.ScheduleCalendarDTO calendar = new PsyCounselorScheduleDTO.ScheduleCalendarDTO();
        calendar.setYearMonth(String.format("%d-%02d", year, month));
        calendar.setMonthDisplay(String.format("%d年%d月", year, month));
        
        List<PsyCounselorScheduleDTO.ScheduleCalendarDTO.CalendarDay> days = new ArrayList<>();
        LocalDate currentDate = startDate;
        LocalDate today = LocalDate.now();
        
        while (!currentDate.isAfter(endDate)) {
            PsyCounselorScheduleDTO.ScheduleCalendarDTO.CalendarDay day = new PsyCounselorScheduleDTO.ScheduleCalendarDTO.CalendarDay();
            day.setDate(currentDate);
            day.setDayDisplay(String.valueOf(currentDate.getDayOfMonth()));
            day.setIsToday(currentDate.equals(today));
            day.setIsCurrentMonth(currentDate.getMonth().getValue() == month);
            
            PsyTimeCounselorSchedule schedule = scheduleMap.get(currentDate);
            if (schedule != null) {
                day.setHasSchedule(true);
                day.setWorkingStatus(schedule.getIsWorking());
                day.setStatusType(schedule.getIsWorking() == 1 ? "work" : "rest");
                day.setTimeDisplay(formatTimeRange(schedule.getStartTime(), schedule.getEndTime()));
                day.setScheduleId(schedule.getId());
            } else {
                day.setHasSchedule(false);
                day.setWorkingStatus(null);
                day.setStatusType("none");
                day.setTimeDisplay(null);
                day.setScheduleId(null);
            }
            
            days.add(day);
            currentDate = currentDate.plusDays(1);
        }
        
        calendar.setDays(days);
        return calendar;
    }

    @Override
    public PsyCounselorScheduleDTO.ScheduleStatistics getScheduleStatistics(Long counselorId, LocalDate startDate, LocalDate endDate) {
        List<PsyTimeCounselorSchedule> schedules = scheduleService.selectSchedulesByDateRange(counselorId, startDate, endDate);
        
        PsyCounselorScheduleDTO.ScheduleStatistics statistics = new PsyCounselorScheduleDTO.ScheduleStatistics();
        statistics.setStartDate(startDate);
        statistics.setEndDate(endDate);
        statistics.setTotalDays((long) schedules.size());
        
        long workingDays = schedules.stream().filter(s -> s.getIsWorking() == 1).count();
        statistics.setWorkingDays(workingDays);
        statistics.setRestDays(statistics.getTotalDays() - workingDays);
        
        if (statistics.getTotalDays() > 0) {
            statistics.setWorkingRate((double) workingDays / statistics.getTotalDays() * 100);
        } else {
            statistics.setWorkingRate(0.0);
        }
        
        // 计算总工作时长
        double totalWorkingHours = schedules.stream()
                .filter(s -> s.getIsWorking() == 1)
                .mapToDouble(s -> ChronoUnit.MINUTES.between(s.getStartTime(), s.getEndTime()) / 60.0)
                .sum();
        statistics.setTotalWorkingHours(totalWorkingHours);
        
        if (workingDays > 0) {
            statistics.setAverageDailyHours(totalWorkingHours / workingDays);
        } else {
            statistics.setAverageDailyHours(0.0);
        }
        
        return statistics;
    }

    @Override
    public int createSchedule(PsyTimeCounselorSchedule schedule, String createBy) {
        schedule.setCreateBy(createBy);
        return scheduleService.insertSchedule(schedule);
    }

    @Override
    public int batchCreateSchedules(List<PsyTimeCounselorSchedule> schedules, String createBy) {
        schedules.forEach(schedule -> schedule.setCreateBy(createBy));
        return scheduleService.batchInsertSchedules(schedules);
    }

    @Override
    public int updateSchedule(PsyTimeCounselorSchedule schedule, String updateBy) {
        schedule.setUpdateBy(updateBy);
        return scheduleService.updateSchedule(schedule);
    }

    @Override
    public int deleteSchedule(Long scheduleId) {
        return scheduleService.deleteScheduleById(scheduleId);
    }

    @Override
    public int batchDeleteSchedules(List<Long> scheduleIds) {
        return scheduleService.deleteScheduleByIds(scheduleIds.toArray(new Long[0]));
    }

    @Override
    public int copyScheduleToOtherDates(Long sourceScheduleId, List<LocalDate> targetDates, String createBy) {
        PsyTimeCounselorSchedule sourceSchedule = scheduleService.selectScheduleById(sourceScheduleId);
        if (sourceSchedule == null) {
            return 0;
        }
        
        List<PsyTimeCounselorSchedule> newSchedules = new ArrayList<>();
        
        for (LocalDate targetDate : targetDates) {
            // 检查目标日期是否已存在排期
            PsyTimeCounselorSchedule existingSchedule = scheduleService.selectScheduleByCounselorAndDate(
                sourceSchedule.getCounselorId(), targetDate);
            if (existingSchedule != null) {
                continue; // 跳过已存在的日期
            }
            
            // 创建新排期
            PsyTimeCounselorSchedule newSchedule = new PsyTimeCounselorSchedule();
            newSchedule.setCounselorId(sourceSchedule.getCounselorId());
            newSchedule.setScheduleDate(targetDate);
            newSchedule.setStartTime(sourceSchedule.getStartTime());
            newSchedule.setEndTime(sourceSchedule.getEndTime());
            newSchedule.setCenterId(sourceSchedule.getCenterId());
            newSchedule.setIsWorking(sourceSchedule.getIsWorking());
            newSchedule.setIsTemplate(0); // 复制的排期不是模板生成
            newSchedule.setRemark("复制自 " + sourceSchedule.getScheduleDate() + " 的排期");
            newSchedule.setCreateBy(createBy);
            
            newSchedules.add(newSchedule);
        }
        
        return newSchedules.isEmpty() ? 0 : scheduleService.batchInsertSchedules(newSchedules);
    }

    @Override
    public int generateDefaultSchedule(Long counselorId, LocalDate startDate, LocalDate endDate, Long centerId, String createBy) {
        return scheduleService.generateDefaultSchedule(counselorId, startDate, endDate, centerId);
    }

    @Override
    public int toggleWorkingStatus(Long scheduleId, String updateBy) {
        PsyTimeCounselorSchedule schedule = scheduleService.selectScheduleById(scheduleId);
        if (schedule == null) {
            return 0;
        }
        
        schedule.setIsWorking(schedule.getIsWorking() == 1 ? 0 : 1);
        schedule.setUpdateBy(updateBy);
        
        return scheduleService.updateSchedule(schedule);
    }

    @Override
    public PsyCounselorScheduleDTO.ConflictCheckResult checkScheduleConflict(Long counselorId, LocalDate scheduleDate, Long excludeScheduleId) {
        PsyCounselorScheduleDTO.ConflictCheckResult result = new PsyCounselorScheduleDTO.ConflictCheckResult();
        
        PsyTimeCounselorSchedule existingSchedule = scheduleService.selectScheduleByCounselorAndDate(counselorId, scheduleDate);
        
        if (existingSchedule != null && !existingSchedule.getId().equals(excludeScheduleId)) {
            result.setHasConflict(true);
            result.setConflictDates(Arrays.asList(scheduleDate));
            
            PsyCounselorScheduleDTO.ConflictCheckResult.ConflictDetail detail = new PsyCounselorScheduleDTO.ConflictCheckResult.ConflictDetail();
            detail.setDate(scheduleDate);
            detail.setReason("该日期已存在排期");
            detail.setExistingSchedule(formatTimeRange(existingSchedule.getStartTime(), existingSchedule.getEndTime()));
            
            result.setConflictDetails(Arrays.asList(detail));
        } else {
            result.setHasConflict(false);
            result.setConflictDates(new ArrayList<>());
            result.setConflictDetails(new ArrayList<>());
        }
        
        return result;
    }

    /**
     * 转换为DTO对象
     */
    private PsyCounselorScheduleDTO convertToDTO(PsyTimeCounselorSchedule schedule) {
        PsyCounselorScheduleDTO dto = new PsyCounselorScheduleDTO();
        dto.setId(schedule.getId());
        dto.setCounselorId(schedule.getCounselorId());
        dto.setScheduleDate(schedule.getScheduleDate());
        dto.setStartTime(schedule.getStartTime());
        dto.setEndTime(schedule.getEndTime());
        dto.setCenterId(schedule.getCenterId());
        dto.setIsWorking(schedule.getIsWorking());
        dto.setIsTemplate(schedule.getIsTemplate());
        dto.setTemplateId(schedule.getTemplateId());
        dto.setRemark(schedule.getRemark());
        
        // 格式化显示字段
        dto.setDateDisplay(schedule.getScheduleDate().format(DATE_FORMATTER));
        dto.setWeekDayDisplay(WEEK_DAYS[schedule.getScheduleDate().getDayOfWeek().getValue() % 7]);
        dto.setIsToday(schedule.getScheduleDate().equals(LocalDate.now()));
        dto.setTimeDisplay(formatTimeRange(schedule.getStartTime(), schedule.getEndTime()));
        dto.setWorkingStatusDisplay(schedule.getIsWorking() == 1 ? "工作" : "休假");
        
        // 获取咨询师信息
        try {
            PsyConsultant consultant = consultantService.getConsultantWithUserById(schedule.getCounselorId());
            if (consultant != null) {
                dto.setCounselorName(consultant.getName());
                dto.setCounselorAvatar(consultant.getImageUrl());
            }
        } catch (Exception e) {
            logger.warn("获取咨询师信息失败: {}", e.getMessage());
        }
        
        return dto;
    }
    
    @Override
    public int applyScheduleTemplate(PsyCounselorScheduleDTO.ApplyTemplateRequest request, String createBy) {
        // TODO: 实现模板应用逻辑
        logger.info("应用排期模板功能待实现");
        return 0;
    }

    @Override
    public List<PsyTimeScheduleTemplate> getAvailableTemplates(Long counselorId) {
        // TODO: 获取可用模板
        logger.info("获取可用模板功能待实现");
        return new ArrayList<>();
    }

    @Override
    public List<PsyCounselorScheduleDTO.ScheduleTimeRange> getScheduleTimeRangeAnalysis(Long counselorId, LocalDate date) {
        PsyTimeCounselorSchedule schedule = scheduleService.selectScheduleByCounselorAndDate(counselorId, date);
        if (schedule == null || schedule.getIsWorking() == 0) {
            return new ArrayList<>();
        }

        List<PsyCounselorScheduleDTO.ScheduleTimeRange> ranges = new ArrayList<>();

        // 分析时间段（上午、下午、晚上）
        LocalTime startTime = schedule.getStartTime();
        LocalTime endTime = schedule.getEndTime();

        // 上午时段 (09:00-12:00)
        if (startTime.isBefore(LocalTime.of(12, 0))) {
            PsyCounselorScheduleDTO.ScheduleTimeRange morningRange = new PsyCounselorScheduleDTO.ScheduleTimeRange();
            morningRange.setRangeName("上午");
            morningRange.setStartTime(startTime.isBefore(LocalTime.of(9, 0)) ? startTime : LocalTime.of(9, 0));
            morningRange.setEndTime(endTime.isAfter(LocalTime.of(12, 0)) ? LocalTime.of(12, 0) : endTime);
            morningRange.setTimeDisplay(formatTimeRange(morningRange.getStartTime(), morningRange.getEndTime()));
            morningRange.setIsWorkingTime(true);
            ranges.add(morningRange);
        }

        // 下午时段 (12:00-18:00)
        if (startTime.isBefore(LocalTime.of(18, 0)) && endTime.isAfter(LocalTime.of(12, 0))) {
            PsyCounselorScheduleDTO.ScheduleTimeRange afternoonRange = new PsyCounselorScheduleDTO.ScheduleTimeRange();
            afternoonRange.setRangeName("下午");
            afternoonRange.setStartTime(startTime.isBefore(LocalTime.of(12, 0)) ? LocalTime.of(12, 0) : startTime);
            afternoonRange.setEndTime(endTime.isAfter(LocalTime.of(18, 0)) ? LocalTime.of(18, 0) : endTime);
            afternoonRange.setTimeDisplay(formatTimeRange(afternoonRange.getStartTime(), afternoonRange.getEndTime()));
            afternoonRange.setIsWorkingTime(true);
            ranges.add(afternoonRange);
        }

        // 晚上时段 (18:00-22:00)
        if (endTime.isAfter(LocalTime.of(18, 0))) {
            PsyCounselorScheduleDTO.ScheduleTimeRange eveningRange = new PsyCounselorScheduleDTO.ScheduleTimeRange();
            eveningRange.setRangeName("晚上");
            eveningRange.setStartTime(startTime.isBefore(LocalTime.of(18, 0)) ? LocalTime.of(18, 0) : startTime);
            eveningRange.setEndTime(endTime);
            eveningRange.setTimeDisplay(formatTimeRange(eveningRange.getStartTime(), eveningRange.getEndTime()));
            eveningRange.setIsWorkingTime(true);
            ranges.add(eveningRange);
        }

        return ranges;
    }

    @Override
    public int batchOperateSchedules(PsyCounselorScheduleDTO.BatchOperationRequest request, String operatorBy) {
        switch (request.getOperationType()) {
            case "create":
                if (request.getSchedules() != null && !request.getSchedules().isEmpty()) {
                    List<PsyTimeCounselorSchedule> schedules = request.getSchedules().stream()
                            .map(this::convertFromDTO)
                            .collect(Collectors.toList());
                    return batchCreateSchedules(schedules, operatorBy);
                }
                break;
            case "delete":
                if (request.getScheduleIds() != null && !request.getScheduleIds().isEmpty()) {
                    return batchDeleteSchedules(request.getScheduleIds());
                }
                break;
            case "copy":
                if (request.getSourceScheduleId() != null && request.getTargetDates() != null) {
                    return copyScheduleToOtherDates(request.getSourceScheduleId(), request.getTargetDates(), operatorBy);
                }
                break;
        }
        return 0;
    }

    @Override
    public List<PsyCounselorScheduleDTO> getRecentScheduleOverview(Long counselorId, int days) {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(days - 1);

        return getCounselorScheduleList(counselorId, startDate, endDate, null);
    }

    @Override
    public List<String> getScheduleChangeHistory(Long scheduleId) {
        // TODO: 实现变更历史功能
        logger.info("排期变更历史功能待实现");
        return new ArrayList<>();
    }

    @Override
    public String validateScheduleData(PsyTimeCounselorSchedule schedule) {
        if (schedule.getCounselorId() == null) {
            return "咨询师ID不能为空";
        }
        if (schedule.getScheduleDate() == null) {
            return "排期日期不能为空";
        }
        if (schedule.getStartTime() == null || schedule.getEndTime() == null) {
            return "排期时间不能为空";
        }
        if (schedule.getStartTime().isAfter(schedule.getEndTime())) {
            return "开始时间不能晚于结束时间";
        }
        if (schedule.getScheduleDate().isBefore(LocalDate.now())) {
            return "不能创建过去日期的排期";
        }
        return null; // 验证通过
    }

    @Override
    public List<String> getScheduleSuggestions(Long counselorId, LocalDate date) {
        List<String> suggestions = new ArrayList<>();

        // 检查是否是周末
        if (date.getDayOfWeek().getValue() >= 6) {
            suggestions.add("周末建议适当减少工作时间，注意休息");
        }

        // 检查是否有连续工作
        LocalDate yesterday = date.minusDays(1);
        LocalDate tomorrow = date.plusDays(1);

        PsyTimeCounselorSchedule yesterdaySchedule = scheduleService.selectScheduleByCounselorAndDate(counselorId, yesterday);
        PsyTimeCounselorSchedule tomorrowSchedule = scheduleService.selectScheduleByCounselorAndDate(counselorId, tomorrow);

        if (yesterdaySchedule != null && yesterdaySchedule.getIsWorking() == 1 &&
            tomorrowSchedule != null && tomorrowSchedule.getIsWorking() == 1) {
            suggestions.add("连续工作日较多，建议合理安排休息时间");
        }

        // 检查工作时长
        PsyTimeCounselorSchedule currentSchedule = scheduleService.selectScheduleByCounselorAndDate(counselorId, date);
        if (currentSchedule != null && currentSchedule.getIsWorking() == 1) {
            long workingHours = ChronoUnit.HOURS.between(currentSchedule.getStartTime(), currentSchedule.getEndTime());
            if (workingHours > 10) {
                suggestions.add("工作时间较长，建议适当调整以保证服务质量");
            }
        }

        return suggestions;
    }

    /**
     * 从DTO转换为实体对象
     */
    private PsyTimeCounselorSchedule convertFromDTO(PsyCounselorScheduleDTO dto) {
        PsyTimeCounselorSchedule schedule = new PsyTimeCounselorSchedule();
        schedule.setId(dto.getId());
        schedule.setCounselorId(dto.getCounselorId());
        schedule.setScheduleDate(dto.getScheduleDate());
        schedule.setStartTime(dto.getStartTime());
        schedule.setEndTime(dto.getEndTime());
        schedule.setCenterId(dto.getCenterId());
        schedule.setIsWorking(dto.getIsWorking());
        schedule.setIsTemplate(dto.getIsTemplate());
        schedule.setTemplateId(dto.getTemplateId());
        schedule.setRemark(dto.getRemark());
        return schedule;
    }

    /**
     * 格式化时间范围
     */
    private String formatTimeRange(LocalTime startTime, LocalTime endTime) {
        if (startTime == null || endTime == null) {
            return "";
        }
        return startTime.format(TIME_FORMATTER) + "-" + endTime.format(TIME_FORMATTER);
    }
}
