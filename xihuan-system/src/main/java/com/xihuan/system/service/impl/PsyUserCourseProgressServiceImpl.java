package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyUserCourseProgress;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyUserCourseProgressMapper;
import com.xihuan.system.service.IPsyUserCourseProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户课程学习进度表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyUserCourseProgressServiceImpl implements IPsyUserCourseProgressService {
    
    @Autowired
    private PsyUserCourseProgressMapper progressMapper;

    /**
     * 查询学习进度列表
     * 
     * @param progress 学习进度信息
     * @return 学习进度集合
     */
    @Override
    public List<PsyUserCourseProgress> selectProgressList(PsyUserCourseProgress progress) {
        return progressMapper.selectProgressList(progress);
    }

    /**
     * 根据ID查询学习进度
     * 
     * @param id 学习进度ID
     * @return 学习进度信息
     */
    @Override
    public PsyUserCourseProgress selectProgressById(Long id) {
        return progressMapper.selectProgressById(id);
    }

    /**
     * 根据用户ID和章节ID查询学习进度
     * 
     * @param userId 用户ID
     * @param chapterId 章节ID
     * @return 学习进度信息
     */
    @Override
    public PsyUserCourseProgress selectProgressByUserAndChapter(Long userId, Long chapterId) {
        return progressMapper.selectProgressByUserAndChapter(userId, chapterId);
    }

    /**
     * 根据用户ID和课程ID查询学习进度列表
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 学习进度集合
     */
    @Override
    public List<PsyUserCourseProgress> selectProgressByUserAndCourse(Long userId, Long courseId) {
        return progressMapper.selectProgressByUserAndCourse(userId, courseId);
    }

    /**
     * 新增学习进度
     * 
     * @param progress 学习进度信息
     * @return 结果
     */
    @Override
    public int insertProgress(PsyUserCourseProgress progress) {
        progress.setCreateTime(DateUtils.getNowDate());
        progress.setUpdateTime(DateUtils.getNowDate());
        
        // 设置默认值
        if (progress.getProgressPercent() == null) {
            progress.setProgressPercent(BigDecimal.ZERO);
        }
        if (progress.getLastPosition() == null) {
            progress.setLastPosition(0);
        }
        if (progress.getIsCompleted() == null) {
            progress.setIsCompleted(0);
        }
        if (progress.getStudyTime() == null) {
            progress.setStudyTime(0);
        }
        
        return progressMapper.insertProgress(progress);
    }

    /**
     * 修改学习进度
     * 
     * @param progress 学习进度信息
     * @return 结果
     */
    @Override
    public int updateProgress(PsyUserCourseProgress progress) {
        progress.setUpdateTime(DateUtils.getNowDate());
        return progressMapper.updateProgress(progress);
    }

    /**
     * 保存学习进度（新增或更新）
     * 
     * @param progress 学习进度信息
     * @return 结果
     */
    @Override
    public int saveProgress(PsyUserCourseProgress progress) {
        // 检查是否已存在记录
        PsyUserCourseProgress existingProgress = progressMapper.selectProgressByUserAndChapter(
            progress.getUserId(), progress.getChapterId());
        
        if (existingProgress != null) {
            // 更新现有记录
            progress.setId(existingProgress.getId());
            
            // 累加学习时长
            if (progress.getStudyTime() != null && existingProgress.getStudyTime() != null) {
                progress.setStudyTime(existingProgress.getStudyTime() + progress.getStudyTime());
            }
            
            return updateProgress(progress);
        } else {
            // 新增记录
            return insertProgress(progress);
        }
    }

    /**
     * 删除学习进度
     * 
     * @param id 学习进度ID
     * @return 结果
     */
    @Override
    public int deleteProgressById(Long id) {
        return progressMapper.deleteProgressById(id);
    }

    /**
     * 批量删除学习进度
     * 
     * @param ids 需要删除的学习进度ID
     * @return 结果
     */
    @Override
    public int deleteProgressByIds(Long[] ids) {
        return progressMapper.deleteProgressByIds(ids);
    }

    /**
     * 标记章节为已完成
     * 
     * @param userId 用户ID
     * @param chapterId 章节ID
     * @return 结果
     */
    @Override
    public int markChapterCompleted(Long userId, Long chapterId) {
        return progressMapper.markChapterCompleted(userId, chapterId);
    }

    /**
     * 计算用户课程总进度
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 总进度百分比
     */
    @Override
    public BigDecimal calculateCourseProgress(Long userId, Long courseId) {
        BigDecimal progress = progressMapper.calculateCourseProgress(userId, courseId);
        return progress != null ? progress : BigDecimal.ZERO;
    }

    /**
     * 统计用户已完成的章节数量
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 已完成章节数量
     */
    @Override
    public int countCompletedChapters(Long userId, Long courseId) {
        return progressMapper.countCompletedChapters(userId, courseId);
    }

    /**
     * 统计用户总学习时长
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 总学习时长（秒）
     */
    @Override
    public int sumStudyTime(Long userId, Long courseId) {
        return progressMapper.sumStudyTime(userId, courseId);
    }
}
