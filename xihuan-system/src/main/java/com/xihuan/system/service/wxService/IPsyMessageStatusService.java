package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.PsyMessageStatus;

import java.util.List;

/**
 * 消息状态服务接口
 */
public interface IPsyMessageStatusService {
    
    /**
     * 创建消息状态
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 消息状态对象
     */
    PsyMessageStatus createMessageStatus(Long messageId, Long userId);
    
    /**
     * 批量创建消息状态
     * 
     * @param messageId 消息ID
     * @param userIds 用户ID列表
     * @return 操作结果
     */
    boolean batchCreateMessageStatus(Long messageId, List<Long> userIds);
    
    /**
     * 更新消息已读状态
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @param isRead 是否已读
     * @return 操作结果
     */
    boolean updateMessageReadStatus(Long messageId, Long userId, boolean isRead);
    
    /**
     * 批量更新消息已读状态
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @param isRead 是否已读
     * @return 操作结果
     */
    boolean batchUpdateMessageReadStatus(List<Long> messageIds, Long userId, boolean isRead);
    
    /**
     * 获取消息状态
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 消息状态对象
     */
    PsyMessageStatus getMessageStatus(Long messageId, Long userId);
    
    /**
     * 获取用户未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int getUnreadCount(Long userId);
} 