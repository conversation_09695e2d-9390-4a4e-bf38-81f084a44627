package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.entity.PsyProduct;
import com.xihuan.common.core.domain.entity.PsyServiceItem;
import com.xihuan.system.domain.dto.PsyProductDTO;
import com.xihuan.system.mapper.PsyProductMapper;
import com.xihuan.system.service.wxService.PsyProductService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

// PsyProductServiceImpl.java
@Service
public class PsyProductServiceImpl implements PsyProductService {

    @Autowired
    private PsyProductMapper productMapper;

    @Override
    public List<PsyProduct> selectProductList(PsyProduct product) {
        return productMapper.selectProductList(product);
    }

    @Override
    public PsyProduct selectProductWithDetails(Long productId) {
        PsyProduct psyProduct = productMapper.selectProductWithDetails(productId);
        System.out.println(psyProduct);
        return psyProduct;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertProduct(PsyProductDTO productDTO) {
        // 插入主表
        PsyProduct product = convertToEntity(productDTO);
        int rows = productMapper.insertProduct(product);

        // 处理关联服务项目
        if (CollectionUtils.isNotEmpty(productDTO.getServiceItems())) {
            insertProductServiceRelation(productDTO);
        }

        // 处理分类关联
        if (CollectionUtils.isNotEmpty(productDTO.getCategoryIds())) {
            productMapper.batchInsertProductCategories(product.getProductId(), productDTO.getCategoryIds());
        }

        return rows;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateProduct(PsyProductDTO productDTO) {
        // 更新主表
        PsyProduct product = convertToEntity(productDTO);
        int rows = productMapper.updateProduct(product);

        // 删除原有服务项目关联
        productMapper.deleteByProductId(productDTO.getProductId());
        // 插入新服务项目关联
        if (CollectionUtils.isNotEmpty(productDTO.getServiceItems())) {
            insertProductServiceRelation(productDTO);
        }

        // 删除原有分类关联
        productMapper.deleteProductCategories(productDTO.getProductId());
        // 插入新分类关联
        if (CollectionUtils.isNotEmpty(productDTO.getCategoryIds())) {
            productMapper.batchInsertProductCategories(product.getProductId(), productDTO.getCategoryIds());
        }

        return rows;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteProductByIds(Long[] productIds) {
        // 删除主表记录
        int rows = productMapper.deleteProductByIds(productIds);

        if (rows > 0) {
            // 删除服务项目关联
            productMapper.deleteByProductIds(productIds);
            // 删除分类关联
            productMapper.deleteProductCategoriesByIds(productIds);
        }
        return rows;
    }

    /**
     * 处理产品-服务项目关联关系
     */
    private void insertProductServiceRelation(PsyProductDTO productDTO) {
        List<PsyServiceItem> relations = productDTO.getServiceItems().stream()
                .map(dtoItem -> {
                    PsyServiceItem item = new PsyServiceItem();
                    item.setProductId(productDTO.getProductId());
                    item.setItemId(dtoItem.getItemId());
                    return item;
                })
                .collect(Collectors.toList());
        productMapper.batchInsert(relations);
    }

    /**
     * DTO转Entity核心方法
     */
    private PsyProduct convertToEntity(PsyProductDTO dto) {
        PsyProduct product = new PsyProduct();
        // 基础字段映射
        product.setProductId(dto.getProductId());
        product.setProductName(dto.getProductName());
        product.setProductImage(dto.getProductImage());
        product.setServiceMethod(String.valueOf(dto.getServiceMethod()));
        product.setServiceGuarantee(dto.getServiceGuarantee());
        product.setServiceDirection(dto.getServiceDirection());
        product.setServiceDirectionType(String.valueOf(dto.getServiceDirectionType()));
        product.setConsultantGrade(String.valueOf(dto.getConsultantGrade()));
        product.setServiceDuration(String.valueOf(dto.getServiceDuration()));
        product.setGraphicDetails(dto.getGraphicDetails());
        product.setSupplementInfo(dto.getSupplementInfo());
        product.setOriginalPrice(dto.getOriginalPrice());
        product.setDiscountPrice(dto.getDiscountPrice());
        product.setDiscountRate(dto.getDiscountRate());
        product.setApplicableStores(dto.getApplicableStores());
        product.setValidityPeriod(String.valueOf(dto.getValidityPeriod()));
        product.setUnavailableDates(dto.getUnavailableDates());
        product.setNeedAppointment(dto.getNeedAppointment() ? "1" : "0");
        product.setSinglePurchaseLimit(dto.getSinglePurchaseLimit());
        product.setAgeRange(dto.getAgeRange());
        product.setDisableFlag(dto.getDisableFlag());

        // 系统字段
        product.setCreateBy(dto.getCreateBy());
        product.setCreateTime(dto.getCreateTime());
        product.setUpdateBy(dto.getUpdateBy());
        product.setUpdateTime(dto.getUpdateTime());
        return product;
    }

}