package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;
import com.xihuan.common.core.domain.entity.PsyTQuestion;
import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.common.utils.uuid.IdUtils;
import com.xihuan.system.mapper.PsyTAssessmentRecordMapper;
import com.xihuan.system.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测评记录Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class PsyTAssessmentRecordServiceImpl implements IPsyTAssessmentRecordService {

    private static final Logger logger = LoggerFactory.getLogger(PsyTAssessmentRecordServiceImpl.class);

    @Autowired
    private PsyTAssessmentRecordMapper assessmentRecordMapper;
    
    @Autowired
    private IPsyTAnswerRecordService answerRecordService;

    @Autowired
    private IPsyTInterpretationService interpretationService;

    @Autowired
    private IPsyTScaleService scaleService;

    @Autowired
    private IPsyTQuestionService questionService;

    @Autowired
    private IPsyTAdvancedScoringService advancedScoringService;

    @Autowired
    private IPsyTReportGenerationService reportGenerationService;

    /**
     * 查询测评记录
     * 
     * @param id 测评记录主键
     * @return 测评记录
     */
    @Override
    public PsyTAssessmentRecord selectAssessmentRecordById(Long id) {
        return assessmentRecordMapper.selectAssessmentRecordById(id);
    }

    /**
     * 查询测评记录列表
     * 
     * @param assessmentRecord 测评记录
     * @return 测评记录
     */
    @Override
    public List<PsyTAssessmentRecord> selectAssessmentRecordList(PsyTAssessmentRecord assessmentRecord) {
        return assessmentRecordMapper.selectAssessmentRecordList(assessmentRecord);
    }

    /**
     * 新增测评记录
     * 
     * @param assessmentRecord 测评记录
     * @return 结果
     */
    @Override
    public int insertAssessmentRecord(PsyTAssessmentRecord assessmentRecord) {
        assessmentRecord.setCreateBy(SecurityUtils.getUsername());
        assessmentRecord.setCreateTime(DateUtils.getNowDate());
        assessmentRecord.setDelFlag("0");
        
        // 生成测评编号
        if (assessmentRecord.getAssessmentNo() == null) {
            assessmentRecord.setAssessmentNo(generateAssessmentNo());
        }
        
        return assessmentRecordMapper.insertAssessmentRecord(assessmentRecord);
    }

    /**
     * 修改测评记录
     * 
     * @param assessmentRecord 测评记录
     * @return 结果
     */
    @Override
    public int updateAssessmentRecord(PsyTAssessmentRecord assessmentRecord) {
        assessmentRecord.setUpdateBy(SecurityUtils.getUsername());
        assessmentRecord.setUpdateTime(DateUtils.getNowDate());
        return assessmentRecordMapper.updateAssessmentRecord(assessmentRecord);
    }

    /**
     * 批量删除测评记录
     * 
     * @param ids 需要删除的测评记录主键
     * @return 结果
     */
    @Override
    public int deleteAssessmentRecordByIds(Long[] ids) {
        return assessmentRecordMapper.deleteAssessmentRecordByIds(ids);
    }

    /**
     * 删除测评记录信息
     * 
     * @param id 测评记录主键
     * @return 结果
     */
    @Override
    public int deleteAssessmentRecordById(Long id) {
        return assessmentRecordMapper.deleteAssessmentRecordById(id);
    }

    /**
     * 根据用户ID查询测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyTAssessmentRecord> selectRecordsByUserId(Long userId) {
        return assessmentRecordMapper.selectRecordsByUserId(userId);
    }

    /**
     * 根据量表ID查询测评记录
     * 
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyTAssessmentRecord> selectRecordsByScaleId(Long scaleId) {
        return assessmentRecordMapper.selectRecordsByScaleId(scaleId);
    }

    /**
     * 根据用户ID和量表ID查询测评记录
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyTAssessmentRecord> selectRecordsByUserAndScale(Long userId, Long scaleId) {
        return assessmentRecordMapper.selectRecordsByUserAndScale(userId, scaleId);
    }

    /**
     * 开始测评
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param enterpriseId 企业ID（可为空）
     * @return 测评记录ID
     */
    @Override
    @Transactional
    public Long startAssessment(Long userId, Long scaleId, Long enterpriseId) {
        PsyTAssessmentRecord record = new PsyTAssessmentRecord();
        record.setUserId(userId);
        record.setScaleId(scaleId);
        // 注意：enterpriseId字段在数据库表中不存在，不设置此字段
        // record.setEnterpriseId(enterpriseId);
        // 生成唯一的会话ID
        record.setSessionId(generateSessionId(userId, scaleId));
        record.setStatus(0); // 0=进行中, 1=已完成, 2=已放弃
        record.setStartTime(DateUtils.getNowDate());

        insertAssessmentRecord(record);
        return record.getId();
    }

    /**
     * 生成唯一的会话ID
     */
    private String generateSessionId(Long userId, Long scaleId) {
        // 格式: USER_{userId}_SCALE_{scaleId}_TIME_{timestamp}
        long timestamp = System.currentTimeMillis();
        return String.format("USER_%d_SCALE_%d_TIME_%d", userId, scaleId, timestamp);
    }

    /**
     * 完成测评
     * 
     * @param recordId 测评记录ID
     * @param totalScore 总分
     * @param dimensionScores 维度分数
     * @return 结果
     */
    @Override
    @Transactional
    public int completeAssessment(Long recordId, BigDecimal totalScore, Map<String, BigDecimal> dimensionScores) {
        PsyTAssessmentRecord record = selectAssessmentRecordById(recordId);
        if (record == null) {
            return 0;
        }

        try {
            // 执行高级计分
            Map<String, Object> scoringResult = advancedScoringService.executeAdvancedScoring(recordId);

            if ((Boolean) scoringResult.get("success")) {
                Map<String, Object> scores = (Map<String, Object>) scoringResult.get("scoringResult");

                // 使用高级计分的结果
                if (scores.containsKey("totalScore")) {
                    Object totalScoreObj = scores.get("totalScore");
                    if (totalScoreObj instanceof BigDecimal) {
                        totalScore = (BigDecimal) totalScoreObj;
                    } else if (totalScoreObj instanceof Number) {
                        totalScore = new BigDecimal(totalScoreObj.toString());
                    }
                }

                // 获取维度分数
                if (scores.containsKey("dimensionScores")) {
                    dimensionScores = (Map<String, BigDecimal>) scores.get("dimensionScores");
                }

                // 获取标准分（如果有）
                if (scores.containsKey("standardScore")) {
                    Object standardScoreObj = scores.get("standardScore");
                    if (standardScoreObj instanceof BigDecimal) {
                        record.setStandardScore((BigDecimal) standardScoreObj);
                    }
                }
            }

            // 更新测评记录
            record.setStatus(1); // 已完成
            record.setCompletionTime(DateUtils.getNowDate());
            record.setTotalScore(totalScore);

            // 保存维度分数为JSON
            if (dimensionScores != null && !dimensionScores.isEmpty()) {
                try {
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    String dimensionScoresJson = mapper.writeValueAsString(dimensionScores);
                    record.setDimensionScores(dimensionScoresJson);
                } catch (Exception e) {
                    // 记录日志但不影响主流程
                    System.err.println("保存维度分数JSON失败: " + e.getMessage());
                }
            }

            // 更新记录
            int result = updateAssessmentRecord(record);

            // 异步生成报告
            if (result > 0) {
                try {
                    Map<String, Object> report = reportGenerationService.generateCompleteReport(recordId);
                    if ((Boolean) report.get("success")) {
                        System.out.println("测评报告生成成功，recordId: " + recordId);
                    }
                } catch (Exception e) {
                    // 报告生成失败不影响测评完成
                    System.err.println("生成测评报告失败: " + e.getMessage());
                }
            }

            return result;

        } catch (Exception e) {
            System.err.println("完成测评时发生异常: " + e.getMessage());
            // 如果高级计分失败，使用传入的分数
            record.setStatus(1);
            record.setCompletionTime(DateUtils.getNowDate());
            record.setTotalScore(totalScore);
            return updateAssessmentRecord(record);
        }
    }

    /**
     * 暂停测评
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    @Override
    public int pauseAssessment(Long recordId) {
        return updateAssessmentStatus(recordId, 3); // 暂停
    }

    /**
     * 恢复测评
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    @Override
    public int resumeAssessment(Long recordId) {
        return updateAssessmentStatus(recordId, 1); // 进行中
    }

    @Override
    public Map<String, Object> selectAssessmentDetail(Long recordId) {
        Map<String, Object> detail = new HashMap<>();

        // 1. 获取测评记录基本信息
        PsyTAssessmentRecord record = selectAssessmentRecordById(recordId);
        detail.put("record", record);

        // 2. 获取答题进度
        Map<String, Object> progress = answerRecordService.selectAnswerProgress(recordId);
        detail.put("progress", progress);

        // 3. 获取答题历史记录
        List<Map<String, Object>> answerHistory = answerRecordService.selectAnswerHistoryByRecordId(recordId);
        detail.put("answerHistory", answerHistory);

        // 4. 获取量表信息
        if (record != null && record.getScaleId() != null) {
            PsyTScale scale = scaleService.selectScaleById(record.getScaleId());
            detail.put("scale", scale);

            // 5. 获取所有题目和选项（用于前端显示）
            List<PsyTQuestion> questions = questionService.selectQuestionsWithOptionsByScaleId(record.getScaleId());
            detail.put("questions", questions);
        }

        return detail;
    }

    /**
     * 取消测评
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    @Override
    public int cancelAssessment(Long recordId) {
        return updateAssessmentStatus(recordId, 4); // 已取消
    }

    /**
     * 查询测评详情（包含答题记录）
     * 
     * @param recordId 测评记录ID
     * @return 测评详情
     */
    @Override
    public PsyTAssessmentRecord selectRecordWithAnswers(Long recordId) {
        return assessmentRecordMapper.selectRecordWithAnswers(recordId);
    }

    /**
     * 查询测评结果
     * 
     * @param recordId 测评记录ID
     * @return 测评结果
     */
    @Override
    public Map<String, Object> selectAssessmentResult(Long recordId) {
        Map<String, Object> result = new HashMap<>();
        
        PsyTAssessmentRecord record = selectAssessmentRecordById(recordId);
        if (record == null) {
            return result;
        }
        
        result.put("record", record);
        
        // 获取总分和维度分数
        BigDecimal totalScore = record.getTotalScore();
        if (totalScore == null) {
            totalScore = answerRecordService.calculateTotalScore(recordId);
        }
        
        Map<String, BigDecimal> dimensionScores = answerRecordService.calculateDimensionScores(recordId);
        
        result.put("totalScore", totalScore);
        result.put("dimensionScores", dimensionScores);
        
        // 获取解释结果
        Map<String, Object> interpretationResult = interpretationService.getInterpretationResult(
            record.getScaleId(), totalScore, dimensionScores);
        result.put("interpretation", interpretationResult);
        
        return result;
    }

    /**
     * 生成测评报告
     * 
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    @Override
    public Map<String, Object> generateAssessmentReport(Long recordId) {
        try {
            // 检查是否已有保存的报告
            Map<String, Object> savedReport = reportGenerationService.getSavedReport(recordId);

            // 检查保存的报告是否是新格式（包含reportTitle或basicInfo字段）
            boolean isNewFormat = savedReport.containsKey("reportTitle") || savedReport.containsKey("basicInfo");

            if (!savedReport.isEmpty() && isNewFormat) {
                // 如果已有新格式报告，直接返回
                logger.info("返回已保存的新格式报告，recordId: {}", recordId);
                return savedReport;
            }

            // 生成新的完整报告
            logger.info("生成新格式报告，recordId: {}", recordId);
            Map<String, Object> report = reportGenerationService.generateCompleteReport(recordId);

            if ((Boolean) report.get("success")) {
                // 添加报告编号和生成时间
                report.put("reportNo", generateReportNo());
                report.put("reportGenerateTime", DateUtils.getNowDate());

                // 保存新报告到数据库
                try {
                    reportGenerationService.saveReport(recordId, report);
                    logger.info("新格式报告已保存，recordId: {}", recordId);
                } catch (Exception e) {
                    logger.warn("保存报告失败，但继续返回报告，recordId: {}, error: {}", recordId, e.getMessage());
                }

                return report;
            } else {
                // 如果高级报告生成失败，使用原有逻辑
                Map<String, Object> fallbackReport = new HashMap<>();
                Map<String, Object> result = selectAssessmentResult(recordId);
                fallbackReport.putAll(result);
                fallbackReport.put("reportGenerateTime", DateUtils.getNowDate());
                fallbackReport.put("reportNo", generateReportNo());
                fallbackReport.put("reportType", "FALLBACK");

                return fallbackReport;
            }

        } catch (Exception e) {
            System.err.println("生成测评报告异常: " + e.getMessage());

            // 异常情况下的备用报告
            Map<String, Object> errorReport = new HashMap<>();
            errorReport.put("success", false);
            errorReport.put("message", "报告生成失败: " + e.getMessage());
            errorReport.put("reportGenerateTime", DateUtils.getNowDate());

            return errorReport;
        }
    }

    @Override
    public Map<String, Object> forceRegenerateAssessmentReport(Long recordId) {
        try {
            logger.info("强制重新生成报告，recordId: {}", recordId);

            // 直接生成新的完整报告，不检查缓存
            Map<String, Object> report = reportGenerationService.generateCompleteReport(recordId);

            if ((Boolean) report.get("success")) {
                // 添加报告编号和生成时间
                report.put("reportNo", generateReportNo());
                report.put("reportGenerateTime", DateUtils.getNowDate());

                // 保存新报告到数据库（覆盖旧报告）
                try {
                    reportGenerationService.saveReport(recordId, report);
                    logger.info("强制重新生成的报告已保存，recordId: {}", recordId);
                } catch (Exception e) {
                    logger.warn("保存强制重新生成的报告失败，但继续返回报告，recordId: {}, error: {}", recordId, e.getMessage());
                }

                return report;
            } else {
                logger.error("强制重新生成报告失败，recordId: {}", recordId);
                return report;
            }

        } catch (Exception e) {
            logger.error("强制重新生成测评报告异常，recordId: {}", recordId, e);
            Map<String, Object> errorReport = new HashMap<>();
            errorReport.put("success", false);
            errorReport.put("message", "强制重新生成报告失败: " + e.getMessage());
            return errorReport;
        }
    }

    /**
     * 查询测评统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectAssessmentStats() {
        return assessmentRecordMapper.selectAssessmentStats();
    }

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserAssessmentStats(Long userId) {
        return assessmentRecordMapper.selectUserAssessmentStats(userId);
    }

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleAssessmentStats(Long scaleId) {
        return assessmentRecordMapper.selectScaleAssessmentStats(scaleId);
    }

    /**
     * 查询企业测评统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectEnterpriseAssessmentStats(Long enterpriseId) {
        return assessmentRecordMapper.selectEnterpriseAssessmentStats(enterpriseId);
    }

    /**
     * 查询测评趋势
     * 
     * @param days 天数
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> selectAssessmentTrend(Integer days) {
        return assessmentRecordMapper.selectAssessmentTrend(days);
    }

    /**
     * 查询热门量表排行
     * 
     * @param limit 限制数量
     * @return 排行数据
     */
    @Override
    public List<Map<String, Object>> selectHotScaleRanking(Integer limit) {
        return assessmentRecordMapper.selectHotScaleRanking(limit);
    }

    /**
     * 检查用户是否可以测评
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 检查结果
     */
    @Override
    public Map<String, Object> checkUserCanAssess(Long userId, Long scaleId) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        // 检查是否有未完成的测评
        List<PsyTAssessmentRecord> incompleteRecords = selectIncompleteRecordsByUserId(userId);
        for (PsyTAssessmentRecord record : incompleteRecords) {
            if (record.getScaleId().equals(scaleId)) {
                errors.add("您有未完成的测评，请先完成或取消");
                break;
            }
        }
        
        // 可以添加更多检查逻辑，如权限、付费等
        
        result.put("canAssess", errors.isEmpty());
        result.put("errors", errors);
        
        return result;
    }

    /**
     * 查询用户最近的测评记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 测评记录集合
     */
    @Override
    public List<PsyTAssessmentRecord> selectRecentRecordsByUserId(Long userId, Integer limit) {
        return assessmentRecordMapper.selectRecentRecordsByUserId(userId, limit);
    }

    /**
     * 查询未完成的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyTAssessmentRecord> selectIncompleteRecordsByUserId(Long userId) {
        return assessmentRecordMapper.selectIncompleteRecordsByUserId(userId);
    }

    /**
     * 查询已完成的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyTAssessmentRecord> selectCompletedRecordsByUserId(Long userId) {
        return assessmentRecordMapper.selectCompletedRecordsByUserId(userId);
    }

    /**
     * 更新测评状态
     * 
     * @param recordId 测评记录ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateAssessmentStatus(Long recordId, Integer status) {
        return assessmentRecordMapper.updateAssessmentStatus(recordId, status);
    }

    /**
     * 批量更新测评状态
     * 
     * @param ids 测评记录ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int batchUpdateAssessmentStatus(Long[] ids, Integer status) {
        return assessmentRecordMapper.batchUpdateAssessmentStatus(ids, status);
    }

    /**
     * 导出测评记录
     * 
     * @param ids 测评记录ID数组
     * @return 测评记录列表
     */
    @Override
    public List<PsyTAssessmentRecord> exportAssessmentRecords(Long[] ids) {
        List<PsyTAssessmentRecord> records = new ArrayList<>();
        for (Long id : ids) {
            PsyTAssessmentRecord record = selectAssessmentRecordById(id);
            if (record != null) {
                records.add(record);
            }
        }
        return records;
    }

    /**
     * 查询测评时长分析
     * 
     * @param scaleId 量表ID
     * @return 时长分析
     */
    @Override
    public Map<String, Object> selectDurationAnalysis(Long scaleId) {
        return assessmentRecordMapper.selectDurationAnalysis(scaleId);
    }

    /**
     * 查询测评完成率分析
     * 
     * @param scaleId 量表ID
     * @return 完成率分析
     */
    @Override
    public Map<String, Object> selectCompletionRateAnalysis(Long scaleId) {
        return assessmentRecordMapper.selectCompletionRateAnalysis(scaleId);
    }

    /**
     * 查询测评分数分布
     * 
     * @param scaleId 量表ID
     * @return 分数分布
     */
    @Override
    public List<Map<String, Object>> selectScoreDistribution(Long scaleId) {
        return assessmentRecordMapper.selectScoreDistribution(scaleId);
    }

    /**
     * 重新计算测评分数
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int recalculateAssessmentScore(Long recordId) {
        // 重新计算答题分数
        answerRecordService.recalculateScores(recordId);
        
        // 重新计算总分和维度分数
        BigDecimal totalScore = answerRecordService.calculateTotalScore(recordId);
        Map<String, BigDecimal> dimensionScores = answerRecordService.calculateDimensionScores(recordId);
        
        // 更新测评记录
        PsyTAssessmentRecord record = selectAssessmentRecordById(recordId);
        if (record != null) {
            record.setTotalScore(totalScore);
            return updateAssessmentRecord(record);
        }
        
        return 0;
    }

    /**
     * 查询异常测评记录
     * 
     * @return 异常记录列表
     */
    @Override
    public List<PsyTAssessmentRecord> selectAbnormalRecords() {
        return assessmentRecordMapper.selectAbnormalRecords();
    }

    /**
     * 生成测评编号
     */
    private String generateAssessmentNo() {
        return "ASS" + DateUtils.dateTimeNow() + IdUtils.randomUUID().substring(0, 4).toUpperCase();
    }

    /**
     * 生成报告编号
     */
    private String generateReportNo() {
        return "RPT" + DateUtils.dateTimeNow() + IdUtils.randomUUID().substring(0, 4).toUpperCase();
    }
}
