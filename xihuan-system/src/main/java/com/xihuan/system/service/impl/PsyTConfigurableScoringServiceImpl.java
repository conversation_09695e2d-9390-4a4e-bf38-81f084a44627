package com.xihuan.system.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xihuan.common.core.domain.entity.*;
import com.xihuan.system.mapper.*;
import com.xihuan.system.service.IPsyTConfigurableScoringService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 配置化计分服务实现
 *
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class PsyTConfigurableScoringServiceImpl implements IPsyTConfigurableScoringService {

    private static final Logger logger = LoggerFactory.getLogger(PsyTConfigurableScoringServiceImpl.class);

    @Autowired
    private PsyTAssessmentRecordMapper assessmentRecordMapper;

    @Autowired
    private PsyTAnswerRecordMapper answerRecordMapper;

    @Autowired
    private PsyTScaleMapper scaleMapper;

    @Autowired
    private PsyTQuestionMapper questionMapper;

    @Autowired
    private PsyTSubscaleMapper subscaleMapper;

    @Autowired
    private PsyTScoringConfigMapper scoringConfigMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 执行配置化计分
     */
    @Override
    @Transactional
    public Map<String, Object> executeConfigurableScoring(Long recordId) {
        logger.info("开始执行配置化计分，recordId: {}", recordId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取测评记录
            PsyTAssessmentRecord record = assessmentRecordMapper.selectAssessmentRecordById(recordId);
            if (record == null) {
                result.put("success", false);
                result.put("message", "测评记录不存在");
                return result;
            }

            // 获取量表配置
            Map<String, Object> scaleConfig = getScaleConfig(record.getScaleId());
            if (scaleConfig.isEmpty()) {
                result.put("success", false);
                result.put("message", "量表配置不存在");
                return result;
            }

            String scoringMethod = (String) scaleConfig.get("scoring_method");
            logger.info("量表计分方法: {}", scoringMethod);

            // 根据配置的计分方法执行计分
            Map<String, Object> scoringResult;
            switch (scoringMethod) {
                case "SIMPLE_SUM":
                    scoringResult = calculateSimpleSum(recordId, scaleConfig);
                    break;
                case "REVERSE_SCORING":
                    scoringResult = calculateReverseScoring(recordId, scaleConfig);
                    break;
                case "FORMULA_WITH_BASE":
                    scoringResult = calculateFormulaScoring(recordId, scaleConfig);
                    break;
                case "STANDARD_SCORE":
                    scoringResult = calculateStandardScore(recordId, scaleConfig);
                    break;
                case "SPECIAL_BINARY":
                case "COMPOSITE_SCORING":
                    scoringResult = calculateSpecialScoring(recordId, scaleConfig);
                    break;
                default:
                    // 默认使用简单求和
                    scoringResult = calculateSimpleSum(recordId, scaleConfig);
                    break;
            }

            result.put("success", true);
            result.put("scaleConfig", scaleConfig);
            result.put("scoringResult", scoringResult);
            
            logger.info("配置化计分完成，recordId: {}, method: {}", recordId, scoringMethod);
            
        } catch (Exception e) {
            logger.error("执行配置化计分时发生异常，recordId: {}", recordId, e);
            result.put("success", false);
            result.put("message", "计分过程中发生异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 简单求和计分
     */
    @Override
    public Map<String, Object> calculateSimpleSum(Long recordId, Map<String, Object> scaleConfig) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            BigDecimal totalScore = BigDecimal.ZERO;
            
            for (PsyTAnswerRecord answer : answers) {
                if (answer.getAnswerScore() != null) {
                    totalScore = totalScore.add(answer.getAnswerScore());
                }
            }
            
            result.put("totalScore", totalScore);
            result.put("scoringMethod", "SIMPLE_SUM");
            result.put("description", "简单求和计分");
            
        } catch (Exception e) {
            logger.error("简单求和计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 反向计分
     */
    @Override
    public Map<String, Object> calculateReverseScoring(Long recordId, Map<String, Object> scaleConfig) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            
            // 获取反向计分配置
            Set<Integer> reverseQuestions = getReverseQuestions(scaleConfig);
            Integer reverseMaxValue = (Integer) scaleConfig.get("reverse_max_value");
            if (reverseMaxValue == null) reverseMaxValue = 5; // 默认5点量表
            
            BigDecimal totalScore = BigDecimal.ZERO;
            BigDecimal rawScore = BigDecimal.ZERO; // 原始分（用于标准分转换）
            
            for (PsyTAnswerRecord answer : answers) {
                BigDecimal score = answer.getAnswerScore();
                if (score != null) {
                    rawScore = rawScore.add(score);
                    
                    // 检查是否需要反向计分
                    Integer questionNo = getQuestionNumber(answer.getQuestionId());
                    if (reverseQuestions.contains(questionNo)) {
                        score = new BigDecimal(reverseMaxValue + 1).subtract(score);
                        logger.debug("反向计分 - 题目{}: {} -> {}", questionNo, answer.getAnswerScore(), score);
                    }
                    
                    totalScore = totalScore.add(score);
                }
            }
            
            result.put("totalScore", totalScore);
            result.put("rawScore", rawScore);
            result.put("scoringMethod", "REVERSE_SCORING");
            result.put("description", "反向计分");
            result.put("reverseQuestions", reverseQuestions);
            
            // 如果需要标准分转换
            Boolean hasStandardScore = (Boolean) scaleConfig.get("has_standard_score");
            if (hasStandardScore != null && hasStandardScore) {
                BigDecimal multiplier = (BigDecimal) scaleConfig.get("standard_score_multiplier");
                if (multiplier != null) {
                    BigDecimal standardScore = totalScore.multiply(multiplier).setScale(2, RoundingMode.HALF_UP);
                    result.put("standardScore", standardScore);
                    result.put("totalScore", standardScore); // 使用标准分作为最终分数
                }
            }
            
        } catch (Exception e) {
            logger.error("反向计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 公式计分
     */
    @Override
    public Map<String, Object> calculateFormulaScoring(Long recordId, Map<String, Object> scaleConfig) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            
            // 获取基础分数配置
            Integer baseScore = (Integer) scaleConfig.get("base_score");
            if (baseScore == null) baseScore = 0;
            
            // 获取分量表信息
            PsyTAssessmentRecord record = assessmentRecordMapper.selectAssessmentRecordById(recordId);
            List<PsyTSubscale> subscales = subscaleMapper.selectSubscalesByScaleId(record.getScaleId());
            
            Map<String, BigDecimal> dimensionScores = new HashMap<>();
            BigDecimal totalScore = BigDecimal.ZERO;
            
            // 为每个维度计算分数
            for (PsyTSubscale subscale : subscales) {
                // 获取基础分数，如果subscale没有baseScore字段，使用配置的baseScore
                Integer subscaleBaseScore = baseScore; // 默认使用配置的基础分数
                try {
                    // 尝试获取分量表的基础分数（如果字段存在）
                    if (subscale.getBaseScore() != null) {
                        subscaleBaseScore = subscale.getBaseScore();
                    }
                } catch (Exception e) {
                    // 如果方法不存在，使用默认值
                    logger.debug("分量表基础分数字段不存在，使用默认值: {}", baseScore);
                }

                BigDecimal dimensionScore = new BigDecimal(subscaleBaseScore);

                // 计算该维度的题目分数总和
                BigDecimal sumScore = BigDecimal.ZERO;
                for (PsyTAnswerRecord answer : answers) {
                    if (isQuestionInSubscale(answer.getQuestionId(), subscale.getId())) {
                        if (answer.getAnswerScore() != null) {
                            sumScore = sumScore.add(answer.getAnswerScore());
                        }
                    }
                }

                // 公式计分：基础分 + 题目分数总和
                dimensionScore = dimensionScore.add(sumScore);
                dimensionScores.put(subscale.getAlias(), dimensionScore);
                totalScore = totalScore.add(dimensionScore);

                logger.info("公式计分维度 - {}: 基础分{} + 题目分数{} = {}",
                    subscale.getAlias(), subscaleBaseScore, sumScore, dimensionScore);
            }
            
            result.put("totalScore", totalScore);
            result.put("dimensionScores", dimensionScores);
            result.put("scoringMethod", "FORMULA_WITH_BASE");
            result.put("description", "公式计分（基础分 + 题目分数）");
            result.put("baseScore", baseScore);
            
        } catch (Exception e) {
            logger.error("公式计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 标准分转换
     */
    @Override
    public Map<String, Object> calculateStandardScore(Long recordId, Map<String, Object> scaleConfig) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 先计算原始分（可能包含反向计分）
            Map<String, Object> rawResult;
            Boolean hasReverseItems = (Boolean) scaleConfig.get("has_reverse_items");
            if (hasReverseItems != null && hasReverseItems) {
                rawResult = calculateReverseScoring(recordId, scaleConfig);
            } else {
                rawResult = calculateSimpleSum(recordId, scaleConfig);
            }
            
            BigDecimal rawScore = (BigDecimal) rawResult.get("totalScore");
            
            // 标准分转换
            BigDecimal multiplier = (BigDecimal) scaleConfig.get("standard_score_multiplier");
            if (multiplier == null) multiplier = new BigDecimal("1.0");
            
            BigDecimal standardScore = rawScore.multiply(multiplier).setScale(2, RoundingMode.HALF_UP);
            
            result.put("rawScore", rawScore);
            result.put("standardScore", standardScore);
            result.put("totalScore", standardScore);
            result.put("scoringMethod", "STANDARD_SCORE");
            result.put("description", "标准分转换（原始分 × " + multiplier + "）");
            result.put("multiplier", multiplier);
            
            // 如果有反向计分信息，也包含进来
            if (rawResult.containsKey("reverseQuestions")) {
                result.put("reverseQuestions", rawResult.get("reverseQuestions"));
            }
            
        } catch (Exception e) {
            logger.error("标准分转换异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 特殊计分
     */
    @Override
    public Map<String, Object> calculateSpecialScoring(Long recordId, Map<String, Object> scaleConfig) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String scoringMethod = (String) scaleConfig.get("scoring_method");
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            BigDecimal totalScore = BigDecimal.ZERO;
            
            if ("SPECIAL_BINARY".equals(scoringMethod)) {
                // 二选一计分：选择"是"得1分，选择"否"得0分
                for (PsyTAnswerRecord answer : answers) {
                    if (answer.getAnswerContent() != null) {
                        String content = answer.getAnswerContent().trim();
                        if ("是".equals(content) || "1".equals(content)) {
                            totalScore = totalScore.add(BigDecimal.ONE);
                        }
                    }
                }
                result.put("description", "特殊二选一计分（是=1分，否=0分）");
            } else {
                // 其他特殊计分逻辑可以在这里扩展
                for (PsyTAnswerRecord answer : answers) {
                    if (answer.getAnswerScore() != null) {
                        totalScore = totalScore.add(answer.getAnswerScore());
                    }
                }
                result.put("description", "复合题计分");
            }
            
            result.put("totalScore", totalScore);
            result.put("scoringMethod", scoringMethod);
            
        } catch (Exception e) {
            logger.error("特殊计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取量表计分配置
     */
    @Override
    public Map<String, Object> getScaleConfig(Long scaleId) {
        Map<String, Object> config = new HashMap<>();
        
        try {
            // 从量表表获取基础配置
            PsyTScale scale = scaleMapper.selectScaleById(scaleId);
            if (scale != null) {
                config.put("scale_id", scaleId);
                config.put("scale_code", scale.getCode());
                config.put("scale_name", scale.getName());

                // 安全地获取新增字段，如果字段不存在则使用默认值
                try {
                    config.put("scoring_method", scale.getScoringMethod());
                } catch (Exception e) {
                    config.put("scoring_method", "SIMPLE_SUM"); // 默认值
                }

                try {
                    config.put("has_reverse_items", scale.getHasReverseItems());
                } catch (Exception e) {
                    config.put("has_reverse_items", 0); // 默认值
                }

                try {
                    config.put("has_standard_score", scale.getHasStandardScore());
                } catch (Exception e) {
                    config.put("has_standard_score", 0); // 默认值
                }

                try {
                    config.put("standard_score_multiplier", scale.getStandardScoreMultiplier());
                } catch (Exception e) {
                    config.put("standard_score_multiplier", new BigDecimal("1.0")); // 默认值
                }

                try {
                    config.put("dimension_count", scale.getDimensionCount());
                } catch (Exception e) {
                    config.put("dimension_count", 1); // 默认值
                }

                try {
                    config.put("raw_score_range", scale.getRawScoreRange());
                } catch (Exception e) {
                    config.put("raw_score_range", ""); // 默认值
                }

                try {
                    config.put("standard_score_range", scale.getStandardScoreRange());
                } catch (Exception e) {
                    config.put("standard_score_range", ""); // 默认值
                }

                // 获取反向计分题目
                Integer hasReverseItems = (Integer) config.get("has_reverse_items");
                if (hasReverseItems != null && hasReverseItems == 1) {
                    List<Integer> reverseQuestions = getReverseQuestionsList(scaleId);
                    config.put("reverse_questions", reverseQuestions);

                    // 获取反向计分最大值
                    Integer reverseMaxValue = getReverseMaxValue(scaleId);
                    config.put("reverse_max_value", reverseMaxValue);
                }

                // 获取基础分数
                Integer baseScore = getBaseScore(scaleId);
                config.put("base_score", baseScore);
            }
            
            // 从计分配置表获取详细配置
            PsyTScoringConfig scoringConfig = scoringConfigMapper.selectByScaleId(scaleId);
            if (scoringConfig != null) {
                config.put("scoring_type", scoringConfig.getScoringType());
                
                // 解析JSON配置
                if (scoringConfig.getFormulaConfig() != null) {
                    try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> formulaConfig = objectMapper.readValue(scoringConfig.getFormulaConfig(), Map.class);
                        config.put("formula_config", formulaConfig);
                    } catch (Exception e) {
                        logger.warn("解析公式配置失败: {}", e.getMessage());
                    }
                }

                if (scoringConfig.getReverseItems() != null) {
                    try {
                        @SuppressWarnings("unchecked")
                        List<Integer> reverseItems = objectMapper.readValue(scoringConfig.getReverseItems(), List.class);
                        config.put("reverse_items", reverseItems);
                    } catch (Exception e) {
                        logger.warn("解析反向计分配置失败: {}", e.getMessage());
                    }
                }
                
                if (scoringConfig.getStandardMultiplier() != null) {
                    config.put("standard_multiplier", scoringConfig.getStandardMultiplier());
                }
            }
            
        } catch (Exception e) {
            logger.error("获取量表配置异常，scaleId: {}", scaleId, e);
        }
        
        return config;
    }

    /**
     * 辅助方法：获取反向计分题目集合
     */
    private Set<Integer> getReverseQuestions(Map<String, Object> scaleConfig) {
        Set<Integer> reverseQuestions = new HashSet<>();
        
        // 从配置中获取反向计分题目
        Object reverseItems = scaleConfig.get("reverse_questions");
        if (reverseItems instanceof List) {
            @SuppressWarnings("unchecked")
            List<Integer> items = (List<Integer>) reverseItems;
            reverseQuestions.addAll(items);
        }
        
        return reverseQuestions;
    }

    /**
     * 辅助方法：获取题目编号
     */
    private Integer getQuestionNumber(Long questionId) {
        // 查询题目信息获取题目编号
        PsyTQuestion question = questionMapper.selectQuestionById(questionId);
        return question != null ? question.getQuestionNo() : 0;
    }

    /**
     * 辅助方法：检查题目是否属于指定分量表
     */
    private boolean isQuestionInSubscale(Long questionId, Long subscaleId) {
        // 查询题目与分量表的关联关系
        // 这里需要根据实际的数据库结构实现
        return true; // 临时返回true
    }

    /**
     * 辅助方法：获取反向计分题目列表
     */
    private List<Integer> getReverseQuestionsList(Long scaleId) {
        // 从数据库查询反向计分题目
        return new ArrayList<>(); // 临时返回空列表
    }

    /**
     * 辅助方法：获取反向计分最大值
     */
    private Integer getReverseMaxValue(Long scaleId) {
        // 从数据库查询反向计分最大值
        return 5; // 临时返回5
    }

    /**
     * 辅助方法：获取基础分数
     */
    private Integer getBaseScore(Long scaleId) {
        // 从数据库查询基础分数
        return 0; // 临时返回0
    }

    /**
     * 保存量表计分配置
     */
    @Override
    @Transactional
    public boolean saveScaleConfig(Long scaleId, Map<String, Object> config) {
        try {
            // 更新量表基础配置
            PsyTScale scale = new PsyTScale();
            scale.setId(scaleId);

            // 安全地设置字段，如果setter方法不存在则跳过
            try {
                if (config.containsKey("scoring_method")) {
                    scale.setScoringMethod((String) config.get("scoring_method"));
                }
            } catch (Exception e) {
                logger.debug("setScoringMethod方法不存在，跳过设置");
            }

            try {
                if (config.containsKey("has_reverse_items")) {
                    scale.setHasReverseItems((Integer) config.get("has_reverse_items"));
                }
            } catch (Exception e) {
                logger.debug("setHasReverseItems方法不存在，跳过设置");
            }

            try {
                if (config.containsKey("has_standard_score")) {
                    scale.setHasStandardScore((Integer) config.get("has_standard_score"));
                }
            } catch (Exception e) {
                logger.debug("setHasStandardScore方法不存在，跳过设置");
            }

            try {
                if (config.containsKey("standard_score_multiplier")) {
                    scale.setStandardScoreMultiplier((BigDecimal) config.get("standard_score_multiplier"));
                }
            } catch (Exception e) {
                logger.debug("setStandardScoreMultiplier方法不存在，跳过设置");
            }

            try {
                if (config.containsKey("dimension_count")) {
                    scale.setDimensionCount((Integer) config.get("dimension_count"));
                }
            } catch (Exception e) {
                logger.debug("setDimensionCount方法不存在，跳过设置");
            }

            try {
                if (config.containsKey("raw_score_range")) {
                    scale.setRawScoreRange((String) config.get("raw_score_range"));
                }
            } catch (Exception e) {
                logger.debug("setRawScoreRange方法不存在，跳过设置");
            }

            try {
                if (config.containsKey("standard_score_range")) {
                    scale.setStandardScoreRange((String) config.get("standard_score_range"));
                }
            } catch (Exception e) {
                logger.debug("setStandardScoreRange方法不存在，跳过设置");
            }

            try {
                scaleMapper.updateScale(scale);
            } catch (Exception e) {
                logger.warn("更新量表配置失败，可能是字段不存在: {}", e.getMessage());
            }

            // 保存或更新详细计分配置
            PsyTScoringConfig scoringConfig = scoringConfigMapper.selectByScaleId(scaleId);
            if (scoringConfig == null) {
                scoringConfig = new PsyTScoringConfig();
                scoringConfig.setScaleId(scaleId);
            }

            if (config.containsKey("scoring_type")) {
                scoringConfig.setScoringType((String) config.get("scoring_type"));
            }
            if (config.containsKey("formula_config")) {
                String formulaConfigJson = objectMapper.writeValueAsString(config.get("formula_config"));
                scoringConfig.setFormulaConfig(formulaConfigJson);
            }
            if (config.containsKey("reverse_items")) {
                String reverseItemsJson = objectMapper.writeValueAsString(config.get("reverse_items"));
                scoringConfig.setReverseItems(reverseItemsJson);
            }
            if (config.containsKey("standard_multiplier")) {
                scoringConfig.setStandardMultiplier((BigDecimal) config.get("standard_multiplier"));
            }

            if (scoringConfig.getId() == null) {
                scoringConfigMapper.insertScoringConfig(scoringConfig);
            } else {
                scoringConfigMapper.updateScoringConfig(scoringConfig);
            }

            return true;

        } catch (Exception e) {
            logger.error("保存量表配置异常，scaleId: {}", scaleId, e);
            return false;
        }
    }

    /**
     * 验证计分配置
     */
    @Override
    public Map<String, Object> validateConfig(Map<String, Object> config) {
        Map<String, Object> validation = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 验证计分方法
        String scoringMethod = (String) config.get("scoring_method");
        if (scoringMethod == null || scoringMethod.trim().isEmpty()) {
            errors.add("计分方法不能为空");
        } else {
            Set<String> validMethods = getSupportedScoringMethods().keySet();
            if (!validMethods.contains(scoringMethod)) {
                errors.add("不支持的计分方法: " + scoringMethod);
            }
        }

        // 验证标准分配置
        Boolean hasStandardScore = (Boolean) config.get("has_standard_score");
        if (hasStandardScore != null && hasStandardScore) {
            BigDecimal multiplier = (BigDecimal) config.get("standard_score_multiplier");
            if (multiplier == null || multiplier.compareTo(BigDecimal.ZERO) <= 0) {
                errors.add("标准分转换系数必须大于0");
            }
        }

        // 验证反向计分配置
        Boolean hasReverseItems = (Boolean) config.get("has_reverse_items");
        if (hasReverseItems != null && hasReverseItems) {
            Object reverseItems = config.get("reverse_items");
            if (reverseItems == null) {
                warnings.add("启用了反向计分但未配置反向计分题目");
            }
        }

        validation.put("valid", errors.isEmpty());
        validation.put("errors", errors);
        validation.put("warnings", warnings);

        return validation;
    }

    /**
     * 获取支持的计分方法列表
     */
    @Override
    public Map<String, String> getSupportedScoringMethods() {
        Map<String, String> methods = new HashMap<>();
        methods.put("SIMPLE_SUM", "简单求和");
        methods.put("REVERSE_SCORING", "反向计分");
        methods.put("FORMULA_WITH_BASE", "公式计分（基础分+题目分）");
        methods.put("STANDARD_SCORE", "标准分转换");
        methods.put("SPECIAL_BINARY", "特殊二选一计分");
        methods.put("COMPOSITE_SCORING", "复合题计分");
        return methods;
    }

    /**
     * 根据配置生成计分规则说明
     */
    @Override
    public String generateScoringRuleDescription(Long scaleId) {
        Map<String, Object> config = getScaleConfig(scaleId);
        if (config.isEmpty()) {
            return "未配置计分规则";
        }

        StringBuilder description = new StringBuilder();
        String scoringMethod = (String) config.get("scoring_method");

        switch (scoringMethod) {
            case "SIMPLE_SUM":
                description.append("简单求和计分：所有题目分数相加");
                break;
            case "REVERSE_SCORING":
                description.append("反向计分：部分题目进行反向计分后求和");
                @SuppressWarnings("unchecked")
                List<Integer> reverseQuestions = (List<Integer>) config.get("reverse_questions");
                if (reverseQuestions != null && !reverseQuestions.isEmpty()) {
                    description.append("，反向计分题目：").append(reverseQuestions);
                }
                break;
            case "FORMULA_WITH_BASE":
                description.append("公式计分：基础分数 + 题目分数总和");
                Integer baseScore = (Integer) config.get("base_score");
                if (baseScore != null) {
                    description.append("，基础分：").append(baseScore);
                }
                break;
            case "STANDARD_SCORE":
                description.append("标准分转换：原始分数 × 转换系数");
                BigDecimal multiplier = (BigDecimal) config.get("standard_score_multiplier");
                if (multiplier != null) {
                    description.append("，转换系数：").append(multiplier);
                }
                break;
            case "SPECIAL_BINARY":
                description.append("特殊二选一计分：选择'是'得1分，选择'否'得0分");
                break;
            case "COMPOSITE_SCORING":
                description.append("复合题计分：根据复合题规则进行计分");
                break;
            default:
                description.append("未知计分方法");
                break;
        }

        return description.toString();
    }

    /**
     * 测试计分配置
     */
    @Override
    public Map<String, Object> testScoringConfig(Long scaleId, Map<String, Object> testData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证配置
            Map<String, Object> config = getScaleConfig(scaleId);
            Map<String, Object> validation = validateConfig(config);

            if (!(Boolean) validation.get("valid")) {
                result.put("success", false);
                result.put("message", "配置验证失败");
                result.put("validation", validation);
                return result;
            }

            // 模拟计分测试
            // 这里可以根据testData进行模拟计分
            result.put("success", true);
            result.put("message", "配置测试通过");
            result.put("config", config);
            result.put("validation", validation);

        } catch (Exception e) {
            logger.error("测试计分配置异常，scaleId: {}", scaleId, e);
            result.put("success", false);
            result.put("message", "测试异常: " + e.getMessage());
        }

        return result;
    }
}
