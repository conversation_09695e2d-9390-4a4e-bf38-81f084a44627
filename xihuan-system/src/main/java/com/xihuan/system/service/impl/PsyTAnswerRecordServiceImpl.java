package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTAnswerRecord;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.system.mapper.PsyTAnswerRecordMapper;
import com.xihuan.system.service.IPsyTAnswerRecordService;
import com.xihuan.system.service.IPsyTScoringRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 答题记录Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTAnswerRecordServiceImpl implements IPsyTAnswerRecordService {
    
    @Autowired
    private PsyTAnswerRecordMapper answerRecordMapper;
    
    @Autowired
    private IPsyTScoringRuleService scoringRuleService;

    /**
     * 查询答题记录
     * 
     * @param id 答题记录主键
     * @return 答题记录
     */
    @Override
    public PsyTAnswerRecord selectAnswerRecordById(Long id) {
        return answerRecordMapper.selectAnswerRecordById(id);
    }

    /**
     * 查询答题记录列表
     * 
     * @param answerRecord 答题记录
     * @return 答题记录
     */
    @Override
    public List<PsyTAnswerRecord> selectAnswerRecordList(PsyTAnswerRecord answerRecord) {
        return answerRecordMapper.selectAnswerRecordList(answerRecord);
    }

    /**
     * 新增答题记录
     * 
     * @param answerRecord 答题记录
     * @return 结果
     */
    @Override
    public int insertAnswerRecord(PsyTAnswerRecord answerRecord) {
        answerRecord.setCreateBy(SecurityUtils.getUsername());
        answerRecord.setCreateTime(DateUtils.getNowDate());
        answerRecord.setDelFlag(0);
        answerRecord.setAnswerTime(DateUtils.getNowDate());
        return answerRecordMapper.insertAnswerRecord(answerRecord);
    }

    /**
     * 修改答题记录
     * 
     * @param answerRecord 答题记录
     * @return 结果
     */
    @Override
    public int updateAnswerRecord(PsyTAnswerRecord answerRecord) {
        answerRecord.setUpdateBy(SecurityUtils.getUsername());
        answerRecord.setUpdateTime(DateUtils.getNowDate());
        return answerRecordMapper.updateAnswerRecord(answerRecord);
    }

    /**
     * 批量删除答题记录
     * 
     * @param ids 需要删除的答题记录主键
     * @return 结果
     */
    @Override
    public int deleteAnswerRecordByIds(Long[] ids) {
        return answerRecordMapper.deleteAnswerRecordByIds(ids);
    }

    /**
     * 删除答题记录信息
     * 
     * @param id 答题记录主键
     * @return 结果
     */
    @Override
    public int deleteAnswerRecordById(Long id) {
        return answerRecordMapper.deleteAnswerRecordById(id);
    }

    /**
     * 根据测评记录ID查询答题记录
     * 
     * @param recordId 测评记录ID
     * @return 答题记录集合
     */
    @Override
    public List<PsyTAnswerRecord> selectAnswersByRecordId(Long recordId) {
        return answerRecordMapper.selectAnswersByRecordId(recordId);
    }

    /**
     * 根据测评记录ID和题目ID查询答题记录
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @return 答题记录
     */
    @Override
    public PsyTAnswerRecord selectAnswerByRecordAndQuestion(Long recordId, Long questionId) {
        return answerRecordMapper.selectAnswerByRecordAndQuestion(recordId, questionId);
    }

    /**
     * 保存答题记录
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @param optionId 选项ID（可为空）
     * @param answerContent 答案内容
     * @param answerScore 答案分数
     * @param responseTime 答题时间（秒）
     * @return 结果
     */
    @Override
    @Transactional
    public int saveAnswer(Long recordId, Long questionId, Long optionId, String answerContent, BigDecimal answerScore, Integer responseTime) {
        // 检查是否已存在答题记录
        PsyTAnswerRecord existingAnswer = selectAnswerByRecordAndQuestion(recordId, questionId);
        
        if (existingAnswer != null) {
            // 更新现有记录
            existingAnswer.setOptionId(optionId);
            existingAnswer.setAnswerContent(answerContent);
            existingAnswer.setAnswerScore(answerScore);
            existingAnswer.setResponseTime(responseTime);
            existingAnswer.setAnswerTime(DateUtils.getNowDate());
            return updateAnswerRecord(existingAnswer);
        } else {
            // 创建新记录
            PsyTAnswerRecord newAnswer = new PsyTAnswerRecord();
            newAnswer.setRecordId(recordId);
            newAnswer.setQuestionId(questionId);
            newAnswer.setOptionId(optionId);
            newAnswer.setAnswerContent(answerContent);
            newAnswer.setAnswerScore(answerScore);
            newAnswer.setResponseTime(responseTime);
            return insertAnswerRecord(newAnswer);
        }
    }

    /**
     * 批量保存答题记录
     * 
     * @param answers 答题记录列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveAnswers(List<PsyTAnswerRecord> answers) {
        if (answers == null || answers.isEmpty()) {
            return 0;
        }
        
        String username = SecurityUtils.getUsername();
        Date now = DateUtils.getNowDate();
        
        for (PsyTAnswerRecord answer : answers) {
            answer.setCreateBy(username);
            answer.setCreateTime(now);
            answer.setAnswerTime(now);
            answer.setDelFlag(0);
        }
        
        return answerRecordMapper.batchInsertAnswerRecords(answers);
    }

    /**
     * 根据测评记录ID删除所有答题记录
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    @Override
    public int deleteAnswersByRecordId(Long recordId) {
        return answerRecordMapper.deleteAnswersByRecordId(recordId);
    }

    /**
     * 计算测评记录的总分
     * 
     * @param recordId 测评记录ID
     * @return 总分
     */
    @Override
    public BigDecimal calculateTotalScore(Long recordId) {
        return answerRecordMapper.calculateTotalScore(recordId);
    }

    /**
     * 计算测评记录的维度分数
     *
     * @param recordId 测评记录ID
     * @return 维度分数映射
     */
    @Override
    public Map<String, BigDecimal> calculateDimensionScores(Long recordId) {
        Map<String, BigDecimal> dimensionScores = new HashMap<>();

        // 获取答题统计信息，其中包含维度分数
        List<Map<String, Object>> stats = answerRecordMapper.selectAnswerStats(recordId);

        if (stats != null) {
            for (Map<String, Object> stat : stats) {
                String dimension = (String) stat.get("dimension");
                Object totalScoreObj = stat.get("total_score");

                if (dimension != null && totalScoreObj != null) {
                    BigDecimal totalScore = null;
                    if (totalScoreObj instanceof BigDecimal) {
                        totalScore = (BigDecimal) totalScoreObj;
                    } else if (totalScoreObj instanceof Number) {
                        totalScore = new BigDecimal(totalScoreObj.toString());
                    }

                    if (totalScore != null) {
                        dimensionScores.put(dimension, totalScore);
                    }
                }
            }
        }

        return dimensionScores;
    }

    /**
     * 查询答题统计信息
     *
     * @param recordId 测评记录ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectAnswerStats(Long recordId) {
        Object result = answerRecordMapper.selectAnswerStats(recordId);
        if (result instanceof List) {
            List<Map<String, Object>> statsList = (List<Map<String, Object>>) result;
            if (!statsList.isEmpty()) {
                return statsList.get(0);
            }
        } else if (result instanceof Map) {
            return (Map<String, Object>) result;
        }
        return new HashMap<>();
    }

    /**
     * 查询用户答题统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserAnswerStats(Long userId) {
        // 实现用户答题统计
        Map<String, Object> stats = new HashMap<>();

        // 查询用户的所有答题记录
        PsyTAnswerRecord query = new PsyTAnswerRecord();
        query.setCreateBy(userId.toString()); // 假设createBy存储的是用户ID
        List<PsyTAnswerRecord> userAnswers = selectAnswerRecordList(query);

        if (userAnswers != null && !userAnswers.isEmpty()) {
            // 统计总答题数
            stats.put("totalAnswers", userAnswers.size());

            // 统计平均分
            BigDecimal totalScore = BigDecimal.ZERO;
            for (PsyTAnswerRecord answer : userAnswers) {
                if (answer.getAnswerScore() != null) {
                    totalScore = totalScore.add(answer.getAnswerScore());
                }
            }

            if (userAnswers.size() > 0) {
                BigDecimal avgScore = totalScore.divide(new BigDecimal(userAnswers.size()), 2, BigDecimal.ROUND_HALF_UP);
                stats.put("averageScore", avgScore);
            }

            // 统计答题时间
            long totalTime = 0;
            int timeCount = 0;
            for (PsyTAnswerRecord answer : userAnswers) {
                if (answer.getResponseTime() != null) {
                    totalTime += answer.getResponseTime();
                    timeCount++;
                }
            }

            if (timeCount > 0) {
                stats.put("averageResponseTime", totalTime / timeCount);
            }
        }

        return stats;
    }

    /**
     * 查询题目答题统计
     *
     * @param questionId 题目ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectQuestionAnswerStats(Long questionId) {
        List<Map<String, Object>> statsList = answerRecordMapper.selectQuestionAnswerStats(questionId);

        // 将List转换为Map，合并统计信息
        Map<String, Object> result = new HashMap<>();
        if (statsList != null && !statsList.isEmpty()) {
            // 如果只有一个结果，直接返回
            if (statsList.size() == 1) {
                return statsList.get(0);
            }

            // 如果有多个结果，合并统计信息
            int totalCount = 0;
            for (Map<String, Object> stats : statsList) {
                Object countObj = stats.get("answer_count");
                if (countObj instanceof Number) {
                    totalCount += ((Number) countObj).intValue();
                }
            }

            result.put("questionId", questionId);
            result.put("totalAnswerCount", totalCount);
            result.put("optionStats", statsList);
        }

        return result;
    }

    /**
     * 查询选项选择统计
     *
     * @param questionId 题目ID
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectOptionChoiceStats(Long questionId) {
        return answerRecordMapper.selectQuestionAnswerStats(questionId);
    }

    /**
     * 查询答题进度
     * 
     * @param recordId 测评记录ID
     * @return 进度信息
     */
    @Override
    public Map<String, Object> selectAnswerProgress(Long recordId) {
        return answerRecordMapper.selectAnswerProgress(recordId);
    }

    @Override
    public List<Map<String, Object>> selectAnswerHistoryByRecordId(Long recordId) {
        return answerRecordMapper.selectAnswerHistoryByRecordId(recordId);
    }

    /**
     * 检查是否已完成答题
     *
     * @param recordId 测评记录ID
     * @return 是否完成
     */
    @Override
    public boolean isAnswerCompleted(Long recordId) {
        Map<String, Object> progress = selectAnswerProgress(recordId);
        if (progress != null) {
            // 使用安全的类型转换方法
            Long totalQuestions = getLongValue(progress, "total_questions");
            Long answeredQuestions = getLongValue(progress, "answered_questions");
            return totalQuestions != null && answeredQuestions != null && totalQuestions.equals(answeredQuestions);
        }
        return false;
    }

    /**
     * 获取下一题
     *
     * @param recordId 测评记录ID
     * @return 下一题信息
     */
    @Override
    public Map<String, Object> getNextQuestion(Long recordId) {
        // 实现获取下一题的逻辑
        Map<String, Object> result = new HashMap<>();

        // 获取当前答题进度
        Map<String, Object> progress = selectAnswerProgress(recordId);
        if (progress != null) {
            // 使用安全的类型转换方法
            Long answeredQuestions = getLongValue(progress, "answered_questions");
            Long totalQuestions = getLongValue(progress, "total_questions");

            if (answeredQuestions != null && totalQuestions != null && answeredQuestions < totalQuestions) {
                result.put("hasNext", true);
                result.put("nextQuestionNo", answeredQuestions + 1);
            } else {
                result.put("hasNext", false);
            }
        }

        return result;
    }

    /**
     * 获取上一题
     *
     * @param recordId 测评记录ID
     * @return 上一题信息
     */
    @Override
    public Map<String, Object> getPreviousQuestion(Long recordId) {
        // 实现获取上一题的逻辑
        Map<String, Object> result = new HashMap<>();

        // 获取当前答题进度
        Map<String, Object> progress = selectAnswerProgress(recordId);
        if (progress != null) {
            // 使用安全的类型转换方法
            Long answeredQuestions = getLongValue(progress, "answered_questions");

            if (answeredQuestions != null && answeredQuestions > 1) {
                result.put("hasPrevious", true);
                result.put("previousQuestionNo", answeredQuestions - 1);
            } else {
                result.put("hasPrevious", false);
            }
        }

        return result;
    }

    /**
     * 验证答案的有效性
     * 
     * @param questionId 题目ID
     * @param optionId 选项ID
     * @param answerContent 答案内容
     * @return 验证结果
     */
    @Override
    public Map<String, Object> validateAnswer(Long questionId, Long optionId, String answerContent) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        // 基本验证
        if (questionId == null) {
            errors.add("题目ID不能为空");
        }
        
        if (optionId == null && (answerContent == null || answerContent.trim().isEmpty())) {
            errors.add("必须选择选项或填写答案内容");
        }
        
        // 可以添加更多业务验证逻辑
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        
        return result;
    }

    /**
     * 自动计算答案分数
     * 
     * @param questionId 题目ID
     * @param optionId 选项ID
     * @param answerContent 答案内容
     * @return 分数
     */
    @Override
    public BigDecimal calculateAnswerScore(Long questionId, Long optionId, String answerContent) {
        // 使用计分规则服务计算分数
        return scoringRuleService.calculateQuestionScore(questionId, optionId, answerContent);
    }

    /**
     * 导出答题记录
     * 
     * @param recordId 测评记录ID
     * @return 答题记录列表
     */
    @Override
    public List<PsyTAnswerRecord> exportAnswers(Long recordId) {
        return selectAnswersByRecordId(recordId);
    }

    /**
     * 查询答题时间分析
     *
     * @param recordId 测评记录ID
     * @return 时间分析
     */
    @Override
    public Map<String, Object> selectAnswerTimeAnalysis(Long recordId) {
        return answerRecordMapper.selectAnswerTimeStats(recordId);
    }

    /**
     * 查询答题模式分析
     *
     * @param recordId 测评记录ID
     * @return 模式分析
     */
    @Override
    public Map<String, Object> selectAnswerPatternAnalysis(Long recordId) {
        // 需要获取用户ID和量表ID来调用Mapper方法
        // 这里需要先查询测评记录获取相关信息
        Map<String, Object> result = new HashMap<>();

        // 获取答题记录列表
        List<PsyTAnswerRecord> answers = selectAnswersByRecordId(recordId);
        if (answers != null && !answers.isEmpty()) {
            // 分析答题模式
            result.put("totalAnswers", answers.size());

            // 计算平均答题时间
            long totalTime = 0;
            int timeCount = 0;
            for (PsyTAnswerRecord answer : answers) {
                if (answer.getResponseTime() != null) {
                    totalTime += answer.getResponseTime();
                    timeCount++;
                }
            }

            if (timeCount > 0) {
                result.put("averageResponseTime", totalTime / timeCount);
            }

            // 可以添加更多模式分析逻辑
            result.put("analysisDate", new Date());
        }

        return result;
    }

    /**
     * 重新计算所有分数
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int recalculateScores(Long recordId) {
        List<PsyTAnswerRecord> answers = selectAnswersByRecordId(recordId);
        
        int count = 0;
        for (PsyTAnswerRecord answer : answers) {
            BigDecimal newScore = calculateAnswerScore(answer.getQuestionId(), answer.getOptionId(), answer.getAnswerContent());
            if (newScore != null && !newScore.equals(answer.getAnswerScore())) {
                answer.setAnswerScore(newScore);
                updateAnswerRecord(answer);
                count++;
            }
        }
        
        return count;
    }

    /**
     * 查询异常答题记录
     *
     * @param recordId 测评记录ID
     * @return 异常记录列表
     */
    @Override
    public List<PsyTAnswerRecord> selectAbnormalAnswers(Long recordId) {
        // 实现异常答题记录查询逻辑
        List<PsyTAnswerRecord> abnormalAnswers = new ArrayList<>();

        // 获取所有答题记录
        List<PsyTAnswerRecord> allAnswers = selectAnswersByRecordId(recordId);
        if (allAnswers != null) {
            for (PsyTAnswerRecord answer : allAnswers) {
                // 判断是否为异常答题
                boolean isAbnormal = false;

                // 例如：响应时间过短（小于3秒）
                if (answer.getResponseTime() != null && answer.getResponseTime() < 3) {
                    isAbnormal = true;
                }

                // 例如：未选择选项且未填写内容
                if (answer.getOptionId() == null &&
                    (answer.getAnswerContent() == null || answer.getAnswerContent().trim().isEmpty())) {
                    isAbnormal = true;
                }

                // 如果是异常答题，添加到结果列表
                if (isAbnormal) {
                    abnormalAnswers.add(answer);
                }
            }
        }

        return abnormalAnswers;
    }

    /**
     * 安全地从Map中获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
//            log.warn("无法将值 {} 转换为Long类型", value);

            return null;
        }
    }

    /**
     * 安全地从Map中获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Long longValue = getLongValue(map, key);
        return longValue != null ? longValue.intValue() : null;
    }
}
