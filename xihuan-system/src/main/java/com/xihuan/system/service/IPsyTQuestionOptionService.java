package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTQuestionOption;

import java.util.List;
import java.util.Map;

/**
 * 题目选项Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTQuestionOptionService {
    
    /**
     * 查询题目选项
     * 
     * @param id 题目选项主键
     * @return 题目选项
     */
    public PsyTQuestionOption selectQuestionOptionById(Long id);

    /**
     * 查询题目选项列表
     * 
     * @param questionOption 题目选项
     * @return 题目选项集合
     */
    public List<PsyTQuestionOption> selectQuestionOptionList(PsyTQuestionOption questionOption);

    /**
     * 新增题目选项
     * 
     * @param questionOption 题目选项
     * @return 结果
     */
    public int insertQuestionOption(PsyTQuestionOption questionOption);

    /**
     * 修改题目选项
     * 
     * @param questionOption 题目选项
     * @return 结果
     */
    public int updateQuestionOption(PsyTQuestionOption questionOption);

    /**
     * 批量删除题目选项
     * 
     * @param ids 需要删除的题目选项主键集合
     * @return 结果
     */
    public int deleteQuestionOptionByIds(Long[] ids);

    /**
     * 删除题目选项信息
     * 
     * @param id 题目选项主键
     * @return 结果
     */
    public int deleteQuestionOptionById(Long id);

    /**
     * 根据题目ID查询选项列表
     * 
     * @param questionId 题目ID
     * @return 选项列表
     */
    public List<PsyTQuestionOption> selectOptionsByQuestionId(Long questionId);

    /**
     * 根据题目ID查询启用的选项列表
     * 
     * @param questionId 题目ID
     * @return 启用的选项列表
     */
    public List<PsyTQuestionOption> selectEnabledOptionsByQuestionId(Long questionId);

    /**
     * 根据题目ID删除所有选项
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    public int deleteOptionsByQuestionId(Long questionId);

    /**
     * 批量插入选项
     * 
     * @param options 选项列表
     * @return 结果
     */
    public int batchInsertOptions(List<PsyTQuestionOption> options);

    /**
     * 根据题目ID和选项值查询选项
     * 
     * @param questionId 题目ID
     * @param optionValue 选项值
     * @return 选项信息
     */
    public PsyTQuestionOption selectOptionByValue(Long questionId, String optionValue);

    /**
     * 根据题目ID和选项文本查询选项
     * 
     * @param questionId 题目ID
     * @param optionText 选项文本
     * @return 选项信息
     */
    public PsyTQuestionOption selectOptionByText(Long questionId, String optionText);

    /**
     * 查询题目的选项数量
     * 
     * @param questionId 题目ID
     * @return 选项数量
     */
    public int countOptionsByQuestionId(Long questionId);

    /**
     * 查询题目的启用选项数量
     * 
     * @param questionId 题目ID
     * @return 启用选项数量
     */
    public int countEnabledOptionsByQuestionId(Long questionId);

    /**
     * 更新选项的显示顺序
     * 
     * @param id 选项ID
     * @param orderNum 显示顺序
     * @return 结果
     */
    public int updateOptionOrder(Long id, Integer orderNum);

    /**
     * 查询选项的最大显示顺序
     * 
     * @param questionId 题目ID
     * @return 最大显示顺序
     */
    public Integer selectMaxOrderNum(Long questionId);

    /**
     * 根据量表ID查询所有选项
     * 
     * @param scaleId 量表ID
     * @return 选项列表
     */
    public List<PsyTQuestionOption> selectOptionsByScaleId(Long scaleId);

    /**
     * 查询选项统计信息
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    public Map<String, Object> selectOptionStats(Long questionId);

    /**
     * 根据选项标识查询选项
     * 
     * @param questionId 题目ID
     * @param optionLabel 选项标识(A、B、C、D等)
     * @return 选项信息
     */
    public PsyTQuestionOption selectOptionByLabel(Long questionId, String optionLabel);

    /**
     * 查询正确答案选项
     * 
     * @param questionId 题目ID
     * @return 正确答案选项列表
     */
    public List<PsyTQuestionOption> selectCorrectOptions(Long questionId);

    /**
     * 更新选项状态
     * 
     * @param id 选项ID
     * @param status 状态
     * @return 结果
     */
    public int updateOptionStatus(Long id, Integer status);

    /**
     * 批量更新选项状态
     * 
     * @param ids 选项ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateOptionStatus(Long[] ids, Integer status);

    /**
     * 复制题目选项
     * 
     * @param sourceQuestionId 源题目ID
     * @param targetQuestionId 目标题目ID
     * @return 结果
     */
    public int copyOptionsToQuestion(Long sourceQuestionId, Long targetQuestionId);

    /**
     * 验证选项配置的完整性
     * 
     * @param questionId 题目ID
     * @return 验证结果
     */
    public Map<String, Object> validateOptionConfig(Long questionId);

    /**
     * 导入选项配置
     * 
     * @param questionId 题目ID
     * @param options 选项列表
     * @return 导入结果
     */
    public Map<String, Object> importOptions(Long questionId, List<PsyTQuestionOption> options);

    /**
     * 导出选项配置
     * 
     * @param questionId 题目ID
     * @return 选项列表
     */
    public List<PsyTQuestionOption> exportOptions(Long questionId);

    /**
     * 自动生成选项标识
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    public int generateOptionLabels(Long questionId);

    /**
     * 重新排序选项
     *
     * @param questionId 题目ID
     * @return 结果
     */
    public int reorderOptions(Long questionId);
}
