package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.dto.SearchResultDTO;
import com.xihuan.common.core.domain.entity.*;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.*;
import com.xihuan.system.service.IPsySearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 搜索服务实现
 * 
 * <AUTHOR>
 */
@Service
public class PsySearchServiceImpl implements IPsySearchService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Autowired
    private PsySearchRecordMapper searchRecordMapper;
    
    @Autowired
    private PsyHotSearchMapper hotSearchMapper;
    
    @Autowired
    private PsySearchSuggestionMapper searchSuggestionMapper;
    
    // 各模块的Mapper
    @Autowired(required = false)
    private PsyConsultantMapper consultantMapper;

    @Autowired(required = false)
    private PsyCourseMapper courseMapper;

    @Autowired(required = false)
    private PsyMeditationMapper meditationMapper;

    @Autowired
    private PsyTScaleMapper scaleMapper;



    @Override
    @Transactional
    public SearchResultDTO globalSearch(String keyword, String searchType, Integer pageNum, Integer pageSize, 
                                       Long userId, String ipAddress) {
        long startTime = System.currentTimeMillis();
        
        SearchResultDTO result = new SearchResultDTO();
        result.setKeyword(keyword);
        result.setSearchType(searchType);
        
        List<SearchResultDTO.CategoryResult> categories = new ArrayList<>();
        int totalCount = 0;
        
        try {
            // 根据搜索类型进行搜索
            if ("all".equals(searchType) || "consultant".equals(searchType)) {
                SearchResultDTO.CategoryResult consultantResult = searchConsultants(keyword, pageNum, pageSize);
                if (consultantResult.getCount() > 0) {
                    categories.add(consultantResult);
                    totalCount += consultantResult.getCount();
                }
            }

            if ("all".equals(searchType) || "course".equals(searchType)) {
                SearchResultDTO.CategoryResult courseResult = searchCourses(keyword, pageNum, pageSize);
                if (courseResult.getCount() > 0) {
                    categories.add(courseResult);
                    totalCount += courseResult.getCount();
                }
            }

            if ("all".equals(searchType) || "meditation".equals(searchType)) {
                SearchResultDTO.CategoryResult meditationResult = searchMeditations(keyword, pageNum, pageSize);
                if (meditationResult.getCount() > 0) {
                    categories.add(meditationResult);
                    totalCount += meditationResult.getCount();
                }
            }

            if ("all".equals(searchType) || "assessment".equals(searchType)) {
                SearchResultDTO.CategoryResult assessmentResult = searchAssessments(keyword, pageNum, pageSize);
                if (assessmentResult.getCount() > 0) {
                    categories.add(assessmentResult);
                    totalCount += assessmentResult.getCount();
                }
            }


            
            result.setCategories(categories);
            result.setTotalCount(totalCount);
            
            // 获取搜索建议
            result.setSuggestions(getSearchSuggestions(keyword, userId));
            
        } catch (Exception e) {
            e.printStackTrace();
            result.setTotalCount(0);
            result.setCategories(new ArrayList<>());
        }
        
        long endTime = System.currentTimeMillis();
        result.setSearchTime(endTime - startTime);
        
        // 记录搜索行为
        recordSearch(keyword, searchType, totalCount, userId, ipAddress, null);
        
        // 更新热门搜索
        updateHotSearch(keyword, searchType);
        
        return result;
    }
    
    /**
     * 搜索咨询师
     */
    private SearchResultDTO.CategoryResult searchConsultants(String keyword, Integer pageNum, Integer pageSize) {
        SearchResultDTO.CategoryResult result = new SearchResultDTO.CategoryResult();
        result.setType("consultant");
        result.setTypeName("咨询师");
        result.setItems(new ArrayList<>());
        result.setCount(0);

        if (consultantMapper == null) {
            return result;
        }

        try {
            // 获取所有可用咨询师
            List<PsyConsultant> consultants = consultantMapper.selectAllConsultantsWithDetails();

            // 过滤匹配关键词的咨询师
            List<PsyConsultant> filteredConsultants = consultants.stream()
                .filter(consultant -> matchesConsultantKeyword(consultant, keyword))
                .collect(Collectors.toList());

            result.setCount(filteredConsultants.size());

            // 分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, filteredConsultants.size());

            if (start < filteredConsultants.size()) {
                List<PsyConsultant> pageConsultants = filteredConsultants.subList(start, end);

                for (PsyConsultant consultant : pageConsultants) {
                    SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
                    item.setId(consultant.getId());
                    item.setType("consultant");
                    item.setTitle(consultant.getName());
                    item.setDescription(consultant.getPersonalIntro());
                    item.setCoverImage(consultant.getImageUrl());
                    item.setCreateTime(consultant.getCreateTime());
                    item.setRelevanceScore(calculateRelevanceScore(consultant.getName(), keyword));
                    item.setPrice(consultant.getPrice() != null ? consultant.getPrice().toString() : "");

                    // 高亮处理
                    item.setHighlightTitle(highlightKeyword(consultant.getName(), keyword));
                    item.setHighlightDescription(highlightKeyword(consultant.getPersonalIntro(), keyword));

                    // ==================== 直接设置完整的咨询师实体 ====================
                    item.setConsultant(consultant);

                    result.getItems().add(item);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }
    
    /**
     * 搜索课程
     */
    private SearchResultDTO.CategoryResult searchCourses(String keyword, Integer pageNum, Integer pageSize) {
        SearchResultDTO.CategoryResult result = new SearchResultDTO.CategoryResult();
        result.setType("course");
        result.setTypeName("课程");
        result.setItems(new ArrayList<>());
        result.setCount(0);

        if (courseMapper == null) {
            return result;
        }

        try {
            // 构建搜索条件
            PsyCourse queryCourse = new PsyCourse();
            queryCourse.setStatus(1); // 只搜索已发布的课程

            List<PsyCourse> courses = courseMapper.selectCourseList(queryCourse);

            // 过滤匹配关键词的课程
            List<PsyCourse> filteredCourses = courses.stream()
                .filter(course -> matchesCourseKeyword(course, keyword))
                .collect(Collectors.toList());

            result.setCount(filteredCourses.size());

            // 分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, filteredCourses.size());

            if (start < filteredCourses.size()) {
                List<PsyCourse> pageCourses = filteredCourses.subList(start, end);

                for (PsyCourse course : pageCourses) {
                    SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
                    item.setId(course.getId());
                    item.setType("course");
                    item.setTitle(course.getTitle());
                    item.setDescription(course.getSummary());
                    item.setCoverImage(course.getCoverImage());
                    item.setCreateTime(course.getCreateTime());
                    item.setRelevanceScore(calculateRelevanceScore(course.getTitle(), keyword));
                    item.setPrice(course.getPrice() != null ? course.getPrice().toString() : "");
                    item.setRating(course.getRatingAvg() != null ? course.getRatingAvg().doubleValue() : null);
                    item.setViewCount(course.getViewCount());

                    // 高亮处理
                    item.setHighlightTitle(highlightKeyword(course.getTitle(), keyword));
                    item.setHighlightDescription(highlightKeyword(course.getSummary(), keyword));

                    // ==================== 直接设置完整的课程实体 ====================
                    item.setCourse(course);

                    result.getItems().add(item);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 搜索冥想
     */
    private SearchResultDTO.CategoryResult searchMeditations(String keyword, Integer pageNum, Integer pageSize) {
        SearchResultDTO.CategoryResult result = new SearchResultDTO.CategoryResult();
        result.setType("meditation");
        result.setTypeName("冥想");
        result.setItems(new ArrayList<>());
        result.setCount(0);

        if (meditationMapper == null) {
            return result;
        }

        try {
            // 构建搜索条件
            PsyMeditation queryMeditation = new PsyMeditation();
            queryMeditation.setStatus(1); // 只搜索已发布的冥想

            List<PsyMeditation> meditations = meditationMapper.selectMeditationList(queryMeditation);

            // 过滤匹配关键词的冥想
            List<PsyMeditation> filteredMeditations = meditations.stream()
                .filter(meditation -> matchesMeditationKeyword(meditation, keyword))
                .collect(Collectors.toList());

            result.setCount(filteredMeditations.size());

            // 分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, filteredMeditations.size());

            if (start < filteredMeditations.size()) {
                List<PsyMeditation> pageMeditations = filteredMeditations.subList(start, end);

                for (PsyMeditation meditation : pageMeditations) {
                    SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
                    item.setId(meditation.getId());
                    item.setType("meditation");
                    item.setTitle(meditation.getTitle());
                    item.setDescription(meditation.getDescription());
                    item.setCoverImage(meditation.getCoverImage());
                    item.setCreateTime(meditation.getCreateTime());
                    item.setRelevanceScore(calculateRelevanceScore(meditation.getTitle(), keyword));
                    item.setPrice(meditation.getPrice() != null ? meditation.getPrice().toString() : "");
                    item.setViewCount(meditation.getPlayCount());

                    // 高亮处理
                    item.setHighlightTitle(highlightKeyword(meditation.getTitle(), keyword));
                    item.setHighlightDescription(highlightKeyword(meditation.getDescription(), keyword));

                    // ==================== 直接设置完整的冥想实体 ====================
                    item.setMeditation(meditation);

                    result.getItems().add(item);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }



    /**
     * 检查咨询师是否匹配关键词
     */
    private boolean matchesConsultantKeyword(PsyConsultant consultant, String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return true;
        }

        String lowerKeyword = keyword.toLowerCase();

        return (StringUtils.isNotEmpty(consultant.getName()) &&
                consultant.getName().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(consultant.getPersonalIntro()) &&
                consultant.getPersonalIntro().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(consultant.getPersonalTitle()) &&
                consultant.getPersonalTitle().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(consultant.getSearchKeywords()) &&
                consultant.getSearchKeywords().toLowerCase().contains(lowerKeyword));
    }

    /**
     * 检查课程是否匹配关键词
     */
    private boolean matchesCourseKeyword(PsyCourse course, String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return true;
        }

        String lowerKeyword = keyword.toLowerCase();

        return (StringUtils.isNotEmpty(course.getTitle()) &&
                course.getTitle().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(course.getSummary()) &&
                course.getSummary().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(course.getTags()) &&
                course.getTags().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(course.getSearchKeywords()) &&
                course.getSearchKeywords().toLowerCase().contains(lowerKeyword));
    }

    /**
     * 检查冥想是否匹配关键词
     */
    private boolean matchesMeditationKeyword(PsyMeditation meditation, String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return true;
        }

        String lowerKeyword = keyword.toLowerCase();

        return (StringUtils.isNotEmpty(meditation.getTitle()) &&
                meditation.getTitle().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(meditation.getDescription()) &&
                meditation.getDescription().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(meditation.getNarrator()) &&
                meditation.getNarrator().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(meditation.getTags()) &&
                meditation.getTags().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(meditation.getSearchKeywords()) &&
                meditation.getSearchKeywords().toLowerCase().contains(lowerKeyword));
    }


    
    /**
     * 计算相关度分数
     */
    private Double calculateRelevanceScore(String text, String keyword) {
        if (StringUtils.isEmpty(text) || StringUtils.isEmpty(keyword)) {
            return 0.0;
        }
        
        String lowerText = text.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();
        
        // 简单的相关度计算：完全匹配得分最高，包含匹配次之
        if (lowerText.equals(lowerKeyword)) {
            return 100.0;
        } else if (lowerText.contains(lowerKeyword)) {
            // 根据匹配位置和长度计算分数
            int index = lowerText.indexOf(lowerKeyword);
            double positionScore = index == 0 ? 1.0 : 1.0 / (index + 1);
            double lengthScore = (double) lowerKeyword.length() / lowerText.length();
            return (positionScore + lengthScore) * 50;
        }
        
        return 0.0;
    }
    
    /**
     * 高亮关键词
     */
    private String highlightKeyword(String text, String keyword) {
        if (StringUtils.isEmpty(text) || StringUtils.isEmpty(keyword)) {
            return text;
        }
        
        // 简单的高亮处理，实际项目中可能需要更复杂的逻辑
        return text.replaceAll("(?i)" + keyword, "<em>" + keyword + "</em>");
    }

    @Override
    public List<String> getSearchSuggestions(String keyword, Long userId) {
        List<String> suggestions = new ArrayList<>();
        
        try {
            // 1. 基于关键词前缀的建议
            if (StringUtils.isNotEmpty(keyword)) {
                List<String> keywordSuggestions = searchSuggestionMapper.selectSuggestionsByKeyword(keyword, 5);
                suggestions.addAll(keywordSuggestions);
            }
            
            // 2. 基于用户历史搜索
            if (userId != null) {
                List<String> historySuggestions = getUserSearchHistory(userId, 3);
                suggestions.addAll(historySuggestions);
            }
            
            // 3. 基于热门搜索
            List<PsyHotSearch> hotSearches = hotSearchMapper.selectHotSearchByType("all", 5);
            for (PsyHotSearch hotSearch : hotSearches) {
                suggestions.add(hotSearch.getKeyword());
            }
            
            // 去重并限制数量
            suggestions = suggestions.stream()
                .distinct()
                .limit(10)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return suggestions;
    }

    @Override
    public List<PsyHotSearch> getHotSearches(String searchType, Integer limit) {
        try {
            return hotSearchMapper.selectHotSearchByType(searchType, limit);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public void recordSearch(String keyword, String searchType, Integer resultCount, 
                           Long userId, String ipAddress, String userAgent) {
        try {
            PsySearchRecord record = new PsySearchRecord();
            record.setUserId(userId);
            record.setKeyword(keyword);
            record.setSearchType(searchType);
            record.setResultCount(resultCount);
            record.setSearchTime(DateUtils.getNowDate());
            record.setIpAddress(ipAddress);
            record.setUserAgent(userAgent);
            record.setDelFlag("0");
            
            searchRecordMapper.insertSearchRecord(record);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional
    public void updateHotSearch(String keyword, String searchType) {
        try {
            PsyHotSearch existingHotSearch = hotSearchMapper.selectByKeywordAndType(keyword, searchType);
            
            if (existingHotSearch != null) {
                // 更新现有记录
                hotSearchMapper.incrementSearchCount(keyword, searchType);
                
                // 重新计算热度分数
                BigDecimal hotScore = calculateHotScore(existingHotSearch.getSearchCount() + 1);
                hotSearchMapper.updateHotScore(existingHotSearch.getId(), hotScore);
            } else {
                // 创建新记录
                PsyHotSearch newHotSearch = new PsyHotSearch();
                newHotSearch.setKeyword(keyword);
                newHotSearch.setSearchType(searchType);
                newHotSearch.setSearchCount(1);
                newHotSearch.setLastSearchTime(DateUtils.getNowDate());
                newHotSearch.setHotScore(calculateHotScore(1));
                newHotSearch.setStatus("0");
                newHotSearch.setCreateTime(DateUtils.getNowDate());
                
                hotSearchMapper.insertHotSearch(newHotSearch);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 计算热度分数
     */
    private BigDecimal calculateHotScore(Integer searchCount) {
        // 简单的热度计算公式，可以根据需要调整
        return new BigDecimal(searchCount * 10);
    }

    @Override
    public List<String> getUserSearchHistory(Long userId, Integer limit) {
        try {
            return searchRecordMapper.selectUserSearchHistory(userId, limit);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean clearUserSearchHistory(Long userId) {
        try {
            logger.info("开始清除用户搜索历史，userId: {}", userId);

            // 方案1：软删除 - 推荐方式，保留数据用于分析
            int softDeleteResult = searchRecordMapper.softDeleteByUserId(userId);
            logger.info("软删除用户搜索记录完成，userId: {}, 影响行数: {}", userId, softDeleteResult);
            return softDeleteResult > 0;
        } catch (Exception e) {
            logger.error("清除用户搜索历史失败，userId: {}", userId, e);
            return false;
        }
    }

    // 搜索记录管理方法
    @Override
    public List<PsySearchRecord> selectSearchRecordList(PsySearchRecord searchRecord) {
        return searchRecordMapper.selectSearchRecordList(searchRecord);
    }

    @Override
    public int insertSearchRecord(PsySearchRecord searchRecord) {
        searchRecord.setCreateTime(DateUtils.getNowDate());
        return searchRecordMapper.insertSearchRecord(searchRecord);
    }

    @Override
    public int updateSearchRecord(PsySearchRecord searchRecord) {
        searchRecord.setUpdateTime(DateUtils.getNowDate());
        return searchRecordMapper.updateSearchRecord(searchRecord);
    }

    @Override
    public int deleteSearchRecordByIds(Long[] ids) {
        return searchRecordMapper.deleteSearchRecordByIds(ids);
    }

    // 热门搜索管理方法
    @Override
    public List<PsyHotSearch> selectHotSearchList(PsyHotSearch hotSearch) {
        return hotSearchMapper.selectHotSearchList(hotSearch);
    }

    @Override
    public int insertHotSearch(PsyHotSearch hotSearch) {
        hotSearch.setCreateTime(DateUtils.getNowDate());
        return hotSearchMapper.insertHotSearch(hotSearch);
    }

    @Override
    public int updateHotSearch(PsyHotSearch hotSearch) {
        hotSearch.setUpdateTime(DateUtils.getNowDate());
        return hotSearchMapper.updateHotSearch(hotSearch);
    }

    @Override
    public int deleteHotSearchByIds(Long[] ids) {
        return hotSearchMapper.deleteHotSearchByIds(ids);
    }

    // 搜索建议管理方法
    @Override
    public List<PsySearchSuggestion> selectSearchSuggestionList(PsySearchSuggestion searchSuggestion) {
        return searchSuggestionMapper.selectSearchSuggestionList(searchSuggestion);
    }

    @Override
    public int insertSearchSuggestion(PsySearchSuggestion searchSuggestion) {
        searchSuggestion.setCreateTime(DateUtils.getNowDate());
        return searchSuggestionMapper.insertSearchSuggestion(searchSuggestion);
    }

    @Override
    public int updateSearchSuggestion(PsySearchSuggestion searchSuggestion) {
        searchSuggestion.setUpdateTime(DateUtils.getNowDate());
        return searchSuggestionMapper.updateSearchSuggestion(searchSuggestion);
    }

    @Override
    public int deleteSearchSuggestionByIds(Long[] ids) {
        return searchSuggestionMapper.deleteSearchSuggestionByIds(ids);
    }

    /**
     * 搜索心理测评
     */
    private SearchResultDTO.CategoryResult searchAssessments(String keyword, Integer pageNum, Integer pageSize) {
        try {
            // 获取所有可用的测评
            PsyTScale queryCondition = new PsyTScale();
            queryCondition.setStatus(1); // 只搜索已启用的测评
            queryCondition.setDelFlag("0"); // 只搜索未删除的

            List<PsyTScale> allAssessments = scaleMapper.selectScaleList(queryCondition);

            // 过滤匹配关键词的测评
            List<PsyTScale> assessments = allAssessments.stream()
                .filter(assessment -> matchesAssessmentKeyword(assessment, keyword))
                .collect(Collectors.toList());

            // 转换为搜索结果
            List<SearchResultDTO.SearchItem> items = new ArrayList<>();
            for (PsyTScale assessment : assessments) {
                SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
                item.setId(assessment.getId());
                item.setTitle(assessment.getName());
                item.setDescription(assessment.getDescription());
                item.setCoverImage(assessment.getImageUrl());
                item.setType("assessment");

                // 计算相关度分数
                double score = calculateRelevanceScore(keyword, assessment.getName());
                item.setRelevanceScore(score);

                // 设置价格
                if (assessment.getPrice() != null) {
                    item.setPrice(assessment.getPrice().toString());
                } else {
                    item.setPrice("0.00");
                }

                // 设置评分（使用默认值，因为数据库中没有评分字段）
                item.setRating(4.5); // 默认评分

                // 设置创建时间
                item.setCreateTime(assessment.getCreateTime());

                // 设置查看次数
                if (assessment.getViewCount() != null) {
                    item.setViewCount(assessment.getViewCount());
                } else {
                    item.setViewCount(0);
                }

                // 添加完整的额外信息（按照列表页面的字段）
                Map<String, Object> extra = new HashMap<>();

                // 基础信息
                extra.put("scaleCode", assessment.getCode());
                extra.put("categoryId", assessment.getCategoryId());
                extra.put("introduction", assessment.getIntroduction());

                // 测评相关信息
                extra.put("questionCount", assessment.getQuestionCount());
                extra.put("scoringType", assessment.getScoringType());
                extra.put("duration", assessment.getDuration());
                extra.put("applicableAge", assessment.getApplicableAge());

                // 付费相关信息
                extra.put("payMode", assessment.getPayMode());
                extra.put("payPhase", assessment.getPayPhase());
                extra.put("freeVipLevel", assessment.getFreeVipLevel());
                extra.put("freeReportLevel", assessment.getFreeReportLevel());
                extra.put("paidReportLevel", assessment.getPaidReportLevel());

                // 状态信息
                extra.put("status", assessment.getStatus());
                extra.put("sort", assessment.getSort());

                // 统计信息
                extra.put("searchCount", assessment.getSearchCount() != null ? assessment.getSearchCount() : 0);
                extra.put("testCount", assessment.getSearchCount() != null ? assessment.getSearchCount() : 0);

                // 计分相关信息
                extra.put("scoringMethod", assessment.getScoringMethod());
                extra.put("hasReverseItems", assessment.getHasReverseItems());
                extra.put("hasStandardScore", assessment.getHasStandardScore());
                extra.put("standardScoreMultiplier", assessment.getStandardScoreMultiplier());

                // 常模信息
                extra.put("normMean", assessment.getNormMean());
                extra.put("normSd", assessment.getNormSd());

                // 详细说明信息
                extra.put("testNotice", assessment.getTestNotice());
                extra.put("testPurpose", assessment.getTestPurpose());
                extra.put("testObject", assessment.getTestObject());
                extra.put("testPreparation", assessment.getTestPreparation());
                extra.put("testProcessing", assessment.getTestProcessing());
                extra.put("testAttention", assessment.getTestAttention());
                extra.put("testTheory", assessment.getTestTheory());
                extra.put("testApplication", assessment.getTestApplication());
                extra.put("referenceLiterature", assessment.getReferenceLiterature());

                // 页面链接
                extra.put("url", "/pages/assessment/detail?id=" + assessment.getId());
                extra.put("testUrl", "/pages/assessment/test?id=" + assessment.getId());

                // ==================== 直接设置完整的测评实体 ====================
                item.setAssessment(assessment);

                item.setExtraData(extra);
                items.add(item);
            }

            // 按相关度排序
            items.sort((a, b) -> Double.compare(b.getRelevanceScore(), a.getRelevanceScore()));

            // 分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, items.size());
            List<SearchResultDTO.SearchItem> pagedItems = items.subList(start, end);

            // 创建分类结果
            SearchResultDTO.CategoryResult result = new SearchResultDTO.CategoryResult();
            result.setType("assessment");
            result.setTypeName("心理测评");
            result.setCount(items.size());
            result.setItems(pagedItems);
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            SearchResultDTO.CategoryResult errorResult = new SearchResultDTO.CategoryResult();
            errorResult.setType("assessment");
            errorResult.setTypeName("心理测评");
            errorResult.setCount(0);
            errorResult.setItems(new ArrayList<>());
            return errorResult;
        }
    }

    /**
     * 检查测评是否匹配关键词
     */
    private boolean matchesAssessmentKeyword(PsyTScale assessment, String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return true;
        }

        String lowerKeyword = keyword.toLowerCase();

        // 检查名称
        if (StringUtils.isNotEmpty(assessment.getName()) &&
            assessment.getName().toLowerCase().contains(lowerKeyword)) {
            return true;
        }

        // 检查编码
        if (StringUtils.isNotEmpty(assessment.getCode()) &&
            assessment.getCode().toLowerCase().contains(lowerKeyword)) {
            return true;
        }

        // 检查描述
        if (StringUtils.isNotEmpty(assessment.getDescription()) &&
            assessment.getDescription().toLowerCase().contains(lowerKeyword)) {
            return true;
        }

        // 检查搜索关键词
        if (StringUtils.isNotEmpty(assessment.getSearchKeywords()) &&
            assessment.getSearchKeywords().toLowerCase().contains(lowerKeyword)) {
            return true;
        }

        return false;
    }
}
