package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTimeTemplateItem;
import com.xihuan.system.mapper.PsyTimeTemplateItemMapper;
import com.xihuan.system.service.IPsyTimeTemplateItemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 排班模板明细Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeTemplateItemServiceImpl implements IPsyTimeTemplateItemService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyTimeTemplateItemServiceImpl.class);
    
    @Autowired
    private PsyTimeTemplateItemMapper templateItemMapper;

    /**
     * 查询模板明细列表
     * 
     * @param item 模板明细
     * @return 模板明细集合
     */
    @Override
    public List<PsyTimeTemplateItem> selectTemplateItemList(PsyTimeTemplateItem item) {
        return templateItemMapper.selectTemplateItemList(item);
    }

    /**
     * 根据ID查询模板明细
     * 
     * @param id 模板明细主键
     * @return 模板明细
     */
    @Override
    public PsyTimeTemplateItem selectTemplateItemById(Long id) {
        return templateItemMapper.selectTemplateItemById(id);
    }

    /**
     * 根据模板ID查询明细
     * 
     * @param templateId 模板ID
     * @return 模板明细集合
     */
    @Override
    public List<PsyTimeTemplateItem> selectTemplateItemsByTemplateId(Long templateId) {
        return templateItemMapper.selectTemplateItemsByTemplateId(templateId);
    }

    /**
     * 新增模板明细
     * 
     * @param item 模板明细
     * @return 结果
     */
    @Override
    public int insertTemplateItem(PsyTimeTemplateItem item) {
        return templateItemMapper.insertTemplateItem(item);
    }

    /**
     * 批量新增模板明细
     * 
     * @param items 模板明细列表
     * @return 结果
     */
    @Override
    public int batchInsertTemplateItems(List<PsyTimeTemplateItem> items) {
        if (items == null || items.isEmpty()) {
            return 0;
        }
        
        int count = 0;
        for (PsyTimeTemplateItem item : items) {
            count += insertTemplateItem(item);
        }
        return count;
    }

    /**
     * 修改模板明细
     * 
     * @param item 模板明细
     * @return 结果
     */
    @Override
    public int updateTemplateItem(PsyTimeTemplateItem item) {
        return templateItemMapper.updateTemplateItem(item);
    }

    /**
     * 批量删除模板明细
     * 
     * @param ids 需要删除的模板明细主键集合
     * @return 结果
     */
    @Override
    public int deleteTemplateItemByIds(Long[] ids) {
        return templateItemMapper.deleteTemplateItemByIds(ids);
    }

    /**
     * 删除模板明细信息
     * 
     * @param id 模板明细主键
     * @return 结果
     */
    @Override
    public int deleteTemplateItemById(Long id) {
        return templateItemMapper.deleteTemplateItemById(id);
    }

    /**
     * 根据模板ID删除明细
     * 
     * @param templateId 模板ID
     * @return 结果
     */
    @Override
    public int deleteTemplateItemsByTemplateId(Long templateId) {
        return templateItemMapper.deleteTemplateItemsByTemplateId(templateId);
    }
}
