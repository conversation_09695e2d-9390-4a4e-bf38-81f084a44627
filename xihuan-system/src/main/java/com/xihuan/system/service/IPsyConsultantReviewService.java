package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyConsultantReview;

import java.math.BigDecimal;
import java.util.List;

/**
 * 咨询师评价表Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyConsultantReviewService {
    
    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    List<PsyConsultantReview> selectReviewList(PsyConsultantReview review);

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    PsyConsultantReview selectReviewById(Long id);

    /**
     * 查询评价详情（包含用户、咨询师等信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    PsyConsultantReview selectReviewWithDetails(Long id);

    /**
     * 根据咨询师ID查询评价列表
     * 
     * @param consultantId 咨询师ID
     * @return 评价集合
     */
    List<PsyConsultantReview> selectReviewsByConsultantId(Long consultantId);

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    List<PsyConsultantReview> selectReviewsByUserId(Long userId);

    /**
     * 根据咨询记录ID查询评价
     * 
     * @param recordId 咨询记录ID
     * @return 评价信息
     */
    PsyConsultantReview selectReviewByRecordId(Long recordId);

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int insertReview(PsyConsultantReview review);

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int updateReview(PsyConsultantReview review);

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    int deleteReviewById(Long id);

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    int deleteReviewByIds(Long[] ids);

    /**
     * 计算咨询师平均评分
     * 
     * @param consultantId 咨询师ID
     * @return 平均评分
     */
    BigDecimal calculateAverageRating(Long consultantId);

    /**
     * 统计咨询师评价数量
     * 
     * @param consultantId 咨询师ID
     * @return 评价数量
     */
    int countReviewsByConsultantId(Long consultantId);

    /**
     * 检查用户是否已评价咨询记录
     * 
     * @param userId 用户ID
     * @param recordId 咨询记录ID
     * @return 是否已评价
     */
    boolean checkUserReviewed(Long userId, Long recordId);

    /**
     * 审核评价
     * 
     * @param id 评价ID
     * @param adminCheck 审核状态
     * @param updateBy 更新者
     * @return 结果
     */
    int auditReview(Long id, Integer adminCheck, String updateBy);

    /**
     * 咨询师回复评价
     * 
     * @param id 评价ID
     * @param consultantReply 回复内容
     * @param updateBy 更新者
     * @return 结果
     */
    int replyReview(Long id, String consultantReply, String updateBy);

    /**
     * 查询待审核评价列表
     * 
     * @return 评价集合
     */
    List<PsyConsultantReview> selectPendingReviews();

    /**
     * 查询已通过审核的评价列表
     * 
     * @param consultantId 咨询师ID
     * @return 评价集合
     */
    List<PsyConsultantReview> selectApprovedReviews(Long consultantId);

    /**
     * 统计各星级评价数量
     * 
     * @param consultantId 咨询师ID
     * @return 统计结果
     */
    java.util.Map<String, Object> getRatingDistribution(Long consultantId);

    /**
     * 更新咨询师评分信息
     * 
     * @param consultantId 咨询师ID
     * @return 结果
     */
    int updateConsultantRating(Long consultantId);
}
