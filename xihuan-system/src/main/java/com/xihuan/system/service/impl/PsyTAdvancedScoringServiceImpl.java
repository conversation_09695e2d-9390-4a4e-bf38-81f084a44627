package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.*;
import com.xihuan.system.mapper.*;
import com.xihuan.system.service.IPsyTAdvancedScoringService;
import com.xihuan.system.service.IPsyTConfigurableScoringService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 高级计分服务实现
 *
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"unchecked", "rawtypes"})
public class PsyTAdvancedScoringServiceImpl implements IPsyTAdvancedScoringService {

    private static final Logger logger = LoggerFactory.getLogger(PsyTAdvancedScoringServiceImpl.class);

    @Autowired
    private PsyTAssessmentRecordMapper assessmentRecordMapper;

    @Autowired
    private PsyTAnswerRecordMapper answerRecordMapper;

    @Autowired
    private PsyTScaleMapper scaleMapper;

    @Autowired
    private PsyTQuestionMapper questionMapper;

    @Autowired
    private PsyTSubscaleMapper subscaleMapper;

    @Autowired
    private PsyTInterpretationMapper interpretationMapper;

    @Autowired
    private IPsyTConfigurableScoringService configurableScoringService;

    /**
     * 执行高级计分
     */
    @Override
    @Transactional
    public Map<String, Object> executeAdvancedScoring(Long recordId) {
        logger.info("开始执行高级计分，recordId: {}", recordId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取测评记录
            PsyTAssessmentRecord record = assessmentRecordMapper.selectAssessmentRecordById(recordId);
            if (record == null) {
                result.put("success", false);
                result.put("message", "测评记录不存在");
                return result;
            }

            // 获取量表信息
            PsyTScale scale = scaleMapper.selectScaleById(record.getScaleId());
            if (scale == null) {
                result.put("success", false);
                result.put("message", "量表信息不存在");
                return result;
            }

            String scaleCode = scale.getCode();
            logger.info("量表编码: {}", scaleCode);

            // 优先使用配置化计分，如果配置不存在则使用硬编码计分
            Map<String, Object> scoringResult;
            try {
                // 尝试使用配置化计分
                Map<String, Object> configurableResult = configurableScoringService.executeConfigurableScoring(recordId);
                if ((Boolean) configurableResult.get("success")) {
                    scoringResult = (Map<String, Object>) configurableResult.get("scoringResult");
                    logger.info("使用配置化计分成功，scaleCode: {}", scaleCode);
                } else {
                    // 配置化计分失败，使用硬编码计分
                    logger.warn("配置化计分失败，使用硬编码计分，scaleCode: {}", scaleCode);
                    scoringResult = executeHardcodedScoring(recordId, scaleCode);
                }
            } catch (Exception e) {
                // 配置化计分异常，使用硬编码计分
                logger.warn("配置化计分异常，使用硬编码计分，scaleCode: {}, error: {}", scaleCode, e.getMessage());
                scoringResult = executeHardcodedScoring(recordId, scaleCode);
            }

            result.put("success", true);
            result.put("scaleCode", scaleCode);
            result.put("scoringResult", scoringResult);
            
            logger.info("高级计分完成，recordId: {}, scaleCode: {}", recordId, scaleCode);
            
        } catch (Exception e) {
            logger.error("执行高级计分时发生异常，recordId: {}", recordId, e);
            result.put("success", false);
            result.put("message", "计分过程中发生异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 执行硬编码计分（向后兼容）
     */
    private Map<String, Object> executeHardcodedScoring(Long recordId, String scaleCode) {
        Map<String, Object> scoringResult;
        switch (scaleCode) {
            case "PRCA-24":
                scoringResult = calculatePRCA24Score(recordId);
                break;
            case "STAI":
                scoringResult = calculateSTAIScore(recordId);
                break;
            case "SAD":
                scoringResult = calculateSADScore(recordId);
                break;
            case "PDQ-4+":
                scoringResult = calculatePDQ4Score(recordId);
                break;
            case "FNE":
                scoringResult = calculateFNEScore(recordId);
                break;
            case "SAS":
                scoringResult = calculateSASScore(recordId);
                break;
            case "BAI":
                scoringResult = calculateBAIScore(recordId);
                break;
            default:
                // 使用简单求和计分
                scoringResult = calculateSimpleSum(recordId);
                break;
        }
        return scoringResult;
    }

    /**
     * PRCA-24 公式计分
     * 基础分18 + 加权求和
     */
    @Override
    public Map<String, Object> calculatePRCA24Score(Long recordId) {
        logger.info("开始PRCA-24计分，recordId: {}", recordId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取所有答题记录
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            
            // 获取分量表信息
            PsyTAssessmentRecord record = assessmentRecordMapper.selectAssessmentRecordById(recordId);
            List<PsyTSubscale> subscales = subscaleMapper.selectSubscalesByScaleId(record.getScaleId());
            
            Map<String, BigDecimal> dimensionScores = new HashMap<>();
            BigDecimal totalScore = BigDecimal.ZERO;
            
            // 为每个维度计算分数
            for (PsyTSubscale subscale : subscales) {
                BigDecimal dimensionScore = new BigDecimal(18); // 基础分18
                
                // 计算该维度的题目分数总和
                BigDecimal sumScore = BigDecimal.ZERO;
                int questionCount = 0;
                
                for (PsyTAnswerRecord answer : answers) {
                    // 检查题目是否属于当前维度
                    if (isQuestionInSubscale(answer.getQuestionId(), subscale.getId())) {
                        if (answer.getAnswerScore() != null) {
                            sumScore = sumScore.add(answer.getAnswerScore());
                            questionCount++;
                        }
                    }
                }
                
                // PRCA-24公式：基础分18 + 题目分数总和
                dimensionScore = dimensionScore.add(sumScore);
                dimensionScores.put(subscale.getAlias(), dimensionScore);
                totalScore = totalScore.add(dimensionScore);
                
                logger.info("PRCA-24维度计分 - {}: 基础分18 + 题目分数{} = {}", 
                    subscale.getAlias(), sumScore, dimensionScore);
            }
            
            result.put("totalScore", totalScore);
            result.put("dimensionScores", dimensionScores);
            result.put("scoringMethod", "FORMULA_WITH_BASE");
            
        } catch (Exception e) {
            logger.error("PRCA-24计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * STAI 反向计分
     */
    @Override
    public Map<String, Object> calculateSTAIScore(Long recordId) {
        logger.info("开始STAI计分，recordId: {}", recordId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取所有答题记录
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            
            // STAI反向计分题目编号
            Set<Integer> reverseQuestions = new HashSet<>(Arrays.asList(
                1, 2, 5, 8, 10, 11, 15, 16, 19, 20, 23, 24, 26, 27, 30, 33, 34, 36, 39
            ));
            
            BigDecimal totalScore = BigDecimal.ZERO;
            Map<String, BigDecimal> dimensionScores = new HashMap<>();
            
            for (PsyTAnswerRecord answer : answers) {
                BigDecimal score = answer.getAnswerScore();
                if (score != null) {
                    // 获取题目编号
                    Integer questionNo = getQuestionNumber(answer.getQuestionId());
                    
                    // 如果是反向计分题目，进行反向计分
                    if (reverseQuestions.contains(questionNo)) {
                        score = calculateReverseScore(score, 5); // 5点量表反向计分
                        logger.debug("STAI反向计分 - 题目{}: {} -> {}", questionNo, answer.getAnswerScore(), score);
                    }
                    
                    totalScore = totalScore.add(score);
                }
            }
            
            // 计算维度分数（状态焦虑和特质焦虑）
            dimensionScores.put("StateAnxiety", calculateSTAIDimensionScore(answers, reverseQuestions, "STATE"));
            dimensionScores.put("TraitAnxiety", calculateSTAIDimensionScore(answers, reverseQuestions, "TRAIT"));
            
            result.put("totalScore", totalScore);
            result.put("dimensionScores", dimensionScores);
            result.put("scoringMethod", "REVERSE_SCORING");
            
        } catch (Exception e) {
            logger.error("STAI计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 通用反向计分计算
     */
    @Override
    public BigDecimal calculateReverseScore(BigDecimal originalScore, Integer maxValue) {
        if (originalScore == null || maxValue == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(maxValue + 1).subtract(originalScore);
    }

    /**
     * 通用标准分转换
     */
    @Override
    public BigDecimal calculateStandardScore(BigDecimal rawScore, BigDecimal multiplier) {
        if (rawScore == null || multiplier == null) {
            return BigDecimal.ZERO;
        }
        return rawScore.multiply(multiplier).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 简单求和计分（默认方法）
     */
    private Map<String, Object> calculateSimpleSum(Long recordId) {
        Map<String, Object> result = new HashMap<>();
        
        List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
        BigDecimal totalScore = BigDecimal.ZERO;
        
        for (PsyTAnswerRecord answer : answers) {
            if (answer.getAnswerScore() != null) {
                totalScore = totalScore.add(answer.getAnswerScore());
            }
        }
        
        result.put("totalScore", totalScore);
        result.put("scoringMethod", "SIMPLE_SUM");
        
        return result;
    }

    /**
     * 检查题目是否属于指定分量表
     */
    private boolean isQuestionInSubscale(Long questionId, Long subscaleId) {
        // 这里需要查询题目与分量表的关联关系
        // 简化实现，实际应该查询 psy_t_subscale_question_rel 表
        return true; // 临时返回true，需要根据实际数据库结构实现
    }

    /**
     * 获取题目编号
     */
    private Integer getQuestionNumber(Long questionId) {
        // 查询题目信息获取题目编号
        // 简化实现，实际应该查询数据库
        return 1; // 临时返回1，需要根据实际数据库结构实现
    }

    /**
     * 计算STAI维度分数
     */
    private BigDecimal calculateSTAIDimensionScore(List<PsyTAnswerRecord> answers,
                                                  Set<Integer> reverseQuestions,
                                                  String dimension) {
        // 简化实现，实际需要根据维度划分题目
        return BigDecimal.ZERO;
    }

    /**
     * SAD 特殊二选一计分
     */
    @Override
    public Map<String, Object> calculateSADScore(Long recordId) {
        logger.info("开始SAD计分，recordId: {}", recordId);

        Map<String, Object> result = new HashMap<>();

        try {
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            BigDecimal totalScore = BigDecimal.ZERO;

            // SAD特殊计分：二选一题目，选择"是"得1分，选择"否"得0分
            for (PsyTAnswerRecord answer : answers) {
                if (answer.getAnswerContent() != null) {
                    String content = answer.getAnswerContent().trim();
                    if ("是".equals(content) || "1".equals(content)) {
                        totalScore = totalScore.add(BigDecimal.ONE);
                    }
                    // "否"或其他答案得0分，不需要处理
                }
            }

            result.put("totalScore", totalScore);
            result.put("scoringMethod", "SPECIAL_BINARY");

        } catch (Exception e) {
            logger.error("SAD计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * PDQ-4+ 复合题计分
     */
    @Override
    public Map<String, Object> calculatePDQ4Score(Long recordId) {
        logger.info("开始PDQ-4+计分，recordId: {}", recordId);

        Map<String, Object> result = new HashMap<>();

        try {
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            BigDecimal totalScore = BigDecimal.ZERO;
            Map<String, BigDecimal> dimensionScores = new HashMap<>();

            // PDQ-4+复合题计分逻辑
            // 需要根据具体的复合题规则进行计分
            for (PsyTAnswerRecord answer : answers) {
                if (answer.getAnswerScore() != null) {
                    totalScore = totalScore.add(answer.getAnswerScore());
                }
            }

            result.put("totalScore", totalScore);
            result.put("dimensionScores", dimensionScores);
            result.put("scoringMethod", "COMPOSITE_SCORING");

        } catch (Exception e) {
            logger.error("PDQ-4+计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * FNE 反向计分
     */
    @Override
    public Map<String, Object> calculateFNEScore(Long recordId) {
        logger.info("开始FNE计分，recordId: {}", recordId);

        Map<String, Object> result = new HashMap<>();

        try {
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            BigDecimal totalScore = BigDecimal.ZERO;

            // FNE所有题目都需要反向计分
            for (PsyTAnswerRecord answer : answers) {
                BigDecimal score = answer.getAnswerScore();
                if (score != null) {
                    // 假设是5点量表，进行反向计分
                    score = calculateReverseScore(score, 5);
                    totalScore = totalScore.add(score);
                }
            }

            result.put("totalScore", totalScore);
            result.put("scoringMethod", "REVERSE_SCORING");

        } catch (Exception e) {
            logger.error("FNE计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * SAS 标准分转换
     */
    @Override
    public Map<String, Object> calculateSASScore(Long recordId) {
        logger.info("开始SAS计分，recordId: {}", recordId);

        Map<String, Object> result = new HashMap<>();

        try {
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);

            // SAS反向计分题目编号
            Set<Integer> reverseQuestions = new HashSet<>(Arrays.asList(5, 9, 13, 17, 19));

            BigDecimal rawScore = BigDecimal.ZERO;

            // 先计算原始分（包含反向计分）
            for (PsyTAnswerRecord answer : answers) {
                BigDecimal score = answer.getAnswerScore();
                if (score != null) {
                    Integer questionNo = getQuestionNumber(answer.getQuestionId());

                    // 如果是反向计分题目
                    if (reverseQuestions.contains(questionNo)) {
                        score = calculateReverseScore(score, 4); // 4点量表反向计分
                    }

                    rawScore = rawScore.add(score);
                }
            }

            // 标准分转换：原始分 × 1.25
            BigDecimal standardScore = calculateStandardScore(rawScore, new BigDecimal("1.25"));

            result.put("rawScore", rawScore);
            result.put("standardScore", standardScore);
            result.put("totalScore", standardScore);
            result.put("scoringMethod", "STANDARD_SCORE");

        } catch (Exception e) {
            logger.error("SAS计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * BAI 标准分转换
     */
    @Override
    public Map<String, Object> calculateBAIScore(Long recordId) {
        logger.info("开始BAI计分，recordId: {}", recordId);

        Map<String, Object> result = new HashMap<>();

        try {
            List<PsyTAnswerRecord> answers = answerRecordMapper.selectAnswersByRecordId(recordId);
            BigDecimal rawScore = BigDecimal.ZERO;

            // 计算原始分
            for (PsyTAnswerRecord answer : answers) {
                if (answer.getAnswerScore() != null) {
                    rawScore = rawScore.add(answer.getAnswerScore());
                }
            }

            // 标准分转换：原始分 × 1.19
            BigDecimal standardScore = calculateStandardScore(rawScore, new BigDecimal("1.19"));

            result.put("rawScore", rawScore);
            result.put("standardScore", standardScore);
            result.put("totalScore", standardScore);
            result.put("scoringMethod", "STANDARD_SCORE");

        } catch (Exception e) {
            logger.error("BAI计分异常，recordId: {}", recordId, e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 计算维度分数
     */
    @Override
    public Map<String, BigDecimal> calculateDimensionScores(Long recordId, String scaleCode) {
        Map<String, BigDecimal> dimensionScores = new HashMap<>();

        try {
            // 根据量表类型计算维度分数
            switch (scaleCode) {
                case "PRCA-24":
                    Map<String, Object> prca24Result = calculatePRCA24Score(recordId);
                    if (prca24Result.containsKey("dimensionScores")) {
                        dimensionScores = (Map<String, BigDecimal>) prca24Result.get("dimensionScores");
                    }
                    break;
                case "STAI":
                    Map<String, Object> staiResult = calculateSTAIScore(recordId);
                    if (staiResult.containsKey("dimensionScores")) {
                        dimensionScores = (Map<String, BigDecimal>) staiResult.get("dimensionScores");
                    }
                    break;
                // 其他量表可以继续添加
                default:
                    logger.info("量表 {} 暂不支持维度分数计算", scaleCode);
                    break;
            }

        } catch (Exception e) {
            logger.error("计算维度分数异常，recordId: {}, scaleCode: {}", recordId, scaleCode, e);
        }

        return dimensionScores;
    }

    /**
     * 验证计分结果
     */
    @Override
    public Map<String, Object> validateScoringResult(String scaleCode, Map<String, BigDecimal> scores) {
        Map<String, Object> validation = new HashMap<>();
        validation.put("valid", true);
        validation.put("warnings", new ArrayList<>());
        validation.put("errors", new ArrayList<>());

        // 根据量表类型进行验证
        // 这里可以添加具体的验证逻辑

        return validation;
    }

    /**
     * 生成计分报告
     */
    @Override
    public Map<String, Object> generateScoringReport(Long recordId) {
        Map<String, Object> report = new HashMap<>();

        try {
            // 执行高级计分
            Map<String, Object> scoringResult = executeAdvancedScoring(recordId);

            if ((Boolean) scoringResult.get("success")) {
                report.put("success", true);
                report.put("recordId", recordId);
                report.put("scoringResult", scoringResult.get("scoringResult"));
                report.put("generatedAt", new Date());
            } else {
                report.put("success", false);
                report.put("message", scoringResult.get("message"));
            }

        } catch (Exception e) {
            logger.error("生成计分报告异常，recordId: {}", recordId, e);
            report.put("success", false);
            report.put("message", "生成报告时发生异常: " + e.getMessage());
        }

        return report;
    }
}
