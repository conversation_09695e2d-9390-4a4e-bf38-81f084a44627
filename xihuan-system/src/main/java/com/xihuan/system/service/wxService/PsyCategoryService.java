package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.TreeSelect;
import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.entity.PsyCategory;
import com.xihuan.common.core.domain.vo.ConsultantSimpleVO;

import java.util.List;
import java.util.Map;

public interface PsyCategoryService {
    /**
     * 查询分类列表（支持名称和状态过滤）
     * @param category 查询条件（包含categoryName和status过滤条件）
     * @return 分类列表（非树形结构）
     */
    List<PsyCategory> selectCategoryList(PsyCategory category);

    /**
     * 新增分类（自动校验唯一性）
     * @param category 分类实体（需包含父分类ID和分类名称）
     * @return 插入记录数
     *
     */
    int insertCategory(PsyCategory category);

    /**
     * 删除分类（级联校验）
     * @param categoryId 分类ID
     * @return 删除记录数
     *
     */
    int deleteCategoryById(Long categoryId);

    /**
     * 构建树形分类结构
     * @param categories 平面分类列表
     * @return 树形结构数据（适配前端TreeSelect组件）
     */
    List<TreeSelect> buildCategoryTree(List<PsyCategory> categories);


    /**
     * 获取带产品的分类详情
     * @param categoryId 分类ID
     * @return 分类及关联产品
     */
    PsyCategory getCategoryWithProducts(Long categoryId);

    /**
     * 获取子分类列表
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<PsyCategory> getChildrenCategories(Long parentId);

    /**
     * 更新分类信息
     * @param category 分类实体
     * @return 更新结果
     */
    int updateCategory(PsyCategory category);

    /**
     * 构建带产品的分类树
     */
    List<TreeSelect> buildCategoryTreeWithProducts(List<PsyCategory> categories);

    /**
     * 查询带产品信息的分类列表
     * @return 包含产品信息的分类列表
     */
    List<PsyCategory> selectCategoryListWithProducts();

    /**
     * 查询分类树及其对应的所有产品
     * 团单包含产品，咨询师包含咨询师，课程包含课程，测评包含测评，冥想包含冥想
     * @return 包含所有产品类型的分类树
     */
    Map<String, Object> selectCategoryTreeWithAllProducts();

    /**
     * 心理咨询师服务接口
     * 提供咨询师信息的增删改查、状态管理、分步保存等功能
     *
     * <AUTHOR>
     */
    interface PsyConsultantService {
        /**
         * 新增咨询师
         * 创建新的咨询师记录，包括基本信息和关联数据
         *
         * @param consultant 咨询师信息对象
         * @return 影响的行数
         */
        int insertConsultant(PsyConsultant consultant);

        /**
         * 更新咨询师信息
         * 更新现有咨询师的所有信息，包括基本信息和关联数据
         *
         * @param consultant 咨询师信息对象
         * @return 影响的行数
         */
        int updateConsultant(PsyConsultant consultant);

        /**
         * 根据ID查询咨询师（包含用户信息）
         * 获取咨询师的基本信息以及关联的用户账号信息
         *
         * @param id 咨询师ID
         * @return 咨询师信息对象
         */
        PsyConsultant getConsultantWithUserById(Long id);

        /**
         * 根据用户ID查询咨询师
         * 通过系统用户ID获取对应的咨询师信息
         *
         * @param userId 用户ID
         * @return 咨询师信息对象
         */
        PsyConsultant getConsultantByUserId(Long userId);

        /**
         * 分页查询咨询师列表
         * 根据多个条件筛选并分页返回咨询师列表
         *
         * @param name 咨询师姓名，模糊匹配
         * @param workStatus 工作状态（在线、离线、休假等）
         * @param auditStatus 审核状态（待审核、已通过、未通过等）
         * @param minPrice 最低价格，用于价格区间筛选
         * @param maxPrice 最高价格，用于价格区间筛选
         * @return 咨询师列表
         */
        List<PsyConsultant> listConsultantsByPage(String name, String workStatus, String auditStatus,
                Integer minPrice, Integer maxPrice);

        /**
         * 查询所有可用的咨询师（包含详细信息）
         * 获取所有状态正常且可以提供服务的咨询师列表
         *
         * @return 可用咨询师列表
         */
        List<PsyConsultant> listAllAvailableConsultants();

        /**
         * 根据条件查询咨询师列表
         * 按照姓名和擅长领域筛选咨询师
         *
         * @param name 咨询师姓名，模糊匹配
         * @param field 擅长领域
         * @return 符合条件的咨询师列表
         */
        List<PsyConsultant> listConsultants(String name, String field);

        /**
         * 根据ID查询咨询师完整信息（包含所有关联数据）
         * 获取咨询师的所有信息，包括基本信息、服务信息、资质证书、教育经历等
         *
         * @param id 咨询师ID
         * @return 咨询师完整信息对象
         */
        PsyConsultant getConsultantFullDetails(Long id);

        /**
         * 删除咨询师
         * 根据ID删除指定咨询师的所有信息
         *
         * @param id 咨询师ID
         * @return 影响的行数
         */
        int deleteConsultant(Long id);

        /**
         * 批量删除咨询师
         * 批量删除多个咨询师的信息
         *
         * @param ids 咨询师ID数组
         * @return 影响的行数
         */
        int deleteConsultants(Long[] ids);

        /**
         * 更新咨询师工作状态
         * 修改咨询师的在线状态（在线、离线、休假等）
         *
         * @param id 咨询师ID
         * @param workStatus 工作状态
         * @return 影响的行数
         */
        int updateConsultantWorkStatus(Long id, String workStatus);

        /**
         * 更新咨询师审核状态
         * 修改咨询师的审核状态（待审核、已通过、未通过等）
         *
         * @param id 咨询师ID
         * @param auditStatus 审核状态
         * @return 影响的行数
         */
        int updateConsultantAuditStatus(Long id, String auditStatus);

        /**
         * 保存咨询师基本信息
         * 分步保存功能之一，用于保存基础个人信息
         *
         * @param consultant 包含基本信息的咨询师对象
         * @return 影响的行数
         */
        int saveBasicInfo(PsyConsultant consultant);

        /**
         * 保存咨询师服务信息
         * 分步保存功能之一，用于保存服务相关信息（价格、时段等）
         *
         * @param consultant 包含服务信息的咨询师对象
         * @return 影响的行数
         */
        int saveServiceInfo(PsyConsultant consultant);

        /**
         * 保存咨询师资质信息
         * 分步保存功能之一，用于保存专业资质相关信息
         *
         * @param consultant 包含资质信息的咨询师对象
         * @return 影响的行数
         */
        int saveQualificationInfo(PsyConsultant consultant);

        /**
         * 保存咨询师教育和培训信息
         * 分步保存功能之一，用于保存教育背景、培训经历等信息
         *
         * @param consultant 包含教育培训信息的咨询师对象
         * @return 影响的行数
         */
        int saveEducationAndTraining(PsyConsultant consultant);

        /**
         * 查询所有咨询师简单信息列表
         *
         * @return 咨询师简单信息列表
         */
        List<ConsultantSimpleVO> listAllSimple();
    }
}
