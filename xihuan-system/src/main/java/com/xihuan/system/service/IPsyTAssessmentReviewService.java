package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTAssessmentReview;

import java.util.List;
import java.util.Map;

/**
 * 测评评价Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTAssessmentReviewService {
    
    /**
     * 查询测评评价列表
     * 
     * @param review 测评评价
     * @return 测评评价集合
     */
    List<PsyTAssessmentReview> selectReviewList(PsyTAssessmentReview review);

    /**
     * 根据测评评价ID查询详细信息
     * 
     * @param id 测评评价ID
     * @return 测评评价
     */
    PsyTAssessmentReview selectReviewById(Long id);

    /**
     * 查询评价详情（包含量表、用户等信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    PsyTAssessmentReview selectReviewWithDetails(Long id);

    /**
     * 根据量表ID查询评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectReviewsByScaleId(Long scaleId);

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectReviewsByUserId(Long userId);

    /**
     * 根据测评记录ID查询评价
     * 
     * @param recordId 测评记录ID
     * @return 评价信息
     */
    PsyTAssessmentReview selectReviewByRecordId(Long recordId);

    /**
     * 根据订单ID查询评价
     * 
     * @param orderId 订单ID
     * @return 评价信息
     */
    PsyTAssessmentReview selectReviewByOrderId(Long orderId);

    /**
     * 新增测评评价
     * 
     * @param review 测评评价
     * @return 结果
     */
    int insertReview(PsyTAssessmentReview review);

    /**
     * 修改测评评价
     * 
     * @param review 测评评价
     * @return 结果
     */
    int updateReview(PsyTAssessmentReview review);

    /**
     * 批量删除测评评价
     * 
     * @param ids 需要删除的测评评价ID
     * @return 结果
     */
    int deleteReviewByIds(Long[] ids);

    /**
     * 删除测评评价信息
     * 
     * @param id 测评评价ID
     * @return 结果
     */
    int deleteReviewById(Long id);

    /**
     * 检查用户是否已评价
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param recordId 测评记录ID
     * @return 是否已评价
     */
    boolean checkUserReviewed(Long userId, Long scaleId, Long recordId);

    /**
     * 审核评价
     * 
     * @param id 评价ID
     * @param status 审核状态
     * @param auditRemark 审核意见
     * @return 结果
     */
    int auditReview(Long id, Integer status, String auditRemark);

    /**
     * 批量审核评价
     * 
     * @param ids 评价ID数组
     * @param status 审核状态
     * @param auditRemark 审核意见
     * @return 结果
     */
    int batchAuditReviews(Long[] ids, Integer status, String auditRemark);

    /**
     * 置顶评价
     * 
     * @param id 评价ID
     * @param isTop 是否置顶
     * @return 结果
     */
    int topReview(Long id, Integer isTop);

    /**
     * 点赞评价
     * 
     * @param id 评价ID
     * @param userId 用户ID
     * @return 结果
     */
    int likeReview(Long id, Long userId);

    /**
     * 取消点赞评价
     * 
     * @param id 评价ID
     * @param userId 用户ID
     * @return 结果
     */
    int unlikeReview(Long id, Long userId);

    /**
     * 增加回复数
     * 
     * @param id 评价ID
     * @return 结果
     */
    int increaseReplyCount(Long id);

    /**
     * 减少回复数
     * 
     * @param id 评价ID
     * @return 结果
     */
    int decreaseReplyCount(Long id);

    /**
     * 查询量表评价统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleReviewStats(Long scaleId);

    /**
     * 查询用户评价统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserReviewStats(Long userId);

    /**
     * 查询评价统计信息
     * 
     * @return 统计信息
     */
    List<Map<String, Object>> selectReviewStats();

    /**
     * 查询待审核评价列表
     * 
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectPendingReviews();

    /**
     * 查询置顶评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectTopReviews(Long scaleId);

    /**
     * 查询热门评价列表
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectHotReviews(Long scaleId, Integer limit);

    /**
     * 搜索评价
     * 
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @param userId 用户ID
     * @param status 审核状态
     * @param rating 评分
     * @return 评价集合
     */
    List<PsyTAssessmentReview> searchReviews(String keyword, Long scaleId, Long userId, Integer status, Integer rating);

    /**
     * 查询评价排行榜
     * 
     * @param type 排行类型(like:点赞数, reply:回复数)
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectReviewRanking(String type, Integer limit);

    /**
     * 查询量表评分分布
     * 
     * @param scaleId 量表ID
     * @return 评分分布
     */
    List<Map<String, Object>> selectRatingDistribution(Long scaleId);

    /**
     * 查询最新评价列表
     * 
     * @param limit 限制数量
     * @return 评价集合
     */
    List<PsyTAssessmentReview> selectLatestReviews(Integer limit);

    /**
     * 查询用户是否点赞评价
     * 
     * @param reviewId 评价ID
     * @param userId 用户ID
     * @return 是否点赞
     */
    boolean checkUserLiked(Long reviewId, Long userId);

    /**
     * 查询评价回复列表
     * 
     * @param reviewId 评价ID
     * @return 回复集合
     */
    List<Map<String, Object>> selectReviewReplies(Long reviewId);

    /**
     * 创建评价
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param recordId 测评记录ID
     * @param orderId 订单ID
     * @param rating 评分
     * @param content 评价内容
     * @param isAnonymous 是否匿名
     * @return 结果
     */
    int createReview(Long userId, Long scaleId, Long recordId, Long orderId, Integer rating, String content, Integer isAnonymous);

    /**
     * 验证评价权限
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param recordId 测评记录ID
     * @return 验证结果
     */
    Map<String, Object> validateReviewPermission(Long userId, Long scaleId, Long recordId);

    /**
     * 获取评价统计摘要
     * 
     * @param scaleId 量表ID
     * @return 统计摘要
     */
    Map<String, Object> getReviewSummary(Long scaleId);

    /**
     * 处理评价敏感词
     * 
     * @param content 评价内容
     * @return 处理后的内容
     */
    String processSensitiveWords(String content);

    /**
     * 自动审核评价
     * 
     * @param review 评价信息
     * @return 审核结果
     */
    Map<String, Object> autoAuditReview(PsyTAssessmentReview review);

    /**
     * 发送评价通知
     * 
     * @param reviewId 评价ID
     * @return 结果
     */
    int sendReviewNotification(Long reviewId);

    /**
     * 导出评价数据
     * 
     * @param scaleId 量表ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 导出数据
     */
    List<Map<String, Object>> exportReviewData(Long scaleId, String startDate, String endDate);
}
