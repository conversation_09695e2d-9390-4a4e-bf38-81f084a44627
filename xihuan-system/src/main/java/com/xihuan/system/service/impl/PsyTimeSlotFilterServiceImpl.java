package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.service.IPsyTimeSlotFilterService;
import com.xihuan.system.service.IPsyConsultantCenterConfigService;
import com.xihuan.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 时间槽过滤服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeSlotFilterServiceImpl implements IPsyTimeSlotFilterService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyTimeSlotFilterServiceImpl.class);
    
    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IPsyConsultantCenterConfigService consultantConfigService;
    
    // 咨询类型常量
    private static final int CONSULTATION_TYPE_ONLINE = 1;  // 线上
    private static final int CONSULTATION_TYPE_OFFLINE = 2; // 线下
    
    /**
     * 根据咨询师到店时间过滤时间槽
     */
    @Override
    public List<PsyTimeSlot> filterTimeSlotsByArrivalTime(
            List<PsyTimeSlot> timeSlots, 
            Long consultantId, 
            Long centerId, 
            Integer consultationType,
            LocalDateTime currentTime) {
        
        // 如果是线上咨询，不需要过滤
        if (consultationType == null || consultationType.equals(CONSULTATION_TYPE_ONLINE)) {
            return timeSlots;
        }
        
        // 检查该咨询师是否启用到店时间过滤
        if (!isArrivalFilterEnabled(consultantId, centerId)) {
            return timeSlots;
        }
        
        // 获取咨询师到店所需时间
        Double arrivalTimeHours = getConsultantArrivalTime(consultantId, centerId);
        if (arrivalTimeHours == null || arrivalTimeHours <= 0) {
            return timeSlots;
        }
        
        // 计算最早可预约时间
        LocalDateTime earliestTime = currentTime.plusMinutes((long)(arrivalTimeHours * 60));
        
        // 过滤时间槽
        return timeSlots.stream()
                .filter(slot -> isTimeSlotAfterEarliestTime(slot, earliestTime))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取咨询师到指定中心的所需时间
     */
    @Override
    public Double getConsultantArrivalTime(Long consultantId, Long centerId) {
        if (consultantId == null) {
            return getDefaultArrivalTime();
        }

        // 从配置服务获取咨询师的到店时间
        return consultantConfigService.getArrivalTime(consultantId, centerId);
    }
    
    /**
     * 检查指定时间槽是否可用（考虑到店时间）
     */
    @Override
    public boolean isTimeSlotAvailable(
            PsyTimeSlot timeSlot, 
            Long consultantId, 
            Long centerId, 
            Integer consultationType,
            LocalDateTime currentTime) {
        
        // 线上咨询不受到店时间限制
        if (consultationType == null || consultationType.equals(CONSULTATION_TYPE_ONLINE)) {
            return true;
        }
        
        // 检查该咨询师是否启用过滤
        if (!isArrivalFilterEnabled(consultantId, centerId)) {
            return true;
        }
        
        // 获取到店时间
        Double arrivalTimeHours = getConsultantArrivalTime(consultantId, centerId);
        if (arrivalTimeHours == null || arrivalTimeHours <= 0) {
            return true;
        }
        
        // 计算最早可预约时间
        LocalDateTime earliestTime = currentTime.plusMinutes((long)(arrivalTimeHours * 60));
        
        return isTimeSlotAfterEarliestTime(timeSlot, earliestTime);
    }
    
    /**
     * 计算最早可预约的线下咨询时间
     */
    @Override
    public LocalDateTime getEarliestOfflineAppointmentTime(
            Long consultantId, 
            Long centerId, 
            LocalDateTime currentTime) {
        
        Double arrivalTimeHours = getConsultantArrivalTime(consultantId, centerId);
        if (arrivalTimeHours == null || arrivalTimeHours <= 0) {
            return currentTime;
        }
        
        return currentTime.plusMinutes((long)(arrivalTimeHours * 60));
    }
    
    /**
     * 批量过滤多个咨询师的时间槽
     */
    @Override
    public Map<Long, List<PsyTimeSlot>> batchFilterTimeSlots(
            Map<Long, List<PsyTimeSlot>> consultantTimeSlots,
            Long centerId,
            Integer consultationType,
            LocalDateTime currentTime) {
        
        Map<Long, List<PsyTimeSlot>> filteredMap = new HashMap<>();
        
        for (Map.Entry<Long, List<PsyTimeSlot>> entry : consultantTimeSlots.entrySet()) {
            Long consultantId = entry.getKey();
            List<PsyTimeSlot> timeSlots = entry.getValue();
            
            List<PsyTimeSlot> filteredSlots = filterTimeSlotsByArrivalTime(
                timeSlots, consultantId, centerId, consultationType, currentTime
            );
            
            filteredMap.put(consultantId, filteredSlots);
        }
        
        return filteredMap;
    }
    
    /**
     * 检查时间槽是否在最早可预约时间之后
     */
    private boolean isTimeSlotAfterEarliestTime(PsyTimeSlot timeSlot, LocalDateTime earliestTime) {
        if (timeSlot == null || timeSlot.getDateKey() == null || timeSlot.getStartTime() == null) {
            return false;
        }

        try {
            LocalDate slotDate = LocalDate.parse(timeSlot.getDateKey());
            LocalDateTime slotDateTime = LocalDateTime.of(slotDate, timeSlot.getStartTime());
            return slotDateTime.isAfter(earliestTime) || slotDateTime.isEqual(earliestTime);
        } catch (Exception e) {
            logger.warn("解析时间槽日期失败: dateKey={}, error={}", timeSlot.getDateKey(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取默认到店时间
     */
    private Double getDefaultArrivalTime() {
        try {
            String configValue = configService.selectConfigByKey("psy.consultant.default.arrival.hours");
            if (StringUtils.isNotEmpty(configValue)) {
                return Double.parseDouble(configValue);
            }
        } catch (Exception e) {
            logger.warn("获取默认到店时间配置失败: {}", e.getMessage());
        }
        
        // 默认2小时
        return 2.0;
    }
    
    /**
     * 检查咨询师是否启用到店时间过滤
     */
    @Override
    public boolean isArrivalFilterEnabled(Long consultantId, Long centerId) {
        if (consultantId == null) {
            return getDefaultArrivalFilterEnabled();
        }

        // 从配置服务获取咨询师的过滤启用状态
        return consultantConfigService.isArrivalFilterEnabled(consultantId, centerId);
    }

    /**
     * 获取新咨询师的默认过滤设置
     */
    private boolean getDefaultArrivalFilterEnabled() {
        try {
            String configValue = configService.selectConfigByKey("psy.consultant.default.arrival.filter.enabled");
            return "true".equalsIgnoreCase(configValue);
        } catch (Exception e) {
            logger.warn("获取默认到店时间过滤配置失败: {}", e.getMessage());
            return true; // 默认启用
        }
    }
}
