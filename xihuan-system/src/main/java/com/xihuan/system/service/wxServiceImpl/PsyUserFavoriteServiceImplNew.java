package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.entity.*;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.*;
import com.xihuan.system.service.wxService.IPsyUserFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏服务实现类（新版本）
 */
@Service("psyUserFavoriteServiceNew")
public class PsyUserFavoriteServiceImplNew implements IPsyUserFavoriteService {

    @Autowired
    private PsyUserFavoriteMapper favoriteMapper;

    @Autowired
    private PsyFavoriteStatisticsMapper statisticsMapper;
    
    @Autowired
    private PsyFavoriteGroupMapper groupMapper;

    @Autowired
    private PsyConsultantMapper consultantMapper;

    @Autowired
    private PsyCourseMapper courseMapper;
    
    @Autowired
    private PsyMeditationMapper meditationMapper;

    @Override
    @Transactional
    public PsyUserFavorite addFavorite(PsyUserFavorite favorite) {
        // 1. 设置目标标题和图片（冗余字段）
        setTargetInfo(favorite);
        
        // 2. 插入收藏记录
        favoriteMapper.insertFavorite(favorite);
        
        // 3. 更新统计信息
        updateFavoriteStatistics(favorite.getTargetType(), favorite.getTargetId(), true);
        
        // 4. 添加到默认分组
        addToDefaultGroup(favorite);
        
        return favorite;
    }

    @Override
    @Transactional
    public int removeFavorite(Long[] favoriteIds) {
        // 1. 获取收藏信息，用于更新统计
        for (Long favoriteId : favoriteIds) {
            PsyUserFavorite favorite = favoriteMapper.selectFavoriteById(favoriteId);
            if (favorite != null) {
                // 更新统计信息
                updateFavoriteStatistics(favorite.getTargetType(), favorite.getTargetId(), false);
                
                // 删除分组关系
                groupMapper.deleteGroupRel(favoriteId, null);
            }
        }
        
        // 2. 删除收藏
        return favoriteMapper.deleteFavoriteByIds(favoriteIds);
    }

    @Override
    public List<PsyUserFavorite> getFavorites(PsyUserFavorite favorite) {
        return favoriteMapper.selectFavoriteList(favorite);
    }

    @Override
    public List<Map<String, Object>> getFavoritesWithDetails(PsyUserFavorite favorite) {
        return favoriteMapper.selectFavoriteWithDetails(favorite);
    }

    @Override
    public PsyUserFavorite checkFavorites(Long userId, Integer targetType, Long targetId) {
        return favoriteMapper.checkFavorite(userId, targetType, targetId);
    }
    
    @Override
    public PsyUserFavorite checkFavoritesOld(Long userId, Integer targetType, Long counselorId, Long productId) {
        return favoriteMapper.checkFavoriteOld(userId, targetType, counselorId, productId);
    }
    
    @Override
    public int updateFavorite(PsyUserFavorite favorite) {
        return favoriteMapper.updateFavorite(favorite);
    }
    
    @Override
    public PsyUserFavorite getFavoriteById(Long favoriteId) {
        return favoriteMapper.selectFavoriteById(favoriteId);
    }
    
    @Override
    public int updateViewCount(Long favoriteId) {
        return favoriteMapper.updateViewCount(favoriteId);
    }
    
    @Override
    public Map<String, Object> getUserFavoriteStats(Long userId) {
        return favoriteMapper.selectUserFavoriteStats(userId);
    }
    
    @Override
    public int countFavoriteByTarget(Integer targetType, Long targetId) {
        return favoriteMapper.countFavoriteByTarget(targetType, targetId);
    }
    
    @Override
    public List<Long> getUserFavoriteTargetIds(Long userId, Integer targetType) {
        return favoriteMapper.selectUserFavoriteTargetIds(userId, targetType);
    }
    
    @Override
    public List<Map<String, Object>> getHotFavorites(Integer targetType, Integer limit) {
        return statisticsMapper.selectHotFavorites(targetType, limit);
    }
    
    @Override
    public List<PsyFavoriteGroup> getFavoriteGroups(Long userId) {
        PsyFavoriteGroup condition = new PsyFavoriteGroup();
        condition.setUserId(userId);
        condition.setStatus(1);
        return groupMapper.selectGroupList(condition);
    }
    
    @Override
    @Transactional
    public PsyFavoriteGroup createFavoriteGroup(PsyFavoriteGroup group) {
        groupMapper.insertGroup(group);
        return group;
    }
    
    @Override
    public int updateFavoriteGroup(PsyFavoriteGroup group) {
        return groupMapper.updateGroup(group);
    }
    
    @Override
    @Transactional
    public int deleteFavoriteGroup(Long groupId) {
        // 检查是否为默认分组
        PsyFavoriteGroup group = groupMapper.selectGroupById(groupId);
        if (group != null && group.getIsDefault() == 1) {
            return 0; // 不允许删除默认分组
        }
        
        // 删除分组关系
        groupMapper.deleteGroupRel(null, groupId);
        
        // 删除分组
        return groupMapper.deleteGroupById(groupId);
    }
    
    @Override
    @Transactional
    public int addFavoriteToGroup(Long favoriteId, Long groupId) {
        // 创建关系对象
        PsyFavoriteGroupRel rel = new PsyFavoriteGroupRel();
        rel.setFavoriteId(favoriteId);
        rel.setGroupId(groupId);
        
        // 插入关系
        int result = groupMapper.insertGroupRel(rel);
        
        // 更新分组收藏数量
        if (result > 0) {
            updateGroupFavoriteCount(groupId);
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public int removeFavoriteFromGroup(Long favoriteId, Long groupId) {
        // 删除关系
        int result = groupMapper.deleteGroupRel(favoriteId, groupId);
        
        // 更新分组收藏数量
        if (result > 0) {
            updateGroupFavoriteCount(groupId);
        }
        
        return result;
    }
    
    @Override
    public List<Map<String, Object>> getGroupFavorites(Long groupId, Long userId) {
        return groupMapper.selectGroupFavorites(groupId, userId);
    }
    
    /**
     * 设置目标对象的标题和图片（冗余字段）
     */
    private void setTargetInfo(PsyUserFavorite favorite) {
        if (favorite.getTargetTitle() != null && favorite.getTargetImage() != null) {
            return; // 已设置，无需处理
        }
        
        switch (favorite.getTargetType()) {
            case 1: // 咨询师
                PsyConsultant consultant = consultantMapper.selectConsultantWithUserById(favorite.getTargetId());
                if (consultant != null) {
                    favorite.setTargetTitle(consultant.getName());
                    favorite.setTargetImage(consultant.getImageUrl());
                }
                break;
            case 2: // 课程
                PsyCourse course = courseMapper.selectCourseById(favorite.getTargetId());
                if (course != null) {
                    favorite.setTargetTitle(course.getTitle());
                    favorite.setTargetImage(course.getCoverImage());
                }
                break;
            case 3: // 冥想
                PsyMeditation meditation = meditationMapper.selectMeditationById(favorite.getTargetId());
                if (meditation != null) {
                    favorite.setTargetTitle(meditation.getTitle());
                    favorite.setTargetImage(meditation.getCoverImage());
                }
                break;
            case 4: // 测评
                // 测评相关代码暂时注释，等测评系统重新实现后再启用
                // if (scaleMapper != null) {
                //     PsyScale scale = scaleMapper.selectScaleById(favorite.getTargetId());
                //     if (scale != null) {
                //         favorite.setTargetTitle(scale.getName());
                //         favorite.setTargetImage(scale.getImageUrl());
                //     }
                // }
                break;
        }
    }
    
    /**
     * 更新收藏统计信息
     */
    private void updateFavoriteStatistics(Integer targetType, Long targetId, boolean isAdd) {
        PsyFavoriteStatistics stats = statisticsMapper.selectStatsByTarget(targetType, targetId);
        
        if (stats == null && isAdd) {
            // 新增统计记录
            stats = new PsyFavoriteStatistics();
            stats.setTargetType(targetType);
            stats.setTargetId(targetId);
            stats.setFavoriteCount(1);
            stats.setTodayCount(1);
            stats.setWeekCount(1);
            stats.setMonthCount(1);
            stats.setLastFavoriteTime(DateUtils.getNowDate());
            statisticsMapper.insertStats(stats);
        } else if (stats != null) {
            // 更新统计记录
            if (isAdd) {
                statisticsMapper.incrementFavoriteCount(targetType, targetId);
            } else {
                statisticsMapper.decrementFavoriteCount(targetType, targetId);
            }
        }
    }
    
    /**
     * 添加到默认分组
     */
    private void addToDefaultGroup(PsyUserFavorite favorite) {
        // 查询用户默认分组
        PsyFavoriteGroup defaultGroup = groupMapper.selectDefaultGroup(favorite.getUserId());
        
        // 如果没有默认分组，创建一个
        if (defaultGroup == null) {
            groupMapper.createDefaultGroup(favorite.getUserId());
            defaultGroup = groupMapper.selectDefaultGroup(favorite.getUserId());
        }
        
        // 添加到默认分组
        if (defaultGroup != null) {
            PsyFavoriteGroupRel rel = new PsyFavoriteGroupRel();
            rel.setFavoriteId(favorite.getFavoriteId());
            rel.setGroupId(defaultGroup.getGroupId());
            groupMapper.insertGroupRel(rel);
            
            // 更新分组收藏数量
            updateGroupFavoriteCount(defaultGroup.getGroupId());
        }
    }
    
    /**
     * 更新分组收藏数量
     */
    private void updateGroupFavoriteCount(Long groupId) {
        // 查询分组内收藏数量
        List<PsyFavoriteGroupRel> relList = groupMapper.selectGroupRelList(null, groupId);
        int count = relList != null ? relList.size() : 0;
        
        // 更新分组收藏数量
        groupMapper.updateGroupFavoriteCount(groupId, count);
    }
}
