package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.entity.PsyMessageConversation;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyMessageConversationMapper;
import com.xihuan.system.service.wxService.IPsyMessageConversationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 消息会话服务实现类
 */
@Service
public class PsyMessageConversationServiceImpl implements IPsyMessageConversationService {

    private static final Logger log = LoggerFactory.getLogger(PsyMessageConversationServiceImpl.class);
    @Autowired
    private PsyMessageConversationMapper conversationMapper;

    /**
     * 创建会话
     */
    @Override
    @Transactional
    public PsyMessageConversation createConversation(Long userId, Long consultantId) {
        // 检查是否已存在会话
        PsyMessageConversation existConversation = conversationMapper.selectConversationByUserAndConsultant(userId, consultantId);
        if (existConversation != null) {
            return existConversation;
        }
        
        // 创建新会话
        PsyMessageConversation conversation = new PsyMessageConversation();
        conversation.setUserId(userId);
        conversation.setConsultantId(consultantId);
        conversation.setLastMessageTime(DateUtils.getNowDate());
        conversation.setUserUnreadCount(0);
        conversation.setConsultantUnreadCount(0);
        
        conversationMapper.insertConversation(conversation);
        log.info("创建会话成功，会话ID：{}", conversation.getConversationId());
        
        return conversation;
    }

    /**
     * 获取会话列表
     */
    @Override
    public List<PsyMessageConversation> getUserConversations(Long userId) {
        return conversationMapper.selectConversationsByUserId(userId);
    }

    /**
     * 获取咨询师会话列表
     */
    @Override
    public List<PsyMessageConversation> getConsultantConversations(Long consultantId) {
        return conversationMapper.selectConversationsByConsultantId(consultantId);
    }

    /**
     * 获取所有会话列表(管理员使用)
     */
    @Override
    public List<PsyMessageConversation> getAllConversations() {
        return conversationMapper.selectAllConversations();
    }

    /**
     * 获取会话详情
     */
    @Override
    public PsyMessageConversation getConversation(Long conversationId) {
        return conversationMapper.selectConversationById(conversationId);
    }

    /**
     * 更新会话最后消息
     */
    @Override
    @Transactional
    public boolean updateLastMessage(Long conversationId, Long lastSenderId, String lastMessage) {
        PsyMessageConversation conversation = conversationMapper.selectConversationById(conversationId);
        if (conversation == null) {
            return false;
        }
        
        conversation.setLastSenderId(lastSenderId);
        conversation.setLastMessage(lastMessage);
        conversation.setLastMessageTime(DateUtils.getNowDate());
        
        int rows = conversationMapper.updateConversation(conversation);
        return rows > 0;
    }

    /**
     * 增加未读消息计数
     */
    @Override
    @Transactional
    public boolean incrementUnreadCount(Long conversationId, boolean isUser) {
        int rows;
        if (isUser) {
            // 增加咨询师未读数
            rows = conversationMapper.incrementConsultantUnreadCount(conversationId);
        } else {
            // 增加用户未读数
            rows = conversationMapper.incrementUserUnreadCount(conversationId);
        }
        return rows > 0;
    }

    /**
     * 重置未读消息计数
     */
    @Override
    @Transactional
    public boolean resetUnreadCount(Long conversationId, boolean isUser) {
        int rows;
        if (isUser) {
            // 重置用户未读数
            rows = conversationMapper.resetUserUnreadCount(conversationId);
        } else {
            // 重置咨询师未读数
            rows = conversationMapper.resetConsultantUnreadCount(conversationId);
        }
        return rows > 0;
    }
} 