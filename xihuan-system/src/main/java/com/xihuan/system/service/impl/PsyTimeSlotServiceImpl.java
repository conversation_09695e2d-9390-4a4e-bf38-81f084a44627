package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.dto.PsyTimeSlotDTO;
import com.xihuan.common.core.domain.entity.*;
import com.xihuan.common.core.domain.vo.ConsultantSimpleVO;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.spring.SpringUtils;
import com.xihuan.system.mapper.PsyTimeCounselorScheduleMapper;
import com.xihuan.system.mapper.PsyTimeSlotMapper;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.service.IPsyTimeScheduleTemplateService;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.ISysConfigService;
import com.xihuan.system.service.wxServiceImpl.PsyConsultantServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时间槽Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeSlotServiceImpl implements IPsyTimeSlotService {

    private static final Logger logger = LoggerFactory.getLogger(PsyTimeSlotServiceImpl.class);

    @Autowired
    private PsyTimeSlotMapper timeSlotMapper;

    @Autowired
    private PsyTimeCounselorScheduleMapper scheduleMapper;

    @Autowired
    private IPsyTimeRangeService timeRangeService;

    @Autowired
    private PsyConsultantServiceImpl consultantService;

    @Autowired
    private IPsyTimeScheduleTemplateService templateService;

    /**
     * 查询时间槽列表
     */
    @Override
    public List<PsyTimeSlot> selectTimeSlotList(PsyTimeSlot timeSlot) {
        return timeSlotMapper.selectTimeSlotList(timeSlot);
    }

    /**
     * 根据ID查询时间槽
     */
    @Override
    public PsyTimeSlot selectTimeSlotById(Long id) {
        return timeSlotMapper.selectTimeSlotById(id);
    }

    /**
     * 查询咨询师在指定日期范围内的时间槽
     */
    @Override
    public List<PsyTimeSlot> selectSlotsByCounselorAndDateRange(Long counselorId, LocalDate startDate, LocalDate endDate) {
        return timeSlotMapper.selectSlotsByCounselorAndDateRange(
            counselorId, 
            startDate.toString(), 
            endDate.toString()
        );
    }

    /**
     * 查询指定日期的可用时间槽
     */
    @Override
    public List<PsyTimeSlot> selectAvailableSlotsByDate(LocalDate date, Long centerId, Long counselorId) {
        return timeSlotMapper.selectAvailableSlotsByDate(date.toString(), centerId, counselorId);
    }

    /**
     * 查询公开时间槽
     */
    @Override
    public List<PsyTimeSlot> selectPublicSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        return timeSlotMapper.selectPublicSlots(startDate.toString(), endDate.toString(), centerId);
    }

    /**
     * 获取格式化的时间槽数据（按日期和时间段分组）
     */
    @Override
    public List<PsyTimeSlotDTO> getFormattedTimeSlots(Long counselorId, LocalDate startDate, LocalDate endDate) {
        List<PsyTimeSlot> slots = selectSlotsByCounselorAndDateRange(counselorId, startDate, endDate);
        
        if (CollectionUtils.isEmpty(slots)) {
            return new ArrayList<>();
        }

        // 按日期分组
        Map<String, List<PsyTimeSlot>> slotsByDate = slots.stream()
            .collect(Collectors.groupingBy(PsyTimeSlot::getDateKey));

        List<PsyTimeSlotDTO> result = new ArrayList<>();
        
        for (Map.Entry<String, List<PsyTimeSlot>> entry : slotsByDate.entrySet()) {
            String dateKey = entry.getKey();
            List<PsyTimeSlot> daySlots = entry.getValue();
            
            PsyTimeSlotDTO dayDTO = buildDayDTO(dateKey, daySlots);
            result.add(dayDTO);
        }

        // 按日期排序
        result.sort(Comparator.comparing(PsyTimeSlotDTO::getDate));
        return result;
    }

    /**
     * 新增时间槽
     */
    @Override
    public int insertTimeSlot(PsyTimeSlot timeSlot) {
        timeSlot.setCreateTime(DateUtils.getNowDate());
        return timeSlotMapper.insertTimeSlot(timeSlot);
    }

    /**
     * 批量新增时间槽
     */
    @Override
    @Transactional
    public int batchInsertTimeSlots(List<PsyTimeSlot> timeSlots) {
        if (CollectionUtils.isEmpty(timeSlots)) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        timeSlots.forEach(slot -> slot.setCreateTime(now));
        
        return timeSlotMapper.batchInsertTimeSlots(timeSlots);
    }

    /**
     * 修改时间槽
     */
    @Override
    public int updateTimeSlot(PsyTimeSlot timeSlot) {
        timeSlot.setUpdateTime(DateUtils.getNowDate());
        return timeSlotMapper.updateTimeSlot(timeSlot);
    }

    /**
     * 批量更新时间槽状态
     */
    @Override
    public int batchUpdateSlotStatus(List<Long> slotIds, Integer status) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return 0;
        }
        return timeSlotMapper.batchUpdateSlotStatus(slotIds, status);
    }

    /**
     * 删除时间槽信息
     */
    @Override
    public int deleteTimeSlotById(Long id) {
        return timeSlotMapper.deleteTimeSlotById(id);
    }

    /**
     * 批量删除时间槽
     */
    @Override
    public int deleteTimeSlotByIds(Long[] ids) {
        return timeSlotMapper.deleteTimeSlotByIds(ids);
    }

    /**
     * 为咨询师生成指定日期范围的时间槽
     */
    @Override
    @Transactional
    public int generateSlotsForCounselor(Long counselorId, LocalDate startDate, LocalDate endDate) {
        int totalGenerated = 0;

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            totalGenerated += generateSlotsForCounselorOnDate(counselorId, currentDate);
            currentDate = currentDate.plusDays(1);
        }

        return totalGenerated;
    }

    /**
     * 重新生成咨询师指定日期范围的时间槽（先清理再生成）
     */
    @Override
    @Transactional
    public int regenerateSlotsForCounselor(Long counselorId, LocalDate startDate, LocalDate endDate) {
        logger.info("重新生成咨询师 {} 的时间槽，日期范围：{} 到 {}", counselorId, startDate, endDate);

        try {
            // 先删除指定日期范围的时间槽
            int deletedCount = timeSlotMapper.deleteSlotsByDateRangeAndCounselor(
                counselorId, startDate.toString(), endDate.toString());

            if (deletedCount > 0) {
                logger.info("删除了咨询师 {} 在 {} 到 {} 的 {} 个旧时间槽",
                    counselorId, startDate, endDate, deletedCount);
            }

            // 重新生成
            int generatedCount = generateSlotsForCounselor(counselorId, startDate, endDate);

            logger.info("为咨询师 {} 重新生成了 {} 个时间槽", counselorId, generatedCount);
            return generatedCount;

        } catch (Exception e) {
            logger.error("重新生成咨询师 {} 时间槽失败", counselorId, e);
            throw e;
        }
    }

    /**
     * 为所有咨询师生成指定日期范围的时间槽
     */
    @Override
    @Transactional
    public int generateSlotsForAllCounselors(LocalDate startDate, LocalDate endDate) {
        logger.info("开始为所有咨询师生成时间槽，日期范围：{} 到 {}", startDate, endDate);

        try {
            // 获取所有可用的咨询师
            List<PsyConsultant> consultants = consultantService.listAllAvailableConsultants();

            if (CollectionUtils.isEmpty(consultants)) {
                logger.warn("没有找到可用的咨询师，无法生成时间槽");
                return 0;
            }

            logger.info("找到 {} 个可用咨询师", consultants.size());

            int totalGenerated = 0;

            // 为每个咨询师生成时间槽
            for (PsyConsultant consultant : consultants) {
                try {
                    int generated = generateSlotsForCounselor(consultant.getId(), startDate, endDate);
                    totalGenerated += generated;

                    if (generated > 0) {
                        logger.debug("为咨询师 {} (ID: {}) 生成了 {} 个时间槽",
                            consultant.getName(), consultant.getId(), generated);
                    }
                } catch (Exception e) {
                    logger.error("为咨询师 {} (ID: {}) 生成时间槽失败",
                        consultant.getName(), consultant.getId(), e);
                }
            }

            logger.info("为所有咨询师生成时间槽完成，总共生成 {} 个时间槽", totalGenerated);
            return totalGenerated;

        } catch (Exception e) {
            logger.error("为所有咨询师生成时间槽失败", e);
            return 0;
        }
    }

    /**
     * 为所有咨询师重新生成指定日期范围的时间槽（先清理再生成）
     */
    @Override
    @Transactional
    public int regenerateSlotsForAllCounselors(LocalDate startDate, LocalDate endDate) {
        logger.info("开始为所有咨询师重新生成时间槽，日期范围：{} 到 {}", startDate, endDate);

        try {
            // 获取所有启用状态的咨询师
            List<ConsultantSimpleVO> consultants = consultantService.listAllSimple();

            if (consultants == null || consultants.isEmpty()) {
                logger.warn("没有找到可用的咨询师，无法重新生成时间槽");
                return 0;
            }

            int totalGenerated = 0;
            int failedCount = 0;

            for (ConsultantSimpleVO consultant : consultants) {
                try {
                    int count = regenerateSlotsForCounselor(consultant.getId(), startDate, endDate);
                    totalGenerated += count;
                    logger.debug("为咨询师 {} (ID: {}) 重新生成了 {} 个时间槽",
                        consultant.getName(), consultant.getId(), count);
                } catch (Exception e) {
                    failedCount++;
                    logger.error("为咨询师 {} (ID: {}) 重新生成时间槽失败",
                        consultant.getName(), consultant.getId(), e);
                }
            }

            if (failedCount > 0) {
                logger.warn("为 {} 个咨询师中的 {} 个重新生成时间槽失败", consultants.size(), failedCount);
            }

            logger.info("为所有咨询师重新生成时间槽完成，总共生成 {} 个时间槽", totalGenerated);
            return totalGenerated;

        } catch (Exception e) {
            logger.error("为所有咨询师重新生成时间槽失败", e);
            return 0;
        }
    }

    /**
     * 清理过期的时间槽
     */
    @Override
    @Transactional
    public int cleanExpiredSlots(LocalDate beforeDate) {
        return timeSlotMapper.deleteSlotsByDateRange("1900-01-01", beforeDate.toString(), null);
    }

    /**
     * 更新过期时间槽的状态
     */
    @Override
    @Transactional
    public int updateExpiredSlotStatus() {
        // 查询状态为可用的时间槽
        PsyTimeSlot query = new PsyTimeSlot();
        query.setStatus(0); // 可用状态

        List<PsyTimeSlot> slots = selectTimeSlotList(query);

        if (CollectionUtils.isEmpty(slots)) {
            return 0;
        }

        LocalDateTime now = LocalDateTime.now();
        List<Long> expiredSlotIds = slots.stream()
            .filter(slot -> {
                try {
                    LocalDate slotDate = LocalDate.parse(slot.getDateKey());
                    LocalDateTime slotDateTime = LocalDateTime.of(slotDate, slot.getEndTime());
                    // 时间槽结束时间已过，则认为过期
                    return slotDateTime.isBefore(now);
                } catch (Exception e) {
                    // 解析失败的时间槽，按今天之前处理
                    return LocalDate.parse(slot.getDateKey()).isBefore(now.toLocalDate());
                }
            })
            .map(PsyTimeSlot::getId)
            .collect(Collectors.toList());

        if (!expiredSlotIds.isEmpty()) {
            logger.info("更新过期时间槽状态：找到 {} 个过期时间槽", expiredSlotIds.size());
            return batchUpdateSlotStatus(expiredSlotIds, 2); // 设置为已过期
        }

        return 0;
    }

    /**
     * 更新过期时间槽的状态（支持延后过期配置）
     */
    @Override
    @Transactional
    public int updateExpiredSlotStatusWithDelay() {
        // 查询状态为可用的时间槽
        PsyTimeSlot query = new PsyTimeSlot();
        query.setStatus(0); // 可用状态

        List<PsyTimeSlot> slots = selectTimeSlotList(query);

        if (CollectionUtils.isEmpty(slots)) {
            return 0;
        }

        // 获取延后配置
        boolean isDelayEnabled = isDelayExpirationEnabled();
        int delayHours = getDelayExpirationHours();

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime cutoffTime = isDelayEnabled ? now.minusHours(delayHours) : now;

        List<Long> expiredSlotIds = slots.stream()
            .filter(slot -> {
                try {
                    LocalDate slotDate = LocalDate.parse(slot.getDateKey());
                    LocalDateTime slotDateTime = LocalDateTime.of(slotDate, slot.getEndTime());
                    return slotDateTime.isBefore(cutoffTime);
                } catch (Exception e) {
                    // 解析失败的时间槽，按今天之前处理
                    return LocalDate.parse(slot.getDateKey()).isBefore(now.toLocalDate());
                }
            })
            .map(PsyTimeSlot::getId)
            .collect(Collectors.toList());

        if (!expiredSlotIds.isEmpty()) {
//            logger.info("更新过期时间槽状态，延后配置：{}，延后小时数：{}，过期时间槽数量：{}",
//                isDelayEnabled, delayHours, expiredSlotIds.size());
            return batchUpdateSlotStatus(expiredSlotIds, 2); // 设置为已过期
        }

        return 0;
    }

    /**
     * 检查是否启用延后过期功能
     */
    private boolean isDelayExpirationEnabled() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
            return "true".equalsIgnoreCase(configValue) || "1".equals(configValue);
        } catch (Exception e) {
//            logger.warn("获取延后过期配置失败，使用默认值：false", e);
            return false;
        }
    }

    /**
     * 获取延后过期的小时数
     */
    private int getDelayExpirationHours() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
            return Integer.parseInt(configValue);
        } catch (Exception e) {
//            logger.warn("获取延后过期小时数配置失败，使用默认值：2小时", e);
            return 2;
        }
    }

    /**
     * 为咨询师在指定日期生成时间槽
     * 逻辑：
     * 1. 首先尝试使用咨询师模板的时间点生成
     * 2. 如果模板过期或不存在，使用系统默认时间段（psy_time_range表）生成
     */
    private int generateSlotsForCounselorOnDate(Long counselorId, LocalDate date) {
        // 检查是否已存在时间槽，避免重复生成
        if (hasExistingTimeSlots(counselorId, date)) {
            logger.debug("咨询师 {} 在日期 {} 已存在时间槽，跳过生成", counselorId, date);
            return 0;
        }

        // 不再跳过周末，完全根据咨询师排期生成

        // 1. 首先尝试使用咨询师模板生成时间槽
        int templateSlots = generateSlotsFromTemplate(counselorId, date);
        if (templateSlots > 0) {
            logger.info("为咨询师 {} 在日期 {} 使用模板生成了 {} 个时间槽", counselorId, date, templateSlots);
            return templateSlots;
        }

        // 2. 模板不存在或过期，使用系统默认时间段生成
        int defaultSlots = generateSlotsFromSystemTimeRanges(counselorId, date);
        if (defaultSlots > 0) {
            logger.info("为咨询师 {} 在日期 {} 使用系统默认时间段生成了 {} 个时间槽", counselorId, date, defaultSlots);
            return defaultSlots;
        }

        logger.debug("咨询师 {} 在日期 {} 无法生成时间槽", counselorId, date);
        return 0;
    }

    /**
     * 根据排班生成时间槽
     */
    private int generateSlotsFromSchedule(PsyTimeCounselorSchedule schedule) {
        List<PsyTimeSlot> slots = new ArrayList<>();
        
        LocalTime startTime = schedule.getStartTime();
        LocalTime endTime = schedule.getEndTime();
        LocalDate scheduleDate = schedule.getScheduleDate();
        
        // 15分钟间隔生成时间槽
        LocalTime currentTime = startTime;
        while (currentTime.isBefore(endTime)) {
            LocalTime slotEndTime = currentTime.plusMinutes(15);
            if (slotEndTime.isAfter(endTime)) {
                break;
            }
            
            PsyTimeSlot slot = createTimeSlot(schedule, currentTime, slotEndTime, scheduleDate);
            slots.add(slot);
            
            currentTime = slotEndTime;
        }
        
        return batchInsertTimeSlots(slots);
    }

    /**
     * 创建时间槽对象
     */
    private PsyTimeSlot createTimeSlot(PsyTimeCounselorSchedule schedule, LocalTime startTime, LocalTime endTime, LocalDate date) {
        PsyTimeSlot slot = new PsyTimeSlot();
        slot.setScheduleId(schedule.getId());
        slot.setIsPublic(0);
        slot.setCenterId(schedule.getCenterId());
        slot.setCounselorId(schedule.getCounselorId());
        slot.setDateKey(date.toString());
        slot.setWeekDay(getWeekDay(date));
        slot.setStartTime(startTime);
        slot.setEndTime(endTime);
        slot.setStatus(0); // 可用
        slot.setDelFlag(0);

        // 智能设置时间段ID：优先根据咨询师排班，后备使用时间段表
        PsyTimeRange timeRange = findBestMatchingTimeRange(startTime, endTime);
        if (timeRange != null) {
            slot.setRangeId(timeRange.getId());
        }

        // 生成时间组哈希
        slot.setTimeGroupHash(generateTimeGroupHash(schedule.getId(), startTime));

        return slot;
    }

    /**
     * 智能匹配最合适的时间段
     * 优先考虑时间槽的实际时间范围，而不仅仅是开始时间
     */
    private PsyTimeRange findBestMatchingTimeRange(LocalTime slotStartTime, LocalTime slotEndTime) {
        try {
            // 获取所有有效的时间段
            List<PsyTimeRange> allTimeRanges = timeRangeService.selectAllActiveTimeRanges();
            if (allTimeRanges == null || allTimeRanges.isEmpty()) {
                return null;
            }

            // 计算时间槽的中点时间，用于更准确的匹配
            int slotStartMinutes = slotStartTime.getHour() * 60 + slotStartTime.getMinute();
            int slotEndMinutes = slotEndTime.getHour() * 60 + slotEndTime.getMinute();
            int slotMidMinutes = (slotStartMinutes + slotEndMinutes) / 2;
            int slotMidHour = slotMidMinutes / 60;

            // 寻找最匹配的时间段
            PsyTimeRange bestMatch = null;
            for (PsyTimeRange timeRange : allTimeRanges) {
                if (timeRange.getStartHour() != null && timeRange.getEndHour() != null) {
                    // 检查时间槽的中点是否在时间段范围内
                    if (slotMidHour >= timeRange.getStartHour() && slotMidHour < timeRange.getEndHour()) {
                        bestMatch = timeRange;
                        break;
                    }
                }
            }

            // 如果没有找到精确匹配，使用开始时间进行匹配（兼容原有逻辑）
            if (bestMatch == null) {
                bestMatch = timeRangeService.selectTimeRangeByHour(slotStartTime.getHour());
            }

            return bestMatch;

        } catch (Exception e) {
            logger.warn("匹配时间段失败，使用默认逻辑: {}", e.getMessage());
            // 出错时回退到原有逻辑
            return timeRangeService.selectTimeRangeByHour(slotStartTime.getHour());
        }
    }

    /**
     * 构建日期DTO
     */
    private PsyTimeSlotDTO buildDayDTO(String dateKey, List<PsyTimeSlot> daySlots) {
        PsyTimeSlotDTO dayDTO = new PsyTimeSlotDTO();
        LocalDate date = LocalDate.parse(dateKey);
        
        dayDTO.setDate(dateKey);
        dayDTO.setWeekDay(getWeekDay(date));
        dayDTO.setIsToday(date.equals(LocalDate.now()));
        
        // 按时间段分组
        Map<Long, List<PsyTimeSlot>> slotsByRange = daySlots.stream()
            .collect(Collectors.groupingBy(PsyTimeSlot::getRangeId));
        
        List<PsyTimeSlotDTO.TimeRangeDTO> timeRanges = new ArrayList<>();
        for (Map.Entry<Long, List<PsyTimeSlot>> entry : slotsByRange.entrySet()) {
            PsyTimeSlotDTO.TimeRangeDTO rangeDTO = buildTimeRangeDTO(entry.getKey(), entry.getValue());
            timeRanges.add(rangeDTO);
        }
        
        dayDTO.setTimeRanges(timeRanges);
        return dayDTO;
    }

    /**
     * 构建时间段DTO
     */
    private PsyTimeSlotDTO.TimeRangeDTO buildTimeRangeDTO(Long rangeId, List<PsyTimeSlot> rangeSlots) {
        PsyTimeSlotDTO.TimeRangeDTO rangeDTO = new PsyTimeSlotDTO.TimeRangeDTO();
        
        // 获取时间段信息
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeById(rangeId);
        if (timeRange != null) {
            rangeDTO.setRangeName(timeRange.getName());
            rangeDTO.setIconUrl(timeRange.getIconUrl());
        }
        
        // 转换时间槽
        List<PsyTimeSlotDTO.SlotDTO> slotDTOs = rangeSlots.stream()
            .map(this::convertToSlotDTO)
            .sorted(Comparator.comparing(PsyTimeSlotDTO.SlotDTO::getStartTime))
            .collect(Collectors.toList());
        
        rangeDTO.setSlots(slotDTOs);
        return rangeDTO;
    }

    /**
     * 转换为时间槽DTO
     */
    private PsyTimeSlotDTO.SlotDTO convertToSlotDTO(PsyTimeSlot slot) {
        PsyTimeSlotDTO.SlotDTO slotDTO = new PsyTimeSlotDTO.SlotDTO();
        slotDTO.setSlotId(slot.getId());
        slotDTO.setStartTime(slot.getStartTime());
        slotDTO.setEndTime(slot.getEndTime());
        slotDTO.setTimeDisplay(slot.getStartTime() + "-" + slot.getEndTime());
        slotDTO.setStatus(slot.getStatus());
        slotDTO.setAvailable(slot.getStatus() == 0);
        slotDTO.setCounselorId(slot.getCounselorId());
        
        // 设置状态描述
        switch (slot.getStatus()) {
            case 0:
                slotDTO.setStatusText("可预约");
                break;
            case 1:
                slotDTO.setStatusText("已预约");
                break;
            case 2:
                slotDTO.setStatusText("已过期");
                break;
            default:
                slotDTO.setStatusText("未知");
        }
        
        return slotDTO;
    }

    /**
     * 获取星期几
     */
    private String getWeekDay(LocalDate date) {
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        return weekDays[date.getDayOfWeek().getValue() - 1];
    }

    /**
     * 生成时间组哈希
     */
    private String generateTimeGroupHash(Long scheduleId, LocalTime startTime) {
        String input = scheduleId + "_" + startTime.toSecondOfDay();
        return Integer.toHexString(input.hashCode());
    }

    /**
     * 检查咨询师在指定日期是否已存在时间槽
     */
    private boolean hasExistingTimeSlots(Long counselorId, LocalDate date) {
        try {
            PsyTimeSlot querySlot = new PsyTimeSlot();
            querySlot.setCounselorId(counselorId);
            querySlot.setDateKey(date.toString());
            querySlot.setDelFlag(0);

            List<PsyTimeSlot> existingSlots = timeSlotMapper.selectTimeSlotList(querySlot);
            boolean hasSlots = existingSlots != null && !existingSlots.isEmpty();

            if (hasSlots) {
                logger.debug("咨询师 {} 在日期 {} 已存在 {} 个时间槽", counselorId, date, existingSlots.size());
            }

            return hasSlots;
        } catch (Exception e) {
            logger.warn("检查咨询师 {} 在日期 {} 的时间槽时发生错误: {}", counselorId, date, e.getMessage());
            return false; // 出错时假设不存在，允许生成
        }
    }

    /**
     * 使用咨询师模板生成时间槽
     */
    private int generateSlotsFromTemplate(Long counselorId, LocalDate date) {
        try {
            // 查询咨询师在该日期的有效模板
            PsyTimeScheduleTemplate template = templateService.selectEffectiveTemplate(counselorId, date);
            if (template == null) {
                // 尝试获取默认模板
                template = templateService.selectDefaultTemplateByCounselorId(counselorId);
                if (template == null) {
                    logger.debug("咨询师 {} 没有可用的排班模板", counselorId);
                    return 0;
                }
            }

            // 获取当前日期是星期几 (1=周一, 7=周日)
            int dayOfWeek = date.getDayOfWeek().getValue();

            // 查找匹配当前星期的模板明细
            List<PsyTimeTemplateItem> dayItems = template.getTemplateItems().stream()
                .filter(item -> item.getDayOfWeek() != null && item.getDayOfWeek().equals(dayOfWeek))
                .filter(item -> item.getDelFlag() == null || item.getDelFlag() == 0)
                .sorted((a, b) -> {
                    if (a.getStartTime() == null) return 1;
                    if (b.getStartTime() == null) return -1;
                    return a.getStartTime().compareTo(b.getStartTime());
                })
                .collect(java.util.stream.Collectors.toList());

            if (dayItems.isEmpty()) {
                logger.debug("模板中没有找到星期 {} 的排班设置", dayOfWeek);
                return 0;
            }

            // 根据模板明细生成时间槽
            List<PsyTimeSlot> slots = new ArrayList<>();
            for (PsyTimeTemplateItem item : dayItems) {
                List<PsyTimeSlot> itemSlots = generateSlotsFromTemplateItem(counselorId, date, item);
                slots.addAll(itemSlots);
            }

            return batchInsertTimeSlots(slots);

        } catch (Exception e) {
            logger.error("使用模板为咨询师 {} 在日期 {} 生成时间槽失败: {}", counselorId, date, e.getMessage());
            return 0;
        }
    }

    /**
     * 使用系统默认时间段生成时间槽
     */
    private int generateSlotsFromSystemTimeRanges(Long counselorId, LocalDate date) {
        try {
            // 获取所有有效的系统时间段
            List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();
            if (timeRanges.isEmpty()) {
                logger.warn("系统中没有配置时间段，无法生成默认时间槽");
                return 0;
            }

            // 根据系统时间段生成时间槽
            List<PsyTimeSlot> slots = new ArrayList<>();
            for (PsyTimeRange timeRange : timeRanges) {
                List<PsyTimeSlot> rangeSlots = generateSlotsFromTimeRange(counselorId, date, timeRange);
                slots.addAll(rangeSlots);
            }

            return batchInsertTimeSlots(slots);

        } catch (Exception e) {
            logger.error("使用系统时间段为咨询师 {} 在日期 {} 生成时间槽失败: {}", counselorId, date, e.getMessage());
            return 0;
        }
    }

    /**
     * 为指定日期创建默认排班（仅工作日）
     */
    private PsyTimeCounselorSchedule createDefaultScheduleForDate(Long counselorId, LocalDate date) {
        // 检查是否为工作日（周一到周五）
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        if (dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) {
            logger.debug("日期 {} 是周末，不创建默认排班", date);
            return null; // 周末不创建默认排班
        }

        try {
            // 创建默认工作日排班（不保存到数据库，仅用于时间槽生成）
            PsyTimeCounselorSchedule defaultSchedule = new PsyTimeCounselorSchedule();
            defaultSchedule.setCounselorId(counselorId);
            defaultSchedule.setScheduleDate(date); // 使用正确的字段名
            defaultSchedule.setIsWorking(1);

            // 使用系统默认工作时间：9:00-21:00
            defaultSchedule.setStartTime(getSystemDefaultStartTime());
            defaultSchedule.setEndTime(getSystemDefaultEndTime());

            logger.info("为咨询师 {} 在日期 {} 创建默认排班 {}:{} - {}:{}",
                counselorId, date,
                defaultSchedule.getStartTime().getHour(), defaultSchedule.getStartTime().getMinute(),
                defaultSchedule.getEndTime().getHour(), defaultSchedule.getEndTime().getMinute());

            return defaultSchedule;

        } catch (Exception e) {
            logger.error("为咨询师 {} 在日期 {} 创建默认排班失败: {}", counselorId, date, e.getMessage());
            return null;
        }
    }

    /**
     * 获取系统默认开始时间
     */
    private LocalTime getSystemDefaultStartTime() {
        try {
            // 可以从系统配置中读取，这里先使用硬编码
            // String configValue = configService.selectConfigByKey("schedule.default.start.time");
            // if (StringUtils.isNotEmpty(configValue)) {
            //     return LocalTime.parse(configValue);
            // }
            return LocalTime.of(9, 0); // 默认9:00开始
        } catch (Exception e) {
            logger.warn("获取系统默认开始时间失败，使用硬编码默认值: {}", e.getMessage());
            return LocalTime.of(9, 0);
        }
    }

    /**
     * 获取系统默认结束时间
     */
    private LocalTime getSystemDefaultEndTime() {
        try {
            // 可以从系统配置中读取，这里先使用硬编码
            // String configValue = configService.selectConfigByKey("schedule.default.end.time");
            // if (StringUtils.isNotEmpty(configValue)) {
            //     return LocalTime.parse(configValue);
            // }
            return LocalTime.of(21, 0); // 默认21:00结束，包含晚上时间段
        } catch (Exception e) {
            logger.warn("获取系统默认结束时间失败，使用硬编码默认值: {}", e.getMessage());
            return LocalTime.of(21, 0);
        }
    }

    /**
     * 根据模板明细生成时间槽
     */
    private List<PsyTimeSlot> generateSlotsFromTemplateItem(Long counselorId, LocalDate date, PsyTimeTemplateItem item) {
        List<PsyTimeSlot> slots = new ArrayList<>();

        LocalTime startTime = item.getStartTime();
        LocalTime endTime = item.getEndTime();

        if (startTime == null || endTime == null) {
            return slots;
        }

        // 每15分钟生成一个时间槽
        LocalTime current = startTime;
        while (current.isBefore(endTime)) {
            LocalTime slotEnd = current.plusMinutes(15);
            if (slotEnd.isAfter(endTime)) {
                break;
            }

            PsyTimeSlot slot = createTimeSlotForTemplate(counselorId, date, current, slotEnd, null);
            if (slot != null) {
                slots.add(slot);
            }

            current = slotEnd;
        }

        return slots;
    }

    /**
     * 根据系统时间段生成时间槽
     */
    private List<PsyTimeSlot> generateSlotsFromTimeRange(Long counselorId, LocalDate date, PsyTimeRange timeRange) {
        List<PsyTimeSlot> slots = new ArrayList<>();

        // 将小时转换为具体时间
        LocalTime startTime = LocalTime.of(timeRange.getStartHour(), 0);
        LocalTime endTime = LocalTime.of(timeRange.getEndHour(), 0);

        // 每15分钟生成一个时间槽
        LocalTime current = startTime;
        while (current.isBefore(endTime)) {
            LocalTime slotEnd = current.plusMinutes(15);
            if (slotEnd.isAfter(endTime)) {
                break;
            }

            PsyTimeSlot slot = createTimeSlotForTemplate(counselorId, date, current, slotEnd, timeRange.getId());
            if (slot != null) {
                slots.add(slot);
            }

            current = slotEnd;
        }

        return slots;
    }

    /**
     * 为模板和系统时间段创建时间槽对象
     */
    private PsyTimeSlot createTimeSlotForTemplate(Long counselorId, LocalDate date, LocalTime startTime, LocalTime endTime, Long rangeId) {
        PsyTimeSlot slot = new PsyTimeSlot();
        slot.setCounselorId(counselorId);
        slot.setDateKey(date.toString());
        slot.setWeekDay(getWeekDay(date));
        slot.setStartTime(startTime);
        slot.setEndTime(endTime);
        slot.setStatus(0); // 可用
        slot.setDelFlag(0);
        slot.setIsPublic(0);

        // 设置时间段ID
        if (rangeId != null) {
            slot.setRangeId(rangeId);
        } else {
            // 智能匹配时间段
            PsyTimeRange timeRange = findBestMatchingTimeRange(startTime, endTime);
            if (timeRange != null) {
                slot.setRangeId(timeRange.getId());
            }
        }

        // 生成时间组哈希（使用咨询师ID代替排班ID）
        slot.setTimeGroupHash(generateTimeGroupHashForCounselor(counselorId, startTime));

        return slot;
    }

    /**
     * 为咨询师生成时间组哈希
     */
    private String generateTimeGroupHashForCounselor(Long counselorId, LocalTime startTime) {
        try {
            String input = counselorId + "_" + startTime.toString();
            return String.valueOf(input.hashCode());
        } catch (Exception e) {
            logger.warn("生成时间组哈希失败: {}", e.getMessage());
            return String.valueOf(System.currentTimeMillis());
        }
    }
}
