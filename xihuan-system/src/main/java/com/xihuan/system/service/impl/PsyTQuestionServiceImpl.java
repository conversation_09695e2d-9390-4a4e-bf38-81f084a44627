package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTQuestion;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.system.mapper.PsyTQuestionMapper;
import com.xihuan.system.service.IPsyTQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 题目Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTQuestionServiceImpl implements IPsyTQuestionService {
    
    @Autowired
    private PsyTQuestionMapper questionMapper;

    /**
     * 查询题目
     * 
     * @param id 题目主键
     * @return 题目
     */
    @Override
    public PsyTQuestion selectQuestionById(Long id) {
        return questionMapper.selectQuestionById(id);
    }

    /**
     * 查询题目列表
     * 
     * @param question 题目
     * @return 题目
     */
    @Override
    public List<PsyTQuestion> selectQuestionList(PsyTQuestion question) {
        return questionMapper.selectQuestionList(question);
    }

    /**
     * 新增题目
     * 
     * @param question 题目
     * @return 结果
     */
    @Override
    public int insertQuestion(PsyTQuestion question) {
        question.setCreateBy(SecurityUtils.getUsername());
        question.setCreateTime(DateUtils.getNowDate());
        question.setDelFlag("0");
        
        // 如果没有设置显示顺序，自动设置为最大值+1
        if (question.getOrderNum() == null) {
            Integer maxOrder = questionMapper.selectMaxOrderNum(question.getScaleId());
            question.setOrderNum(maxOrder + 1);
        }
        
        return questionMapper.insertQuestion(question);
    }

    /**
     * 修改题目
     * 
     * @param question 题目
     * @return 结果
     */
    @Override
    public int updateQuestion(PsyTQuestion question) {
        question.setUpdateBy(SecurityUtils.getUsername());
        question.setUpdateTime(DateUtils.getNowDate());
        return questionMapper.updateQuestion(question);
    }

    /**
     * 批量删除题目
     * 
     * @param ids 需要删除的题目主键
     * @return 结果
     */
    @Override
    public int deleteQuestionByIds(Long[] ids) {
        return questionMapper.deleteQuestionByIds(ids);
    }

    /**
     * 删除题目信息
     * 
     * @param id 题目主键
     * @return 结果
     */
    @Override
    public int deleteQuestionById(Long id) {
        return questionMapper.deleteQuestionById(id);
    }

    /**
     * 根据量表ID查询题目列表
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    @Override
    public List<PsyTQuestion> selectQuestionsByScaleId(Long scaleId) {
        return questionMapper.selectQuestionsByScaleId(scaleId);
    }

    /**
     * 根据量表ID查询题目列表（包含选项信息）
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    @Override
    public List<PsyTQuestion> selectQuestionsWithOptionsByScaleId(Long scaleId) {
        return questionMapper.selectQuestionsWithOptionsByScaleId(scaleId);
    }

    /**
     * 查询题目详情（包含选项信息）
     * 
     * @param id 题目ID
     * @return 题目详情
     */
    @Override
    public PsyTQuestion selectQuestionWithOptions(Long id) {
        return questionMapper.selectQuestionWithOptions(id);
    }

    /**
     * 根据题目序号查询题目
     * 
     * @param scaleId 量表ID
     * @param questionNo 题目序号
     * @return 题目信息
     */
    @Override
    public PsyTQuestion selectQuestionByNo(Long scaleId, Integer questionNo) {
        return questionMapper.selectQuestionByNo(scaleId, questionNo);
    }

    /**
     * 根据量表ID删除所有题目
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    @Override
    public int deleteQuestionsByScaleId(Long scaleId) {
        return questionMapper.deleteQuestionsByScaleId(scaleId);
    }

    /**
     * 批量插入题目
     * 
     * @param questions 题目列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertQuestions(List<PsyTQuestion> questions) {
        if (questions == null || questions.isEmpty()) {
            return 0;
        }
        
        String username = SecurityUtils.getUsername();
        Date now = DateUtils.getNowDate();
        
        for (PsyTQuestion question : questions) {
            question.setCreateBy(username);
            question.setCreateTime(now);
            question.setDelFlag("0");
        }
        
        return questionMapper.batchInsertQuestions(questions);
    }

    /**
     * 查询题目数量
     * 
     * @param scaleId 量表ID
     * @return 题目数量
     */
    @Override
    public int countQuestionsByScaleId(Long scaleId) {
        return questionMapper.countQuestionsByScaleId(scaleId);
    }

    /**
     * 查询启用的题目数量
     * 
     * @param scaleId 量表ID
     * @return 启用题目数量
     */
    @Override
    public int countEnabledQuestionsByScaleId(Long scaleId) {
        return questionMapper.countEnabledQuestionsByScaleId(scaleId);
    }

    /**
     * 更新题目的显示顺序
     * 
     * @param id 题目ID
     * @param orderNum 显示顺序
     * @return 结果
     */
    @Override
    public int updateQuestionOrder(Long id, Integer orderNum) {
        return questionMapper.updateQuestionOrder(id, orderNum);
    }

    /**
     * 查询题目的最大显示顺序
     * 
     * @param scaleId 量表ID
     * @return 最大显示顺序
     */
    @Override
    public Integer selectMaxOrderNum(Long scaleId) {
        Integer maxOrder = questionMapper.selectMaxOrderNum(scaleId);
        return maxOrder != null ? maxOrder : 0;
    }

    /**
     * 查询题目统计信息
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectQuestionStats(Long scaleId) {
        return questionMapper.selectQuestionStats(scaleId);
    }

    /**
     * 复制题目
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int copyQuestions(Long sourceScaleId, Long targetScaleId) {
        // 获取源量表的题目
        List<PsyTQuestion> sourceQuestions = selectQuestionsByScaleId(sourceScaleId);
        
        if (sourceQuestions.isEmpty()) {
            return 0;
        }
        
        // 复制题目
        List<PsyTQuestion> targetQuestions = new ArrayList<>();
        for (PsyTQuestion source : sourceQuestions) {
            PsyTQuestion target = new PsyTQuestion();
            target.setScaleId(targetScaleId);
            target.setQuestionNo(source.getQuestionNo());
            target.setQuestionText(source.getQuestionText());
            target.setQuestionType(source.getQuestionType());
            target.setIsRequired(source.getIsRequired());
            target.setOrderNum(source.getOrderNum());
            target.setSubscaleId(source.getSubscaleId());
            target.setStatus(source.getStatus());
            targetQuestions.add(target);
        }
        
        return batchInsertQuestions(targetQuestions);
    }

    /**
     * 导入题目
     * 
     * @param scaleId 量表ID
     * @param questions 题目列表
     * @return 导入结果
     */
    @Override
    @Transactional
    public Map<String, Object> importQuestions(Long scaleId, List<PsyTQuestion> questions) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 先删除现有题目
            deleteQuestionsByScaleId(scaleId);
            
            // 设置量表ID
            for (PsyTQuestion question : questions) {
                question.setScaleId(scaleId);
            }
            
            // 批量插入
            int count = batchInsertQuestions(questions);
            
            result.put("success", true);
            result.put("count", count);
            result.put("message", "成功导入 " + count + " 个题目");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "导入失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 导出题目
     * 
     * @param scaleId 量表ID
     * @return 题目列表
     */
    @Override
    public List<PsyTQuestion> exportQuestions(Long scaleId) {
        return selectQuestionsByScaleId(scaleId);
    }

    /**
     * 验证题目配置的完整性
     * 
     * @param scaleId 量表ID
     * @return 验证结果
     */
    @Override
    public Map<String, Object> validateQuestionConfig(Long scaleId) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 检查题目数量
        int questionCount = countEnabledQuestionsByScaleId(scaleId);
        if (questionCount == 0) {
            errors.add("量表没有题目");
        } else if (questionCount < 5) {
            warnings.add("题目数量较少，建议至少5个题目");
        }
        
        // 检查题目内容
        List<PsyTQuestion> questions = selectQuestionsByScaleId(scaleId);
        for (PsyTQuestion question : questions) {
            if (question.getQuestionText() == null || question.getQuestionText().trim().isEmpty()) {
                errors.add("题目 " + question.getQuestionNo() + " 内容为空");
            }
            if (question.getQuestionType() == null) {
                errors.add("题目 " + question.getQuestionNo() + " 类型未设置");
            }
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        
        return result;
    }

    /**
     * 自动生成题目序号
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int generateQuestionNumbers(Long scaleId) {
        List<PsyTQuestion> questions = selectQuestionsByScaleId(scaleId);
        
        if (questions.isEmpty()) {
            return 0;
        }
        
        // 按显示顺序排序
        questions.sort(Comparator.comparing(PsyTQuestion::getOrderNum));
        
        int count = 0;
        for (int i = 0; i < questions.size(); i++) {
            PsyTQuestion question = questions.get(i);
            int newQuestionNo = i + 1;
            if (newQuestionNo != question.getQuestionNo()) {
                question.setQuestionNo(newQuestionNo);
                updateQuestion(question);
                count++;
            }
        }
        
        return count;
    }

    /**
     * 重新排序题目
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int reorderQuestions(Long scaleId) {
        List<PsyTQuestion> questions = selectQuestionsByScaleId(scaleId);
        
        if (questions.isEmpty()) {
            return 0;
        }
        
        // 按当前顺序重新编号
        questions.sort(Comparator.comparing(PsyTQuestion::getOrderNum));
        
        int count = 0;
        for (int i = 0; i < questions.size(); i++) {
            PsyTQuestion question = questions.get(i);
            int newOrder = (i + 1) * 10; // 10, 20, 30...
            if (newOrder != question.getOrderNum()) {
                updateQuestionOrder(question.getId(), newOrder);
                count++;
            }
        }
        
        return count;
    }

    /**
     * 更新题目状态
     * 
     * @param id 题目ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateQuestionStatus(Long id, Integer status) {
        return questionMapper.updateQuestionStatus(id, status);
    }

    /**
     * 批量更新题目状态
     * 
     * @param ids 题目ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int batchUpdateQuestionStatus(Long[] ids, Integer status) {
        return questionMapper.batchUpdateQuestionStatus(ids, status);
    }

    /**
     * 根据分量表查询题目
     * 
     * @param subscaleId 分量表ID
     * @return 题目集合
     */
    @Override
    public List<PsyTQuestion> selectQuestionsBySubscaleId(Long subscaleId) {
        return questionMapper.selectQuestionsBySubscaleId(subscaleId);
    }

    /**
     * 查询必答题目
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    @Override
    public List<PsyTQuestion> selectRequiredQuestions(Long scaleId) {
        return questionMapper.selectRequiredQuestions(scaleId);
    }

    /**
     * 查询选答题目
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    @Override
    public List<PsyTQuestion> selectOptionalQuestions(Long scaleId) {
        return questionMapper.selectOptionalQuestions(scaleId);
    }

    /**
     * 随机获取题目
     * 
     * @param scaleId 量表ID
     * @param count 题目数量
     * @return 题目集合
     */
    @Override
    public List<PsyTQuestion> selectRandomQuestions(Long scaleId, Integer count) {
        return questionMapper.selectRandomQuestions(scaleId, count);
    }
}
