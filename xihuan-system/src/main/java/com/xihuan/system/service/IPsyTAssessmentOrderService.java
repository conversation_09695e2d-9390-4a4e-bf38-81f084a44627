package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTAssessmentOrder;

import java.util.List;
import java.util.Map;

/**
 * 测评订单Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTAssessmentOrderService {
    
    /**
     * 查询测评订单列表
     * 
     * @param order 测评订单
     * @return 测评订单集合
     */
    List<PsyTAssessmentOrder> selectOrderList(PsyTAssessmentOrder order);

    /**
     * 根据测评订单ID查询详细信息
     * 
     * @param id 测评订单ID
     * @return 测评订单
     */
    PsyTAssessmentOrder selectOrderById(Long id);

    /**
     * 根据订单编号查询测评订单
     * 
     * @param orderNo 订单编号
     * @return 测评订单
     */
    PsyTAssessmentOrder selectOrderByOrderNo(String orderNo);

    /**
     * 查询订单详情（包含量表、用户等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    PsyTAssessmentOrder selectOrderWithDetails(Long id);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectOrdersByUserId(Long userId);

    /**
     * 根据量表ID查询订单列表
     * 
     * @param scaleId 量表ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectOrdersByScaleId(Long scaleId);

    /**
     * 新增测评订单
     * 
     * @param order 测评订单
     * @return 结果
     */
    int insertOrder(PsyTAssessmentOrder order);

    /**
     * 修改测评订单
     * 
     * @param order 测评订单
     * @return 结果
     */
    int updateOrder(PsyTAssessmentOrder order);

    /**
     * 批量删除测评订单
     * 
     * @param ids 需要删除的测评订单ID
     * @return 结果
     */
    int deleteOrderByIds(Long[] ids);

    /**
     * 删除测评订单信息
     * 
     * @param id 测评订单ID
     * @return 结果
     */
    int deleteOrderById(Long id);

    /**
     * 检查用户是否已购买量表
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 是否已购买
     */
    boolean checkUserPurchased(Long userId, Long scaleId);

    /**
     * 创建订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 订单信息
     */
    PsyTAssessmentOrder createOrder(Long userId, Long scaleId);

    /**
     * 确认支付
     * 
     * @param orderNo 订单编号
     * @param transactionId 交易ID
     * @return 结果
     */
    int confirmPayment(String orderNo, String transactionId);

    /**
     * 取消订单
     * 
     * @param id 订单ID
     * @param reason 取消原因
     * @return 结果
     */
    int cancelOrder(Long id, String reason);

    /**
     * 申请退款
     * 
     * @param id 订单ID
     * @param reason 退款原因
     * @return 结果
     */
    int applyRefund(Long id, String reason);

    /**
     * 处理退款
     * 
     * @param id 订单ID
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @return 结果
     */
    int processRefund(Long id, java.math.BigDecimal refundAmount, String reason);

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 状态
     * @return 结果
     */
    int updateOrderStatus(Long id, Integer status);

    /**
     * 查询订单统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectOrderStats();

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserOrderStats(Long userId);

    /**
     * 查询量表订单统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleOrderStats(Long scaleId);

    /**
     * 搜索订单
     * 
     * @param keyword 关键词
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param status 状态
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> searchOrders(String keyword, Long userId, Long scaleId, Integer status);

    /**
     * 查询热销量表排行
     * 
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectHotScaleRanking(Integer limit);

    /**
     * 查询用户消费排行
     * 
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectUserConsumptionRanking(Integer limit);

    /**
     * 查询每日订单统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    List<Map<String, Object>> selectDailyOrderStats(String startDate, String endDate);

    /**
     * 查询每月订单统计
     * 
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 统计信息
     */
    List<Map<String, Object>> selectMonthlyOrderStats(String startMonth, String endMonth);

    /**
     * 查询过期订单
     * 
     * @return 过期订单集合
     */
    List<PsyTAssessmentOrder> selectExpiredOrders();

    /**
     * 取消过期订单
     * 
     * @return 取消数量
     */
    int cancelExpiredOrders();

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    String generateOrderNo();

    /**
     * 计算订单金额
     * 
     * @param scaleId 量表ID
     * @param userId 用户ID
     * @return 订单金额信息
     */
    Map<String, Object> calculateOrderAmount(Long scaleId, Long userId);

    /**
     * 验证订单
     * 
     * @param order 订单信息
     * @return 验证结果
     */
    Map<String, Object> validateOrder(PsyTAssessmentOrder order);

    /**
     * 订单支付回调处理
     * 
     * @param orderNo 订单编号
     * @param paymentData 支付数据
     * @return 处理结果
     */
    Map<String, Object> handlePaymentCallback(String orderNo, Map<String, Object> paymentData);

    /**
     * 查询用户可用订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 可用订单
     */
    PsyTAssessmentOrder selectAvailableOrder(Long userId, Long scaleId);

    /**
     * 使用订单
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    int useOrder(Long orderId);

    /**
     * 检查订单是否可用
     * 
     * @param orderId 订单ID
     * @return 是否可用
     */
    boolean checkOrderAvailable(Long orderId);

    /**
     * 获取订单支付信息
     * 
     * @param orderNo 订单编号
     * @return 支付信息
     */
    Map<String, Object> getOrderPaymentInfo(String orderNo);

    /**
     * 订单完成处理
     *
     * @param orderId 订单ID
     * @return 结果
     */
    int completeOrder(Long orderId);

    /**
     * 获取订单列表DTO
     *
     * @param order 查询条件
     * @return 订单DTO列表
     */
    List<Map<String, Object>> getOrderListDTO(PsyTAssessmentOrder order);

    /**
     * 获取订单详情DTO
     *
     * @param orderId 订单ID
     * @return 订单DTO
     */
    Map<String, Object> getOrderDTO(Long orderId);

    /**
     * 支付订单
     *
     * @param orderNo 订单编号
     * @param paymentMethod 支付方式
     * @return 支付结果
     */
    Map<String, Object> payOrder(String orderNo, String paymentMethod);

    /**
     * 查询待支付订单
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    List<PsyTAssessmentOrder> selectPendingOrders(Long userId);

    /**
     * 查询已支付订单
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    List<PsyTAssessmentOrder> selectPaidOrders(Long userId);

    /**
     * 查询已完成订单
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    List<PsyTAssessmentOrder> selectCompletedOrders(Long userId);

    /**
     * 查询已取消订单
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    List<PsyTAssessmentOrder> selectCancelledOrders(Long userId);
}
