package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyCourseInstructor;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyCourseInstructorMapper;
import com.xihuan.system.service.IPsyCourseInstructorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 讲师信息表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyCourseInstructorServiceImpl implements IPsyCourseInstructorService {
    
    @Autowired
    private PsyCourseInstructorMapper instructorMapper;

    /**
     * 查询讲师列表
     * 
     * @param instructor 讲师信息
     * @return 讲师集合
     */
    @Override
    public List<PsyCourseInstructor> selectInstructorList(PsyCourseInstructor instructor) {
        return instructorMapper.selectInstructorList(instructor);
    }

    /**
     * 根据ID查询讲师
     * 
     * @param id 讲师ID
     * @return 讲师信息
     */
    @Override
    public PsyCourseInstructor selectInstructorById(Long id) {
        return instructorMapper.selectInstructorById(id);
    }

    /**
     * 查询讲师详情（包含头像图片）
     * 
     * @param id 讲师ID
     * @return 讲师详情
     */
    @Override
    public PsyCourseInstructor selectInstructorWithDetails(Long id) {
        return instructorMapper.selectInstructorWithDetails(id);
    }

    /**
     * 新增讲师
     * 
     * @param instructor 讲师信息
     * @return 结果
     */
    @Override
    public int insertInstructor(PsyCourseInstructor instructor) {
        instructor.setCreateTime(DateUtils.getNowDate());
        instructor.setDelFlag(0);
        return instructorMapper.insertInstructor(instructor);
    }

    /**
     * 修改讲师
     * 
     * @param instructor 讲师信息
     * @return 结果
     */
    @Override
    public int updateInstructor(PsyCourseInstructor instructor) {
        instructor.setUpdateTime(DateUtils.getNowDate());
        return instructorMapper.updateInstructor(instructor);
    }

    /**
     * 删除讲师
     * 
     * @param id 讲师ID
     * @return 结果
     */
    @Override
    public int deleteInstructorById(Long id) {
        return instructorMapper.deleteInstructorById(id);
    }

    /**
     * 批量删除讲师
     * 
     * @param ids 需要删除的讲师ID
     * @return 结果
     */
    @Override
    public int deleteInstructorByIds(Long[] ids) {
        return instructorMapper.deleteInstructorByIds(ids);
    }

    /**
     * 查询所有讲师简单信息（仅id和name，用于下拉框）
     * 
     * @return 讲师简单信息集合
     */
    @Override
    public List<PsyCourseInstructor> selectAllSimpleList() {
        return instructorMapper.selectAllSimpleList();
    }
}
