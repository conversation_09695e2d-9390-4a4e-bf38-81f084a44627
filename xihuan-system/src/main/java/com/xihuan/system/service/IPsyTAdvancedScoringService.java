package com.xihuan.system.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 高级计分服务接口
 * 支持复杂计分逻辑，包括反向计分、标准分转换、公式计分等
 * 
 * <AUTHOR>
 */
public interface IPsyTAdvancedScoringService {

    /**
     * 执行高级计分
     * 根据量表类型自动选择合适的计分方法
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> executeAdvancedScoring(Long recordId);

    /**
     * PRCA-24 公式计分
     * 基础分18 + 加权求和
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> calculatePRCA24Score(Long recordId);

    /**
     * STAI 反向计分
     * 部分题目需要反向计分（5-原始分）
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> calculateSTAIScore(Long recordId);

    /**
     * SAD 特殊二选一计分
     * 特殊的二选一计分规则
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> calculateSADScore(Long recordId);

    /**
     * PDQ-4+ 复合题计分
     * 复合题的特殊计分逻辑
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> calculatePDQ4Score(Long recordId);

    /**
     * FNE 反向计分
     * 反向计分处理
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> calculateFNEScore(Long recordId);

    /**
     * SAS 标准分转换
     * 原始分 × 1.25
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> calculateSASScore(Long recordId);

    /**
     * BAI 标准分转换
     * 原始分 × 1.19
     *
     * @param recordId 测评记录ID
     * @return 计分结果
     */
    Map<String, Object> calculateBAIScore(Long recordId);

    /**
     * 通用反向计分计算
     *
     * @param originalScore 原始分数
     * @param maxValue 最大值
     * @return 反向计分结果
     */
    BigDecimal calculateReverseScore(BigDecimal originalScore, Integer maxValue);

    /**
     * 通用标准分转换
     *
     * @param rawScore 原始分数
     * @param multiplier 转换系数
     * @return 标准分
     */
    BigDecimal calculateStandardScore(BigDecimal rawScore, BigDecimal multiplier);

    /**
     * 计算维度分数
     *
     * @param recordId 测评记录ID
     * @param scaleCode 量表编码
     * @return 维度分数映射
     */
    Map<String, BigDecimal> calculateDimensionScores(Long recordId, String scaleCode);

    /**
     * 验证计分结果
     *
     * @param scaleCode 量表编码
     * @param scores 分数映射
     * @return 验证结果
     */
    Map<String, Object> validateScoringResult(String scaleCode, Map<String, BigDecimal> scores);

    /**
     * 生成计分报告
     *
     * @param recordId 测评记录ID
     * @return 计分报告
     */
    Map<String, Object> generateScoringReport(Long recordId);
}
