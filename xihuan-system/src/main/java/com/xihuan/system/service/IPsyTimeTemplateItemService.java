package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTimeTemplateItem;
import java.util.List;

/**
 * 排班模板明细Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTimeTemplateItemService {
    
    /**
     * 查询模板明细列表
     * 
     * @param item 模板明细
     * @return 模板明细集合
     */
    List<PsyTimeTemplateItem> selectTemplateItemList(PsyTimeTemplateItem item);
    
    /**
     * 根据ID查询模板明细
     * 
     * @param id 模板明细主键
     * @return 模板明细
     */
    PsyTimeTemplateItem selectTemplateItemById(Long id);
    
    /**
     * 根据模板ID查询明细
     * 
     * @param templateId 模板ID
     * @return 模板明细集合
     */
    List<PsyTimeTemplateItem> selectTemplateItemsByTemplateId(Long templateId);
    
    /**
     * 新增模板明细
     * 
     * @param item 模板明细
     * @return 结果
     */
    int insertTemplateItem(PsyTimeTemplateItem item);
    
    /**
     * 批量新增模板明细
     * 
     * @param items 模板明细列表
     * @return 结果
     */
    int batchInsertTemplateItems(List<PsyTimeTemplateItem> items);
    
    /**
     * 修改模板明细
     * 
     * @param item 模板明细
     * @return 结果
     */
    int updateTemplateItem(PsyTimeTemplateItem item);
    
    /**
     * 批量删除模板明细
     * 
     * @param ids 需要删除的模板明细主键集合
     * @return 结果
     */
    int deleteTemplateItemByIds(Long[] ids);
    
    /**
     * 删除模板明细信息
     * 
     * @param id 模板明细主键
     * @return 结果
     */
    int deleteTemplateItemById(Long id);
    
    /**
     * 根据模板ID删除明细
     * 
     * @param templateId 模板ID
     * @return 结果
     */
    int deleteTemplateItemsByTemplateId(Long templateId);
}
