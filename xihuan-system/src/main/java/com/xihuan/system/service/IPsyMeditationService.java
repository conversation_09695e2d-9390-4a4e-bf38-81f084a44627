package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyMeditation;

import java.util.List;

/**
 * 冥想主表Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyMeditationService {
    
    /**
     * 查询冥想列表
     * 
     * @param meditation 冥想信息
     * @return 冥想集合
     */
    List<PsyMeditation> selectMeditationList(PsyMeditation meditation);

    /**
     * 查询冥想详情（包含分类等信息）
     * 
     * @param id 冥想ID
     * @return 冥想详情
     */
    PsyMeditation selectMeditationWithDetails(Long id);

    /**
     * 根据ID查询冥想
     * 
     * @param id 冥想ID
     * @return 冥想信息
     */
    PsyMeditation selectMeditationById(Long id);

    /**
     * 新增冥想
     * 
     * @param meditation 冥想信息
     * @return 结果
     */
    int insertMeditation(PsyMeditation meditation);

    /**
     * 修改冥想
     * 
     * @param meditation 冥想信息
     * @return 结果
     */
    int updateMeditation(PsyMeditation meditation);

    /**
     * 删除冥想
     * 
     * @param id 冥想ID
     * @return 结果
     */
    int deleteMeditationById(Long id);

    /**
     * 批量删除冥想
     * 
     * @param ids 需要删除的冥想ID
     * @return 结果
     */
    int deleteMeditationByIds(Long[] ids);

    /**
     * 根据分类ID查询冥想列表
     * 
     * @param categoryId 分类ID
     * @return 冥想集合
     */
    List<PsyMeditation> selectMeditationsByCategoryId(Long categoryId);

    /**
     * 更新冥想评分信息
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    int updateMeditationRating(Long meditationId);

    /**
     * 增加冥想播放次数
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    int incrementPlayCount(Long meditationId);

    /**
     * 发布冥想
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    int publishMeditation(Long meditationId);

    /**
     * 下架冥想
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    int unpublishMeditation(Long meditationId);

    /**
     * 检查冥想是否可以删除
     * 
     * @param meditationId 冥想ID
     * @return 是否可以删除
     */
    boolean canDeleteMeditation(Long meditationId);
}
