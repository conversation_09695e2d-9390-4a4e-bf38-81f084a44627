package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;

import java.time.LocalDate;
import java.util.List;

/**
 * 咨询师排班Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTimeCounselorScheduleService {
    
    /**
     * 查询咨询师排班列表
     * 
     * @param schedule 咨询师排班
     * @return 咨询师排班集合
     */
    List<PsyTimeCounselorSchedule> selectScheduleList(PsyTimeCounselorSchedule schedule);
    
    /**
     * 根据ID查询咨询师排班
     * 
     * @param id 咨询师排班主键
     * @return 咨询师排班
     */
    PsyTimeCounselorSchedule selectScheduleById(Long id);
    
    /**
     * 根据咨询师和日期查询排班
     * 
     * @param counselorId 咨询师ID
     * @param scheduleDate 排班日期
     * @return 排班记录
     */
    PsyTimeCounselorSchedule selectScheduleByCounselorAndDate(Long counselorId, LocalDate scheduleDate);
    
    /**
     * 查询咨询师在日期范围内的排班
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 排班记录集合
     */
    List<PsyTimeCounselorSchedule> selectSchedulesByDateRange(Long counselorId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 查询指定日期的所有咨询师排班
     * 
     * @param scheduleDate 排班日期
     * @param centerId 咨询中心ID
     * @return 排班记录集合
     */
    List<PsyTimeCounselorSchedule> selectSchedulesByDate(LocalDate scheduleDate, Long centerId);
    
    /**
     * 新增咨询师排班
     * 
     * @param schedule 咨询师排班
     * @return 结果
     */
    int insertSchedule(PsyTimeCounselorSchedule schedule);
    
    /**
     * 批量新增咨询师排班
     *
     * @param schedules 咨询师排班列表
     * @return 结果
     */
    int batchInsertSchedules(List<PsyTimeCounselorSchedule> schedules);

    /**
     * 批量新增咨询师排班（忽略重复记录）
     *
     * @param schedules 咨询师排班列表
     * @return 结果
     */
    int batchInsertSchedulesIgnoreDuplicate(List<PsyTimeCounselorSchedule> schedules);
    
    /**
     * 修改咨询师排班
     * 
     * @param schedule 咨询师排班
     * @return 结果
     */
    int updateSchedule(PsyTimeCounselorSchedule schedule);
    
    /**
     * 删除咨询师排班信息
     * 
     * @param id 咨询师排班主键
     * @return 结果
     */
    int deleteScheduleById(Long id);
    
    /**
     * 批量删除咨询师排班
     * 
     * @param ids 需要删除的咨询师排班主键集合
     * @return 结果
     */
    int deleteScheduleByIds(Long[] ids);
    
    /**
     * 删除指定日期范围的排班
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果
     */
    int deleteSchedulesByDateRange(Long counselorId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 检查排班是否存在
     * 
     * @param counselorId 咨询师ID
     * @param scheduleDate 排班日期
     * @return 是否存在
     */
    boolean checkScheduleExists(Long counselorId, LocalDate scheduleDate);
    
    /**
     * 为咨询师生成默认排班（工作日 9:00-18:00）
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 生成的排班数量
     */
    int generateDefaultSchedule(Long counselorId, LocalDate startDate, LocalDate endDate, Long centerId);
    
    /**
     * 为所有咨询师生成默认排班
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 生成的排班数量
     */
    int generateAllCounselorSchedule(LocalDate startDate, LocalDate endDate, Long centerId);

    /**
     * 清理重复的排班记录
     *
     * @return 清理的记录数量
     */
    int cleanupDuplicateSchedules();

    /**
     * 确保咨询师有未来指定天数的排班记录
     * 如果没有则自动生成默认排班
     *
     * @param counselorId 咨询师ID
     * @param days 未来天数
     * @param centerId 咨询中心ID
     * @return 生成的排班数量
     */
    int ensureFutureSchedule(Long counselorId, int days, Long centerId);

    /**
     * 确保所有咨询师都有未来指定天数的排班记录
     *
     * @param days 未来天数
     * @param centerId 咨询中心ID
     * @return 生成的排班数量
     */
    int ensureAllCounselorsFutureSchedule(int days, Long centerId);
}
