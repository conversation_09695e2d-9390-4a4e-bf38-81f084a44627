package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 时间槽过滤服务接口
 * 用于根据咨询师到店时间等条件过滤可用时间槽
 * 
 * <AUTHOR>
 */
public interface IPsyTimeSlotFilterService {
    
    /**
     * 根据咨询师到店时间过滤时间槽
     * 
     * @param timeSlots 原始时间槽列表
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @param consultationType 咨询类型(1=线上 2=线下)
     * @param currentTime 当前时间
     * @return 过滤后的时间槽列表
     */
    List<PsyTimeSlot> filterTimeSlotsByArrivalTime(
        List<PsyTimeSlot> timeSlots, 
        Long consultantId, 
        Long centerId, 
        Integer consultationType,
        LocalDateTime currentTime
    );
    
    /**
     * 获取咨询师到指定中心的所需时间
     *
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 所需时间(小时)
     */
    Double getConsultantArrivalTime(Long consultantId, Long centerId);

    /**
     * 检查咨询师是否启用到店时间过滤
     *
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 是否启用过滤
     */
    boolean isArrivalFilterEnabled(Long consultantId, Long centerId);

    
    /**
     * 检查指定时间槽是否可用（考虑到店时间）
     * 
     * @param timeSlot 时间槽
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @param consultationType 咨询类型
     * @param currentTime 当前时间
     * @return 是否可用
     */
    boolean isTimeSlotAvailable(
        PsyTimeSlot timeSlot, 
        Long consultantId, 
        Long centerId, 
        Integer consultationType,
        LocalDateTime currentTime
    );
    
    /**
     * 计算最早可预约的线下咨询时间
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @param currentTime 当前时间
     * @return 最早可预约时间
     */
    LocalDateTime getEarliestOfflineAppointmentTime(
        Long consultantId, 
        Long centerId, 
        LocalDateTime currentTime
    );
    
    /**
     * 批量过滤多个咨询师的时间槽
     * 
     * @param consultantTimeSlots 咨询师时间槽映射
     * @param centerId 咨询中心ID
     * @param consultationType 咨询类型
     * @param currentTime 当前时间
     * @return 过滤后的时间槽映射
     */
    java.util.Map<Long, List<PsyTimeSlot>> batchFilterTimeSlots(
        java.util.Map<Long, List<PsyTimeSlot>> consultantTimeSlots,
        Long centerId,
        Integer consultationType,
        LocalDateTime currentTime
    );
}
