package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.dto.PsyCounselorScheduleDTO;
import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;

import java.time.LocalDate;
import java.util.List;

/**
 * 咨询师排期小程序端Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyCounselorScheduleWxService {
    
    /**
     * 获取咨询师排期列表（小程序格式）
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 排期列表
     */
    List<PsyCounselorScheduleDTO> getCounselorScheduleList(Long counselorId, LocalDate startDate, LocalDate endDate, Long centerId);
    
    /**
     * 获取排期详情（小程序格式）
     * 
     * @param scheduleId 排期ID
     * @return 排期详情
     */
    PsyCounselorScheduleDTO getScheduleDetail(Long scheduleId);
    
    /**
     * 获取咨询师排期日历视图
     * 
     * @param counselorId 咨询师ID
     * @param year 年份
     * @param month 月份
     * @return 日历数据
     */
    PsyCounselorScheduleDTO.ScheduleCalendarDTO getScheduleCalendar(Long counselorId, int year, int month);
    
    /**
     * 获取咨询师排期统计
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    PsyCounselorScheduleDTO.ScheduleStatistics getScheduleStatistics(Long counselorId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 创建排期
     * 
     * @param schedule 排期信息
     * @param createBy 创建者
     * @return 创建结果
     */
    int createSchedule(PsyTimeCounselorSchedule schedule, String createBy);
    
    /**
     * 批量创建排期
     * 
     * @param schedules 排期列表
     * @param createBy 创建者
     * @return 创建数量
     */
    int batchCreateSchedules(List<PsyTimeCounselorSchedule> schedules, String createBy);
    
    /**
     * 更新排期
     * 
     * @param schedule 排期信息
     * @param updateBy 更新者
     * @return 更新结果
     */
    int updateSchedule(PsyTimeCounselorSchedule schedule, String updateBy);
    
    /**
     * 删除排期
     * 
     * @param scheduleId 排期ID
     * @return 删除结果
     */
    int deleteSchedule(Long scheduleId);
    
    /**
     * 批量删除排期
     * 
     * @param scheduleIds 排期ID列表
     * @return 删除数量
     */
    int batchDeleteSchedules(List<Long> scheduleIds);
    
    /**
     * 复制排期到其他日期
     * 
     * @param sourceScheduleId 源排期ID
     * @param targetDates 目标日期列表
     * @param createBy 创建者
     * @return 复制数量
     */
    int copyScheduleToOtherDates(Long sourceScheduleId, List<LocalDate> targetDates, String createBy);
    
    /**
     * 生成默认排期
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @param createBy 创建者
     * @return 生成数量
     */
    int generateDefaultSchedule(Long counselorId, LocalDate startDate, LocalDate endDate, Long centerId, String createBy);
    
    /**
     * 切换排期工作状态
     * 
     * @param scheduleId 排期ID
     * @param updateBy 更新者
     * @return 更新结果
     */
    int toggleWorkingStatus(Long scheduleId, String updateBy);
    
    /**
     * 检查排期冲突
     * 
     * @param counselorId 咨询师ID
     * @param scheduleDate 排期日期
     * @param excludeScheduleId 排除的排期ID（用于更新时排除自己）
     * @return 冲突检查结果
     */
    PsyCounselorScheduleDTO.ConflictCheckResult checkScheduleConflict(Long counselorId, LocalDate scheduleDate, Long excludeScheduleId);
    
    /**
     * 应用排期模板
     * 
     * @param request 模板应用请求
     * @param createBy 创建者
     * @return 应用结果
     */
    int applyScheduleTemplate(PsyCounselorScheduleDTO.ApplyTemplateRequest request, String createBy);
    
    /**
     * 获取咨询师可用的排期模板
     * 
     * @param counselorId 咨询师ID
     * @return 模板列表
     */
    List<com.xihuan.common.core.domain.entity.PsyTimeScheduleTemplate> getAvailableTemplates(Long counselorId);
    
    /**
     * 获取排期时间段分析
     * 
     * @param counselorId 咨询师ID
     * @param date 日期
     * @return 时间段分析
     */
    List<PsyCounselorScheduleDTO.ScheduleTimeRange> getScheduleTimeRangeAnalysis(Long counselorId, LocalDate date);
    
    /**
     * 批量操作排期
     * 
     * @param request 批量操作请求
     * @param operatorBy 操作者
     * @return 操作结果
     */
    int batchOperateSchedules(PsyCounselorScheduleDTO.BatchOperationRequest request, String operatorBy);
    
    /**
     * 获取咨询师近期排期概览
     * 
     * @param counselorId 咨询师ID
     * @param days 天数（默认7天）
     * @return 排期概览
     */
    List<PsyCounselorScheduleDTO> getRecentScheduleOverview(Long counselorId, int days);
    
    /**
     * 获取排期变更历史
     * 
     * @param scheduleId 排期ID
     * @return 变更历史
     */
    List<String> getScheduleChangeHistory(Long scheduleId);
    
    /**
     * 验证排期数据
     * 
     * @param schedule 排期数据
     * @return 验证结果消息，null表示验证通过
     */
    String validateScheduleData(PsyTimeCounselorSchedule schedule);
    
    /**
     * 获取排期建议
     * 
     * @param counselorId 咨询师ID
     * @param date 日期
     * @return 建议信息
     */
    List<String> getScheduleSuggestions(Long counselorId, LocalDate date);
}
