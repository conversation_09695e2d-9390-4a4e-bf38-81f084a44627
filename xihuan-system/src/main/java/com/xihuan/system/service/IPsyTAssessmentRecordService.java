package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 测评记录Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTAssessmentRecordService {
    
    /**
     * 查询测评记录
     * 
     * @param id 测评记录主键
     * @return 测评记录
     */
    public PsyTAssessmentRecord selectAssessmentRecordById(Long id);

    /**
     * 查询测评记录列表
     * 
     * @param assessmentRecord 测评记录
     * @return 测评记录集合
     */
    public List<PsyTAssessmentRecord> selectAssessmentRecordList(PsyTAssessmentRecord assessmentRecord);

    /**
     * 新增测评记录
     * 
     * @param assessmentRecord 测评记录
     * @return 结果
     */
    public int insertAssessmentRecord(PsyTAssessmentRecord assessmentRecord);

    /**
     * 修改测评记录
     * 
     * @param assessmentRecord 测评记录
     * @return 结果
     */
    public int updateAssessmentRecord(PsyTAssessmentRecord assessmentRecord);

    /**
     * 批量删除测评记录
     * 
     * @param ids 需要删除的测评记录主键集合
     * @return 结果
     */
    public int deleteAssessmentRecordByIds(Long[] ids);

    /**
     * 删除测评记录信息
     * 
     * @param id 测评记录主键
     * @return 结果
     */
    public int deleteAssessmentRecordById(Long id);

    /**
     * 根据用户ID查询测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    public List<PsyTAssessmentRecord> selectRecordsByUserId(Long userId);

    /**
     * 根据量表ID查询测评记录
     * 
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    public List<PsyTAssessmentRecord> selectRecordsByScaleId(Long scaleId);

    /**
     * 根据用户ID和量表ID查询测评记录
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    public List<PsyTAssessmentRecord> selectRecordsByUserAndScale(Long userId, Long scaleId);

    /**
     * 开始测评
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param enterpriseId 企业ID（可为空）
     * @return 测评记录ID
     */
    public Long startAssessment(Long userId, Long scaleId, Long enterpriseId);

    /**
     * 完成测评
     * 
     * @param recordId 测评记录ID
     * @param totalScore 总分
     * @param dimensionScores 维度分数
     * @return 结果
     */
    public int completeAssessment(Long recordId, BigDecimal totalScore, Map<String, BigDecimal> dimensionScores);

    /**
     * 暂停测评
     *
     * @param recordId 测评记录ID
     * @return 结果
     */
    public int pauseAssessment(Long recordId);

    /**
     * 查询测评详细信息（包含题目、选项、答题历史、进度等）
     *
     * @param recordId 测评记录ID
     * @return 测评详细信息
     */
    public Map<String, Object> selectAssessmentDetail(Long recordId);

    /**
     * 恢复测评
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    public int resumeAssessment(Long recordId);

    /**
     * 取消测评
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    public int cancelAssessment(Long recordId);

    /**
     * 查询测评详情（包含答题记录）
     * 
     * @param recordId 测评记录ID
     * @return 测评详情
     */
    public PsyTAssessmentRecord selectRecordWithAnswers(Long recordId);

    /**
     * 查询测评结果
     * 
     * @param recordId 测评记录ID
     * @return 测评结果
     */
    public Map<String, Object> selectAssessmentResult(Long recordId);

    /**
     * 生成测评报告
     *
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    public Map<String, Object> generateAssessmentReport(Long recordId);

    /**
     * 强制重新生成测评报告（清除缓存）
     *
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    public Map<String, Object> forceRegenerateAssessmentReport(Long recordId);

    /**
     * 查询测评统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> selectAssessmentStats();

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectUserAssessmentStats(Long userId);

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    public Map<String, Object> selectScaleAssessmentStats(Long scaleId);

    /**
     * 查询企业测评统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    public Map<String, Object> selectEnterpriseAssessmentStats(Long enterpriseId);

    /**
     * 查询测评趋势
     * 
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> selectAssessmentTrend(Integer days);

    /**
     * 查询热门量表排行
     * 
     * @param limit 限制数量
     * @return 排行数据
     */
    public List<Map<String, Object>> selectHotScaleRanking(Integer limit);

    /**
     * 检查用户是否可以测评
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 检查结果
     */
    public Map<String, Object> checkUserCanAssess(Long userId, Long scaleId);

    /**
     * 查询用户最近的测评记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 测评记录集合
     */
    public List<PsyTAssessmentRecord> selectRecentRecordsByUserId(Long userId, Integer limit);

    /**
     * 查询未完成的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    public List<PsyTAssessmentRecord> selectIncompleteRecordsByUserId(Long userId);

    /**
     * 查询已完成的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    public List<PsyTAssessmentRecord> selectCompletedRecordsByUserId(Long userId);

    /**
     * 更新测评状态
     * 
     * @param recordId 测评记录ID
     * @param status 状态
     * @return 结果
     */
    public int updateAssessmentStatus(Long recordId, Integer status);

    /**
     * 批量更新测评状态
     * 
     * @param ids 测评记录ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateAssessmentStatus(Long[] ids, Integer status);

    /**
     * 导出测评记录
     * 
     * @param ids 测评记录ID数组
     * @return 测评记录列表
     */
    public List<PsyTAssessmentRecord> exportAssessmentRecords(Long[] ids);

    /**
     * 查询测评时长分析
     * 
     * @param scaleId 量表ID
     * @return 时长分析
     */
    public Map<String, Object> selectDurationAnalysis(Long scaleId);

    /**
     * 查询测评完成率分析
     * 
     * @param scaleId 量表ID
     * @return 完成率分析
     */
    public Map<String, Object> selectCompletionRateAnalysis(Long scaleId);

    /**
     * 查询测评分数分布
     * 
     * @param scaleId 量表ID
     * @return 分数分布
     */
    public List<Map<String, Object>> selectScoreDistribution(Long scaleId);

    /**
     * 重新计算测评分数
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    public int recalculateAssessmentScore(Long recordId);

    /**
     * 查询异常测评记录
     * 
     * @return 异常记录列表
     */
    public List<PsyTAssessmentRecord> selectAbnormalRecords();
}
