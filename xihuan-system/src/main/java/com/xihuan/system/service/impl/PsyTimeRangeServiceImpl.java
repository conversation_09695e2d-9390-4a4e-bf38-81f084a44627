package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.PsyTimeRangeMapper;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.service.task.PsyTimeSlotTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 时间段定义Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeRangeServiceImpl implements IPsyTimeRangeService {
    private static final Logger logger = LoggerFactory.getLogger(PsyTimeSlotTaskService.class);
    
    @Autowired
    private PsyTimeRangeMapper timeRangeMapper;

    /**
     * 查询时间段定义列表
     * 
     * @param timeRange 时间段定义
     * @return 时间段定义
     */
    @Override
    public List<PsyTimeRange> selectTimeRangeList(PsyTimeRange timeRange) {
        return timeRangeMapper.selectTimeRangeList(timeRange);
    }

    /**
     * 查询所有有效的时间段定义
     * 
     * @return 时间段定义集合
     */
    @Override
    public List<PsyTimeRange> selectAllActiveTimeRanges() {
        return timeRangeMapper.selectAllActiveTimeRanges();
    }

    /**
     * 根据ID查询时间段定义
     * 
     * @param id 时间段定义主键
     * @return 时间段定义
     */
    @Override
    public PsyTimeRange selectTimeRangeById(Long id) {
        return timeRangeMapper.selectTimeRangeById(id);
    }

    /**
     * 根据时间查询所属时间段
     * 
     * @param hour 小时（0-23）
     * @return 时间段定义
     */
    @Override
    public PsyTimeRange selectTimeRangeByHour(Integer hour) {
        return timeRangeMapper.selectTimeRangeByHour(hour);
    }

    /**
     * 新增时间段定义
     * 
     * @param timeRange 时间段定义
     * @return 结果
     */
    @Override
    public int insertTimeRange(PsyTimeRange timeRange) {
        timeRange.setCreateTime(DateUtils.getNowDate());
        return timeRangeMapper.insertTimeRange(timeRange);
    }

    /**
     * 修改时间段定义
     * 
     * @param timeRange 时间段定义
     * @return 结果
     */
    @Override
    public int updateTimeRange(PsyTimeRange timeRange) {
        timeRange.setUpdateTime(DateUtils.getNowDate());
        return timeRangeMapper.updateTimeRange(timeRange);
    }

    /**
     * 批量删除时间段定义
     * 
     * @param ids 需要删除的时间段定义主键
     * @return 结果
     */
    @Override
    public int deleteTimeRangeByIds(Long[] ids) {
        return timeRangeMapper.deleteTimeRangeByIds(ids);
    }

    /**
     * 删除时间段定义信息
     * 
     * @param id 时间段定义主键
     * @return 结果
     */
    @Override
    public int deleteTimeRangeById(Long id) {
        return timeRangeMapper.deleteTimeRangeById(id);
    }

    /**
     * 校验时间段名称是否唯一
     * 
     * @param timeRange 时间段定义信息
     * @return 结果
     */
    @Override
    public String checkTimeRangeNameUnique(PsyTimeRange timeRange) {
        Long id = StringUtils.isNull(timeRange.getId()) ? -1L : timeRange.getId();
        PsyTimeRange info = timeRangeMapper.checkTimeRangeNameUnique(timeRange.getName(), id) > 0 ? new PsyTimeRange() : null;
        if (StringUtils.isNotNull(info)) {
            return "时间段名称已存在";
        }
        return "0";
    }

    /**
     * 初始化默认时间段
     * 优化版本：创建更细粒度的时间段，确保时间槽连续性
     *
     * @return 结果
     */
    @Override
    @Transactional
    public int initDefaultTimeRanges() {
        // 检查是否已经初始化
        List<PsyTimeRange> existing = selectAllActiveTimeRanges();
        if (!existing.isEmpty()) {
            logger.info("时间段已存在 {} 个，跳过初始化", existing.size());
            return 0;
        }

        logger.info("开始初始化默认时间段配置");

        // 创建更细粒度的默认时间段，确保覆盖完整的工作时间
        PsyTimeRange[] defaultRanges = {
            // 上午时段
            createTimeRange("上午早段", "https://example.com/icons/morning-early.png", 9, 10),
            createTimeRange("上午中段", "https://example.com/icons/morning-mid.png", 10, 11),
            createTimeRange("上午晚段", "https://example.com/icons/morning-late.png", 11, 12),

            // 中午时段
            createTimeRange("中午早段", "https://example.com/icons/noon-early.png", 12, 13),
            createTimeRange("中午晚段", "https://example.com/icons/noon-late.png", 13, 14),

            // 下午时段
            createTimeRange("下午早段", "https://example.com/icons/afternoon-early.png", 14, 15),
            createTimeRange("下午中段", "https://example.com/icons/afternoon-mid.png", 15, 16),
            createTimeRange("下午晚段", "https://example.com/icons/afternoon-late.png", 16, 17),
            createTimeRange("下午末段", "https://example.com/icons/afternoon-end.png", 17, 18),

            // 晚上时段
            createTimeRange("晚上早段", "https://example.com/icons/evening-early.png", 18, 19),
            createTimeRange("晚上中段", "https://example.com/icons/evening-mid.png", 19, 20),
            createTimeRange("晚上晚段", "https://example.com/icons/evening-late.png", 20, 21)
        };

        int result = 0;
        for (PsyTimeRange range : defaultRanges) {
            try {
                int insertResult = insertTimeRange(range);
                result += insertResult;
                logger.debug("成功创建时间段：{} ({}:00-{}:00)",
                    range.getName(), range.getStartHour(), range.getEndHour());
            } catch (Exception e) {
                logger.error("创建时间段失败：{} ({}:00-{}:00), 错误：{}",
                    range.getName(), range.getStartHour(), range.getEndHour(), e.getMessage());
            }
        }

        logger.info("默认时间段初始化完成，成功创建 {} 个时间段", result);
        return result;
    }

    /**
     * 创建时间段对象
     */
    private PsyTimeRange createTimeRange(String name, String iconUrl, int startHour, int endHour) {
        PsyTimeRange range = new PsyTimeRange();
        range.setName(name);
        range.setIconUrl(iconUrl);
        range.setStartHour(startHour);
        range.setEndHour(endHour);
        range.setDelFlag(0);
        return range;
    }
}
