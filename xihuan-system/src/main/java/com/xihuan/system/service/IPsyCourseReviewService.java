package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyCourseReview;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程评价表Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyCourseReviewService {
    
    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    List<PsyCourseReview> selectReviewList(PsyCourseReview review);

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    PsyCourseReview selectReviewById(Long id);

    /**
     * 查询评价详情（包含课程和用户信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    PsyCourseReview selectReviewWithDetails(Long id);

    /**
     * 根据课程ID查询评价列表
     * 
     * @param courseId 课程ID
     * @return 评价集合
     */
    List<PsyCourseReview> selectReviewsByCourseId(Long courseId);

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    List<PsyCourseReview> selectReviewsByUserId(Long userId);

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int insertReview(PsyCourseReview review);

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int updateReview(PsyCourseReview review);

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    int deleteReviewById(Long id);

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    int deleteReviewByIds(Long[] ids);

    /**
     * 计算课程平均评分
     * 
     * @param courseId 课程ID
     * @return 平均评分
     */
    BigDecimal calculateAverageRating(Long courseId);

    /**
     * 统计课程评价数量
     * 
     * @param courseId 课程ID
     * @return 评价数量
     */
    int countReviewsByCourseId(Long courseId);

    /**
     * 检查用户是否已评价课程
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否已评价
     */
    boolean checkUserReviewed(Long userId, Long courseId);
}
