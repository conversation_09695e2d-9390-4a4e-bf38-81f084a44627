package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.PsyProduct;
import com.xihuan.system.domain.dto.PsyProductDTO;

import java.util.List;

public interface PsyProductService {
    /**
     * 查询产品列表
     */
    List<PsyProduct> selectProductList(PsyProduct product);

    /**
     * 查询产品详情（含关联内容）
     */
    PsyProduct selectProductWithDetails(Long productId);

    /**
     * 新增产品（含关联内容）
     */
    int insertProduct(PsyProductDTO product);

    /**
     * 修改产品（含关联内容）
     */
    int updateProduct(PsyProductDTO product);

    /**
     * 批量删除产品
     */
    int deleteProductByIds(Long[] productIds);
}