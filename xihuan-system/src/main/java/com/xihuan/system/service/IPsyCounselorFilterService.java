package com.xihuan.system.service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 咨询师筛选Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyCounselorFilterService {
    
    /**
     * 根据时间段筛选可用的咨询师
     * 
     * @param date 日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param centerId 咨询中心ID
     * @return 可用咨询师列表
     */
    List<Map<String, Object>> filterAvailableCounselors(
        LocalDate date, LocalTime startTime, LocalTime endTime, Long centerId
    );
    
    /**
     * 根据时间段筛选可用的咨询师（支持连续时间段）
     * 
     * @param date 日期
     * @param startTime 开始时间
     * @param duration 持续时间（分钟）
     * @param centerId 咨询中心ID
     * @return 可用咨询师列表
     */
    List<Map<String, Object>> filterAvailableCounselorsByDuration(
        LocalDate date, LocalTime startTime, Integer duration, Long centerId
    );
    
    /**
     * 根据系统时间槽ID筛选可用的咨询师
     * 
     * @param systemSlotId 系统时间槽ID
     * @return 可用咨询师列表
     */
    List<Map<String, Object>> filterCounselorsBySystemSlot(Long systemSlotId);
    
    /**
     * 根据多个时间段筛选可用的咨询师（取交集）
     * 
     * @param date 日期
     * @param timeSlots 时间段列表（开始时间-结束时间）
     * @param centerId 咨询中心ID
     * @return 可用咨询师列表
     */
    List<Map<String, Object>> filterCounselorsByMultipleTimeSlots(
        LocalDate date, List<Map<String, LocalTime>> timeSlots, Long centerId
    );
    
    /**
     * 获取咨询师在指定时间段的详细可用性信息
     * 
     * @param counselorId 咨询师ID
     * @param date 日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 可用性详细信息
     */
    Map<String, Object> getCounselorAvailabilityDetail(
        Long counselorId, LocalDate date, LocalTime startTime, LocalTime endTime
    );
    
    /**
     * 获取咨询师的推荐度评分
     * 
     * @param counselorId 咨询师ID
     * @param date 日期
     * @param startTime 开始时间
     * @return 推荐度评分信息
     */
    Map<String, Object> getCounselorRecommendationScore(
        Long counselorId, LocalDate date, LocalTime startTime
    );
    
    /**
     * 根据用户偏好筛选咨询师
     * 
     * @param date 日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param centerId 咨询中心ID
     * @param preferences 用户偏好（性别、专业领域、经验等）
     * @return 筛选后的咨询师列表
     */
    List<Map<String, Object>> filterCounselorsByPreferences(
        LocalDate date, LocalTime startTime, LocalTime endTime, Long centerId,
        Map<String, Object> preferences
    );
    
    /**
     * 获取时间段内咨询师的统计信息
     * 
     * @param date 日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param centerId 咨询中心ID
     * @return 统计信息
     */
    Map<String, Object> getCounselorStatistics(
        LocalDate date, LocalTime startTime, LocalTime endTime, Long centerId
    );
}
