package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTEnterpriseAssessmentPlan;

import java.util.List;
import java.util.Map;

/**
 * 企业测评计划Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTEnterpriseAssessmentPlanService {
    
    /**
     * 查询企业测评计划列表
     * 
     * @param plan 企业测评计划信息
     * @return 企业测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectPlanList(PsyTEnterpriseAssessmentPlan plan);

    /**
     * 根据ID查询企业测评计划
     * 
     * @param id 企业测评计划ID
     * @return 企业测评计划信息
     */
    PsyTEnterpriseAssessmentPlan selectPlanById(Long id);

    /**
     * 查询计划详情（包含参与记录、企业、量表等信息）
     * 
     * @param id 计划ID
     * @return 计划详情
     */
    PsyTEnterpriseAssessmentPlan selectPlanWithDetails(Long id);

    /**
     * 根据企业ID查询测评计划列表
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectPlansByEnterpriseId(Long enterpriseId);

    /**
     * 根据量表ID查询测评计划列表
     * 
     * @param scaleId 量表ID
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectPlansByScaleId(Long scaleId);

    /**
     * 新增企业测评计划
     * 
     * @param plan 企业测评计划信息
     * @return 结果
     */
    int insertPlan(PsyTEnterpriseAssessmentPlan plan);

    /**
     * 修改企业测评计划
     * 
     * @param plan 企业测评计划信息
     * @return 结果
     */
    int updatePlan(PsyTEnterpriseAssessmentPlan plan);

    /**
     * 删除企业测评计划
     * 
     * @param ids 需要删除的企业测评计划ID
     * @return 结果
     */
    int deletePlanByIds(Long[] ids);

    /**
     * 创建测评计划
     * 
     * @param plan 计划信息
     * @return 结果
     */
    int createAssessmentPlan(PsyTEnterpriseAssessmentPlan plan);

    /**
     * 启动测评计划
     * 
     * @param planId 计划ID
     * @return 结果
     */
    int startAssessmentPlan(Long planId);

    /**
     * 暂停测评计划
     * 
     * @param planId 计划ID
     * @return 结果
     */
    int pauseAssessmentPlan(Long planId);

    /**
     * 恢复测评计划
     * 
     * @param planId 计划ID
     * @return 结果
     */
    int resumeAssessmentPlan(Long planId);

    /**
     * 结束测评计划
     * 
     * @param planId 计划ID
     * @return 结果
     */
    int endAssessmentPlan(Long planId);

    /**
     * 取消测评计划
     * 
     * @param planId 计划ID
     * @param reason 取消原因
     * @return 结果
     */
    int cancelAssessmentPlan(Long planId, String reason);

    /**
     * 查询待开始的测评计划
     * 
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectPendingPlans();

    /**
     * 查询进行中的测评计划
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectInProgressPlans(Long enterpriseId);

    /**
     * 查询已结束的测评计划
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectEndedPlans(Long enterpriseId);

    /**
     * 查询即将结束的测评计划
     * 
     * @param days 天数
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> selectEndingPlans(Integer days);

    /**
     * 更新计划状态
     * 
     * @param id 计划ID
     * @param status 状态
     * @return 结果
     */
    int updatePlanStatus(Long id, Integer status);

    /**
     * 更新计划完成率
     * 
     * @param id 计划ID
     * @return 结果
     */
    int updatePlanCompletionRate(Long id);

    /**
     * 统计企业测评计划数量
     * 
     * @param enterpriseId 企业ID
     * @return 数量
     */
    int countPlansByEnterpriseId(Long enterpriseId);

    /**
     * 统计量表测评计划数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countPlansByScaleId(Long scaleId);

    /**
     * 查询计划统计信息
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    Map<String, Object> selectPlanStats(Long planId);

    /**
     * 查询企业计划统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    Map<String, Object> selectEnterprisePlanStats(Long enterpriseId);

    /**
     * 查询计划类型统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectPlanTypeStats(Long enterpriseId);

    /**
     * 查询计划完成率统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectPlanCompletionStats(Long enterpriseId);

    /**
     * 查询计划趋势统计
     * 
     * @param enterpriseId 企业ID
     * @param days 天数
     * @return 趋势统计
     */
    List<Map<String, Object>> selectPlanTrendStats(Long enterpriseId, Integer days);

    /**
     * 搜索测评计划
     * 
     * @param keyword 关键词
     * @param enterpriseId 企业ID
     * @param planType 计划类型
     * @param status 状态
     * @return 测评计划集合
     */
    List<PsyTEnterpriseAssessmentPlan> searchPlans(String keyword, Long enterpriseId, Integer planType, Integer status);

    /**
     * 批量更新计划状态
     * 
     * @param ids 计划ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdatePlanStatus(Long[] ids, Integer status);

    /**
     * 自动开始到期的计划
     * 
     * @return 开始数量
     */
    int autoStartDuePlans();

    /**
     * 自动结束到期的计划
     * 
     * @return 结束数量
     */
    int autoEndExpiredPlans();

    /**
     * 查询计划参与度分析
     * 
     * @param planId 计划ID
     * @return 参与度分析
     */
    Map<String, Object> selectPlanParticipationAnalysis(Long planId);

    /**
     * 查询计划效果评估
     * 
     * @param planId 计划ID
     * @return 效果评估
     */
    Map<String, Object> selectPlanEffectivenessAnalysis(Long planId);

    /**
     * 生成计划报告
     * 
     * @param planId 计划ID
     * @return 报告内容
     */
    Map<String, Object> generatePlanReport(Long planId);

    /**
     * 导出计划数据
     * 
     * @param planId 计划ID
     * @return 导出数据
     */
    Map<String, Object> exportPlanData(Long planId);

    /**
     * 复制测评计划
     * 
     * @param sourcePlanId 源计划ID
     * @param targetPlan 目标计划信息
     * @return 结果
     */
    int copyAssessmentPlan(Long sourcePlanId, PsyTEnterpriseAssessmentPlan targetPlan);

    /**
     * 发送计划提醒
     * 
     * @param planId 计划ID
     * @return 结果
     */
    int sendPlanReminder(Long planId);

    /**
     * 验证计划权限
     * 
     * @param planId 计划ID
     * @param userId 用户ID
     * @param operation 操作类型
     * @return 验证结果
     */
    boolean validatePlanPermission(Long planId, Long userId, String operation);
}
