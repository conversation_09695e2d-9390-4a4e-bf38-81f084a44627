package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.kangnili.KangniliQuestion;
import com.xihuan.system.mapper.KangniliQuestionMapper;
import com.xihuan.system.service.IKangniliQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


// 实现类
@Service
public class KangniliQuestionServiceImpl implements IKangniliQuestionService {

    @Autowired
    private KangniliQuestionMapper questionMapper;

    @Override
    public List<KangniliQuestion> selectQuestionList(KangniliQuestion question) {
        return questionMapper.selectQuestionList(question);
    }

    @Override
    public int insertQuestion(KangniliQuestion question) {
        return questionMapper.insertQuestion(question);
    }

    @Override
    public int updateQuestion(KangniliQuestion question) {
        return questionMapper.updateQuestion(question);
    }

    @Override
    public int deleteQuestionByIds(Long[] ids) {
        return questionMapper.deleteQuestionByIds(ids);
    }

    // 在KangniliQuestionServiceImpl实现类添加
    @Override
    public KangniliQuestion selectQuestionById(Long id) {
        return questionMapper.selectQuestionById(id);
    }

    // 查询全部题干和选项
    public List<KangniliQuestion> getQuestionListWithOptions() {
        return questionMapper.selectQuestionListWithOptions();
    }
}