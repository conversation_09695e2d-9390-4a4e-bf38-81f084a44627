package com.xihuan.system.service;

import com.xihuan.common.core.domain.kangnili.KangniliQuestion;

import java.util.List;


public interface IKangniliQuestionService {
    List<KangniliQuestion> selectQuestionList(KangniliQuestion question);
    int insertQuestion(KangniliQuestion question);
    int updateQuestion(KangniliQuestion question);
    int deleteQuestionByIds(Long[] ids);
    // 在IKangniliQuestionService接口添加
    KangniliQuestion selectQuestionById(Long id);

    List<KangniliQuestion> getQuestionListWithOptions();
}