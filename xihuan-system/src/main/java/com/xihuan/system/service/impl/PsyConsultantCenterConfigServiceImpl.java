package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyConsultantCenterConfig;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.PsyConsultantCenterConfigMapper;
import com.xihuan.system.service.IPsyConsultantCenterConfigService;
import com.xihuan.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 咨询师-咨询中心配置Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyConsultantCenterConfigServiceImpl implements IPsyConsultantCenterConfigService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyConsultantCenterConfigServiceImpl.class);
    
    @Autowired
    private PsyConsultantCenterConfigMapper configMapper;
    
    @Autowired
    private ISysConfigService configService;

    /**
     * 查询咨询师配置列表
     * 
     * @param config 咨询师配置
     * @return 咨询师配置集合
     */
    @Override
    public List<PsyConsultantCenterConfig> selectConfigList(PsyConsultantCenterConfig config) {
        return configMapper.selectConfigList(config);
    }

    /**
     * 根据ID查询咨询师配置
     * 
     * @param id 咨询师配置主键
     * @return 咨询师配置
     */
    @Override
    public PsyConsultantCenterConfig selectConfigById(Long id) {
        return configMapper.selectConfigById(id);
    }

    /**
     * 根据咨询师ID和中心ID查询配置
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 咨询师配置
     */
    @Override
    public PsyConsultantCenterConfig selectConfigByConsultantAndCenter(Long consultantId, Long centerId) {
        return configMapper.selectConfigByConsultantAndCenter(consultantId, centerId);
    }

    /**
     * 根据咨询师ID查询所有配置
     * 
     * @param consultantId 咨询师ID
     * @return 咨询师配置集合
     */
    @Override
    public List<PsyConsultantCenterConfig> selectConfigsByConsultantId(Long consultantId) {
        return configMapper.selectConfigsByConsultantId(consultantId);
    }

    /**
     * 新增咨询师配置
     * 
     * @param config 咨询师配置
     * @return 结果
     */
    @Override
    public int insertConfig(PsyConsultantCenterConfig config) {
        config.setCreateTime(new Date());
        return configMapper.insertConfig(config);
    }

    /**
     * 修改咨询师配置
     * 
     * @param config 咨询师配置
     * @return 结果
     */
    @Override
    public int updateConfig(PsyConsultantCenterConfig config) {
        config.setUpdateTime(new Date());
        return configMapper.updateConfig(config);
    }

    /**
     * 批量删除咨询师配置
     * 
     * @param ids 需要删除的咨询师配置主键集合
     * @return 结果
     */
    @Override
    public int deleteConfigByIds(Long[] ids) {
        return configMapper.deleteConfigByIds(ids);
    }

    /**
     * 删除咨询师配置信息
     * 
     * @param id 咨询师配置主键
     * @return 结果
     */
    @Override
    public int deleteConfigById(Long id) {
        return configMapper.deleteConfigById(id);
    }

    /**
     * 获取咨询师到店时间
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 到店时间(小时)
     */
    @Override
    public Double getArrivalTime(Long consultantId, Long centerId) {
        PsyConsultantCenterConfig config = selectConfigByConsultantAndCenter(consultantId, centerId);
        if (config != null && config.getArrivalTimeHours() != null) {
            return config.getArrivalTimeHours().doubleValue();
        }
        
        // 返回默认值
        return getDefaultArrivalTime();
    }

    /**
     * 检查咨询师是否启用到店时间过滤
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 是否启用
     */
    @Override
    public boolean isArrivalFilterEnabled(Long consultantId, Long centerId) {
        PsyConsultantCenterConfig config = selectConfigByConsultantAndCenter(consultantId, centerId);
        if (config != null && config.getEnableArrivalFilter() != null) {
            return config.getEnableArrivalFilter() == 1;
        }
        
        // 返回默认值
        return getDefaultArrivalFilterEnabled();
    }

    /**
     * 设置咨询师到店时间
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @param arrivalTimeHours 到店时间(小时)
     * @return 结果
     */
    @Override
    @Transactional
    public int setArrivalTime(Long consultantId, Long centerId, Double arrivalTimeHours) {
        PsyConsultantCenterConfig config = selectConfigByConsultantAndCenter(consultantId, centerId);
        
        if (config == null) {
            // 创建新配置
            config = new PsyConsultantCenterConfig();
            config.setConsultantId(consultantId);
            config.setCenterId(centerId);
            config.setArrivalTimeHours(BigDecimal.valueOf(arrivalTimeHours));
            config.setEnableArrivalFilter(1); // 默认启用
            config.setIsDefault(0);
            config.setStatus(1);
            config.setCreateBy("system");
            return insertConfig(config);
        } else {
            // 更新现有配置
            config.setArrivalTimeHours(BigDecimal.valueOf(arrivalTimeHours));
            config.setUpdateBy("system");
            return updateConfig(config);
        }
    }

    /**
     * 设置咨询师是否启用到店时间过滤
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @param enabled 是否启用
     * @return 结果
     */
    @Override
    @Transactional
    public int setArrivalFilterEnabled(Long consultantId, Long centerId, boolean enabled) {
        PsyConsultantCenterConfig config = selectConfigByConsultantAndCenter(consultantId, centerId);
        
        if (config == null) {
            // 创建新配置
            config = new PsyConsultantCenterConfig();
            config.setConsultantId(consultantId);
            config.setCenterId(centerId);
            config.setArrivalTimeHours(BigDecimal.valueOf(getDefaultArrivalTime()));
            config.setEnableArrivalFilter(enabled ? 1 : 0);
            config.setIsDefault(0);
            config.setStatus(1);
            config.setCreateBy("system");
            return insertConfig(config);
        } else {
            // 更新现有配置
            config.setEnableArrivalFilter(enabled ? 1 : 0);
            config.setUpdateBy("system");
            return updateConfig(config);
        }
    }

    /**
     * 为咨询师创建默认配置
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 结果
     */
    @Override
    @Transactional
    public int createDefaultConfig(Long consultantId, Long centerId) {
        // 检查是否已存在配置
        PsyConsultantCenterConfig existingConfig = selectConfigByConsultantAndCenter(consultantId, centerId);
        if (existingConfig != null) {
            logger.info("咨询师 {} 在中心 {} 的配置已存在，跳过创建", consultantId, centerId);
            return 0;
        }
        
        // 创建默认配置
        PsyConsultantCenterConfig config = new PsyConsultantCenterConfig();
        config.setConsultantId(consultantId);
        config.setCenterId(centerId);
        config.setArrivalTimeHours(BigDecimal.valueOf(getDefaultArrivalTime()));
        config.setEnableArrivalFilter(getDefaultArrivalFilterEnabled() ? 1 : 0);
        config.setIsDefault(1);
        config.setStatus(1);
        config.setCreateBy("system");
        config.setRemark("系统自动创建的默认配置");
        
        return insertConfig(config);
    }

    /**
     * 批量为咨询师创建默认配置
     * 
     * @param consultantIds 咨询师ID列表
     * @param centerId 咨询中心ID
     * @return 成功创建的数量
     */
    @Override
    @Transactional
    public int batchCreateDefaultConfig(List<Long> consultantIds, Long centerId) {
        int successCount = 0;
        
        for (Long consultantId : consultantIds) {
            try {
                int result = createDefaultConfig(consultantId, centerId);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("为咨询师 {} 创建默认配置失败: {}", consultantId, e.getMessage());
            }
        }
        
        logger.info("批量创建默认配置完成，成功: {}, 总数: {}", successCount, consultantIds.size());
        return successCount;
    }
    
    /**
     * 获取默认到店时间
     */
    private Double getDefaultArrivalTime() {
        try {
            String configValue = configService.selectConfigByKey("psy.consultant.default.arrival.hours");
            if (StringUtils.isNotEmpty(configValue)) {
                return Double.parseDouble(configValue);
            }
        } catch (Exception e) {
            logger.warn("获取默认到店时间配置失败: {}", e.getMessage());
        }
        
        // 默认2小时
        return 2.0;
    }
    
    /**
     * 获取默认过滤启用状态
     */
    private boolean getDefaultArrivalFilterEnabled() {
        try {
            String configValue = configService.selectConfigByKey("psy.consultant.default.arrival.filter.enabled");
            return "true".equalsIgnoreCase(configValue);
        } catch (Exception e) {
            logger.warn("获取默认过滤启用状态配置失败: {}", e.getMessage());
        }
        
        // 默认启用
        return true;
    }
}
