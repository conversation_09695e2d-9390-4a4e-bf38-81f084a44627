package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyMessage;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyMessageMapper;
import com.xihuan.system.service.wxService.IPsyMessageConversationService;
import com.xihuan.system.service.wxService.IPsyMessageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * 消息服务实现类
 */
@Service
public class PsyMessageServiceImpl implements IPsyMessageService {

    private static final Logger log = LoggerFactory.getLogger(PsyMessageServiceImpl.class);

    @Autowired
    private PsyMessageMapper messageMapper;

    @Autowired
    private IPsyMessageConversationService conversationService;

    /**
     * 发送消息
     */
    @Override
    @Transactional
    public PsyMessage sendMessage(PsyMessage message) {
        message.setSendTime(DateUtils.getNowDate());
        message.setIsWithdrawn("0"); // 默认未撤回
        messageMapper.insertMessage(message);

        // 更新会话最后消息
        conversationService.updateLastMessage(
                message.getConversationId(),
                message.getSenderId(),
                message.getContent()
        );

        conversationService.incrementUnreadCount(message.getConversationId(), message.getIsUser());


        log.info("发送消息成功，消息ID：{}", message.getMessageId());
        return message;
    }

    /**
     * 管理员代替咨询师发送消息
     */
    @Override
    @Transactional
    public PsyMessage sendMessageAsAdmin(PsyMessage message, Long adminId) {
        message.setSendTime(DateUtils.getNowDate());
        message.setIsWithdrawn("0"); // 默认未撤回
        message.setCreateBy(adminId.toString()); // 记录管理员ID作为创建者
        messageMapper.insertMessage(message);

        // 更新会话最后消息
        conversationService.updateLastMessage(
                message.getConversationId(),
                message.getSenderId(), // 使用咨询师ID作为发送者
                message.getContent()
        );

        // 更新会话未读计数
        boolean isUserSender = false; // 对用户来说，发送者是咨询师
        conversationService.incrementUnreadCount(message.getConversationId(), isUserSender);

        log.info("管理员代替咨询师发送消息成功，消息ID：{}, 管理员ID: {}", message.getMessageId(), adminId);
        return message;
    }

    /**
     * 获取会话消息列表
     */
    @Override
    public List<PsyMessage> getMessageList(Long conversationId) {
        return messageMapper.selectMessagesByConversationId(conversationId);
    }

    /**
     * 获取消息详情
     */
    @Override
    public PsyMessage getMessage(Long messageId) {
        return messageMapper.selectMessageById(messageId);
    }

    /**
     * 撤回消息
     */
    @Override
    @Transactional
    public AjaxResult withdrawMessage(Long messageId, Long userId) {
        PsyMessage message = messageMapper.selectMessageById(messageId);

        // 消息不存在或不是发送者
        if (message == null) {
            return AjaxResult.error("消息不存在");
        }
        
        if (!message.getSenderId().equals(userId)) {
            return AjaxResult.error("您不是消息发送者，无权撤回");
        }

        // 检查消息发送时间，超过一定时间不允许撤回（如5分钟）
        Date now = DateUtils.getNowDate();
        long diff = now.getTime() - message.getSendTime().getTime();
        if (diff > 5 * 60 * 1000) {
            log.info("消息已超过撤回时间限制");
            return AjaxResult.error("消息发送超过5分钟，无法撤回");
        }

        // 更新消息为已撤回
        message.setIsWithdrawn("1");
        int rows = messageMapper.updateMessage(message);
        return rows > 0 ? AjaxResult.success("消息撤回成功") : AjaxResult.error("消息撤回失败");
    }

    /**
     * 标记消息为已读
     */
    @Override
    public boolean markAsRead(Long messageId, Long userId) {
        return messageMapper.markMessageAsRead(messageId, userId) > 0;
    }

    /**
     * 批量标记消息为已读
     */
    @Override
    @Transactional
    public boolean markAllAsRead(Long conversationId, Long userId) {
        return messageMapper.markAllMessagesAsRead(conversationId, userId) > 0;
    }
} 