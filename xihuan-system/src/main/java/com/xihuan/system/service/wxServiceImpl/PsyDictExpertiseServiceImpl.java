package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.consultant.PsyDictExpertise;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyDictExpertiseMapper;
import com.xihuan.system.service.wxService.IPsyDictExpertiseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 咨询领域Service业务层处理
 */
@Service
public class PsyDictExpertiseServiceImpl implements IPsyDictExpertiseService {
    @Autowired
    private PsyDictExpertiseMapper psyDictExpertiseMapper;

    /**
     * 查询咨询领域
     */
    @Override
    public PsyDictExpertise selectPsyDictExpertiseById(Long id) {
        return psyDictExpertiseMapper.selectPsyDictExpertiseById(id);
    }

    /**
     * 查询咨询领域列表
     */
    @Override
    public List<PsyDictExpertise> selectPsyDictExpertiseList(PsyDictExpertise psyDictExpertise) {
        return psyDictExpertiseMapper.selectPsyDictExpertiseList(psyDictExpertise);
    }

    /**
     * 新增咨询领域
     */
    @Override
    public int insertPsyDictExpertise(PsyDictExpertise psyDictExpertise) {
        psyDictExpertise.setCreateTime(DateUtils.getNowDate());
        return psyDictExpertiseMapper.insertPsyDictExpertise(psyDictExpertise);
    }

    /**
     * 修改咨询领域
     */
    @Override
    public int updatePsyDictExpertise(PsyDictExpertise psyDictExpertise) {
        psyDictExpertise.setUpdateTime(DateUtils.getNowDate());
        return psyDictExpertiseMapper.updatePsyDictExpertise(psyDictExpertise);
    }

    /**
     * 批量删除咨询领域
     */
    @Override
    public int deletePsyDictExpertiseByIds(Long[] ids) {
        return psyDictExpertiseMapper.deletePsyDictExpertiseByIds(ids);
    }

    /**
     * 删除咨询领域信息
     */
    @Override
    public int deletePsyDictExpertiseById(Long id) {
        return psyDictExpertiseMapper.deletePsyDictExpertiseById(id);
    }

    /**
     * 构建树形结构的咨询领域列表
     */
    @Override
    public List<PsyDictExpertise> buildExpertiseTree() {
        // 获取所有父级领域
        List<PsyDictExpertise> parentExpertises = psyDictExpertiseMapper.selectParentExpertises();
        
        // 为每个父级领域添加子领域
        for (PsyDictExpertise parent : parentExpertises) {
            List<PsyDictExpertise> children = psyDictExpertiseMapper.selectChildrenByParentId(parent.getId());
            parent.setChildren(children);
        }
        
        return parentExpertises;
    }

    /**
     * 只查询父节点列表
     */
    @Override
    public List<PsyDictExpertise> selectParentExpertiseList() {
        return psyDictExpertiseMapper.selectParentExpertises();
    }

    /**
     * 只查询子节点列表
     */
    @Override
    public List<PsyDictExpertise> selectChildExpertiseList() {
        return psyDictExpertiseMapper.selectAllChildExpertises();
    }
} 