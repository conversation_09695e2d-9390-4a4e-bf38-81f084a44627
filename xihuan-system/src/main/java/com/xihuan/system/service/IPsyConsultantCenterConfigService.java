package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyConsultantCenterConfig;
import java.util.List;

/**
 * 咨询师-咨询中心配置Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyConsultantCenterConfigService {
    
    /**
     * 查询咨询师配置列表
     * 
     * @param config 咨询师配置
     * @return 咨询师配置集合
     */
    List<PsyConsultantCenterConfig> selectConfigList(PsyConsultantCenterConfig config);
    
    /**
     * 根据ID查询咨询师配置
     * 
     * @param id 咨询师配置主键
     * @return 咨询师配置
     */
    PsyConsultantCenterConfig selectConfigById(Long id);
    
    /**
     * 根据咨询师ID和中心ID查询配置
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 咨询师配置
     */
    PsyConsultantCenterConfig selectConfigByConsultantAndCenter(Long consultantId, Long centerId);
    
    /**
     * 根据咨询师ID查询所有配置
     * 
     * @param consultantId 咨询师ID
     * @return 咨询师配置集合
     */
    List<PsyConsultantCenterConfig> selectConfigsByConsultantId(Long consultantId);
    
    /**
     * 新增咨询师配置
     * 
     * @param config 咨询师配置
     * @return 结果
     */
    int insertConfig(PsyConsultantCenterConfig config);
    
    /**
     * 修改咨询师配置
     * 
     * @param config 咨询师配置
     * @return 结果
     */
    int updateConfig(PsyConsultantCenterConfig config);
    
    /**
     * 批量删除咨询师配置
     * 
     * @param ids 需要删除的咨询师配置主键集合
     * @return 结果
     */
    int deleteConfigByIds(Long[] ids);
    
    /**
     * 删除咨询师配置信息
     * 
     * @param id 咨询师配置主键
     * @return 结果
     */
    int deleteConfigById(Long id);
    
    /**
     * 获取咨询师到店时间
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 到店时间(小时)
     */
    Double getArrivalTime(Long consultantId, Long centerId);
    
    /**
     * 检查咨询师是否启用到店时间过滤
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 是否启用
     */
    boolean isArrivalFilterEnabled(Long consultantId, Long centerId);
    
    /**
     * 设置咨询师到店时间
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @param arrivalTimeHours 到店时间(小时)
     * @return 结果
     */
    int setArrivalTime(Long consultantId, Long centerId, Double arrivalTimeHours);
    
    /**
     * 设置咨询师是否启用到店时间过滤
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @param enabled 是否启用
     * @return 结果
     */
    int setArrivalFilterEnabled(Long consultantId, Long centerId, boolean enabled);
    
    /**
     * 为咨询师创建默认配置
     * 
     * @param consultantId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 结果
     */
    int createDefaultConfig(Long consultantId, Long centerId);
    
    /**
     * 批量为咨询师创建默认配置
     * 
     * @param consultantIds 咨询师ID列表
     * @param centerId 咨询中心ID
     * @return 成功创建的数量
     */
    int batchCreateDefaultConfig(List<Long> consultantIds, Long centerId);
}
