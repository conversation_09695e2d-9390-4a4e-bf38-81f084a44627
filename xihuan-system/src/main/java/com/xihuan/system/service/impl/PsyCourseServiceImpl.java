package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyCourse;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyCourseChapterMapper;
import com.xihuan.system.mapper.PsyCourseMapper;
import com.xihuan.system.mapper.PsyCourseOrderMapper;
import com.xihuan.system.mapper.PsyCourseReviewMapper;
import com.xihuan.system.mapper.PsyCourseCategoryRelMapper;
import com.xihuan.system.service.IPsyCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 心理咨询课程主表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyCourseServiceImpl implements IPsyCourseService {
    
    @Autowired
    private PsyCourseMapper courseMapper;
    
    @Autowired
    private PsyCourseChapterMapper chapterMapper;
    
    @Autowired
    private PsyCourseOrderMapper orderMapper;
    
    @Autowired
    private PsyCourseReviewMapper reviewMapper;
    
    @Autowired
    private PsyCourseCategoryRelMapper categoryRelMapper;

    /**
     * 查询课程列表
     * 
     * @param course 课程信息
     * @return 课程集合
     */
    @Override
    public List<PsyCourse> selectCourseList(PsyCourse course) {
        return courseMapper.selectCourseList(course);
    }

    /**
     * 查询课程详情（包含章节、讲师、分类等信息）
     * 
     * @param id 课程ID
     * @return 课程详情
     */
    @Override
    public PsyCourse selectCourseWithDetails(Long id) {
        return courseMapper.selectCourseWithDetails(id);
    }

    /**
     * 根据ID查询课程
     * 
     * @param id 课程ID
     * @return 课程信息
     */
    @Override
    public PsyCourse selectCourseById(Long id) {
        return courseMapper.selectCourseById(id);
    }

    /**
     * 新增课程
     * 
     * @param course 课程信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertCourse(PsyCourse course) {
        course.setCreateTime(DateUtils.getNowDate());
        course.setDelFlag(0);
        course.setStatus(0); // 默认未发布状态
        course.setSalesCount(0);
        course.setChapterCount(0);
        course.setTrialChapterCount(0);
        course.setViewCount(0);
        course.setRatingAvg(BigDecimal.ZERO);
        course.setRatingCount(0);
        
        int result = courseMapper.insertCourse(course);
        
        // 处理分类关系
        if (result > 0 && !CollectionUtils.isEmpty(course.getCategoryIds())) {
            courseMapper.batchInsertCourseCategories(course.getId(), course.getCategoryIds());
        }
        
        return result;
    }

    /**
     * 修改课程
     * 
     * @param course 课程信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateCourse(PsyCourse course) {
        course.setUpdateTime(DateUtils.getNowDate());
        
        // 删除原有分类关系
        courseMapper.deleteCourseCategories(course.getId());
        
        // 重新插入分类关系
        if (!CollectionUtils.isEmpty(course.getCategoryIds())) {
            courseMapper.batchInsertCourseCategories(course.getId(), course.getCategoryIds());
        }
        
        return courseMapper.updateCourse(course);
    }

    /**
     * 删除课程
     * 
     * @param id 课程ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCourseById(Long id) {
        // 检查是否可以删除
        if (!canDeleteCourse(id)) {
            throw new ServiceException("该课程已有订单，无法删除");
        }
        
        // 删除分类关系
        courseMapper.deleteCourseCategories(id);
        
        // 删除章节
        chapterMapper.deleteChapterByCourseId(id);
        
        // 删除评价
        reviewMapper.deleteReviewByCourseId(id);
        
        // 删除课程
        return courseMapper.deleteCourseById(id);
    }

    /**
     * 批量删除课程
     * 
     * @param ids 需要删除的课程ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCourseByIds(Long[] ids) {
        // 检查是否可以删除
        for (Long id : ids) {
            if (!canDeleteCourse(id)) {
                throw new ServiceException("课程ID为" + id + "的课程已有订单，无法删除");
            }
        }
        
        // 删除分类关系
        courseMapper.deleteCourseCategoriesByIds(ids);
        
        // 删除章节
        chapterMapper.deleteChapterByCourseIds(ids);
        
        // 删除评价
        reviewMapper.deleteReviewByCourseIds(ids);
        
        // 删除课程
        return courseMapper.deleteCourseByIds(ids);
    }

    /**
     * 根据分类ID查询课程列表
     * 
     * @param categoryId 分类ID
     * @return 课程集合
     */
    @Override
    public List<PsyCourse> selectCoursesByCategoryId(Long categoryId) {
        return courseMapper.selectCoursesByCategoryId(categoryId);
    }

    /**
     * 根据讲师ID查询课程列表
     * 
     * @param instructorId 讲师ID
     * @return 课程集合
     */
    @Override
    public List<PsyCourse> selectCoursesByInstructorId(Long instructorId) {
        return courseMapper.selectCoursesByInstructorId(instructorId);
    }

    /**
     * 更新课程统计信息
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    @Override
    public int updateCourseStatistics(Long courseId) {
        // 统计章节数量
        int chapterCount = chapterMapper.selectChapterCount(courseId);
        
        // 统计试听章节数量
        int trialChapterCount = chapterMapper.selectTrialChapterCount(courseId);
        
        // 统计总时长
        int durationTotal = chapterMapper.selectTotalDuration(courseId);
        
        return courseMapper.updateCourseStatistics(courseId, chapterCount, trialChapterCount, durationTotal);
    }

    /**
     * 更新课程评分信息
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    @Override
    public int updateCourseRating(Long courseId) {
        // 计算平均评分
        BigDecimal ratingAvg = reviewMapper.calculateAverageRating(courseId);
        
        // 统计评价数量
        int ratingCount = reviewMapper.countReviewsByCourseId(courseId);
        
        return courseMapper.updateCourseRating(courseId, ratingAvg, ratingCount);
    }

    /**
     * 增加课程观看次数
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    @Override
    public int incrementViewCount(Long courseId) {
        return courseMapper.incrementViewCount(courseId);
    }

    /**
     * 增加课程销售数量
     * 
     * @param courseId 课程ID
     * @param count 增加数量
     * @return 结果
     */
    @Override
    public int incrementSalesCount(Long courseId, Integer count) {
        return courseMapper.incrementSalesCount(courseId, count);
    }

    /**
     * 发布课程
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    @Override
    public int publishCourse(Long courseId) {
        PsyCourse course = new PsyCourse();
        course.setId(courseId);
        course.setStatus(1); // 已发布
        course.setUpdateTime(DateUtils.getNowDate());
        return courseMapper.updateCourse(course);
    }

    /**
     * 下架课程
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    @Override
    public int unpublishCourse(Long courseId) {
        PsyCourse course = new PsyCourse();
        course.setId(courseId);
        course.setStatus(2); // 已下架
        course.setUpdateTime(DateUtils.getNowDate());
        return courseMapper.updateCourse(course);
    }

    /**
     * 检查课程是否可以删除
     * 
     * @param courseId 课程ID
     * @return 是否可以删除
     */
    @Override
    public boolean canDeleteCourse(Long courseId) {
        // 检查是否有已支付的订单
        List<com.xihuan.common.core.domain.entity.PsyCourseOrder> orders = orderMapper.selectOrdersByCourseId(courseId);
        for (com.xihuan.common.core.domain.entity.PsyCourseOrder order : orders) {
            if (order.getStatus() != null && order.getStatus() >= 1) { // 已支付或已完成
                return false;
            }
        }
        return true;
    }
}
