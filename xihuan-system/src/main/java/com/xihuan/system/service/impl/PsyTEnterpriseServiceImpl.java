package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTEnterprise;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.PsyTEnterpriseMapper;
import com.xihuan.system.service.IPsyTEnterpriseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 企业信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTEnterpriseServiceImpl implements IPsyTEnterpriseService {

    @Autowired
    private PsyTEnterpriseMapper enterpriseMapper;

    /**
     * 查询企业列表
     * 
     * @param enterprise 企业信息
     * @return 企业集合
     */
    @Override
    public List<PsyTEnterprise> selectEnterpriseList(PsyTEnterprise enterprise) {
        return enterpriseMapper.selectEnterpriseList(enterprise);
    }

    /**
     * 根据ID查询企业
     * 
     * @param id 企业ID
     * @return 企业信息
     */
    @Override
    public PsyTEnterprise selectEnterpriseById(Long id) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }
        return enterpriseMapper.selectEnterpriseById(id);
    }

    /**
     * 查询企业详情（包含部门、测评计划等信息）
     * 
     * @param id 企业ID
     * @return 企业详情
     */
    @Override
    public PsyTEnterprise selectEnterpriseWithDetails(Long id) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }
        return enterpriseMapper.selectEnterpriseWithDetails(id);
    }

    /**
     * 根据企业编码查询企业
     * 
     * @param enterpriseCode 企业编码
     * @return 企业信息
     */
    @Override
    public PsyTEnterprise selectEnterpriseByCode(String enterpriseCode) {
        if (StringUtils.isEmpty(enterpriseCode)) {
            throw new ServiceException("企业编码不能为空");
        }
        return enterpriseMapper.selectEnterpriseByCode(enterpriseCode);
    }

    /**
     * 新增企业
     * 
     * @param enterprise 企业信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEnterprise(PsyTEnterprise enterprise) {
        if (enterprise == null) {
            throw new ServiceException("企业信息不能为空");
        }
        
        // 验证企业编码唯一性
        if (StringUtils.isNotEmpty(enterprise.getEnterpriseCode()) && 
            !checkEnterpriseCodeUnique(enterprise.getEnterpriseCode(), null)) {
            throw new ServiceException("企业编码已存在");
        }
        
        enterprise.setCreateTime(DateUtils.getNowDate());
        return enterpriseMapper.insertEnterprise(enterprise);
    }

    /**
     * 修改企业
     * 
     * @param enterprise 企业信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEnterprise(PsyTEnterprise enterprise) {
        if (enterprise == null || enterprise.getId() == null) {
            throw new ServiceException("企业ID不能为空");
        }
        
        // 验证企业编码唯一性
        if (StringUtils.isNotEmpty(enterprise.getEnterpriseCode()) && 
            !checkEnterpriseCodeUnique(enterprise.getEnterpriseCode(), enterprise.getId())) {
            throw new ServiceException("企业编码已存在");
        }
        
        enterprise.setUpdateTime(DateUtils.getNowDate());
        return enterpriseMapper.updateEnterprise(enterprise);
    }

    /**
     * 删除企业
     * 
     * @param ids 需要删除的企业ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEnterpriseByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            throw new ServiceException("删除的企业ID不能为空");
        }
        return enterpriseMapper.deleteEnterpriseByIds(ids);
    }

    /**
     * 检查企业编码唯一性
     * 
     * @param enterpriseCode 企业编码
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    @Override
    public boolean checkEnterpriseCodeUnique(String enterpriseCode, Long excludeId) {
        if (StringUtils.isEmpty(enterpriseCode)) {
            return false;
        }
        
        PsyTEnterprise enterprise = enterpriseMapper.selectEnterpriseByCode(enterpriseCode);
        if (enterprise == null) {
            return true;
        }
        
        return excludeId != null && enterprise.getId().equals(excludeId);
    }

    /**
     * 查询启用的企业列表
     * 
     * @return 企业集合
     */
    @Override
    public List<PsyTEnterprise> selectEnabledEnterprises() {
        return enterpriseMapper.selectEnabledEnterprises();
    }

    /**
     * 查询即将过期的企业列表
     * 
     * @param days 天数
     * @return 企业集合
     */
    @Override
    public List<PsyTEnterprise> selectExpiringEnterprises(Integer days) {
        if (days == null || days <= 0) {
            days = 30; // 默认30天
        }
        return enterpriseMapper.selectExpiringEnterprises(days);
    }

    /**
     * 查询已过期的企业列表
     * 
     * @return 企业集合
     */
    @Override
    public List<PsyTEnterprise> selectExpiredEnterprises() {
        return enterpriseMapper.selectExpiredEnterprises();
    }

    /**
     * 更新企业测评使用次数
     * 
     * @param id 企业ID
     * @param count 使用次数
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAssessmentUsageCount(Long id, Integer count) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }
        if (count == null || count < 0) {
            throw new ServiceException("使用次数不能为空或小于0");
        }
        return enterpriseMapper.updateAssessmentUsageCount(id, count);
    }

    /**
     * 增加企业测评使用次数
     * 
     * @param id 企业ID
     * @param increment 增量
     * @return 结果
     */
    @Override
    @Transactional
    public int incrementAssessmentUsageCount(Long id, Integer increment) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }
        if (increment == null || increment <= 0) {
            throw new ServiceException("增量必须大于0");
        }
        return enterpriseMapper.incrementAssessmentUsageCount(id, increment);
    }

    /**
     * 统计企业数量
     * 
     * @param enterprise 查询条件
     * @return 数量
     */
    @Override
    public int countEnterprises(PsyTEnterprise enterprise) {
        return enterpriseMapper.countEnterprises(enterprise);
    }

    /**
     * 查询企业统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectEnterpriseStats() {
        return enterpriseMapper.selectEnterpriseStats();
    }

    /**
     * 查询企业类型统计
     * 
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectEnterpriseTypeStats() {
        return enterpriseMapper.selectEnterpriseTypeStats();
    }

    /**
     * 查询企业规模统计
     * 
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectEnterpriseScaleStats() {
        return enterpriseMapper.selectEnterpriseScaleStats();
    }

    /**
     * 查询企业测评使用统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectEnterpriseUsageStats(Long enterpriseId) {
        if (enterpriseId == null) {
            throw new ServiceException("企业ID不能为空");
        }
        return enterpriseMapper.selectEnterpriseUsageStats(enterpriseId);
    }

    /**
     * 查询企业测评趋势
     * 
     * @param enterpriseId 企业ID
     * @param days 天数
     * @return 趋势信息
     */
    @Override
    public List<Map<String, Object>> selectEnterpriseUsageTrend(Long enterpriseId, Integer days) {
        if (enterpriseId == null) {
            throw new ServiceException("企业ID不能为空");
        }
        if (days == null || days <= 0) {
            days = 30; // 默认30天
        }
        return enterpriseMapper.selectEnterpriseUsageTrend(enterpriseId, days);
    }

    /**
     * 搜索企业
     * 
     * @param keyword 关键词
     * @param enterpriseType 企业类型
     * @param scale 企业规模
     * @param status 状态
     * @return 企业集合
     */
    @Override
    public List<PsyTEnterprise> searchEnterprises(String keyword, Integer enterpriseType, String scale, Integer status) {
        return enterpriseMapper.searchEnterprises(keyword, enterpriseType, scale, status);
    }

    /**
     * 批量更新企业状态
     * 
     * @param ids 企业ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUpdateEnterpriseStatus(Long[] ids, Integer status) {
        if (ids == null || ids.length == 0) {
            throw new ServiceException("企业ID不能为空");
        }
        if (status == null) {
            throw new ServiceException("状态不能为空");
        }
        return enterpriseMapper.batchUpdateEnterpriseStatus(ids, status);
    }

    /**
     * 查询企业合同到期提醒
     *
     * @param days 提前天数
     * @return 企业集合
     */
    @Override
    public List<PsyTEnterprise> selectContractExpiryReminder(Integer days) {
        if (days == null || days <= 0) {
            days = 30; // 默认30天
        }
        return enterpriseMapper.selectContractExpiryReminder(days);
    }

    /**
     * 查询企业测评额度预警
     *
     * @param threshold 预警阈值(百分比)
     * @return 企业集合
     */
    @Override
    public List<PsyTEnterprise> selectAssessmentQuotaWarning(Integer threshold) {
        if (threshold == null || threshold <= 0 || threshold > 100) {
            threshold = 80; // 默认80%
        }
        return enterpriseMapper.selectAssessmentQuotaWarning(threshold);
    }

    /**
     * 查询企业行业分布
     *
     * @return 分布信息
     */
    @Override
    public List<Map<String, Object>> selectEnterpriseIndustryDistribution() {
        return enterpriseMapper.selectEnterpriseIndustryDistribution();
    }

    /**
     * 查询企业地区分布
     *
     * @return 分布信息
     */
    @Override
    public List<Map<String, Object>> selectEnterpriseRegionDistribution() {
        return enterpriseMapper.selectEnterpriseRegionDistribution();
    }

    /**
     * 查询企业服务套餐统计
     *
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectServicePackageStats() {
        return enterpriseMapper.selectServicePackageStats();
    }

    /**
     * 查询企业活跃度统计
     *
     * @param days 天数
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectEnterpriseActivityStats(Integer days) {
        if (days == null || days <= 0) {
            days = 30; // 默认30天
        }
        return enterpriseMapper.selectEnterpriseActivityStats(days);
    }

    /**
     * 查询企业收入统计
     *
     * @param year 年份
     * @param month 月份
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectEnterpriseRevenueStats(Integer year, Integer month) {
        if (year == null) {
            year = Calendar.getInstance().get(Calendar.YEAR);
        }
        return enterpriseMapper.selectEnterpriseRevenueStats(year, month);
    }

    /**
     * 查询企业续费提醒
     *
     * @param days 提前天数
     * @return 企业集合
     */
    @Override
    public List<PsyTEnterprise> selectRenewalReminder(Integer days) {
        if (days == null || days <= 0) {
            days = 30; // 默认30天
        }
        return enterpriseMapper.selectRenewalReminder(days);
    }

    /**
     * 自动更新过期企业状态
     *
     * @return 更新数量
     */
    @Override
    @Transactional
    public int autoUpdateExpiredEnterpriseStatus() {
        return enterpriseMapper.autoUpdateExpiredEnterpriseStatus();
    }

    /**
     * 企业认证
     *
     * @param id 企业ID
     * @return 结果
     */
    @Override
    @Transactional
    public int certifyEnterprise(Long id) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }

        PsyTEnterprise enterprise = new PsyTEnterprise();
        enterprise.setId(id);
        enterprise.setStatus(1); // 设置为已认证状态
        enterprise.setUpdateTime(DateUtils.getNowDate());

        return enterpriseMapper.updateEnterprise(enterprise);
    }

    /**
     * 企业续费
     *
     * @param id 企业ID
     * @param months 续费月数
     * @param assessmentCount 测评次数
     * @return 结果
     */
    @Override
    @Transactional
    public int renewEnterprise(Long id, Integer months, Integer assessmentCount) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }
        if (months == null || months <= 0) {
            throw new ServiceException("续费月数必须大于0");
        }

        // 获取当前企业信息
        PsyTEnterprise enterprise = enterpriseMapper.selectEnterpriseById(id);
        if (enterprise == null) {
            throw new ServiceException("企业不存在");
        }

        // 计算新的合同结束时间
        Calendar calendar = Calendar.getInstance();
        if (enterprise.getContractEndDate() != null && enterprise.getContractEndDate().after(new Date())) {
            calendar.setTime(enterprise.getContractEndDate());
        } else {
            calendar.setTime(new Date());
        }
        calendar.add(Calendar.MONTH, months);

        // 更新企业信息
        PsyTEnterprise updateEnterprise = new PsyTEnterprise();
        updateEnterprise.setId(id);
        updateEnterprise.setContractEndDate(calendar.getTime());
        if (assessmentCount != null && assessmentCount > 0) {
            updateEnterprise.setMaxAssessmentCount(enterprise.getMaxAssessmentCount() + assessmentCount);
        }
        updateEnterprise.setUpdateTime(DateUtils.getNowDate());

        return enterpriseMapper.updateEnterprise(updateEnterprise);
    }

    /**
     * 企业升级
     *
     * @param id 企业ID
     * @param servicePackage 服务套餐
     * @return 结果
     */
    @Override
    @Transactional
    public int upgradeEnterprise(Long id, String servicePackage) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }
        if (StringUtils.isEmpty(servicePackage)) {
            throw new ServiceException("服务套餐不能为空");
        }

        // 更新企业服务套餐
        PsyTEnterprise enterprise = new PsyTEnterprise();
        enterprise.setId(id);
        enterprise.setServicePackage(servicePackage);
        enterprise.setUpdateTime(DateUtils.getNowDate());

        return enterpriseMapper.updateEnterprise(enterprise);
    }

    /**
     * 企业降级
     *
     * @param id 企业ID
     * @param servicePackage 服务套餐
     * @return 结果
     */
    @Override
    @Transactional
    public int downgradeEnterprise(Long id, String servicePackage) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }
        if (StringUtils.isEmpty(servicePackage)) {
            throw new ServiceException("服务套餐不能为空");
        }

        // 更新企业服务套餐
        PsyTEnterprise enterprise = new PsyTEnterprise();
        enterprise.setId(id);
        enterprise.setServicePackage(servicePackage);
        enterprise.setUpdateTime(DateUtils.getNowDate());

        return enterpriseMapper.updateEnterprise(enterprise);
    }

    /**
     * 冻结企业
     *
     * @param id 企业ID
     * @param reason 冻结原因
     * @return 结果
     */
    @Override
    @Transactional
    public int freezeEnterprise(Long id, String reason) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }

        // 更新企业状态为冻结
        PsyTEnterprise enterprise = new PsyTEnterprise();
        enterprise.setId(id);
        enterprise.setStatus(0); // 设置为冻结状态
        enterprise.setRemark(reason); // 将冻结原因记录在备注中
        enterprise.setUpdateTime(DateUtils.getNowDate());

        return enterpriseMapper.updateEnterprise(enterprise);
    }

    /**
     * 解冻企业
     *
     * @param id 企业ID
     * @return 结果
     */
    @Override
    @Transactional
    public int unfreezeEnterprise(Long id) {
        if (id == null) {
            throw new ServiceException("企业ID不能为空");
        }

        // 更新企业状态为正常
        PsyTEnterprise enterprise = new PsyTEnterprise();
        enterprise.setId(id);
        enterprise.setStatus(1); // 设置为正常状态
        enterprise.setUpdateTime(DateUtils.getNowDate());

        return enterpriseMapper.updateEnterprise(enterprise);
    }

    /**
     * 企业数据导出
     *
     * @param ids 企业ID数组
     * @return 导出数据
     */
    @Override
    public List<Map<String, Object>> exportEnterpriseData(Long[] ids) {
        if (ids == null || ids.length == 0) {
            throw new ServiceException("导出的企业ID不能为空");
        }

        List<Map<String, Object>> exportData = new ArrayList<>();

        for (Long id : ids) {
            PsyTEnterprise enterprise = enterpriseMapper.selectEnterpriseById(id);
            if (enterprise != null) {
                Map<String, Object> data = new HashMap<>();
                data.put("id", enterprise.getId());
                data.put("enterpriseName", enterprise.getEnterpriseName());
                data.put("enterpriseCode", enterprise.getEnterpriseCode());
                data.put("contactPerson", enterprise.getContactPerson());
                data.put("contactPhone", enterprise.getContactPhone());
                data.put("contactEmail", enterprise.getContactEmail());
                data.put("address", enterprise.getAddress());
                data.put("industry", enterprise.getIndustry());
                data.put("scale", enterprise.getScale());
                data.put("servicePackage", enterprise.getServicePackage());
                data.put("maxAssessmentCount", enterprise.getMaxAssessmentCount());
                data.put("usedAssessmentCount", enterprise.getUsedAssessmentCount());
                data.put("contractStartDate", enterprise.getContractStartDate());
                data.put("contractEndDate", enterprise.getContractEndDate());
                data.put("status", enterprise.getStatus());
                data.put("createTime", enterprise.getCreateTime());
                exportData.add(data);
            }
        }

        return exportData;
    }

    /**
     * 企业数据导入
     *
     * @param enterpriseData 企业数据
     * @return 结果
     */
    @Override
    @Transactional
    public int importEnterpriseData(List<Map<String, Object>> enterpriseData) {
        if (enterpriseData == null || enterpriseData.isEmpty()) {
            throw new ServiceException("导入数据不能为空");
        }

        int successCount = 0;
        for (Map<String, Object> data : enterpriseData) {
            try {
                // 将 Map 转换为 PsyTEnterprise 对象
                PsyTEnterprise enterprise = convertMapToEnterprise(data);

                // 检查企业编码是否已存在
                if (StringUtils.isNotEmpty(enterprise.getEnterpriseCode()) &&
                    !checkEnterpriseCodeUnique(enterprise.getEnterpriseCode(), null)) {
                    continue; // 跳过重复的企业编码
                }

                enterprise.setCreateTime(DateUtils.getNowDate());
                if (enterpriseMapper.insertEnterprise(enterprise) > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                // 记录错误但继续处理其他数据
                continue;
            }
        }

        return successCount;
    }

    /**
     * 验证企业权限
     *
     * @param enterpriseId 企业ID
     * @param operation 操作类型
     * @return 验证结果
     */
    @Override
    public boolean validateEnterprisePermission(Long enterpriseId, String operation) {
        if (enterpriseId == null) {
            return false;
        }

        PsyTEnterprise enterprise = selectEnterpriseById(enterpriseId);
        if (enterprise == null) {
            return false;
        }

        // 检查企业状态
        if (!enterprise.isEnabled()) {
            return false;
        }

        // 检查是否过期
        if (enterprise.isExpired()) {
            return false;
        }

        // 根据操作类型进行权限验证
        if ("ASSESSMENT".equals(operation)) {
            // 检查测评权限
            return enterprise.canUseAssessment();
        }

        return true;
    }

    /**
     * 将 Map 转换为 PsyTEnterprise 对象
     *
     * @param data 数据Map
     * @return PsyTEnterprise对象
     */
    private PsyTEnterprise convertMapToEnterprise(Map<String, Object> data) {
        PsyTEnterprise enterprise = new PsyTEnterprise();

        if (data.get("enterpriseName") != null) {
            enterprise.setEnterpriseName(data.get("enterpriseName").toString());
        }
        if (data.get("enterpriseCode") != null) {
            enterprise.setEnterpriseCode(data.get("enterpriseCode").toString());
        }
        if (data.get("contactPerson") != null) {
            enterprise.setContactPerson(data.get("contactPerson").toString());
        }
        if (data.get("contactPhone") != null) {
            enterprise.setContactPhone(data.get("contactPhone").toString());
        }
        if (data.get("contactEmail") != null) {
            enterprise.setContactEmail(data.get("contactEmail").toString());
        }
        if (data.get("address") != null) {
            enterprise.setAddress(data.get("address").toString());
        }
        if (data.get("industry") != null) {
            enterprise.setIndustry(data.get("industry").toString());
        }
        if (data.get("scale") != null) {
            enterprise.setScale(data.get("scale").toString());
        }
        if (data.get("maxAssessmentCount") != null) {
            enterprise.setMaxAssessmentCount(Integer.valueOf(data.get("maxAssessmentCount").toString()));
        }

        // 设置默认值
        enterprise.setStatus(1); // 默认启用
        enterprise.setDelFlag("0"); // 默认未删除

        return enterprise;
    }
}
