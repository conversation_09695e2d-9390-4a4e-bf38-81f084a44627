package com.xihuan.system.utils;

import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时间槽诊断工具类
 * 用于诊断和分析时间槽生成问题
 * 
 * <AUTHOR>
 */
public class TimeSlotDiagnosticUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeSlotDiagnosticUtil.class);
    
    /**
     * 诊断时间段配置
     */
    public static Map<String, Object> diagnoseTimeRanges(List<PsyTimeRange> timeRanges) {
        Map<String, Object> result = new HashMap<>();
        List<String> issues = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();
        
        if (timeRanges == null || timeRanges.isEmpty()) {
            issues.add("没有找到任何时间段配置");
            suggestions.add("请初始化默认时间段配置");
            result.put("issues", issues);
            result.put("suggestions", suggestions);
            result.put("severity", "CRITICAL");
            return result;
        }
        
        // 按开始时间排序
        List<PsyTimeRange> sortedRanges = timeRanges.stream()
            .sorted(Comparator.comparing(PsyTimeRange::getStartHour))
            .collect(Collectors.toList());
        
        // 检查时间段完整性
        for (PsyTimeRange range : sortedRanges) {
            if (range.getStartHour() == null || range.getEndHour() == null) {
                issues.add(String.format("时间段 %d 缺少开始或结束时间", range.getId()));
            } else if (range.getStartHour() >= range.getEndHour()) {
                issues.add(String.format("时间段 %d 开始时间(%d)不能大于等于结束时间(%d)", 
                    range.getId(), range.getStartHour(), range.getEndHour()));
            }
        }
        
        // 检查时间段连续性
        for (int i = 0; i < sortedRanges.size() - 1; i++) {
            PsyTimeRange current = sortedRanges.get(i);
            PsyTimeRange next = sortedRanges.get(i + 1);
            
            if (current.getEndHour() < next.getStartHour()) {
                int gap = next.getStartHour() - current.getEndHour();
                issues.add(String.format("时间段空隙：%d:00-%d:00 (空隙%d小时)", 
                    current.getEndHour(), next.getStartHour(), gap));
                suggestions.add(String.format("建议添加时间段填补 %d:00-%d:00 的空隙", 
                    current.getEndHour(), next.getStartHour()));
            } else if (current.getEndHour() > next.getStartHour()) {
                issues.add(String.format("时间段重叠：%d:00-%d:00 与 %d:00-%d:00", 
                    current.getStartHour(), current.getEndHour(),
                    next.getStartHour(), next.getEndHour()));
                suggestions.add("建议调整重叠的时间段边界");
            }
        }
        
        // 检查工作时间覆盖
        int minHour = sortedRanges.get(0).getStartHour();
        int maxHour = sortedRanges.get(sortedRanges.size() - 1).getEndHour();
        
        if (minHour > 9) {
            suggestions.add(String.format("建议将工作时间提前到9:00（当前最早%d:00）", minHour));
        }
        if (maxHour < 21) {
            suggestions.add(String.format("建议将工作时间延后到21:00（当前最晚%d:00）", maxHour));
        }
        
        result.put("issues", issues);
        result.put("suggestions", suggestions);
        result.put("totalRanges", timeRanges.size());
        result.put("workingHours", maxHour - minHour);
        result.put("severity", issues.isEmpty() ? "OK" : (issues.size() > 3 ? "HIGH" : "MEDIUM"));
        
        return result;
    }
    
    /**
     * 诊断系统时间槽生成结果
     */
    public static Map<String, Object> diagnoseSystemTimeSlots(List<PsySystemTimeSlot> timeSlots, LocalDate date) {
        Map<String, Object> result = new HashMap<>();
        List<String> issues = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();
        
        if (timeSlots == null || timeSlots.isEmpty()) {
            issues.add("没有生成任何系统时间槽");
            suggestions.add("检查时间段配置是否正确");
            result.put("issues", issues);
            result.put("suggestions", suggestions);
            result.put("severity", "CRITICAL");
            return result;
        }
        
        // 按开始时间排序
        List<PsySystemTimeSlot> sortedSlots = timeSlots.stream()
            .filter(slot -> slot.getDateKey().equals(date.toString()))
            .sorted(Comparator.comparing(PsySystemTimeSlot::getStartTime))
            .collect(Collectors.toList());
        
        // 检查时间槽连续性
        for (int i = 0; i < sortedSlots.size() - 1; i++) {
            PsySystemTimeSlot current = sortedSlots.get(i);
            PsySystemTimeSlot next = sortedSlots.get(i + 1);
            
            if (!current.getEndTime().equals(next.getStartTime())) {
                issues.add(String.format("时间槽不连续：%s-%s 与 %s-%s", 
                    current.getStartTime(), current.getEndTime(),
                    next.getStartTime(), next.getEndTime()));
            }
        }
        
        // 统计时间段分布
        Map<Long, Long> rangeSlotCount = sortedSlots.stream()
            .collect(Collectors.groupingBy(PsySystemTimeSlot::getRangeId, Collectors.counting()));
        
        // 检查时间槽数量合理性
        LocalTime firstSlotTime = sortedSlots.get(0).getStartTime();
        LocalTime lastSlotTime = sortedSlots.get(sortedSlots.size() - 1).getEndTime();
        long totalMinutes = java.time.Duration.between(firstSlotTime, lastSlotTime).toMinutes();
        long expectedSlots = totalMinutes / 15; // 每15分钟一个时间槽
        
        if (sortedSlots.size() < expectedSlots * 0.8) {
            issues.add(String.format("时间槽数量偏少：实际%d个，预期约%d个", sortedSlots.size(), expectedSlots));
            suggestions.add("检查时间段配置是否有遗漏");
        }
        
        result.put("issues", issues);
        result.put("suggestions", suggestions);
        result.put("totalSlots", sortedSlots.size());
        result.put("expectedSlots", expectedSlots);
        result.put("rangeDistribution", rangeSlotCount);
        result.put("timeSpan", String.format("%s-%s", firstSlotTime, lastSlotTime));
        result.put("severity", issues.isEmpty() ? "OK" : (issues.size() > 2 ? "HIGH" : "MEDIUM"));
        
        return result;
    }
    
    /**
     * 生成时间槽修复建议
     */
    public static List<String> generateFixSuggestions(Map<String, Object> timeRangeDiagnosis, 
                                                     Map<String, Object> timeSlotDiagnosis) {
        List<String> suggestions = new ArrayList<>();
        
        // 从时间段诊断获取建议
        @SuppressWarnings("unchecked")
        List<String> rangeIssues = (List<String>) timeRangeDiagnosis.get("issues");
        if (rangeIssues != null && !rangeIssues.isEmpty()) {
            suggestions.add("1. 修复时间段配置问题：");
            suggestions.addAll(rangeIssues.stream()
                .map(issue -> "   - " + issue)
                .collect(Collectors.toList()));
        }
        
        // 从时间槽诊断获取建议
        @SuppressWarnings("unchecked")
        List<String> slotIssues = (List<String>) timeSlotDiagnosis.get("issues");
        if (slotIssues != null && !slotIssues.isEmpty()) {
            suggestions.add("2. 修复时间槽生成问题：");
            suggestions.addAll(slotIssues.stream()
                .map(issue -> "   - " + issue)
                .collect(Collectors.toList()));
        }
        
        // 通用建议
        suggestions.add("3. 通用修复步骤：");
        suggestions.add("   - 执行 checkAndFixTimeRangeContinuity() 方法修复时间段连续性");
        suggestions.add("   - 执行 regenerateSystemTimeSlots() 方法重新生成系统时间槽");
        suggestions.add("   - 检查数据库约束和索引是否正常");
        suggestions.add("   - 查看应用日志获取详细错误信息");
        
        return suggestions;
    }
    
    /**
     * 打印诊断报告
     */
    public static void printDiagnosticReport(Map<String, Object> timeRangeDiagnosis, 
                                           Map<String, Object> timeSlotDiagnosis,
                                           LocalDate date) {
        logger.info("========== 时间槽诊断报告 (日期: {}) ==========", date);
        
        logger.info("时间段配置诊断：");
        logger.info("  - 总数: {}", timeRangeDiagnosis.get("totalRanges"));
        logger.info("  - 工作时长: {}小时", timeRangeDiagnosis.get("workingHours"));
        logger.info("  - 严重程度: {}", timeRangeDiagnosis.get("severity"));
        
        logger.info("系统时间槽诊断：");
        logger.info("  - 总数: {}", timeSlotDiagnosis.get("totalSlots"));
        logger.info("  - 预期数量: {}", timeSlotDiagnosis.get("expectedSlots"));
        logger.info("  - 时间跨度: {}", timeSlotDiagnosis.get("timeSpan"));
        logger.info("  - 严重程度: {}", timeSlotDiagnosis.get("severity"));
        
        List<String> fixSuggestions = generateFixSuggestions(timeRangeDiagnosis, timeSlotDiagnosis);
        logger.info("修复建议：");
        fixSuggestions.forEach(suggestion -> logger.info("  {}", suggestion));
        
        logger.info("================================================");
    }
}
