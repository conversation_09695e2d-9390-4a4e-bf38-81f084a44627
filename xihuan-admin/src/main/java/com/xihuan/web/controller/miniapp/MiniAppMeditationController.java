package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyMeditation;
import com.xihuan.common.core.domain.entity.PsyMeditationOrder;
import com.xihuan.common.core.domain.entity.PsyMeditationReview;
import com.xihuan.common.core.domain.entity.PsyUserMeditationRecord;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.IPsyMeditationOrderService;
import com.xihuan.system.service.IPsyMeditationReviewService;
import com.xihuan.system.service.IPsyMeditationService;
import com.xihuan.system.service.IPsyUserMeditationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序冥想Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/meditation")
public class MiniAppMeditationController extends BaseController {
    
    @Autowired
    private IPsyMeditationService meditationService;
    
    @Autowired
    private IPsyMeditationOrderService orderService;
    
    @Autowired
    private IPsyMeditationReviewService reviewService;
    
    @Autowired
    private IPsyUserMeditationRecordService recordService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取冥想列表
     */
    @GetMapping("/list")
    public AjaxResult list(PsyMeditation meditation) {
        // 小程序端只显示已发布的冥想
        meditation.setStatus(1);
        List<PsyMeditation> list = meditationService.selectMeditationList(meditation);
        return success(list);
    }

    /**
     * 获取冥想详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        // 获取冥想详情
        PsyMeditation meditation = meditationService.selectMeditationWithDetails(id);
        if (meditation == null || meditation.getStatus() != 1) {
            return error("冥想不存在或已下架");
        }

        // 增加冥想播放次数
        meditationService.incrementPlayCount(id);

        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser != null) {
            Long userId = loginUser.getUserId();

            // 检查用户是否已购买
            boolean purchased = orderService.checkUserPurchased(userId, id);
            meditation.setPurchased(purchased);

            // 获取用户最新播放记录
            if (purchased || meditation.getIsFree() == 1) {
                PsyUserMeditationRecord record = recordService.selectLatestRecordByUserAndMeditation(userId, id);
                meditation.setUserRecord(record);
            }
        }

        return success(meditation);
    }

    /**
     * 开始播放冥想
     */
    @PostMapping("/play/{id}")
    public AjaxResult playMeditation(@PathVariable("id") Long id, @RequestBody PsyUserMeditationRecord record, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 获取冥想信息
        PsyMeditation meditation = meditationService.selectMeditationById(id);
        if (meditation == null || meditation.getStatus() != 1) {
            return error("冥想不存在或已下架");
        }

        // 检查是否需要购买
        if (meditation.getIsFree() != 1) {
            boolean purchased = orderService.checkUserPurchased(loginUser.getUserId(), id);
            if (!purchased) {
                return error("请先购买冥想");
            }
        }

        // 记录播放信息
        record.setUserId(loginUser.getUserId());
        record.setMeditationId(id);
        record.setCreateTime(DateUtils.getNowDate());

        return toAjax(recordService.insertRecord(record));
    }

    /**
     * 创建冥想订单
     */
    @PostMapping("/order/{meditationId}")
    public AjaxResult createOrder(@PathVariable("meditationId") Long meditationId, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 检查用户是否已购买
        boolean purchased = orderService.checkUserPurchased(loginUser.getUserId(), meditationId);
        if (purchased) {
            return error("您已购买该冥想");
        }

        // 创建订单
        PsyMeditationOrder order = new PsyMeditationOrder();
        order.setMeditationId(meditationId);
        order.setUserId(loginUser.getUserId());
        order.setStatus(0); // 待支付
        order.setCreateBy(loginUser.getUsername());

        // 保存订单
        int result = orderService.insertOrder(order);
        if (result > 0) {
            Map<String, Object> data = new HashMap<>();
            data.put("orderNo", order.getOrderNo());
            data.put("orderId", order.getId());
            return success(data);
        } else {
            return error("创建订单失败");
        }
    }

    /**
     * 提交冥想评价
     */
    @PostMapping("/review")
    public AjaxResult submitReview(@RequestBody PsyMeditationReview review, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 检查用户是否已购买或冥想是否免费
        PsyMeditation meditation = meditationService.selectMeditationById(review.getMeditationId());
        if (meditation.getIsFree() != 1) {
            boolean purchased = orderService.checkUserPurchased(loginUser.getUserId(), review.getMeditationId());
            if (!purchased) {
                return error("您尚未购买该冥想，无法评价");
            }
        }

        // 检查用户是否已评价
        boolean reviewed = reviewService.checkUserReviewed(loginUser.getUserId(), review.getMeditationId());
        if (reviewed) {
            return error("您已评价过该冥想");
        }

        // 设置评价信息
        review.setUserId(loginUser.getUserId());
        review.setCreateBy(loginUser.getUsername());
        review.setCreateTime(DateUtils.getNowDate());
        review.setDelFlag(0);

        // 保存评价
        int result = reviewService.insertReview(review);
        if (result > 0) {
            // 更新冥想评分信息
            meditationService.updateMeditationRating(review.getMeditationId());
            return success();
        } else {
            return error("提交评价失败");
        }
    }

    /**
     * 获取冥想评价列表
     */
    @GetMapping("/reviews/{meditationId}")
    public AjaxResult getReviews(@PathVariable("meditationId") Long meditationId) {
        List<PsyMeditationReview> reviews = reviewService.selectReviewsByMeditationId(meditationId);
        return success(reviews);
    }

    /**
     * 检查用户是否已评价
     */
    @GetMapping("/checkReviewed/{meditationId}")
    public AjaxResult checkUserReviewed(@PathVariable("meditationId") Long meditationId, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        boolean reviewed = reviewService.checkUserReviewed(loginUser.getUserId(), meditationId);
        return success(reviewed);
    }

    /**
     * 获取用户已购冥想列表
     */
    @GetMapping("/purchased")
    public AjaxResult getPurchasedMeditations(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        List<PsyMeditation> meditations = orderService.selectUserPurchasedMeditations(loginUser.getUserId());
        return success(meditations);
    }

    /**
     * 获取用户冥想记录列表
     */
    @GetMapping("/records")
    public AjaxResult getUserRecords(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        List<PsyUserMeditationRecord> records = recordService.selectRecordsByUserId(loginUser.getUserId());
        return success(records);
    }

    /**
     * 获取用户冥想统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getUserStatistics(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        Long userId = loginUser.getUserId();

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalDuration", recordService.sumUserMeditationDuration(userId));
        statistics.put("totalTimes", recordService.countUserMeditationTimes(userId));
        statistics.put("completedTimes", recordService.countUserCompletedMeditations(userId));

        return success(statistics);
    }

    /**
     * 根据分类ID查询冥想列表
     */
    @GetMapping("/category/{categoryId}")
    public AjaxResult getMeditationsByCategory(@PathVariable Long categoryId) {
        List<PsyMeditation> list = meditationService.selectMeditationsByCategoryId(categoryId);
        return success(list);
    }
}
