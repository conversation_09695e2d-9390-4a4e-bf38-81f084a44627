package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultationRecord;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyConsultationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 心理咨询记录表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/consultationRecord")
public class PsyConsultationRecordController extends BaseController {
    
    @Autowired
    private IPsyConsultationRecordService recordService;

    /**
     * 查询咨询记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyConsultationRecord record) {
        startPage();
        List<PsyConsultationRecord> list = recordService.selectRecordList(record);
        return getDataTable(list);
    }

    /**
     * 导出咨询记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:export')")
    @Log(title = "咨询记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyConsultationRecord record) {
        List<PsyConsultationRecord> list = recordService.selectRecordList(record);
        ExcelUtil<PsyConsultationRecord> util = new ExcelUtil<PsyConsultationRecord>(PsyConsultationRecord.class);
        util.exportExcel(response, list, "咨询记录数据");
    }

    /**
     * 获取咨询记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(recordService.selectRecordById(id));
    }

    /**
     * 获取咨询记录详细信息（包含关联信息）
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(recordService.selectRecordWithDetails(id));
    }

    /**
     * 新增咨询记录
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:add')")
    @Log(title = "咨询记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyConsultationRecord record) {
        return toAjax(recordService.insertRecord(record));
    }

    /**
     * 修改咨询记录
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:edit')")
    @Log(title = "咨询记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyConsultationRecord record) {
        return toAjax(recordService.updateRecord(record));
    }

    /**
     * 删除咨询记录（支持单个删除和批量删除）
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:remove')")
    @Log(title = "咨询记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String ids) {
        // 解析ID字符串，支持单个ID或多个ID（用逗号分隔）
        String[] idArray = ids.split(",");
        Long[] idLongs = new Long[idArray.length];

        try {
            for (int i = 0; i < idArray.length; i++) {
                idLongs[i] = Long.parseLong(idArray[i].trim());
            }
        } catch (NumberFormatException e) {
            return error("无效的记录ID格式");
        }

        // 如果只有一个ID，调用单个删除方法
        if (idLongs.length == 1) {
            return toAjax(recordService.deleteRecordById(idLongs[0]));
        } else {
            // 多个ID，调用批量删除方法
            return toAjax(recordService.deleteRecordByIds(idLongs));
        }
    }

    /**
     * 根据用户ID查询咨询记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getRecordsByUser(@PathVariable Long userId) {
        List<PsyConsultationRecord> list = recordService.selectRecordsByUserId(userId);
        return success(list);
    }

    /**
     * 根据咨询师ID查询咨询记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:list')")
    @GetMapping("/consultant/{consultantId}")
    public AjaxResult getRecordsByConsultant(@PathVariable Long consultantId) {
        List<PsyConsultationRecord> list = recordService.selectRecordsByConsultantId(consultantId);
        return success(list);
    }

    /**
     * 根据订单ID查询咨询记录
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:query')")
    @GetMapping("/order/{orderId}")
    public AjaxResult getRecordByOrder(@PathVariable Long orderId) {
        PsyConsultationRecord record = recordService.selectRecordByOrderId(orderId);
        return success(record);
    }

    /**
     * 开始咨询
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:edit')")
    @Log(title = "开始咨询", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{orderId}")
    public AjaxResult startConsultation(@PathVariable Long orderId) {
        try {
            PsyConsultationRecord record = recordService.startConsultation(orderId);
            return success(record);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 结束咨询
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:edit')")
    @Log(title = "结束咨询", businessType = BusinessType.UPDATE)
    @PostMapping("/end/{recordId}")
    public AjaxResult endConsultation(@PathVariable Long recordId, @RequestParam(required = false) String consultContent) {
        return toAjax(recordService.endConsultation(recordId, consultContent));
    }

    /**
     * 中断咨询
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:edit')")
    @Log(title = "中断咨询", businessType = BusinessType.UPDATE)
    @PostMapping("/interrupt/{recordId}")
    public AjaxResult interruptConsultation(@PathVariable Long recordId, 
                                          @RequestParam String interruptType, 
                                          @RequestParam String reason) {
        return toAjax(recordService.interruptConsultation(recordId, interruptType, reason));
    }

    /**
     * 恢复咨询
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:edit')")
    @Log(title = "恢复咨询", businessType = BusinessType.UPDATE)
    @PostMapping("/resume/{recordId}")
    public AjaxResult resumeConsultation(@PathVariable Long recordId) {
        return toAjax(recordService.resumeConsultation(recordId));
    }

    /**
     * 用户评价
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:edit')")
    @Log(title = "用户评价", businessType = BusinessType.UPDATE)
    @PostMapping("/rate/{recordId}")
    public AjaxResult rateConsultation(@PathVariable Long recordId, @RequestParam Integer userRating) {
        return toAjax(recordService.rateConsultation(recordId, userRating));
    }

    /**
     * 获取用户咨询统计
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:list')")
    @GetMapping("/statistics/user/{userId}")
    public AjaxResult getUserStats(@PathVariable Long userId) {
        return success(recordService.getUserConsultationStats(userId));
    }

    /**
     * 获取咨询师咨询统计
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:list')")
    @GetMapping("/statistics/consultant/{consultantId}")
    public AjaxResult getConsultantStats(@PathVariable Long consultantId) {
        return success(recordService.getConsultantConsultationStats(consultantId));
    }

    /**
     * 查询用户与咨询师的咨询记录
     */
    @PreAuthorize("@ss.hasPermi('system:consultationRecord:list')")
    @GetMapping("/history/{userId}/{consultantId}")
    public AjaxResult getConsultationHistory(@PathVariable Long userId, @PathVariable Long consultantId) {
        List<PsyConsultationRecord> list = recordService.selectRecordsByUserAndConsultant(userId, consultantId);
        return success(list);
    }
}
