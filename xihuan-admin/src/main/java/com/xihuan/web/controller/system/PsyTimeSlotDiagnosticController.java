package com.xihuan.web.controller.system;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;
import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.domain.vo.ConsultantSimpleVO;
import com.xihuan.system.service.IPsyTimeCounselorScheduleService;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.task.PsyTimeSlotTaskService;
import com.xihuan.system.service.wxService.PsyCategoryService;
import lombok.var;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 时间槽诊断控制器
 * 用于诊断和修复时间槽生成问题
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/timeSlotDiagnostic")
public class PsyTimeSlotDiagnosticController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyTimeSlotDiagnosticController.class);
    
    @Autowired
    private PsyTimeSlotTaskService taskService;
    
    @Autowired
    private IPsyTimeRangeService timeRangeService;
    
    @Autowired
    private PsyCategoryService.PsyConsultantService consultantService;
    
    @Autowired
    private IPsyTimeCounselorScheduleService scheduleService;
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;

    /**
     * 执行完整的系统诊断
     */
    @GetMapping("/diagnose")
    public AjaxResult diagnose() {
        logger.info("开始执行时间槽系统诊断");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 检查时间段配置
            List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();
            result.put("timeRangeCount", timeRanges != null ? timeRanges.size() : 0);
            result.put("timeRanges", timeRanges);
            
            if (timeRanges == null || timeRanges.isEmpty()) {
                logger.warn("时间段配置为空，尝试初始化");
                int initResult = timeRangeService.initDefaultTimeRanges();
                result.put("timeRangeInitResult", initResult);
                
                // 重新查询
                timeRanges = timeRangeService.selectAllActiveTimeRanges();
                result.put("timeRangeCountAfterInit", timeRanges != null ? timeRanges.size() : 0);
            }
            
            // 2. 检查咨询师数据
            List<ConsultantSimpleVO> consultants = consultantService.listAllSimple();
            result.put("consultantCount", consultants != null ? consultants.size() : 0);
            result.put("consultants", consultants);
            
            // 3. 检查排班记录（检查明天的排班）
            LocalDate tomorrow = LocalDate.now().plusDays(1);
            if (consultants != null && !consultants.isEmpty()) {
                ConsultantSimpleVO firstConsultant = consultants.get(0);
                var schedules = scheduleService.selectSchedulesByDateRange(
                    firstConsultant.getId(), tomorrow, tomorrow);
                result.put("sampleScheduleCount", schedules != null ? schedules.size() : 0);
                result.put("sampleSchedules", schedules);
            }
            
            // 4. 尝试生成时间槽测试
            if (consultants != null && !consultants.isEmpty()) {
                ConsultantSimpleVO firstConsultant = consultants.get(0);
                int testSlots = timeSlotService.generateSlotsForCounselor(
                    firstConsultant.getId(), tomorrow, tomorrow);
                result.put("testSlotGeneration", testSlots);
            }
            
            result.put("status", "success");
            result.put("message", "诊断完成");
            
            return AjaxResult.success("诊断完成", result);
            
        } catch (Exception e) {
            logger.error("诊断过程中发生异常", e);
            result.put("status", "error");
            result.put("message", "诊断失败：" + e.getMessage());
            return AjaxResult.error("诊断失败", result);
        }
    }

    /**
     * 手动触发时间槽生成
     */
    @PostMapping("/generateTimeSlots")
    public AjaxResult generateTimeSlots() {
        logger.info("手动触发时间槽生成");
        
        try {
            taskService.generateFutureTimeSlots();
            return AjaxResult.success("时间槽生成任务已触发，请查看日志了解详细结果");
        } catch (Exception e) {
            logger.error("手动触发时间槽生成失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }

    /**
     * 初始化时间段配置
     */
    @PostMapping("/initTimeRanges")
    public AjaxResult initTimeRanges() {
        logger.info("手动初始化时间段配置");
        
        try {
            int result = timeRangeService.initDefaultTimeRanges();
            return AjaxResult.success("成功初始化 " + result + " 个时间段");
        } catch (Exception e) {
            logger.error("初始化时间段配置失败", e);
            return AjaxResult.error("初始化失败：" + e.getMessage());
        }
    }

    /**
     * 为指定咨询师生成排班记录
     */
    @PostMapping("/generateSchedule/{counselorId}")
    public AjaxResult generateScheduleForCounselor(@PathVariable Long counselorId) {
        logger.info("为咨询师 {} 生成排班记录", counselorId);
        
        try {
            int result = scheduleService.ensureFutureSchedule(counselorId, 7, 1L);
            return AjaxResult.success("为咨询师生成了 " + result + " 条排班记录");
        } catch (Exception e) {
            logger.error("为咨询师生成排班记录失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }

    /**
     * 为指定咨询师生成时间槽
     */
    @PostMapping("/generateSlots/{counselorId}")
    public AjaxResult generateSlotsForCounselor(@PathVariable Long counselorId) {
        logger.info("为咨询师 {} 生成时间槽", counselorId);
        
        try {
            LocalDate startDate = LocalDate.now();
            LocalDate endDate = startDate.plusDays(6);
            
            int result = timeSlotService.regenerateSlotsForCounselor(counselorId, startDate, endDate);
            return AjaxResult.success("为咨询师生成了 " + result + " 个时间槽");
        } catch (Exception e) {
            logger.error("为咨询师生成时间槽失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }

    /**
     * 修复排班记录时间（从18:00更新为21:00）
     */
    @PostMapping("/fixScheduleTimes")
    public AjaxResult fixScheduleTimes() {
        logger.info("手动修复排班记录时间");

        try {
            LocalDate today = LocalDate.now();
            LocalDate endDate = today.plusDays(7);

            // 获取所有咨询师
            List<ConsultantSimpleVO> consultants = consultantService.listAllSimple();
            if (consultants == null || consultants.isEmpty()) {
                return AjaxResult.error("没有找到咨询师数据");
            }

            int fixedCount = 0;

            for (ConsultantSimpleVO consultant : consultants) {
                // 查询该咨询师未来7天的排班记录
                List<PsyTimeCounselorSchedule> schedules = scheduleService.selectSchedulesByDateRange(
                    consultant.getId(), today, endDate);

                if (schedules != null) {
                    for (PsyTimeCounselorSchedule schedule : schedules) {
                        // 检查是否需要修复（结束时间为18:00）
                        if (schedule.getEndTime() != null &&
                            schedule.getEndTime().getHour() == 18 &&
                            schedule.getEndTime().getMinute() == 0) {

                            // 更新结束时间为21:00
                            schedule.setEndTime(java.time.LocalTime.of(21, 0));
                            schedule.setUpdateTime(new java.util.Date());

                            String originalRemark = schedule.getRemark();
                            schedule.setRemark((originalRemark != null ? originalRemark : "") +
                                " [手动修复：将结束时间从18:00更新为21:00]");

                            int result = scheduleService.updateSchedule(schedule);
                            if (result > 0) {
                                fixedCount++;
                            }
                        }
                    }
                }
            }

            return AjaxResult.success("成功修复 " + fixedCount + " 条排班记录的时间");
        } catch (Exception e) {
            logger.error("修复排班记录时间失败", e);
            return AjaxResult.error("修复失败：" + e.getMessage());
        }
    }

    /**
     * 获取系统状态概览
     */
    @GetMapping("/status")
    public AjaxResult getSystemStatus() {
        Map<String, Object> status = new HashMap<>();

        try {
            // 时间段数量
            List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();
            status.put("timeRangeCount", timeRanges != null ? timeRanges.size() : 0);

            // 咨询师数量
            List<ConsultantSimpleVO> consultants = consultantService.listAllSimple();
            status.put("consultantCount", consultants != null ? consultants.size() : 0);

            // 检查排班记录时间分布
            if (consultants != null && !consultants.isEmpty()) {
                LocalDate tomorrow = LocalDate.now().plusDays(1);
                var sampleSchedules = scheduleService.selectSchedulesByDateRange(
                    consultants.get(0).getId(), tomorrow, tomorrow);
                if (sampleSchedules != null && !sampleSchedules.isEmpty()) {
                    var schedule = sampleSchedules.get(0);
                    status.put("sampleScheduleTime", schedule.getStartTime() + "-" + schedule.getEndTime());
                }
            }

            return AjaxResult.success(status);
        } catch (Exception e) {
            logger.error("获取系统状态失败", e);
            return AjaxResult.error("获取状态失败：" + e.getMessage());
        }
    }
}
