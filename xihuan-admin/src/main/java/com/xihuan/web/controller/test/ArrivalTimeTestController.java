package com.xihuan.web.controller.test;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantCenterConfig;
import com.xihuan.system.service.IPsyConsultantCenterConfigService;
import com.xihuan.system.service.IPsyTimeSlotFilterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 到店时间过滤功能测试控制器
 * 用于验证功能是否正常工作
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test/arrivalTime")
public class ArrivalTimeTestController extends BaseController {
    
    @Autowired
    private IPsyConsultantCenterConfigService configService;
    
    @Autowired
    private IPsyTimeSlotFilterService filterService;

    /**
     * 测试创建默认配置
     */
    @PostMapping("/createDefaultConfig")
    public AjaxResult createDefaultConfig(
            @RequestParam Long consultantId,
            @RequestParam(defaultValue = "1") Long centerId) {
        
        int result = configService.createDefaultConfig(consultantId, centerId);
        
        if (result > 0) {
            PsyConsultantCenterConfig config = configService.selectConfigByConsultantAndCenter(consultantId, centerId);
            return AjaxResult.success("默认配置创建成功", config);
        } else {
            return AjaxResult.error("默认配置创建失败");
        }
    }

    /**
     * 测试获取咨询师配置
     */
    @GetMapping("/getConfig")
    public AjaxResult getConfig(
            @RequestParam Long consultantId,
            @RequestParam(defaultValue = "1") Long centerId) {
        
        PsyConsultantCenterConfig config = configService.selectConfigByConsultantAndCenter(consultantId, centerId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("config", config);
        result.put("arrivalTime", filterService.getConsultantArrivalTime(consultantId, centerId));
        result.put("filterEnabled", filterService.isArrivalFilterEnabled(consultantId, centerId));
        
        return AjaxResult.success(result);
    }

    /**
     * 测试设置到店时间
     */
    @PostMapping("/setArrivalTime")
    public AjaxResult setArrivalTime(
            @RequestParam Long consultantId,
            @RequestParam(defaultValue = "1") Long centerId,
            @RequestParam Double hours) {
        
        int result = configService.setArrivalTime(consultantId, centerId, hours);
        
        if (result > 0) {
            return AjaxResult.success("到店时间设置成功：" + hours + " 小时");
        } else {
            return AjaxResult.error("到店时间设置失败");
        }
    }

    /**
     * 测试切换过滤开关
     */
    @PostMapping("/toggleFilter")
    public AjaxResult toggleFilter(
            @RequestParam Long consultantId,
            @RequestParam(defaultValue = "1") Long centerId,
            @RequestParam boolean enabled) {
        
        int result = configService.setArrivalFilterEnabled(consultantId, centerId, enabled);
        
        if (result > 0) {
            String action = enabled ? "启用" : "禁用";
            return AjaxResult.success("过滤功能" + action + "成功");
        } else {
            return AjaxResult.error("过滤功能切换失败");
        }
    }

    /**
     * 测试计算最早预约时间
     */
    @GetMapping("/getEarliestTime")
    public AjaxResult getEarliestTime(
            @RequestParam Long consultantId,
            @RequestParam(defaultValue = "1") Long centerId) {
        
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime earliestTime = filterService.getEarliestOfflineAppointmentTime(consultantId, centerId, currentTime);
        
        Map<String, Object> result = new HashMap<>();
        result.put("currentTime", currentTime);
        result.put("earliestTime", earliestTime);
        result.put("arrivalTimeHours", filterService.getConsultantArrivalTime(consultantId, centerId));
        result.put("filterEnabled", filterService.isArrivalFilterEnabled(consultantId, centerId));
        
        return AjaxResult.success(result);
    }

    /**
     * 综合测试
     */
    @PostMapping("/fullTest")
    public AjaxResult fullTest(@RequestParam Long consultantId) {
        Long centerId = 1L;
        Map<String, Object> testResults = new HashMap<>();
        
        try {
            // 1. 创建默认配置
            int createResult = configService.createDefaultConfig(consultantId, centerId);
            testResults.put("createConfig", createResult > 0 ? "成功" : "失败或已存在");
            
            // 2. 获取配置
            PsyConsultantCenterConfig config = configService.selectConfigByConsultantAndCenter(consultantId, centerId);
            testResults.put("getConfig", config != null ? "成功" : "失败");
            
            // 3. 测试设置到店时间
            int setTimeResult = configService.setArrivalTime(consultantId, centerId, 1.5);
            testResults.put("setArrivalTime", setTimeResult > 0 ? "成功" : "失败");
            
            // 4. 测试禁用过滤
            int disableResult = configService.setArrivalFilterEnabled(consultantId, centerId, false);
            testResults.put("disableFilter", disableResult > 0 ? "成功" : "失败");
            
            // 5. 测试启用过滤
            int enableResult = configService.setArrivalFilterEnabled(consultantId, centerId, true);
            testResults.put("enableFilter", enableResult > 0 ? "成功" : "失败");
            
            // 6. 验证最终状态
            Double finalArrivalTime = filterService.getConsultantArrivalTime(consultantId, centerId);
            boolean finalFilterEnabled = filterService.isArrivalFilterEnabled(consultantId, centerId);
            
            testResults.put("finalArrivalTime", finalArrivalTime);
            testResults.put("finalFilterEnabled", finalFilterEnabled);
            
            // 7. 计算最早预约时间
            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime earliestTime = filterService.getEarliestOfflineAppointmentTime(consultantId, centerId, currentTime);
            testResults.put("currentTime", currentTime);
            testResults.put("earliestTime", earliestTime);
            
            return AjaxResult.success("综合测试完成", testResults);
            
        } catch (Exception e) {
            testResults.put("error", e.getMessage());
            return AjaxResult.error("测试过程中发生错误", testResults);
        }
    }
}
