package com.xihuan.web.controller.miniapp.user;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;
import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.*;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序用户测评Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/assessment")
public class MiniAppUserAssessmentController extends BaseController {

    /**
     * 开始测评请求参数内部类
     */
    @Data
    public static class StartAssessmentRequest {
        private Long scaleId;
    }

    /**
     * 答题请求参数内部类
     */
    @Data
    public static class AnswerRequest {
        private Long recordId;
        private Long questionId;
        private Long optionId;
        private String answerContent;
        private Integer responseTime;
    }
    
    @Autowired
    private IPsyTScaleService scaleService;
    
    @Autowired
    private IPsyTQuestionService questionService;
    
    @Autowired
    private IPsyTAssessmentRecordService assessmentRecordService;
    
    @Autowired
    private IPsyTAnswerRecordService answerRecordService;
    
    @Autowired
    private IPsyTInterpretationService interpretationService;

    @Autowired
    private TokenService tokenService;

    /**
     * 查询启用的量表列表
     */
    @GetMapping("/scales")
    public AjaxResult getEnabledScales() {
        List<PsyTScale> list = scaleService.selectEnabledScales();
        return success(list);
    }

    /**
     * 查询热门量表
     */
    @GetMapping("/scales/hot")
    public AjaxResult getHotScales(@RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectHotScales(limit);
        return success(list);
    }

    /**
     * 查询最新量表
     */
    @GetMapping("/scales/latest")
    public AjaxResult getLatestScales(@RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectLatestScales(limit);
        return success(list);
    }

    /**
     * 根据分类查询量表
     */
    @GetMapping("/scales/category/{categoryId}")
    public AjaxResult getScalesByCategory(@PathVariable Integer categoryId) {
        List<PsyTScale> list = scaleService.selectScalesByCategory(categoryId);
        return success(list);
    }

    /**
     * 搜索量表
     */
    @GetMapping("/scales/search")
    public AjaxResult searchScales(@RequestParam(required = false) String keyword,
                                  @RequestParam(required = false) Integer categoryId) {
        List<PsyTScale> list = scaleService.searchScales(keyword, categoryId, 1, null);
        return success(list);
    }

    /**
     * 获取量表详情
     */
    @GetMapping("/scales/{id}")
    public AjaxResult getScaleDetails(@PathVariable Long id) {
        PsyTScale scale = scaleService.selectScaleById(id);
        if (scale == null) {
            return error("量表不存在");
        }
        
        // 增加查看次数
        scaleService.increaseViewCount(id);
        
        return success(scale);
    }

    /**
     * 检查用户是否可以开始测评
     */
    @GetMapping("/check-can-start")
    public AjaxResult checkCanStart(@RequestParam Long scaleId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        Map<String, Object> result = assessmentRecordService.checkUserCanAssess(loginUser.getUserId(), scaleId);
        return success(result);
    }

    /**
     * 开始测评（使用内部类接收参数）
     */
    @Log(title = "用户测评", businessType = BusinessType.INSERT)
    @PostMapping("/start")
    public AjaxResult startAssessment(@RequestBody StartAssessmentRequest request, HttpServletRequest httpRequest) {

        logger.info("收到开始测评请求: {}", request);

        // 参数验证
        if (request.getScaleId() == null) {
            return error("量表ID不能为空");
        }

        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(httpRequest);
        if (loginUser == null) {
            return error("用户未登录");
        }

        Long userId = loginUser.getUserId();

        // 检查用户是否可以测评
        Map<String, Object> checkResult = assessmentRecordService.checkUserCanAssess(userId, request.getScaleId());
        if (!(Boolean) checkResult.get("canAssess")) {
            return error("无法开始测评：" + checkResult.get("errors"));
        }

        Long recordId = assessmentRecordService.startAssessment(userId, request.getScaleId(), null);
        return success(recordId);
    }

    /**
     * 开始测评（兼容表单参数）
     */
    @Log(title = "用户测评", businessType = BusinessType.INSERT)
    @PostMapping("/start/form")
    public AjaxResult startAssessmentForm(@RequestParam(value = "scaleId", required = true) Long scaleId, HttpServletRequest request) {

        // 创建StartAssessmentRequest对象
        StartAssessmentRequest startRequest = new StartAssessmentRequest();
        startRequest.setScaleId(scaleId);

        // 调用主要的开始测评方法
        return startAssessment(startRequest, request);
    }

    /**
     * 获取测评题目
     */
    @GetMapping("/questions/{recordId}")
    public AjaxResult getAssessmentQuestions(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null) {
            return error("测评记录不存在");
        }

        // 验证测评记录是否属于当前用户
        if (!record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        // 获取量表的所有题目（包含选项）
        List<com.xihuan.common.core.domain.entity.PsyTQuestion> questions =
            questionService.selectQuestionsWithOptionsByScaleId(record.getScaleId());

        return success(questions);
    }

    /**
     * 获取下一题
     */
    @GetMapping("/next-question/{recordId}")
    public AjaxResult getNextQuestion(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        Map<String, Object> nextQuestion = answerRecordService.getNextQuestion(recordId);
        return success(nextQuestion);
    }

    /**
     * 获取上一题
     */
    @GetMapping("/previous-question/{recordId}")
    public AjaxResult getPreviousQuestion(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        Map<String, Object> previousQuestion = answerRecordService.getPreviousQuestion(recordId);
        return success(previousQuestion);
    }

    /**
     * 保存答题记录（使用RequestBody接收JSON参数）
     */
    @Log(title = "用户测评", businessType = BusinessType.INSERT)
    @PostMapping("/answer")
    public AjaxResult saveAnswer(@RequestBody AnswerRequest answerRequest, HttpServletRequest request) {

        logger.info("收到答题请求: {}", answerRequest);

        // 参数验证
        if (answerRequest.getRecordId() == null) {
            return error("测评记录ID不能为空");
        }
        if (answerRequest.getQuestionId() == null) {
            return error("题目ID不能为空");
        }

        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(answerRequest.getRecordId());
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        // 验证答案
        Map<String, Object> validation = answerRecordService.validateAnswer(
            answerRequest.getQuestionId(),
            answerRequest.getOptionId(),
            answerRequest.getAnswerContent()
        );
        if (!(Boolean) validation.get("valid")) {
            return error("答案无效：" + validation.get("errors"));
        }

        // 自动计算分数
        BigDecimal answerScore = answerRecordService.calculateAnswerScore(
            answerRequest.getQuestionId(),
            answerRequest.getOptionId(),
            answerRequest.getAnswerContent()
        );

        // 保存答题记录
        int result = answerRecordService.saveAnswer(
            answerRequest.getRecordId(),
            answerRequest.getQuestionId(),
            answerRequest.getOptionId(),
            answerRequest.getAnswerContent(),
            answerScore,
            answerRequest.getResponseTime()
        );

        if (result > 0) {
            return success("答案已保存");
        } else {
            return error("保存答案失败");
        }
    }

    /**
     * 查询答题进度
     */
    @GetMapping("/progress/{recordId}")
    public AjaxResult getAnswerProgress(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        Map<String, Object> progress = answerRecordService.selectAnswerProgress(recordId);
        return success(progress);
    }

    /**
     * 获取答题历史记录（用于继续测评时恢复答题状态）
     */
    @GetMapping("/answers/{recordId}")
    public AjaxResult getAnswerHistory(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        // 获取答题历史记录
        List<Map<String, Object>> answerHistory = answerRecordService.selectAnswerHistoryByRecordId(recordId);
        return success(answerHistory);
    }

    /**
     * 获取测评详细信息（包含题目、选项、答题历史、进度等）
     */
    @GetMapping("/detail/{recordId}")
    public AjaxResult getAssessmentDetail(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        // 获取测评详细信息
        Map<String, Object> detail = assessmentRecordService.selectAssessmentDetail(recordId);
        return success(detail);
    }

    /**
     * 重新计算答题分数（调试用）
     */
    @PostMapping("/recalculate-scores/{recordId}")
    public AjaxResult recalculateScores(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        try {
            // 重新计算分数
            int updatedCount = answerRecordService.recalculateScores(recordId);

            Map<String, Object> result = new HashMap<>();
            result.put("updatedCount", updatedCount);
            result.put("message", "重新计算了 " + updatedCount + " 条答题记录的分数");

            return success(result);
        } catch (Exception e) {
            logger.error("重新计算分数失败", e);
            return error("重新计算分数失败：" + e.getMessage());
        }
    }

    /**
     * 暂停测评
     */
    @Log(title = "用户测评", businessType = BusinessType.UPDATE)
    @PostMapping("/pause/{recordId}")
    public AjaxResult pauseAssessment(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        return toAjax(assessmentRecordService.pauseAssessment(recordId));
    }

    /**
     * 恢复测评
     */
    @Log(title = "用户测评", businessType = BusinessType.UPDATE)
    @PostMapping("/resume/{recordId}")
    public AjaxResult resumeAssessment(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        return toAjax(assessmentRecordService.resumeAssessment(recordId));
    }

    /**
     * 完成测评
     */
    @Log(title = "用户测评", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{recordId}")
    public AjaxResult completeAssessment(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        // 检查是否已完成所有题目
        if (!answerRecordService.isAnswerCompleted(recordId)) {
            return error("还有题目未完成，无法提交测评");
        }

        // 计算总分和维度分数
        BigDecimal totalScore = answerRecordService.calculateTotalScore(recordId);
        Map<String, BigDecimal> dimensionScores = answerRecordService.calculateDimensionScores(recordId);

        // 完成测评
        int result = assessmentRecordService.completeAssessment(recordId, totalScore, dimensionScores);

        if (result > 0) {
            return success("测评已完成");
        } else {
            return error("完成测评失败");
        }
    }

    /**
     * 取消测评
     */
    @Log(title = "用户测评", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{recordId}")
    public AjaxResult cancelAssessment(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        return toAjax(assessmentRecordService.cancelAssessment(recordId));
    }

    /**
     * 查询测评结果
     */
    @GetMapping("/result/{recordId}")
    public AjaxResult getAssessmentResult(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        Map<String, Object> result = assessmentRecordService.selectAssessmentResult(recordId);
        return success(result);
    }

    /**
     * 生成测评报告
     */
    @GetMapping("/report/{recordId}")
    public AjaxResult generateReport(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        Map<String, Object> report = assessmentRecordService.generateAssessmentReport(recordId);
        return success(report);
    }

    /**
     * 强制重新生成测评报告（清除缓存）
     */
    @PostMapping("/report/regenerate/{recordId}")
    public AjaxResult regenerateReport(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 验证测评记录权限
        PsyTAssessmentRecord record = assessmentRecordService.selectAssessmentRecordById(recordId);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("无权限访问该测评记录");
        }

        // 强制重新生成报告
        Map<String, Object> report = assessmentRecordService.forceRegenerateAssessmentReport(recordId);
        return success(report);
    }

    /**
     * 查询用户的测评记录
     */
    @GetMapping("/records")
    public AjaxResult getUserRecords(HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        List<PsyTAssessmentRecord> list = assessmentRecordService.selectRecordsByUserId(loginUser.getUserId());
        return success(list);
    }

    /**
     * 查询用户最近的测评记录
     */
    @GetMapping("/records/recent")
    public AjaxResult getRecentRecords(@RequestParam(defaultValue = "10") Integer limit, HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        List<PsyTAssessmentRecord> list = assessmentRecordService.selectRecentRecordsByUserId(loginUser.getUserId(), limit);
        return success(list);
    }

    /**
     * 查询用户未完成的测评记录
     */
    @GetMapping("/records/incomplete")
    public AjaxResult getIncompleteRecords(HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        List<PsyTAssessmentRecord> list = assessmentRecordService.selectIncompleteRecordsByUserId(loginUser.getUserId());
        return success(list);
    }

    /**
     * 查询用户已完成的测评记录
     */
    @GetMapping("/records/completed")
    public AjaxResult getCompletedRecords(HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        List<PsyTAssessmentRecord> list = assessmentRecordService.selectCompletedRecordsByUserId(loginUser.getUserId());
        return success(list);
    }

    /**
     * 查询用户测评统计
     */
    @GetMapping("/stats")
    public AjaxResult getUserStats(HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        Map<String, Object> stats = assessmentRecordService.selectUserAssessmentStats(loginUser.getUserId());
        return success(stats);
    }

    /**
     * 查询用户收藏的量表
     */
    @GetMapping("/favorites")
    public AjaxResult getFavoriteScales(HttpServletRequest request) {
        // 获取当前登录用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        List<PsyTScale> list = scaleService.selectFavoriteScalesByUserId(loginUser.getUserId());
        return success(list);
    }

    /**
     * 查询相似量表推荐
     */
    @GetMapping("/recommendations/{scaleId}")
    public AjaxResult getRecommendations(@PathVariable Long scaleId, @RequestParam(defaultValue = "5") Integer limit) {
        List<PsyTScale> list = scaleService.selectSimilarScales(scaleId, limit);
        return success(list);
    }
}
