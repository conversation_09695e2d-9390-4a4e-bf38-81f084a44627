package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsyTAdvancedScoringService;
import com.xihuan.system.service.IPsyTReportGenerationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 高级计分控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/advanced-scoring")
public class PsyTAdvancedScoringController extends BaseController {

    @Autowired
    private IPsyTAdvancedScoringService advancedScoringService;

    @Autowired
    private IPsyTReportGenerationService reportGenerationService;

    /**
     * 执行高级计分
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @Log(title = "高级计分", businessType = BusinessType.OTHER)
    @PostMapping("/execute/{recordId}")
    public AjaxResult executeAdvancedScoring(@PathVariable Long recordId) {
        try {
            Map<String, Object> result = advancedScoringService.executeAdvancedScoring(recordId);
            return success(result);
        } catch (Exception e) {
            logger.error("执行高级计分失败", e);
            return error("计分失败: " + e.getMessage());
        }
    }

    /**
     * PRCA-24专项计分
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @Log(title = "PRCA-24计分", businessType = BusinessType.OTHER)
    @PostMapping("/prca24/{recordId}")
    public AjaxResult calculatePRCA24Score(@PathVariable Long recordId) {
        try {
            Map<String, Object> result = advancedScoringService.calculatePRCA24Score(recordId);
            return success(result);
        } catch (Exception e) {
            logger.error("PRCA-24计分失败", e);
            return error("PRCA-24计分失败: " + e.getMessage());
        }
    }

    /**
     * STAI专项计分
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @Log(title = "STAI计分", businessType = BusinessType.OTHER)
    @PostMapping("/stai/{recordId}")
    public AjaxResult calculateSTAIScore(@PathVariable Long recordId) {
        try {
            Map<String, Object> result = advancedScoringService.calculateSTAIScore(recordId);
            return success(result);
        } catch (Exception e) {
            logger.error("STAI计分失败", e);
            return error("STAI计分失败: " + e.getMessage());
        }
    }

    /**
     * SAS专项计分
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @Log(title = "SAS计分", businessType = BusinessType.OTHER)
    @PostMapping("/sas/{recordId}")
    public AjaxResult calculateSASScore(@PathVariable Long recordId) {
        try {
            Map<String, Object> result = advancedScoringService.calculateSASScore(recordId);
            return success(result);
        } catch (Exception e) {
            logger.error("SAS计分失败", e);
            return error("SAS计分失败: " + e.getMessage());
        }
    }

    /**
     * BAI专项计分
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @Log(title = "BAI计分", businessType = BusinessType.OTHER)
    @PostMapping("/bai/{recordId}")
    public AjaxResult calculateBAIScore(@PathVariable Long recordId) {
        try {
            Map<String, Object> result = advancedScoringService.calculateBAIScore(recordId);
            return success(result);
        } catch (Exception e) {
            logger.error("BAI计分失败", e);
            return error("BAI计分失败: " + e.getMessage());
        }
    }

    /**
     * 反向计分计算
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @PostMapping("/reverse-score")
    public AjaxResult calculateReverseScore(@RequestParam BigDecimal originalScore, 
                                          @RequestParam Integer maxValue) {
        try {
            BigDecimal reverseScore = advancedScoringService.calculateReverseScore(originalScore, maxValue);
            return success(reverseScore);
        } catch (Exception e) {
            logger.error("反向计分失败", e);
            return error("反向计分失败: " + e.getMessage());
        }
    }

    /**
     * 标准分转换
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @PostMapping("/standard-score")
    public AjaxResult calculateStandardScore(@RequestParam BigDecimal rawScore, 
                                           @RequestParam BigDecimal multiplier) {
        try {
            BigDecimal standardScore = advancedScoringService.calculateStandardScore(rawScore, multiplier);
            return success(standardScore);
        } catch (Exception e) {
            logger.error("标准分转换失败", e);
            return error("标准分转换失败: " + e.getMessage());
        }
    }

    /**
     * 计算维度分数
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @PostMapping("/dimension-scores/{recordId}")
    public AjaxResult calculateDimensionScores(@PathVariable Long recordId, 
                                             @RequestParam String scaleCode) {
        try {
            Map<String, BigDecimal> dimensionScores = advancedScoringService.calculateDimensionScores(recordId, scaleCode);
            return success(dimensionScores);
        } catch (Exception e) {
            logger.error("计算维度分数失败", e);
            return error("计算维度分数失败: " + e.getMessage());
        }
    }

    /**
     * 验证计分结果
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @PostMapping("/validate")
    public AjaxResult validateScoringResult(@RequestParam String scaleCode, 
                                          @RequestBody Map<String, BigDecimal> scores) {
        try {
            Map<String, Object> validation = advancedScoringService.validateScoringResult(scaleCode, scores);
            return success(validation);
        } catch (Exception e) {
            logger.error("验证计分结果失败", e);
            return error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 生成计分报告
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:report')")
    @Log(title = "生成计分报告", businessType = BusinessType.OTHER)
    @PostMapping("/report/{recordId}")
    public AjaxResult generateScoringReport(@PathVariable Long recordId) {
        try {
            Map<String, Object> report = advancedScoringService.generateScoringReport(recordId);
            return success(report);
        } catch (Exception e) {
            logger.error("生成计分报告失败", e);
            return error("生成报告失败: " + e.getMessage());
        }
    }

    /**
     * 生成完整测评报告
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:report')")
    @Log(title = "生成完整测评报告", businessType = BusinessType.OTHER)
    @PostMapping("/complete-report/{recordId}")
    public AjaxResult generateCompleteReport(@PathVariable Long recordId) {
        try {
            Map<String, Object> report = reportGenerationService.generateCompleteReport(recordId);
            return success(report);
        } catch (Exception e) {
            logger.error("生成完整测评报告失败", e);
            return error("生成报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取已保存的报告
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:report')")
    @GetMapping("/saved-report/{recordId}")
    public AjaxResult getSavedReport(@PathVariable Long recordId) {
        try {
            Map<String, Object> report = reportGenerationService.getSavedReport(recordId);
            return success(report);
        } catch (Exception e) {
            logger.error("获取已保存报告失败", e);
            return error("获取报告失败: " + e.getMessage());
        }
    }

    /**
     * 批量重新计分
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @Log(title = "批量重新计分", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-rescoring")
    public AjaxResult batchRescoring(@RequestBody Long[] recordIds) {
        try {
            int successCount = 0;
            int failCount = 0;
            
            for (Long recordId : recordIds) {
                try {
                    Map<String, Object> result = advancedScoringService.executeAdvancedScoring(recordId);
                    if ((Boolean) result.get("success")) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    failCount++;
                    logger.error("重新计分失败，recordId: {}", recordId, e);
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("total", recordIds.length);
            result.put("success", successCount);
            result.put("failed", failCount);
            
            return success(result);
        } catch (Exception e) {
            logger.error("批量重新计分失败", e);
            return error("批量重新计分失败: " + e.getMessage());
        }
    }

    /**
     * 获取计分方法信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:view')")
    @GetMapping("/scoring-methods")
    public AjaxResult getScoringMethods() {
        try {
            Map<String, Object> methods = new HashMap<>();
            methods.put("PRCA-24", "公式计分（基础分18 + 加权求和）");
            methods.put("STAI", "反向计分（5-原始分）");
            methods.put("SAD", "特殊二选一计分");
            methods.put("PDQ-4+", "复合题计分");
            methods.put("FNE", "反向计分");
            methods.put("SAS", "标准分转换（×1.25）");
            methods.put("BAI", "标准分转换（×1.19）");
            return success(methods);
        } catch (Exception e) {
            logger.error("获取计分方法信息失败", e);
            return error("获取信息失败: " + e.getMessage());
        }
    }
}
