package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyImageResource;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.wxService.IPsyImageResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 心理咨询平台图片资源Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/imageResource")
public class PsyImageResourceController extends BaseController {
    @Autowired
    private IPsyImageResourceService psyImageResourceService;

    /**
     * 查询图片资源列表
     */
    @GetMapping("/list")
    public AjaxResult list(PsyImageResource psyImageResource) {
        List<PsyImageResource> list = psyImageResourceService.selectPsyImageResourceList(psyImageResource);
        return AjaxResult.success(list);
    }

    /**
     * 获取所有图片资源
     */
    @GetMapping("/all")
    public AjaxResult getAllImages() {
        PsyImageResource query = new PsyImageResource();
        query.setEnableStatus("0"); // 只查询启用的图片
        query.setDelFlag("0"); // 只查询未删除的图片
        List<PsyImageResource> list = psyImageResourceService.selectPsyImageResourceList(query);
        return AjaxResult.success(list);
    }

    /**
     * 获取图片资源详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(psyImageResourceService.selectPsyImageResourceById(id));
    }

    /**
     * 新增图片资源
     */
    @Log(title = "图片资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyImageResource psyImageResource) {
        return toAjax(psyImageResourceService.insertPsyImageResource(psyImageResource));
    }

    /**
     * 修改图片资源
     */
    @Log(title = "图片资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyImageResource psyImageResource) {
        return toAjax(psyImageResourceService.updatePsyImageResource(psyImageResource));
    }

    /**
     * 删除图片资源
     */
    @Log(title = "图片资源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(psyImageResourceService.deletePsyImageResourceByIds(ids));
    }
}