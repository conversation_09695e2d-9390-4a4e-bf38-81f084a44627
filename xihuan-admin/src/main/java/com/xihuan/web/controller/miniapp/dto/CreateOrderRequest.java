package com.xihuan.web.controller.miniapp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 创建咨询订单请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class CreateOrderRequest {
    
    /** 咨询师ID */
    private String consultantId;
    
    /** 服务ID */
    private Long serviceId;
    
    /** 预约时间 */
    private String appointmentTime;
    
    /** 咨询时长(分钟) */
    private Integer duration;
    
    /** 计算时长 */
    private Integer calculatedDuration;
    
    /** 咨询类型 */
    private String consultationType;
    
    /** 原价 */
    private BigDecimal originalPrice;
    
    /** 支付金额 */
    private BigDecimal paymentAmount;
    
    /** 选择的时间段列表 */
    private List<String> selectedTimeSlots;
    
    /** 备注 */
    private String remark;
}
