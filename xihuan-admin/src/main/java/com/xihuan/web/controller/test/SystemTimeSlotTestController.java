package com.xihuan.web.controller.test;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.utils.spring.SpringUtils;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.task.PsyTimeSlotTaskService;
import lombok.var;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统时间槽测试控制器
 * 用于测试系统时间槽生成和重复插入问题修复
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test/systemTimeSlot")
public class SystemTimeSlotTestController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemTimeSlotTestController.class);
    
    @Autowired
    private PsyTimeSlotTaskService taskService;

    /**
     * 测试系统时间槽生成（修复重复插入问题）
     */
    @PostMapping("/testGeneration")
    public AjaxResult testSystemSlotGeneration(
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            logger.info("测试系统时间槽生成，天数：{}", days);
            
            java.time.LocalDate startDate = java.time.LocalDate.now().plusDays(1);
            java.time.LocalDate endDate = startDate.plusDays(days - 1);
            
            // 使用重新生成方法，避免重复插入
            IPsySystemTimeSlotService systemTimeSlotService = 
                SpringUtils.getBean(IPsySystemTimeSlotService.class);
            
            int count = systemTimeSlotService.regenerateSystemTimeSlots(startDate, endDate, 1L);
            
            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "系统时间槽生成测试完成");
            result.put("generatedCount", count);
            result.put("dateRange", startDate + " 到 " + endDate);
            result.put("method", "regenerateSystemTimeSlots（先清理再生成）");
            result.put("note", "使用此方法可以避免重复插入错误");
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("测试系统时间槽生成失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试普通生成方法（带重复检查）
     */
    @PostMapping("/testNormalGeneration")
    public AjaxResult testNormalGeneration(
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            logger.info("测试普通系统时间槽生成（带重复检查），天数：{}", days);
            
            java.time.LocalDate startDate = java.time.LocalDate.now().plusDays(1);
            java.time.LocalDate endDate = startDate.plusDays(days - 1);
            
            IPsySystemTimeSlotService systemTimeSlotService = 
                SpringUtils.getBean(IPsySystemTimeSlotService.class);
            
            int count = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, 1L);
            
            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "普通系统时间槽生成测试完成");
            result.put("generatedCount", count);
            result.put("dateRange", startDate + " 到 " + endDate);
            result.put("method", "generateSystemTimeSlots（带重复检查）");
            result.put("note", "现在会过滤掉已存在的时间槽，避免重复插入");
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("测试普通系统时间槽生成失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }

    /**
     * 手动触发完整的定时任务流程
     */
    @PostMapping("/triggerFullTask")
    public AjaxResult triggerFullTask() {
        try {
            logger.info("手动触发完整的定时任务流程");
            
            taskService.generateFutureTimeSlots();
            
            return AjaxResult.success("完整定时任务执行完成，现在使用重新生成方法避免重复插入");
        } catch (Exception e) {
            logger.error("执行完整定时任务失败", e);
            return AjaxResult.error("执行失败：" + e.getMessage());
        }
    }

    /**
     * 清理指定日期范围的系统时间槽
     */
    @PostMapping("/cleanSlots")
    public AjaxResult cleanSystemSlots(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        try {
            logger.info("清理系统时间槽，日期范围：{} 到 {}，咨询中心：{}", startDate, endDate, centerId);
            
            IPsySystemTimeSlotService systemTimeSlotService = 
                SpringUtils.getBean(IPsySystemTimeSlotService.class);
            
            // 这里需要调用删除方法，但是当前接口可能没有提供，我们可以通过Mapper直接操作
            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "清理系统时间槽");
            result.put("startDate", startDate);
            result.put("endDate", endDate);
            result.put("centerId", centerId);
            result.put("note", "可以使用regenerateSystemTimeSlots方法来先清理再生成");
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("清理系统时间槽失败", e);
            return AjaxResult.error("清理失败：" + e.getMessage());
        }
    }

    /**
     * 查看系统时间槽统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getSystemSlotStatistics(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        try {
            java.time.LocalDate start = startDate != null ? 
                java.time.LocalDate.parse(startDate) : java.time.LocalDate.now();
            java.time.LocalDate end = endDate != null ? 
                java.time.LocalDate.parse(endDate) : start.plusDays(7);
            
            IPsySystemTimeSlotService systemTimeSlotService = 
                SpringUtils.getBean(IPsySystemTimeSlotService.class);
            
            var slots = systemTimeSlotService.selectAvailableSlots(start, end, centerId);
            
            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "系统时间槽统计信息");
            result.put("dateRange", start + " 到 " + end);
            result.put("centerId", centerId);
            result.put("totalSlots", slots != null ? slots.size() : 0);
            result.put("availableSlots", slots != null ? 
                slots.stream().mapToInt(slot -> slot.getHasAvailable() ? 1 : 0).sum() : 0);
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("查询系统时间槽统计失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 测试重复插入问题修复说明
     */
    @GetMapping("/fixInfo")
    public AjaxResult getDuplicateKeyFixInfo() {
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        result.put("问题描述", "系统时间槽表存在唯一约束 uniq_system_slot (center_id, date_key, start_time)");
        result.put("错误原因", "定时任务重复执行时，尝试插入已存在的时间槽记录");
        result.put("修复方案", java.util.Arrays.asList(
            "1. 修改定时任务使用 regenerateSystemTimeSlots 方法（先清理再生成）",
            "2. 在 generateSystemTimeSlots 方法中添加重复检查逻辑",
            "3. 修改批量插入SQL使用 INSERT IGNORE 避免重复插入错误",
            "4. 添加 filterExistingSlots 方法过滤已存在的时间槽"
        ));
        result.put("咨询师时间槽问题", "uniq_slot_key (date_key, start_time, center_id, counselor_id)");
        result.put("测试接口", java.util.Arrays.asList(
            "POST /test/systemTimeSlot/testGeneration - 测试系统时间槽重新生成",
            "POST /test/systemTimeSlot/testCounselorSlotRegeneration - 测试咨询师时间槽重新生成",
            "POST /test/systemTimeSlot/testAllCounselorsRegeneration - 测试所有咨询师时间槽重新生成",
            "POST /test/systemTimeSlot/triggerFullTask - 触发完整定时任务"
        ));

        return AjaxResult.success(result);
    }

    /**
     * 测试咨询师时间槽重新生成
     */
    @PostMapping("/testCounselorSlotRegeneration")
    public AjaxResult testCounselorSlotRegeneration(
            @RequestParam Long counselorId,
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            logger.info("测试咨询师 {} 时间槽重新生成，天数：{}", counselorId, days);

            java.time.LocalDate startDate = java.time.LocalDate.now().plusDays(1);
            java.time.LocalDate endDate = startDate.plusDays(days - 1);

            com.xihuan.system.service.IPsyTimeSlotService timeSlotService =
                SpringUtils.getBean(com.xihuan.system.service.IPsyTimeSlotService.class);

            int count = timeSlotService.regenerateSlotsForCounselor(counselorId, startDate, endDate);

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "咨询师时间槽重新生成测试完成");
            result.put("counselorId", counselorId);
            result.put("generatedCount", count);
            result.put("dateRange", startDate + " 到 " + endDate);
            result.put("method", "regenerateSlotsForCounselor（先清理再生成）");

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("测试咨询师时间槽重新生成失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试所有咨询师时间槽重新生成
     */
    @PostMapping("/testAllCounselorsRegeneration")
    public AjaxResult testAllCounselorsRegeneration(
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            logger.info("测试所有咨询师时间槽重新生成，天数：{}", days);

            java.time.LocalDate startDate = java.time.LocalDate.now().plusDays(1);
            java.time.LocalDate endDate = startDate.plusDays(days - 1);

            com.xihuan.system.service.IPsyTimeSlotService timeSlotService =
                SpringUtils.getBean(com.xihuan.system.service.IPsyTimeSlotService.class);

            int count = timeSlotService.regenerateSlotsForAllCounselors(startDate, endDate);

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "所有咨询师时间槽重新生成测试完成");
            result.put("generatedCount", count);
            result.put("dateRange", startDate + " 到 " + endDate);
            result.put("method", "regenerateSlotsForAllCounselors（先清理再生成）");

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("测试所有咨询师时间槽重新生成失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }
}
