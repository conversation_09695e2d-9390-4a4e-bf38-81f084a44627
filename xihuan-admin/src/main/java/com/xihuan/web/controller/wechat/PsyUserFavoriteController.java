package com.xihuan.web.controller.wechat;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyUserFavorite;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.system.service.wxService.IPsyUserFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏管理
 */
@RestController
@RequestMapping("/psy/favorite")
public class PsyUserFavoriteController extends BaseController {
    @Autowired
    private IPsyUserFavoriteService favoriteService;

    /**
     * 获取收藏列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PsyUserFavorite favorite) {
        startPage();
        List<Map<String, Object>> list = favoriteService.getFavoritesWithDetails(favorite);
        return getDataTable(list);
    }

    /**
     * 新增收藏
     */
    @PostMapping
    public AjaxResult add(@RequestBody PsyUserFavorite favorite) {
        return AjaxResult.success(favoriteService.addFavorite(favorite));
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/{favoriteIds}")
    public AjaxResult remove(@PathVariable Long[] favoriteIds) {
        return toAjax(favoriteService.removeFavorite(favoriteIds));
    }

    /**
     * 检查是否已收藏
     */
//    @GetMapping("/check")
//    public AjaxResult check(@RequestParam("userId") Long userId,
//                           @RequestParam("targetType") Integer targetType,
//                           @RequestParam(value = "counselorId", required = false) Long counselorId,
//                           @RequestParam(value = "productId", required = false) Long productId) {
//        return AjaxResult.success(favoriteService.checkFavorites(userId, targetType, counselorId, productId));
//    }
} 