package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTInterpretation;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTInterpretationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 测评结果解释Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/interpretation")
public class PsyTInterpretationController extends BaseController {
    
    @Autowired
    private IPsyTInterpretationService interpretationService;

    /**
     * 查询测评结果解释列表
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTInterpretation interpretation) {
        startPage();
        List<PsyTInterpretation> list = interpretationService.selectInterpretationList(interpretation);
        return getDataTable(list);
    }

    /**
     * 导出测评结果解释列表
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:export')")
    @Log(title = "测评结果解释", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTInterpretation interpretation) {
        List<PsyTInterpretation> list = interpretationService.selectInterpretationList(interpretation);
        ExcelUtil<PsyTInterpretation> util = new ExcelUtil<PsyTInterpretation>(PsyTInterpretation.class);
        util.exportExcel(response, list, "测评结果解释数据");
    }

    /**
     * 获取测评结果解释详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(interpretationService.selectInterpretationById(id));
    }

    /**
     * 新增测评结果解释
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:add')")
    @Log(title = "测评结果解释", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PsyTInterpretation interpretation) {
        // 检查分数范围是否重叠
        boolean overlap = interpretationService.checkScoreRangeOverlap(
            interpretation.getScaleId(), interpretation.getDimension(),
            interpretation.getMinScore(), interpretation.getMaxScore(), null);
        
        if (overlap) {
            return error("分数范围与现有解释重叠");
        }
        
        return toAjax(interpretationService.insertInterpretation(interpretation));
    }

    /**
     * 修改测评结果解释
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:edit')")
    @Log(title = "测评结果解释", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PsyTInterpretation interpretation) {
        // 检查分数范围是否重叠（排除自己）
        boolean overlap = interpretationService.checkScoreRangeOverlap(
            interpretation.getScaleId(), interpretation.getDimension(),
            interpretation.getMinScore(), interpretation.getMaxScore(), interpretation.getId());
        
        if (overlap) {
            return error("分数范围与现有解释重叠");
        }
        
        return toAjax(interpretationService.updateInterpretation(interpretation));
    }

    /**
     * 删除测评结果解释
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:remove')")
    @Log(title = "测评结果解释", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(interpretationService.deleteInterpretationByIds(ids));
    }

    /**
     * 根据量表ID查询解释列表
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:list')")
    @GetMapping("/scale/{scaleId}")
    public AjaxResult interpretationsByScale(@PathVariable Long scaleId) {
        List<PsyTInterpretation> list = interpretationService.selectInterpretationsByScaleId(scaleId);
        return success(list);
    }

    /**
     * 根据量表ID和维度查询解释列表
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:list')")
    @GetMapping("/scale/{scaleId}/dimension")
    public AjaxResult interpretationsByScaleAndDimension(@PathVariable Long scaleId, 
                                                        @RequestParam(required = false) String dimension) {
        List<PsyTInterpretation> list = interpretationService.selectInterpretationsByScaleAndDimension(scaleId, dimension);
        return success(list);
    }

    /**
     * 根据分数查询匹配的解释
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:query')")
    @GetMapping("/match")
    public AjaxResult matchInterpretation(@RequestParam Long scaleId,
                                         @RequestParam BigDecimal score,
                                         @RequestParam(required = false) String dimension) {
        PsyTInterpretation interpretation = interpretationService.selectInterpretationByScore(scaleId, score, dimension);
        return success(interpretation);
    }

    /**
     * 查询总分解释列表
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:list')")
    @GetMapping("/scale/{scaleId}/total")
    public AjaxResult totalScoreInterpretations(@PathVariable Long scaleId) {
        List<PsyTInterpretation> list = interpretationService.selectTotalScoreInterpretations(scaleId);
        return success(list);
    }

    /**
     * 查询维度解释列表
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:list')")
    @GetMapping("/scale/{scaleId}/dimensions")
    public AjaxResult dimensionInterpretations(@PathVariable Long scaleId) {
        List<PsyTInterpretation> list = interpretationService.selectDimensionInterpretations(scaleId);
        return success(list);
    }

    /**
     * 查询量表的所有维度
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:list')")
    @GetMapping("/scale/{scaleId}/dimension-list")
    public AjaxResult dimensionList(@PathVariable Long scaleId) {
        List<String> dimensions = interpretationService.selectDimensionsByScaleId(scaleId);
        return success(dimensions);
    }

    /**
     * 查询解释统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:stats')")
    @GetMapping("/stats/{scaleId}")
    public AjaxResult interpretationStats(@PathVariable Long scaleId) {
        Map<String, Object> stats = interpretationService.selectInterpretationStats(scaleId);
        return success(stats);
    }

    /**
     * 验证解释配置的完整性
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:validate')")
    @GetMapping("/validate/{scaleId}")
    public AjaxResult validateConfig(@PathVariable Long scaleId) {
        Map<String, Object> result = interpretationService.validateInterpretationConfig(scaleId);
        return success(result);
    }

    /**
     * 复制解释配置
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:copy')")
    @Log(title = "测评结果解释", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public AjaxResult copyInterpretations(@RequestParam Long sourceScaleId, @RequestParam Long targetScaleId) {
        int count = interpretationService.copyInterpretations(sourceScaleId, targetScaleId);
        return success("成功复制 " + count + " 条解释配置");
    }

    /**
     * 导入解释配置
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:import')")
    @Log(title = "测评结果解释", businessType = BusinessType.IMPORT)
    @PostMapping("/import/{scaleId}")
    public AjaxResult importInterpretations(@PathVariable Long scaleId, MultipartFile file) throws Exception {
        ExcelUtil<PsyTInterpretation> util = new ExcelUtil<PsyTInterpretation>(PsyTInterpretation.class);
        List<PsyTInterpretation> interpretationList = util.importExcel(file.getInputStream());
        Map<String, Object> result = interpretationService.importInterpretations(scaleId, interpretationList);
        return success(result);
    }

    /**
     * 导出解释配置
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:export')")
    @Log(title = "测评结果解释", businessType = BusinessType.EXPORT)
    @PostMapping("/export/{scaleId}")
    public void exportInterpretations(HttpServletResponse response, @PathVariable Long scaleId) {
        List<PsyTInterpretation> list = interpretationService.exportInterpretations(scaleId);
        ExcelUtil<PsyTInterpretation> util = new ExcelUtil<PsyTInterpretation>(PsyTInterpretation.class);
        util.exportExcel(response, list, "解释配置数据");
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PsyTInterpretation> util = new ExcelUtil<PsyTInterpretation>(PsyTInterpretation.class);
        util.importTemplateExcel(response, "解释配置数据");
    }

    /**
     * 更新解释的显示顺序
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:edit')")
    @Log(title = "测评结果解释", businessType = BusinessType.UPDATE)
    @PutMapping("/order")
    public AjaxResult updateOrder(@RequestParam Long id, @RequestParam Integer orderNum) {
        return toAjax(interpretationService.updateInterpretationOrder(id, orderNum));
    }

    /**
     * 根据等级名称查询解释
     */
    @PreAuthorize("@ss.hasPermi('system:interpretation:query')")
    @GetMapping("/level")
    public AjaxResult interpretationByLevel(@RequestParam Long scaleId,
                                           @RequestParam String levelName,
                                           @RequestParam(required = false) String dimension) {
        PsyTInterpretation interpretation = interpretationService.selectInterpretationByLevel(scaleId, levelName, dimension);
        return success(interpretation);
    }

    /**
     * 获取解释结果
     */
    @GetMapping("/result")
    public AjaxResult getInterpretationResult(@RequestParam Long scaleId,
                                             @RequestParam BigDecimal totalScore,
                                             @RequestBody(required = false) Map<String, BigDecimal> dimensionScores) {
        Map<String, Object> result = interpretationService.getInterpretationResult(scaleId, totalScore, dimensionScores);
        return success(result);
    }

    /**
     * 检查分数范围重叠
     */
    @GetMapping("/check-overlap")
    public AjaxResult checkScoreRangeOverlap(@RequestParam Long scaleId,
                                            @RequestParam(required = false) String dimension,
                                            @RequestParam BigDecimal minScore,
                                            @RequestParam BigDecimal maxScore,
                                            @RequestParam(required = false) Long excludeId) {
        boolean overlap = interpretationService.checkScoreRangeOverlap(scaleId, dimension, minScore, maxScore, excludeId);
        return success(overlap);
    }
}
