package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.dto.MatchQuestionDTO;
import com.xihuan.common.core.domain.entity.PsyMatchQuestion;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.wxService.IPsyMatchQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 极速匹配问题Controller
 */
@RestController
@RequestMapping("/match/question")
public class PsyMatchQuestionController extends BaseController {
    @Autowired
    private IPsyMatchQuestionService questionService;
    
    /**
     * 获取问题列表
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam(required = false) String title) {
        startPage();
        List<PsyMatchQuestion> list = questionService.selectQuestionList(title);
        return getDataTable(list);
    }

    /**
     * 导出问题列表
     */
    @PreAuthorize("@ss.hasPermi('match:question:export')")
    @Log(title = "极速匹配问题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) String title) {
        List<PsyMatchQuestion> list = questionService.selectQuestionList(title);
        ExcelUtil<PsyMatchQuestion> util = new ExcelUtil<PsyMatchQuestion>(PsyMatchQuestion.class);
        util.exportExcel(response, list, "极速匹配问题数据");
    }
    
    /**
     * 获取问题详情
     */
    @GetMapping("/{questionId}")
    public AjaxResult getInfo(@PathVariable Long questionId) {
        return AjaxResult.success(questionService.getQuestion(questionId));
    }
    
    /**
     * 新增问题
     */
    @PostMapping
    public AjaxResult add(@RequestBody PsyMatchQuestion question) {
        return toAjax(questionService.addQuestion(question));
    }
    
    /**
     * 修改问题
     */
    @PutMapping
    public AjaxResult edit(@RequestBody PsyMatchQuestion question) {
        return toAjax(questionService.updateQuestion(question));
    }
    
    /**
     * 删除问题（支持单个删除和批量删除）
     */
    @DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable String questionIds) {
        // 解析ID字符串，支持单个ID或多个ID（用逗号分隔）
        String[] idArray = questionIds.split(",");
        Long[] ids = new Long[idArray.length];

        try {
            for (int i = 0; i < idArray.length; i++) {
                ids[i] = Long.parseLong(idArray[i].trim());
            }
        } catch (NumberFormatException e) {
            return error("无效的问题ID格式");
        }

        // 如果只有一个ID，调用单个删除方法
        if (ids.length == 1) {
            return toAjax(questionService.deleteQuestion(ids[0]));
        } else {
            // 多个ID，调用批量删除方法
            return toAjax(questionService.deleteQuestions(ids));
        }
    }

    /**
     * 根据选项筛选咨询师
     */
    @PostMapping("/match")
    public AjaxResult matchConsultants(@RequestBody List<MatchQuestionDTO> questions) {
        return AjaxResult.success(questionService.matchConsultants(questions));
    }

    /**
     * 更新选项关联的咨询师
     */
    @PostMapping("/option/{optionId}/consultants")
    public AjaxResult updateOptionConsultants(@PathVariable Long optionId, @RequestBody List<Long> consultantIds) {
        return toAjax(questionService.updateOptionConsultants(optionId, consultantIds));
    }

    /**
     * 获取选项关联的咨询师
     */
    @GetMapping("/option/{optionId}/consultants")
    public AjaxResult getOptionConsultants(@PathVariable Long optionId) {
        return AjaxResult.success(questionService.getOptionConsultants(optionId));
    }
} 