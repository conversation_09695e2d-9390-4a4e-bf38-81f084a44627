package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyMeditationReview;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyMeditationReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 冥想评价表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/meditationReview")
public class PsyMeditationReviewController extends BaseController {
    
    @Autowired
    private IPsyMeditationReviewService reviewService;

    /**
     * 查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyMeditationReview review) {
        startPage();
        List<PsyMeditationReview> list = reviewService.selectReviewList(review);
        return getDataTable(list);
    }

    /**
     * 导出评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:export')")
    @Log(title = "冥想评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyMeditationReview review) {
        List<PsyMeditationReview> list = reviewService.selectReviewList(review);
        ExcelUtil<PsyMeditationReview> util = new ExcelUtil<PsyMeditationReview>(PsyMeditationReview.class);
        util.exportExcel(response, list, "冥想评价数据");
    }

    /**
     * 获取评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(reviewService.selectReviewById(id));
    }

    /**
     * 获取评价详细信息（包含冥想和用户信息）
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(reviewService.selectReviewWithDetails(id));
    }

    /**
     * 新增评价
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:add')")
    @Log(title = "冥想评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyMeditationReview review) {
        return toAjax(reviewService.insertReview(review));
    }

    /**
     * 修改评价
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:edit')")
    @Log(title = "冥想评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyMeditationReview review) {
        return toAjax(reviewService.updateReview(review));
    }

    /**
     * 删除评价
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:remove')")
    @Log(title = "冥想评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(reviewService.deleteReviewByIds(ids));
    }

    /**
     * 根据冥想ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:list')")
    @GetMapping("/meditation/{meditationId}")
    public AjaxResult getReviewsByMeditation(@PathVariable Long meditationId) {
        List<PsyMeditationReview> list = reviewService.selectReviewsByMeditationId(meditationId);
        return success(list);
    }

    /**
     * 根据用户ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getReviewsByUser(@PathVariable Long userId) {
        List<PsyMeditationReview> list = reviewService.selectReviewsByUserId(userId);
        return success(list);
    }

    /**
     * 获取冥想评价统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditationReview:list')")
    @GetMapping("/statistics/{meditationId}")
    public AjaxResult getReviewStatistics(@PathVariable Long meditationId) {
        // 计算平均评分
        java.math.BigDecimal avgRating = reviewService.calculateAverageRating(meditationId);
        
        // 统计评价数量
        int totalCount = reviewService.countReviewsByMeditationId(meditationId);
        
        // 统计各星级评价数量
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("avgRating", avgRating);
        statistics.put("totalCount", totalCount);
        
        // 统计各星级数量
        for (int i = 1; i <= 5; i++) {
            PsyMeditationReview queryReview = new PsyMeditationReview();
            queryReview.setMeditationId(meditationId);
            queryReview.setRating(i);
            List<PsyMeditationReview> ratingList = reviewService.selectReviewList(queryReview);
            statistics.put("rating" + i + "Count", ratingList.size());
        }
        
        return success(statistics);
    }
}
