package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import com.xihuan.system.service.IPsyCounselorFilterService;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.IPsyTimeSlotFilterService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序时间槽选择Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/timeSlot")
public class MiniAppTimeSlotController extends BaseController {
    
    @Autowired
    private IPsySystemTimeSlotService systemTimeSlotService;
    
    @Autowired
    private IPsyCounselorFilterService counselorFilterService;

    @Autowired
    private IPsyTimeSlotFilterService timeSlotFilterService;

    /**
     * 获取系统公共时间槽（小程序首页展示）
     */
    @GetMapping("/public")
    public AjaxResult getPublicTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        
        Map<String, Object> result = systemTimeSlotService.getFormattedSystemTimeSlots(startDate, endDate, centerId);
        return AjaxResult.success(result);
    }

    /**
     * 获取指定日期的系统时间槽
     */
    @GetMapping("/date/{date}")
    public AjaxResult getTimeSlotsByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "1") Long centerId) {
        
        Map<String, Object> result = systemTimeSlotService.getFormattedSystemTimeSlots(date, date, centerId);
        return AjaxResult.success(result);
    }

    /**
     * 获取有可用咨询师的时间槽（只返回可用状态）
     */
    @GetMapping("/available")
    public AjaxResult getAvailableTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId,
            @RequestParam(defaultValue = "true") Boolean onlyAvailable) {

        List<PsySystemTimeSlot> availableSlots = systemTimeSlotService.selectAvailableSlots(startDate, endDate, centerId);

        // 如果只要可用状态，过滤掉已过期的
        if (onlyAvailable) {
            availableSlots = availableSlots.stream()
                .filter(slot -> slot.getStatus() == null || slot.getStatus() == 0)
                .collect(java.util.stream.Collectors.toList());
        }

        return AjaxResult.success(availableSlots);
    }

    /**
     * 获取过滤后的可用时间槽（考虑咨询师到店时间）
     */
    @GetMapping("/filtered")
    public AjaxResult getFilteredTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId,
            @RequestParam(required = false) Long consultantId,
            @RequestParam(defaultValue = "2") Integer consultationType) { // 1=线上 2=线下

        // 获取基础可用时间槽
        List<PsySystemTimeSlot> availableSlots = systemTimeSlotService.selectAvailableSlots(startDate, endDate, centerId);

        // 如果指定了咨询师且是线下咨询，应用到店时间过滤
        if (consultantId != null && consultationType == 2) {
            LocalDateTime currentTime = LocalDateTime.now();

            // 转换为PsyTimeSlot格式进行过滤（这里需要适配）
            // 注意：这里简化处理，实际应该有专门的转换方法
            availableSlots = availableSlots.stream()
                .filter(slot -> {
                    try {
                        // 检查时间槽是否在咨询师可到达时间之后
                        LocalDate slotDate = LocalDate.parse(slot.getDateKey());
                        LocalDateTime slotDateTime = LocalDateTime.of(slotDate, slot.getStartTime());
                        LocalDateTime earliestTime = timeSlotFilterService.getEarliestOfflineAppointmentTime(
                            consultantId, centerId, currentTime
                        );
                        return slotDateTime.isAfter(earliestTime) || slotDateTime.isEqual(earliestTime);
                    } catch (Exception e) {
                        logger.warn("解析时间槽日期失败: dateKey={}", slot.getDateKey());
                        return false;
                    }
                })
                .collect(java.util.stream.Collectors.toList());
        }

        Map<String, Object> result = new HashMap<>();
        result.put("timeSlots", availableSlots);
        result.put("consultationType", consultationType);
        result.put("consultantId", consultantId);
        result.put("filteredCount", availableSlots.size());

        if (consultantId != null && consultationType == 2) {
            LocalDateTime earliestTime = timeSlotFilterService.getEarliestOfflineAppointmentTime(
                consultantId, centerId, LocalDateTime.now()
            );
            result.put("earliestOfflineTime", earliestTime);
            result.put("arrivalTimeHours", timeSlotFilterService.getConsultantArrivalTime(consultantId, centerId));
        }

        return AjaxResult.success(result);
    }

    /**
     * 根据选择的时间筛选可用咨询师（单个时间段）
     */
    @GetMapping("/counselors")
    public AjaxResult getAvailableCounselors(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime endTime,
            @RequestParam(defaultValue = "1") Long centerId,
            @RequestParam(defaultValue = "2") Integer consultationType) { // 1=线上 2=线下

        List<Map<String, Object>> counselors = counselorFilterService.filterAvailableCounselors(
            date, startTime, endTime, centerId
        );

        // 如果是线下咨询，过滤掉来不及到店的咨询师
        if (consultationType == 2) {
            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime appointmentTime = LocalDateTime.of(date, startTime);

            counselors = counselors.stream()
                .filter(counselor -> {
                    Long consultantId = Long.valueOf(counselor.get("id").toString());
                    LocalDateTime earliestTime = timeSlotFilterService.getEarliestOfflineAppointmentTime(
                        consultantId, centerId, currentTime
                    );
                    return appointmentTime.isAfter(earliestTime) || appointmentTime.isEqual(earliestTime);
                })
                .collect(java.util.stream.Collectors.toList());

            // 为每个咨询师添加到店时间信息
            for (Map<String, Object> counselor : counselors) {
                Long consultantId = Long.valueOf(counselor.get("id").toString());
                Double arrivalTime = timeSlotFilterService.getConsultantArrivalTime(consultantId, centerId);
                counselor.put("arrivalTimeHours", arrivalTime);

                LocalDateTime earliestTime = timeSlotFilterService.getEarliestOfflineAppointmentTime(
                    consultantId, centerId, currentTime
                );
                counselor.put("earliestOfflineTime", earliestTime);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("counselors", counselors);
        result.put("consultationType", consultationType);
        result.put("totalCount", counselors.size());
        result.put("timeRange", startTime + "-" + endTime);

        return AjaxResult.success(result);
    }

    /**
     * 根据多个时间段筛选可用咨询师（取交集）
     */
    @PostMapping("/counselors/multiple")
    public AjaxResult getCounselorsByMultipleTimeSlots(@RequestBody Map<String, Object> request) {
        try {
            LocalDate date = LocalDate.parse((String) request.get("date"));
            Long centerId = Long.valueOf(request.getOrDefault("centerId", 1).toString());

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> timeSlotList = (List<Map<String, Object>>) request.get("timeSlots");

            if (timeSlotList == null || timeSlotList.isEmpty()) {
                return AjaxResult.error("时间段列表不能为空");
            }

            // 转换时间段格式
            List<Map<String, LocalTime>> timeSlots = new ArrayList<>();
            for (Map<String, Object> slot : timeSlotList) {
                Map<String, LocalTime> timeSlot = new HashMap<>();
                timeSlot.put("startTime", LocalTime.parse((String) slot.get("startTime")));
                timeSlot.put("endTime", LocalTime.parse((String) slot.get("endTime")));
                timeSlots.add(timeSlot);
            }

            List<Map<String, Object>> counselors = counselorFilterService.filterCounselorsByMultipleTimeSlots(
                date, timeSlots, centerId
            );

            return AjaxResult.success(counselors);
        } catch (Exception e) {
            logger.error("多时间段筛选咨询师失败", e);
            return AjaxResult.error("筛选失败：" + e.getMessage());
        }
    }

    /**
     * 灵活的咨询师筛选接口（支持单个或多个时间段）
     */
    @PostMapping("/counselors/flexible")
    public AjaxResult getFlexibleCounselors(@RequestBody Map<String, Object> request) {
        try {
            LocalDate date = LocalDate.parse((String) request.get("date"));
            Long centerId = Long.valueOf(request.getOrDefault("centerId", 1).toString());

            // 检查是单个时间段还是多个时间段
            if (request.containsKey("startTime") && request.containsKey("endTime")) {
                // 单个时间段
                LocalTime startTime = LocalTime.parse((String) request.get("startTime"));
                LocalTime endTime = LocalTime.parse((String) request.get("endTime"));

                List<Map<String, Object>> counselors = counselorFilterService.filterAvailableCounselors(
                    date, startTime, endTime, centerId
                );

                Map<String, Object> result = new HashMap<>();
                result.put("type", "single");
                result.put("timeRange", startTime + "-" + endTime);
                result.put("counselors", counselors);
                result.put("count", counselors.size());

                return AjaxResult.success(result);

            } else if (request.containsKey("timeSlots")) {
                // 多个时间段
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> timeSlotList = (List<Map<String, Object>>) request.get("timeSlots");

                if (timeSlotList == null || timeSlotList.isEmpty()) {
                    return AjaxResult.error("时间段列表不能为空");
                }

                // 转换时间段格式
                List<Map<String, LocalTime>> timeSlots = new ArrayList<>();
                List<String> timeRanges = new ArrayList<>();

                for (Map<String, Object> slot : timeSlotList) {
                    Map<String, LocalTime> timeSlot = new HashMap<>();
                    LocalTime startTime = LocalTime.parse((String) slot.get("startTime"));
                    LocalTime endTime = LocalTime.parse((String) slot.get("endTime"));

                    timeSlot.put("startTime", startTime);
                    timeSlot.put("endTime", endTime);
                    timeSlots.add(timeSlot);
                    timeRanges.add(startTime + "-" + endTime);
                }

                List<Map<String, Object>> counselors = counselorFilterService.filterCounselorsByMultipleTimeSlots(
                    date, timeSlots, centerId
                );

                Map<String, Object> result = new HashMap<>();
                result.put("type", "multiple");
                result.put("timeRanges", timeRanges);
                result.put("timeSlotCount", timeSlots.size());
                result.put("counselors", counselors);
                result.put("count", counselors.size());
                result.put("description", "筛选出在所有时间段都可用的咨询师");

                return AjaxResult.success(result);

            } else {
                return AjaxResult.error("请提供时间段信息：单个时间段使用startTime和endTime，多个时间段使用timeSlots数组");
            }

        } catch (Exception e) {
            logger.error("灵活筛选咨询师失败", e);
            return AjaxResult.error("筛选失败：" + e.getMessage());
        }
    }

    /**
     * 根据时长筛选可用咨询师
     */
    @GetMapping("/counselors/duration")
    public AjaxResult getCounselorsByDuration(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime startTime,
            @RequestParam Integer duration,
            @RequestParam(defaultValue = "1") Long centerId) {
        
        List<Map<String, Object>> counselors = counselorFilterService.filterAvailableCounselorsByDuration(
            date, startTime, duration, centerId
        );
        
        return AjaxResult.success(counselors);
    }

    /**
     * 根据系统时间槽ID筛选咨询师
     */
    @GetMapping("/counselors/slot/{systemSlotId}")
    public AjaxResult getCounselorsBySystemSlot(@PathVariable Long systemSlotId) {
        List<Map<String, Object>> counselors = counselorFilterService.filterCounselorsBySystemSlot(systemSlotId);
        return AjaxResult.success(counselors);
    }

    /**
     * 获取咨询师详细可用性信息
     */
    @GetMapping("/counselor/{counselorId}/availability")
    public AjaxResult getCounselorAvailability(
            @PathVariable Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime endTime) {
        
        Map<String, Object> availability = counselorFilterService.getCounselorAvailabilityDetail(
            counselorId, date, startTime, endTime
        );
        
        return AjaxResult.success(availability);
    }

    /**
     * 根据用户偏好筛选咨询师
     */
    @PostMapping("/counselors/preferences")
    public AjaxResult getCounselorsByPreferences(@RequestBody Map<String, Object> request) {
        LocalDate date = LocalDate.parse((String) request.get("date"));
        LocalTime startTime = LocalTime.parse((String) request.get("startTime"));
        LocalTime endTime = LocalTime.parse((String) request.get("endTime"));
        Long centerId = Long.valueOf(request.getOrDefault("centerId", 1).toString());
        
        @SuppressWarnings("unchecked")
        Map<String, Object> preferences = (Map<String, Object>) request.get("preferences");
        
        List<Map<String, Object>> counselors = counselorFilterService.filterCounselorsByPreferences(
            date, startTime, endTime, centerId, preferences
        );
        
        return AjaxResult.success(counselors);
    }

    /**
     * 获取时间段统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getTimeSlotStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime endTime,
            @RequestParam(defaultValue = "1") Long centerId) {
        
        Map<String, Object> statistics = counselorFilterService.getCounselorStatistics(
            date, startTime, endTime, centerId
        );
        
        return AjaxResult.success(statistics);
    }

    /**
     * 获取推荐时间段（基于历史数据和当前可用性）
     */
    @GetMapping("/recommendations")
    public AjaxResult getRecommendedTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId,
            @RequestParam(defaultValue = "5") Integer limit) {
        
        // 获取有可用咨询师的时间槽
        var availableSlots = systemTimeSlotService.selectAvailableSlots(startDate, endDate, centerId);
        
        // 按可用咨询师数量排序，取前N个
        var recommendations = availableSlots.stream()
            .sorted((a, b) -> b.getAvailableCounselors().compareTo(a.getAvailableCounselors()))
            .limit(limit)
            .collect(java.util.stream.Collectors.toList());
        
        return AjaxResult.success(recommendations);
    }
}
