package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTabbarMenu;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.wxService.IPsyTabbarMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 心理咨询移动端导航菜单Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/tabbar")
public class PsyTabbarMenuController extends BaseController {
    @Autowired
    private IPsyTabbarMenuService menuService;

    /**
     * 获取菜单列表（支持权限搜索）
     */
    @PreAuthorize("@ss.hasPermi('wechat:tabbar:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTabbarMenu menu, @RequestParam(required = false) String permissions) {
        startPage();
        // 如果有权限参数，使用权限搜索
        if (permissions != null && !permissions.trim().isEmpty()) {
            // 处理单个权限或多个权限（逗号分隔）
            List<String> permissionList = new java.util.ArrayList<>();
            String[] permissionArray = permissions.split(",");
            for (String permission : permissionArray) {
                String trimmed = permission.trim();
                if (!trimmed.isEmpty()) {
                    permissionList.add(trimmed);
                }
            }

            System.out.println("权限搜索参数: " + permissionList);
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(permissionList);
            System.out.println("权限搜索结果数量: " + list.size());
            return getDataTable(list);
        } else {
            // 否则使用普通搜索
            List<PsyTabbarMenu> list = menuService.selectMenuList(menu);
            return getDataTable(list);
        }
    }

    /**
     * 导出菜单列表
     */
    @PreAuthorize("@ss.hasPermi('wechat:tabbar:export')")
    @Log(title = "移动端导航菜单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTabbarMenu menu, @RequestParam(required = false) String permissions) {
        List<PsyTabbarMenu> list;
        // 如果有权限参数，使用权限搜索
        if (permissions != null && !permissions.trim().isEmpty()) {
            List<String> permissionList = new java.util.ArrayList<>();
            String[] permissionArray = permissions.split(",");
            for (String permission : permissionArray) {
                String trimmed = permission.trim();
                if (!trimmed.isEmpty()) {
                    permissionList.add(trimmed);
                }
            }
            list = menuService.selectMenuByPermissions(permissionList);
        } else {
            list = menuService.selectMenuList(menu);
        }
        ExcelUtil<PsyTabbarMenu> util = new ExcelUtil<PsyTabbarMenu>(PsyTabbarMenu.class);
        util.exportExcel(response, list, "移动端导航菜单数据");
    }

    /**
     * 获取菜单详细信息
     */
    @PreAuthorize("@ss.hasPermi('wechat:tabbar:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(menuService.selectMenuById(id));
    }

    /**
     * 根据权限获取菜单列表
     */
    @GetMapping("/listByPermissions")
    public AjaxResult listByPermissions(@RequestParam(required = false) List<String> permissions) {
        List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(permissions);
        return success(list);
    }

    /**
     * 新增菜单
     */
    @PreAuthorize("@ss.hasPermi('wechat:tabbar:add')")
    @Log(title = "移动端导航菜单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTabbarMenu menu) {
        return toAjax(menuService.insertMenu(menu));
    }

    /**
     * 修改菜单
     */
    @PreAuthorize("@ss.hasPermi('wechat:tabbar:edit')")
    @Log(title = "移动端导航菜单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTabbarMenu menu) {
        return toAjax(menuService.updateMenu(menu));
    }

    /**
     * 删除菜单
     */
    @PreAuthorize("@ss.hasPermi('wechat:tabbar:remove')")
    @Log(title = "移动端导航菜单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(menuService.deleteMenuByIds(ids));
    }
} 