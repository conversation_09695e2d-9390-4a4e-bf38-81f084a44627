package com.xihuan.web.controller.miniapp;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyFavoriteGroup;
import com.xihuan.common.core.domain.entity.PsyUserFavorite;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.ServletUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.wxService.IPsyUserFavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 小程序收藏管理
 */
@RestController
@RequestMapping("/miniapp/favorite")
public class MiniAppFavoriteController extends BaseController {
    
    @Autowired
    @Qualifier("psyUserFavoriteServiceNew")
    private IPsyUserFavoriteService favoriteService;
    
    @Autowired
    private TokenService tokenService;
    
    /**
     * 添加收藏
     */
    @PostMapping("/add")
    @Log(title = "添加收藏", businessType = BusinessType.INSERT)
    public AjaxResult addFavorite(@RequestBody PsyUserFavorite favorite, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        favorite.setUserId(loginUser.getUserId());
        
        // 检查是否已收藏
        PsyUserFavorite existFavorite = favoriteService.checkFavorites(
                loginUser.getUserId(), favorite.getTargetType(), favorite.getTargetId());
        
        if (existFavorite != null) {
            return AjaxResult.error("已收藏，请勿重复操作");
        }
        
        // 添加收藏
        favoriteService.addFavorite(favorite);
        
        return AjaxResult.success("收藏成功");
    }
    
    /**
     * 取消收藏
     */
    @DeleteMapping("/remove/{favoriteId}")
    @Log(title = "取消收藏", businessType = BusinessType.DELETE)
    public AjaxResult removeFavorite(@PathVariable Long favoriteId, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        // 检查是否是自己的收藏
        PsyUserFavorite favorite = favoriteService.getFavoriteById(favoriteId);
        if (favorite == null || !favorite.getUserId().equals(loginUser.getUserId())) {
            return AjaxResult.error("无权操作此收藏");
        }
        
        // 取消收藏
        favoriteService.removeFavorite(new Long[]{favoriteId});
        
        return AjaxResult.success("取消收藏成功");
    }
    
    /**
     * 批量取消收藏
     */
    @DeleteMapping("/batchRemove")
    @Log(title = "批量取消收藏", businessType = BusinessType.DELETE)
    public AjaxResult batchRemoveFavorite(@RequestBody Long[] favoriteIds, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        // 取消收藏
        favoriteService.removeFavorite(favoriteIds);
        
        return AjaxResult.success("批量取消收藏成功");
    }
    
    /**
     * 查询收藏列表
     */
    @GetMapping("/list")
    public AjaxResult listFavorites(
            @RequestParam(required = false) Integer targetType,
            HttpServletRequest request) {
        
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        PsyUserFavorite favorite = new PsyUserFavorite();
        favorite.setUserId(loginUser.getUserId());
        if (targetType != null) {
            favorite.setTargetType(targetType);
        }
        
        List<Map<String, Object>> favorites = favoriteService.getFavoritesWithDetails(favorite);
        
        return AjaxResult.success(favorites);
    }
    
    /**
     * 查询收藏详情
     */
    @GetMapping("/detail/{favoriteId}")
    public AjaxResult getFavoriteDetail(@PathVariable Long favoriteId, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        PsyUserFavorite favorite = favoriteService.getFavoriteById(favoriteId);
        if (favorite == null) {
            return AjaxResult.error("收藏不存在");
        }
        
        // 更新查看次数
        favoriteService.updateViewCount(favoriteId);
        
        return AjaxResult.success(favorite);
    }
    
    /**
     * 检查是否已收藏
     */
    @GetMapping("/check")
    public AjaxResult checkFavorite(
            @RequestParam Integer targetType,
            @RequestParam Long targetId,
            HttpServletRequest request) {
        
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        PsyUserFavorite favorite = favoriteService.checkFavorites(
                loginUser.getUserId(), targetType, targetId);
        
        return AjaxResult.success("查询成功", favorite != null);
    }
    
    /**
     * 查询收藏统计
     */
    @GetMapping("/stats")
    public AjaxResult getFavoriteStats(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        Map<String, Object> stats = favoriteService.getUserFavoriteStats(loginUser.getUserId());
        
        return AjaxResult.success(stats);
    }
    
    /**
     * 查询收藏分组列表
     */
    @GetMapping("/group/list")
    public AjaxResult listFavoriteGroups(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        List<PsyFavoriteGroup> groups = favoriteService.getFavoriteGroups(loginUser.getUserId());
        
        return AjaxResult.success(groups);
    }
    
    /**
     * 创建收藏分组
     */
    @PostMapping("/group/create")
    @Log(title = "创建收藏分组", businessType = BusinessType.INSERT)
    public AjaxResult createFavoriteGroup(@RequestBody PsyFavoriteGroup group, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        group.setUserId(loginUser.getUserId());
        group.setFavoriteCount(0);
        group.setIsDefault(0);
        
        favoriteService.createFavoriteGroup(group);
        
        return AjaxResult.success("创建成功", group);
    }
    
    /**
     * 更新收藏分组
     */
    @PutMapping("/group/update")
    @Log(title = "更新收藏分组", businessType = BusinessType.UPDATE)
    public AjaxResult updateFavoriteGroup(@RequestBody PsyFavoriteGroup group, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        // 检查是否是自己的分组
        PsyFavoriteGroup existGroup = favoriteService.getFavoriteGroups(loginUser.getUserId())
                .stream()
                .filter(g -> g.getGroupId().equals(group.getGroupId()))
                .findFirst()
                .orElse(null);
        
        if (existGroup == null) {
            return AjaxResult.error("无权操作此分组");
        }
        
        favoriteService.updateFavoriteGroup(group);
        
        return AjaxResult.success("更新成功");
    }
    
    /**
     * 删除收藏分组
     */
    @DeleteMapping("/group/delete/{groupId}")
    @Log(title = "删除收藏分组", businessType = BusinessType.DELETE)
    public AjaxResult deleteFavoriteGroup(@PathVariable Long groupId, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        // 检查是否是自己的分组
        PsyFavoriteGroup existGroup = favoriteService.getFavoriteGroups(loginUser.getUserId())
                .stream()
                .filter(g -> g.getGroupId().equals(groupId))
                .findFirst()
                .orElse(null);
        
        if (existGroup == null) {
            return AjaxResult.error("无权操作此分组");
        }
        
        if (existGroup.getIsDefault() == 1) {
            return AjaxResult.error("默认分组不能删除");
        }
        
        favoriteService.deleteFavoriteGroup(groupId);
        
        return AjaxResult.success("删除成功");
    }
    
    /**
     * 添加收藏到分组
     */
    @PostMapping("/group/addFavorite")
    @Log(title = "添加收藏到分组", businessType = BusinessType.INSERT)
    public AjaxResult addFavoriteToGroup(
            @RequestParam Long favoriteId,
            @RequestParam Long groupId,
            HttpServletRequest request) {
        
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        favoriteService.addFavoriteToGroup(favoriteId, groupId);
        
        return AjaxResult.success("添加成功");
    }
    
    /**
     * 从分组中移除收藏
     */
    @DeleteMapping("/group/removeFavorite")
    @Log(title = "从分组中移除收藏", businessType = BusinessType.DELETE)
    public AjaxResult removeFavoriteFromGroup(
            @RequestParam Long favoriteId,
            @RequestParam Long groupId,
            HttpServletRequest request) {
        
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        favoriteService.removeFavoriteFromGroup(favoriteId, groupId);
        
        return AjaxResult.success("移除成功");
    }
    
    /**
     * 查询分组内收藏列表
     */
    @GetMapping("/group/favorites/{groupId}")
    public AjaxResult getGroupFavorites(@PathVariable Long groupId, HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }
        
        List<Map<String, Object>> favorites = favoriteService.getGroupFavorites(groupId, loginUser.getUserId());
        
        return AjaxResult.success(favorites);
    }
}
