package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyCourseOrder;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyCourseOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 课程订单表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/order")
public class PsyCourseOrderController extends BaseController {
    
    @Autowired
    private IPsyCourseOrderService orderService;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyCourseOrder order) {
        startPage();
        List<PsyCourseOrder> list = orderService.selectOrderList(order);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyCourseOrder order) {
        List<PsyCourseOrder> list = orderService.selectOrderList(order);
        ExcelUtil<PsyCourseOrder> util = new ExcelUtil<PsyCourseOrder>(PsyCourseOrder.class);
        util.exportExcel(response, list, "订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(orderService.selectOrderById(id));
    }

    /**
     * 获取订单详细信息（包含课程和用户信息）
     */
    @PreAuthorize("@ss.hasPermi('system:order:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(orderService.selectOrderWithDetails(id));
    }

    /**
     * 根据订单号查询订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:query')")
    @GetMapping(value = "/orderNo/{orderNo}")
    public AjaxResult getByOrderNo(@PathVariable("orderNo") String orderNo) {
        return success(orderService.selectOrderByOrderNo(orderNo));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyCourseOrder order) {
        return toAjax(orderService.insertOrder(order));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyCourseOrder order) {
        return toAjax(orderService.updateOrder(order));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(orderService.deleteOrderByIds(ids));
    }

    /**
     * 更新订单支付状态
     */
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "订单支付", businessType = BusinessType.UPDATE)
    @PutMapping("/payment/{orderNo}")
    public AjaxResult updatePaymentStatus(@PathVariable String orderNo, 
                                        @RequestParam Integer status,
                                        @RequestParam(required = false) String paymentMethod,
                                        @RequestParam(required = false) String transactionId) {
        return toAjax(orderService.updateOrderPaymentStatus(orderNo, status, paymentMethod, transactionId, DateUtils.getNowDate()));
    }

    /**
     * 订单退款
     */
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "订单退款", businessType = BusinessType.UPDATE)
    @PutMapping("/refund/{orderNo}")
    public AjaxResult refund(@PathVariable String orderNo, @RequestParam BigDecimal refundAmount) {
        return toAjax(orderService.updateOrderRefund(orderNo, refundAmount, DateUtils.getNowDate()));
    }

    /**
     * 生成订单号
     */
    @PreAuthorize("@ss.hasPermi('system:order:add')")
    @GetMapping("/generateOrderNo")
    public AjaxResult generateOrderNo() {
        String orderNo = orderService.generateOrderNo();
        return success(orderNo);
    }
}
