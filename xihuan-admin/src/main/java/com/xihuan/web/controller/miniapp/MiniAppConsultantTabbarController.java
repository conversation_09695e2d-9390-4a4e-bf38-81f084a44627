package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTabbarMenu;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.wxService.IPsyTabbarMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 小程序导航菜单Controller（咨询师端）
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/consultant/tabbar")
public class MiniAppConsultantTabbarController extends BaseController {
    
    @Autowired
    private IPsyTabbarMenuService menuService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取咨询师端导航菜单列表
     */
    @GetMapping("/list")
    public AjaxResult list(HttpServletRequest request) {
        try {
            // 获取当前用户权限
            LoginUser loginUser = null;
            List<String> userPermissions = new ArrayList<>();
            
            try {
                loginUser = tokenService.getLoginUser(request);
                if (loginUser != null && loginUser.getPermissions() != null) {
                    userPermissions.addAll(loginUser.getPermissions());
                }
            } catch (Exception e) {
                // 未登录用户，返回错误
                return error("请先登录");
            }
            
            // 咨询师端默认权限
            userPermissions.add("consultant");
            userPermissions.add("wxconsultant");
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(userPermissions);
            return success(list);
        } catch (Exception e) {
            logger.error("获取咨询师端导航菜单失败", e);
            return error("获取导航菜单失败");
        }
    }

    /**
     * 获取咨询师工作台菜单
     */
    @GetMapping("/workbench")
    public AjaxResult getWorkbenchMenus(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            // 验证是否为咨询师
            if (loginUser.getUser().getDeptId() == null || !loginUser.getUser().getDeptId().equals(201L)) {
                return error("您不是咨询师，无权限访问");
            }
            
            List<String> consultantPermissions = new ArrayList<>();
            consultantPermissions.add("consultant");
            consultantPermissions.add("wxconsultant");
            consultantPermissions.add("workbench");
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(consultantPermissions);
            return success(list);
        } catch (Exception e) {
            logger.error("获取咨询师工作台菜单失败", e);
            return error("获取工作台菜单失败");
        }
    }

    /**
     * 获取咨询师个人中心菜单
     */
    @GetMapping("/profile")
    public AjaxResult getProfileMenus(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            List<String> profilePermissions = new ArrayList<>();
            profilePermissions.add("consultant");
            profilePermissions.add("profile");
            profilePermissions.add("personal");
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(profilePermissions);
            return success(list);
        } catch (Exception e) {
            logger.error("获取咨询师个人中心菜单失败", e);
            return error("获取个人中心菜单失败");
        }
    }

    /**
     * 检查咨询师权限
     */
    @GetMapping("/checkConsultantPermission")
    public AjaxResult checkConsultantPermission(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return success(false);
            }
            
            // 检查是否为咨询师（部门ID为201）
            boolean isConsultant = loginUser.getUser().getDeptId() != null && 
                                 loginUser.getUser().getDeptId().equals(201L);
            
            return success(isConsultant);
        } catch (Exception e) {
            logger.error("检查咨询师权限失败", e);
            return success(false);
        }
    }

    /**
     * 获取咨询师状态菜单
     */
    @GetMapping("/statusMenus")
    public AjaxResult getStatusMenus(@RequestParam String status, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            List<String> statusPermissions = new ArrayList<>();
            statusPermissions.add("consultant");
            
            // 根据状态添加不同权限
            switch (status) {
                case "online":
                    statusPermissions.add("online");
                    statusPermissions.add("available");
                    break;
                case "busy":
                    statusPermissions.add("busy");
                    statusPermissions.add("consulting");
                    break;
                case "offline":
                    statusPermissions.add("offline");
                    break;
                default:
                    statusPermissions.add("default");
            }
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(statusPermissions);
            return success(list);
        } catch (Exception e) {
            logger.error("获取咨询师状态菜单失败", e);
            return error("获取状态菜单失败");
        }
    }

    /**
     * 获取咨询师快捷操作菜单
     */
    @GetMapping("/quickActions")
    public AjaxResult getQuickActions(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            List<String> quickPermissions = new ArrayList<>();
            quickPermissions.add("consultant");
            quickPermissions.add("quick");
            quickPermissions.add("action");
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(quickPermissions);
            return success(list);
        } catch (Exception e) {
            logger.error("获取快捷操作菜单失败", e);
            return error("获取快捷操作菜单失败");
        }
    }
}
