package com.xihuan.web.controller.system;

import com.xihuan.common.constant.Constants;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.SysMenu;
import com.xihuan.common.core.domain.entity.SysUser;
import com.xihuan.common.core.domain.model.LoginBody;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.framework.web.service.SysLoginService;
import com.xihuan.framework.web.service.SysPermissionService;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.ISysMenuService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        System.out.println(loginBody);
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    @PostMapping("/wx/login")
    public AjaxResult login(@RequestParam String code) {
        System.out.println(code);
        String token = loginService.wxLogin(code);
        return AjaxResult.success().put("token", token);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Set<String> permissions = loginUser.getPermissions();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合 - 所有用户都应该获取权限
        permissions = permissionService.getMenuPermission(user);
        if (permissions != null && !permissions.equals(loginUser.getPermissions())) {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    /**
     * 手机验证码登录
     *
     * @param phoneLoginBody 登录信息
     * @return 结果
     */
    @PostMapping("/phoneLogin")
    public AjaxResult phoneLogin(@RequestBody PhoneLoginBody phoneLoginBody) {
        String token = loginService.phoneLogin(phoneLoginBody.getPhone(), phoneLoginBody.getCode());
        return AjaxResult.success().put(Constants.TOKEN, token);
    }

    /**
     * 发送短信验证码
     *
     * @param phone 手机号
     * @return 结果
     */
    @GetMapping("/sms/sendCode")
    public AjaxResult sendCode(@RequestParam String phone) {
        // TODO: 添加手机号格式校验
        boolean success = loginService.sendSmsCode(phone);
        return success ? AjaxResult.success("发送成功") : AjaxResult.error("发送失败");
    }

    /**
     * 微信手机号验证组件登录
     *
     * @param wxPhoneLoginBody 手机号获取凭证
     * @return 结果
     */
    @PostMapping("/wx/phone/login")
    public AjaxResult wxPhoneLogin(@RequestBody WxPhoneLoginBody wxPhoneLoginBody) {
        String token = loginService.wxPhoneLogin(wxPhoneLoginBody.getCode());
        return AjaxResult.success().put(Constants.TOKEN, token);
    }

    @Data
    public static class PhoneLoginBody {
        private String phone;
        private String code;
    }

    @Data
    public static class WxPhoneLoginBody {
        private String code;
    }
}
