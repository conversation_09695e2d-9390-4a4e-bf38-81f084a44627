package com.xihuan.web.controller.miniapp;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantCenterConfig;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsyConsultantCenterConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 咨询师个人设置Controller（小程序端）
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/consultant/settings")
public class MiniAppConsultantSettingsController extends BaseController {
    
    @Autowired
    private IPsyConsultantCenterConfigService configService;

    /**
     * 获取咨询师的所有配置
     */
    @GetMapping("/{consultantId}")
    public AjaxResult getSettings(@PathVariable("consultantId") Long consultantId) {
        List<PsyConsultantCenterConfig> configs = configService.selectConfigsByConsultantId(consultantId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("configs", configs);
        result.put("consultantId", consultantId);
        result.put("totalCenters", configs.size());
        
        return AjaxResult.success(result);
    }

    /**
     * 获取咨询师在指定中心的配置
     */
    @GetMapping("/{consultantId}/center/{centerId}")
    public AjaxResult getCenterSettings(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId) {
        
        PsyConsultantCenterConfig config = configService.selectConfigByConsultantAndCenter(consultantId, centerId);
        
        if (config == null) {
            // 如果没有配置，创建默认配置
            configService.createDefaultConfig(consultantId, centerId);
            config = configService.selectConfigByConsultantAndCenter(consultantId, centerId);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("config", config);
        result.put("arrivalTimeHours", config != null ? config.getArrivalTimeHours() : 2.0);
        result.put("enableArrivalFilter", config != null ? config.getEnableArrivalFilter() : 1);
        result.put("isEnabled", config != null && config.getEnableArrivalFilter() == 1);
        
        return AjaxResult.success(result);
    }

    /**
     * 更新到店时间设置
     */
    @Log(title = "更新到店时间", businessType = BusinessType.UPDATE)
    @PutMapping("/{consultantId}/center/{centerId}/arrivalTime")
    public AjaxResult updateArrivalTime(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId,
            @RequestParam("hours") Double hours) {
        
        // 验证参数
        if (hours == null || hours < 0 || hours > 24) {
            return AjaxResult.error("到店时间必须在0-24小时之间");
        }
        
        int result = configService.setArrivalTime(consultantId, centerId, hours);
        
        if (result > 0) {
            return AjaxResult.success("到店时间已更新为 " + hours + " 小时");
        } else {
            return AjaxResult.error("更新失败");
        }
    }

    /**
     * 切换到店时间过滤开关
     */
    @Log(title = "切换到店时间过滤", businessType = BusinessType.UPDATE)
    @PutMapping("/{consultantId}/center/{centerId}/toggleFilter")
    public AjaxResult toggleArrivalFilter(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId,
            @RequestParam("enabled") boolean enabled) {
        
        int result = configService.setArrivalFilterEnabled(consultantId, centerId, enabled);
        
        if (result > 0) {
            String action = enabled ? "启用" : "禁用";
            String message = "已" + action + "到店时间过滤功能";
            
            if (!enabled) {
                message += "，客户可以预约任何时间段的线下咨询";
            } else {
                Double arrivalTime = configService.getArrivalTime(consultantId, centerId);
                message += "，客户只能预约您能及时到店的时间段（需要" + arrivalTime + "小时到店）";
            }
            
            Map<String, Object> result_data = new HashMap<>();
            result_data.put("enabled", enabled);
            result_data.put("message", message);
            result_data.put("arrivalTimeHours", configService.getArrivalTime(consultantId, centerId));
            
            return AjaxResult.success(message, result_data);
        } else {
            return AjaxResult.error("操作失败");
        }
    }

    /**
     * 批量更新多个中心的设置
     */
    @Log(title = "批量更新设置", businessType = BusinessType.UPDATE)
    @PutMapping("/{consultantId}/batchUpdate")
    public AjaxResult batchUpdateSettings(
            @PathVariable("consultantId") Long consultantId,
            @RequestBody Map<String, Object> settings) {
        
        Double arrivalTimeHours = Double.valueOf(settings.get("arrivalTimeHours").toString());
        Boolean enableFilter = Boolean.valueOf(settings.get("enableFilter").toString());
        @SuppressWarnings("unchecked")
        List<Long> centerIds = (List<Long>) settings.get("centerIds");
        
        int successCount = 0;
        for (Long centerId : centerIds) {
            try {
                configService.setArrivalTime(consultantId, centerId, arrivalTimeHours);
                configService.setArrivalFilterEnabled(consultantId, centerId, enableFilter);
                successCount++;
            } catch (Exception e) {
                logger.error("更新咨询师{}在中心{}的配置失败: {}", consultantId, centerId, e.getMessage());
            }
        }
        
        return AjaxResult.success("成功更新 " + successCount + " 个中心的配置");
    }

    /**
     * 重置为默认设置
     */
    @Log(title = "重置默认设置", businessType = BusinessType.UPDATE)
    @PostMapping("/{consultantId}/center/{centerId}/reset")
    public AjaxResult resetToDefault(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId) {
        
        // 删除现有配置
        PsyConsultantCenterConfig existingConfig = configService.selectConfigByConsultantAndCenter(consultantId, centerId);
        if (existingConfig != null) {
            configService.deleteConfigById(existingConfig.getId());
        }
        
        // 创建默认配置
        int result = configService.createDefaultConfig(consultantId, centerId);
        
        if (result > 0) {
            PsyConsultantCenterConfig newConfig = configService.selectConfigByConsultantAndCenter(consultantId, centerId);
            return AjaxResult.success("已重置为默认设置", newConfig);
        } else {
            return AjaxResult.error("重置失败");
        }
    }

    /**
     * 获取设置说明信息
     */
    @GetMapping("/help")
    public AjaxResult getHelpInfo() {
        Map<String, Object> help = new HashMap<>();
        help.put("arrivalTimeHelp", "设置您从当前位置到咨询中心所需的时间，系统会自动过滤掉您来不及到店的预约时间段");
        help.put("filterHelp", "启用后，客户只能预约您能及时到店的时间段；禁用后，客户可以预约任何时间段");
        help.put("onlineHelp", "线上咨询不受到店时间限制，无论是否启用过滤都可以正常预约");
        help.put("examples", new String[]{
            "如果您需要2小时到店，现在是上午10点，那么客户只能预约12点之后的线下咨询",
            "如果您住得很近（0.5小时），客户几乎可以预约任何时间的线下咨询",
            "如果您禁用过滤，客户可以预约任何时间，但请确保您能按时到达"
        });
        
        return AjaxResult.success(help);
    }
}
