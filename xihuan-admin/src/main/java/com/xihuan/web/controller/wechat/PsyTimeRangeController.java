package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTimeRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 时间段定义Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/timeRange")
public class PsyTimeRangeController extends BaseController {
    
    @Autowired
    private IPsyTimeRangeService timeRangeService;

    /**
     * 查询时间段定义列表
     */
    @PreAuthorize("@ss.hasPermi('system:timeRange:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTimeRange timeRange) {
        startPage();
        List<PsyTimeRange> list = timeRangeService.selectTimeRangeList(timeRange);
        return getDataTable(list);
    }

    /**
     * 查询所有有效的时间段定义
     */
    @GetMapping("/listActive")
    public AjaxResult listActive() {
        List<PsyTimeRange> list = timeRangeService.selectAllActiveTimeRanges();
        return AjaxResult.success(list);
    }

    /**
     * 导出时间段定义列表
     */
    @PreAuthorize("@ss.hasPermi('system:timeRange:export')")
    @Log(title = "时间段定义", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTimeRange timeRange) {
        List<PsyTimeRange> list = timeRangeService.selectTimeRangeList(timeRange);
        ExcelUtil<PsyTimeRange> util = new ExcelUtil<PsyTimeRange>(PsyTimeRange.class);
        util.exportExcel(response, list, "时间段定义数据");
    }

    /**
     * 获取时间段定义详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:timeRange:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(timeRangeService.selectTimeRangeById(id));
    }

    /**
     * 根据时间查询所属时间段
     */
    @GetMapping("/getByHour/{hour}")
    public AjaxResult getByHour(@PathVariable("hour") Integer hour) {
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeByHour(hour);
        return AjaxResult.success(timeRange);
    }

    /**
     * 新增时间段定义
     */
    @PreAuthorize("@ss.hasPermi('system:timeRange:add')")
    @Log(title = "时间段定义", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTimeRange timeRange) {
        // 校验时间段名称唯一性
        String checkResult = timeRangeService.checkTimeRangeNameUnique(timeRange);
        if (!"0".equals(checkResult)) {
            return AjaxResult.error(checkResult);
        }
        
        return toAjax(timeRangeService.insertTimeRange(timeRange));
    }

    /**
     * 修改时间段定义
     */
    @PreAuthorize("@ss.hasPermi('system:timeRange:edit')")
    @Log(title = "时间段定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTimeRange timeRange) {
        // 校验时间段名称唯一性
        String checkResult = timeRangeService.checkTimeRangeNameUnique(timeRange);
        if (!"0".equals(checkResult)) {
            return AjaxResult.error(checkResult);
        }
        
        return toAjax(timeRangeService.updateTimeRange(timeRange));
    }

    /**
     * 删除时间段定义
     */
    @PreAuthorize("@ss.hasPermi('system:timeRange:remove')")
    @Log(title = "时间段定义", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(timeRangeService.deleteTimeRangeByIds(ids));
    }

    /**
     * 初始化默认时间段
     */
    @PreAuthorize("@ss.hasPermi('system:timeRange:init')")
    @Log(title = "时间段定义", businessType = BusinessType.INSERT)
    @PostMapping("/init")
    public AjaxResult initDefault() {
        int result = timeRangeService.initDefaultTimeRanges();
        if (result > 0) {
            return AjaxResult.success("初始化成功，创建了 " + result + " 个默认时间段");
        } else {
            return AjaxResult.success("时间段已存在，无需初始化");
        }
    }
}
