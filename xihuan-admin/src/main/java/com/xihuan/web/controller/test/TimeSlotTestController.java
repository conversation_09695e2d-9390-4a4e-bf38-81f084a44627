package com.xihuan.web.controller.test;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.task.PsyTimeSlotTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * 时间槽生成测试控制器
 * 用于测试和调试时间槽生成功能
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test/timeslot")
public class TimeSlotTestController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeSlotTestController.class);
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;
    
    @Autowired
    private IPsySystemTimeSlotService systemTimeSlotService;
    
    @Autowired
    private PsyTimeSlotTaskService taskService;
    
    /**
     * 手动触发生成咨询师时间槽
     */
    @PostMapping("/generate/counselor")
    public AjaxResult generateCounselorSlots(@RequestParam(defaultValue = "7") int days) {
        try {
            logger.info("手动触发生成咨询师时间槽，天数：{}", days);
            
            LocalDate startDate = LocalDate.now().plusDays(1);
            LocalDate endDate = startDate.plusDays(days - 1);
            
            int count = timeSlotService.generateSlotsForAllCounselors(startDate, endDate);
            
            logger.info("生成咨询师时间槽完成，共生成 {} 个时间槽", count);
            
            return AjaxResult.success("成功生成 " + count + " 个咨询师时间槽");
        } catch (Exception e) {
            logger.error("生成咨询师时间槽失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动触发生成系统时间槽
     */
    @PostMapping("/generate/system")
    public AjaxResult generateSystemSlots(@RequestParam(defaultValue = "7") int days) {
        try {
            logger.info("手动触发生成系统时间槽，天数：{}", days);
            
            LocalDate startDate = LocalDate.now().plusDays(1);
            LocalDate endDate = startDate.plusDays(days - 1);
            
            int count = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, 1L);
            
            logger.info("生成系统时间槽完成，共生成 {} 个时间槽", count);
            
            return AjaxResult.success("成功生成 " + count + " 个系统时间槽");
        } catch (Exception e) {
            logger.error("生成系统时间槽失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动触发定时任务：生成未来时间槽
     */
    @PostMapping("/task/future")
    public AjaxResult triggerFutureTask() {
        try {
            logger.info("手动触发定时任务：生成未来时间槽");
            
            taskService.generateFutureTimeSlots();
            
            return AjaxResult.success("定时任务执行完成");
        } catch (Exception e) {
            logger.error("执行定时任务失败", e);
            return AjaxResult.error("执行失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动触发定时任务：生成系统公共时间槽
     */
    @PostMapping("/task/system")
    public AjaxResult triggerSystemTask() {
        try {
            logger.info("手动触发定时任务：生成系统公共时间槽");
            
            taskService.generateSystemTimeSlots();
            
            return AjaxResult.success("定时任务执行完成");
        } catch (Exception e) {
            logger.error("执行定时任务失败", e);
            return AjaxResult.error("执行失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询时间槽统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            // 这里可以添加统计查询逻辑
            return AjaxResult.success("统计信息查询功能待实现");
        } catch (Exception e) {
            logger.error("查询统计信息失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 调试匹配问题查询
     */
    @GetMapping("/debug/match-questions")
    public AjaxResult debugMatchQuestions(@RequestParam(required = false) String title) {
        try {
            logger.info("调试匹配问题查询，title参数: {}", title);

            // 获取匹配问题服务和Mapper
            com.xihuan.system.service.wxService.IPsyMatchQuestionService matchQuestionService =
                com.xihuan.common.utils.spring.SpringUtils.getBean(com.xihuan.system.service.wxService.IPsyMatchQuestionService.class);

            com.xihuan.system.mapper.PsyMatchQuestionMapper questionMapper =
                com.xihuan.common.utils.spring.SpringUtils.getBean(com.xihuan.system.mapper.PsyMatchQuestionMapper.class);

            java.util.Map<String, Object> result = new java.util.HashMap<>();

            // 1. 查看数据库中所有问题的状态
            java.util.List<java.util.Map<String, Object>> allQuestions = questionMapper.debugAllQuestions();
            result.put("allQuestionsInDB", allQuestions);
            result.put("totalInDB", allQuestions.size());

            // 2. 查看符合del_flag='0'条件的问题
            java.util.List<java.util.Map<String, Object>> filteredQuestions = questionMapper.debugFilteredQuestions(title);
            result.put("filteredQuestions", filteredQuestions);
            result.put("filteredCount", filteredQuestions.size());

            // 3. 调用Service方法查询
            java.util.List<com.xihuan.common.core.domain.entity.PsyMatchQuestion> serviceResult =
                matchQuestionService.selectQuestionList(title);
            result.put("serviceResultCount", serviceResult != null ? serviceResult.size() : 0);

            // 4. 测试简化查询（避免JOIN问题）
            java.util.List<com.xihuan.common.core.domain.entity.PsyMatchQuestion> simpleResult =
                questionMapper.selectQuestionListSimple(title);
            result.put("simpleQueryCount", simpleResult != null ? simpleResult.size() : 0);

            // 5. 分析差异
            java.util.List<String> analysis = new java.util.ArrayList<>();
            analysis.add("数据库总记录数: " + allQuestions.size());
            analysis.add("del_flag='0'的记录数: " + filteredQuestions.size());
            analysis.add("Service返回记录数: " + (serviceResult != null ? serviceResult.size() : 0));
            analysis.add("简化查询返回记录数: " + (simpleResult != null ? simpleResult.size() : 0));

            // 检查del_flag不为'0'的记录
            long deletedCount = allQuestions.stream()
                .filter(q -> !"0".equals(q.get("del_flag")))
                .count();
            analysis.add("del_flag不为'0'的记录数: " + deletedCount);

            // 如果简化查询返回7条，说明是JOIN问题
            if (simpleResult != null && simpleResult.size() == allQuestions.size() &&
                serviceResult != null && serviceResult.size() < allQuestions.size()) {
                analysis.add("⚠️ 问题确认：复杂JOIN查询导致记录丢失");
                analysis.add("💡 建议：使用简化查询或修复JOIN逻辑");
            }

            result.put("analysis", analysis);
            result.put("queryTitle", title);

            logger.info("调试结果 - 数据库总数: {}, 过滤后: {}, Service返回: {}, 简化查询: {}",
                allQuestions.size(), filteredQuestions.size(),
                serviceResult != null ? serviceResult.size() : 0,
                simpleResult != null ? simpleResult.size() : 0);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("调试匹配问题查询失败", e);
            return AjaxResult.error("调试失败：" + e.getMessage());
        }
    }

    /**
     * 测试过期时间槽状态更新
     */
    @PostMapping("/test/expired")
    public AjaxResult testExpiredUpdate() {
        try {
            logger.info("手动测试：过期时间槽状态更新");

            java.util.Map<String, Object> result = taskService.testUpdateExpiredSlotStatus();

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("测试过期时间槽状态更新失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }

    /**
     * 手动执行过期时间槽状态更新（基础版本）
     */
    @PostMapping("/update/expired/basic")
    public AjaxResult updateExpiredBasic() {
        try {
            logger.info("手动执行：过期时间槽状态更新（基础版本）");

            int count = timeSlotService.updateExpiredSlotStatus();

            logger.info("基础版本更新完成，共更新 {} 个时间槽", count);

            return AjaxResult.success("成功更新 " + count + " 个过期时间槽状态（基础版本）");
        } catch (Exception e) {
            logger.error("基础版本更新失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 手动执行过期时间槽状态更新（延后配置版本）
     */
    @PostMapping("/update/expired/delay")
    public AjaxResult updateExpiredWithDelay() {
        try {
            logger.info("手动执行：过期时间槽状态更新（延后配置版本）");

            int count = timeSlotService.updateExpiredSlotStatusWithDelay();

            logger.info("延后配置版本更新完成，共更新 {} 个时间槽", count);

            return AjaxResult.success("成功更新 " + count + " 个过期时间槽状态（延后配置版本）");
        } catch (Exception e) {
            logger.error("延后配置版本更新失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 手动执行定时任务：更新过期时间槽状态
     */
    @PostMapping("/task/expired")
    public AjaxResult triggerExpiredTask() {
        try {
            logger.info("手动触发定时任务：更新过期时间槽状态");

            taskService.updateExpiredSlotStatus();

            return AjaxResult.success("定时任务执行完成");
        } catch (Exception e) {
            logger.error("执行定时任务失败", e);
            return AjaxResult.error("执行失败：" + e.getMessage());
        }
    }

    /**
     * 手动执行定时任务：更新过期时间槽状态（延后配置）
     */
    @PostMapping("/task/expired/delay")
    public AjaxResult triggerExpiredDelayTask() {
        try {
            logger.info("手动触发定时任务：更新过期时间槽状态（延后配置）");

            taskService.updateExpiredSlotStatusWithDelay();

            return AjaxResult.success("延后配置定时任务执行完成");
        } catch (Exception e) {
            logger.error("执行延后配置定时任务失败", e);
            return AjaxResult.error("执行失败：" + e.getMessage());
        }
    }

    /**
     * 获取延后配置信息
     */
    @GetMapping("/config/delay")
    public AjaxResult getDelayConfig() {
        try {
            boolean enabled = taskService.isDelayExpirationEnabled();
            int hours = taskService.getDelayExpirationHours();

            java.util.Map<String, Object> config = new java.util.HashMap<>();
            config.put("enabled", enabled);
            config.put("hours", hours);
            config.put("currentTime", java.time.LocalDateTime.now().toString());

            return AjaxResult.success(config);
        } catch (Exception e) {
            logger.error("获取延后配置失败", e);
            return AjaxResult.error("获取配置失败：" + e.getMessage());
        }
    }

    /**
     * 简单测试过期状态更新
     */
    @PostMapping("/simple/test")
    public AjaxResult simpleTest() {
        try {
            logger.info("执行简单测试：过期时间槽状态更新");

            // 只测试延后配置版本
            int count = timeSlotService.updateExpiredSlotStatusWithDelay();

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("updatedCount", count);
            result.put("testTime", java.time.LocalDateTime.now().toString());
            result.put("success", true);

            logger.info("简单测试完成，更新了 {} 个时间槽", count);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("简单测试失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试Mapper方法修复效果
     */
    @GetMapping("/test/mapper-fix")
    public AjaxResult testMapperFix() {
        try {
            logger.info("测试Mapper方法修复效果");

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            java.util.List<String> testResults = new java.util.ArrayList<>();

            // 测试1：PsyTScaleMapper.selectSimilarScales
            try {
                com.xihuan.system.mapper.PsyTScaleMapper scaleMapper =
                    com.xihuan.common.utils.spring.SpringUtils.getBean(com.xihuan.system.mapper.PsyTScaleMapper.class);

                java.util.List<com.xihuan.common.core.domain.entity.PsyTScale> similarScales =
                    scaleMapper.selectSimilarScales(1L, 5);

                testResults.add("✅ PsyTScaleMapper.selectSimilarScales - 成功，返回 " +
                    (similarScales != null ? similarScales.size() : 0) + " 条记录");
            } catch (Exception e) {
                testResults.add("❌ PsyTScaleMapper.selectSimilarScales - 失败：" + e.getMessage());
            }

            // 测试2：PsyTAnswerRecordMapper.selectAnswerProgress
            try {
                com.xihuan.system.mapper.PsyTAnswerRecordMapper answerMapper =
                    com.xihuan.common.utils.spring.SpringUtils.getBean(com.xihuan.system.mapper.PsyTAnswerRecordMapper.class);

                java.util.Map<String, Object> progress = answerMapper.selectAnswerProgress(1L);

                testResults.add("✅ PsyTAnswerRecordMapper.selectAnswerProgress - 成功，返回数据：" +
                    (progress != null ? progress.size() + " 个字段" : "null"));
            } catch (Exception e) {
                testResults.add("❌ PsyTAnswerRecordMapper.selectAnswerProgress - 失败：" + e.getMessage());
            }

            // 测试3：PsyMatchQuestionMapper.selectQuestionList
            try {
                com.xihuan.system.mapper.PsyMatchQuestionMapper questionMapper =
                    com.xihuan.common.utils.spring.SpringUtils.getBean(com.xihuan.system.mapper.PsyMatchQuestionMapper.class);

                java.util.List<com.xihuan.common.core.domain.entity.PsyMatchQuestion> questions =
                    questionMapper.selectQuestionList(null);

                testResults.add("✅ PsyMatchQuestionMapper.selectQuestionList - 成功，返回 " +
                    (questions != null ? questions.size() : 0) + " 条记录");
            } catch (Exception e) {
                testResults.add("❌ PsyMatchQuestionMapper.selectQuestionList - 失败：" + e.getMessage());
            }

            result.put("testResults", testResults);
            result.put("testTime", java.time.LocalDateTime.now().toString());
            result.put("totalTests", testResults.size());

            long successCount = testResults.stream()
                .filter(r -> r.startsWith("✅"))
                .count();

            result.put("successCount", successCount);
            result.put("failureCount", testResults.size() - successCount);
            result.put("allPassed", successCount == testResults.size());

            logger.info("Mapper方法测试完成：{}/{} 个测试通过", successCount, testResults.size());

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("Mapper方法测试失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }
}
