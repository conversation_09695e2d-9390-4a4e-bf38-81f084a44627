package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTimeTemplateItem;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsyTimeTemplateItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 排班模板明细Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/schedule/template/item")
public class PsyTimeTemplateItemController extends BaseController {
    
    @Autowired
    private IPsyTimeTemplateItemService templateItemService;

    /**
     * 查询模板明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:template:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTimeTemplateItem item) {
        startPage();
        List<PsyTimeTemplateItem> list = templateItemService.selectTemplateItemList(item);
        return getDataTable(list);
    }

    /**
     * 根据模板ID查询明细
     */
    @GetMapping("/template/{templateId}")
    public AjaxResult getByTemplateId(@PathVariable("templateId") Long templateId) {
        List<PsyTimeTemplateItem> list = templateItemService.selectTemplateItemsByTemplateId(templateId);
        return success(list);
    }

    /**
     * 获取模板明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:template:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(templateItemService.selectTemplateItemById(id));
    }

    /**
     * 新增模板明细
     */
    @PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "模板明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTimeTemplateItem item) {
        item.setCreateBy(getUsername());
        return toAjax(templateItemService.insertTemplateItem(item));
    }

    /**
     * 批量新增模板明细
     */
    @PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "模板明细", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<PsyTimeTemplateItem> items) {
        for (PsyTimeTemplateItem item : items) {
            item.setCreateBy(getUsername());
        }
        int result = templateItemService.batchInsertTemplateItems(items);
        return toAjax(result);
    }

    /**
     * 修改模板明细
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "模板明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTimeTemplateItem item) {
        item.setUpdateBy(getUsername());
        return toAjax(templateItemService.updateTemplateItem(item));
    }

    /**
     * 删除模板明细
     */
    @PreAuthorize("@ss.hasPermi('system:template:remove')")
    @Log(title = "模板明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(templateItemService.deleteTemplateItemByIds(ids));
    }

    /**
     * 根据模板ID删除明细
     */
    @PreAuthorize("@ss.hasPermi('system:template:remove')")
    @Log(title = "模板明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/template/{templateId}")
    public AjaxResult removeByTemplateId(@PathVariable("templateId") Long templateId) {
        int result = templateItemService.deleteTemplateItemsByTemplateId(templateId);
        return AjaxResult.success("成功删除 " + result + " 条明细记录");
    }
}
