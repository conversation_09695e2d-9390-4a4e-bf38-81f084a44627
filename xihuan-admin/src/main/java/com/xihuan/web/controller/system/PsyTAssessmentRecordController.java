package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTAssessmentRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 测评记录管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/assessment-record")
public class PsyTAssessmentRecordController extends BaseController {
    
    @Autowired
    private IPsyTAssessmentRecordService assessmentRecordService;

    /**
     * 查询测评记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTAssessmentRecord assessmentRecord) {
        startPage();
        List<PsyTAssessmentRecord> list = assessmentRecordService.selectAssessmentRecordList(assessmentRecord);
        return getDataTable(list);
    }

    /**
     * 导出测评记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:export')")
    @Log(title = "测评记录管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTAssessmentRecord assessmentRecord) {
        List<PsyTAssessmentRecord> list = assessmentRecordService.selectAssessmentRecordList(assessmentRecord);
        ExcelUtil<PsyTAssessmentRecord> util = new ExcelUtil<PsyTAssessmentRecord>(PsyTAssessmentRecord.class);
        util.exportExcel(response, list, "测评记录数据");
    }

    /**
     * 获取测评记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(assessmentRecordService.selectAssessmentRecordById(id));
    }

    /**
     * 获取测评记录详情（包含答题记录）
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(assessmentRecordService.selectRecordWithAnswers(id));
    }

    /**
     * 新增测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:add')")
    @Log(title = "测评记录管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PsyTAssessmentRecord assessmentRecord) {
        return toAjax(assessmentRecordService.insertAssessmentRecord(assessmentRecord));
    }

    /**
     * 修改测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "测评记录管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PsyTAssessmentRecord assessmentRecord) {
        return toAjax(assessmentRecordService.updateAssessmentRecord(assessmentRecord));
    }

    /**
     * 删除测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:remove')")
    @Log(title = "测评记录管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(assessmentRecordService.deleteAssessmentRecordByIds(ids));
    }

    /**
     * 开始测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:start')")
    @Log(title = "测评记录管理", businessType = BusinessType.INSERT)
    @PostMapping("/start")
    public AjaxResult startAssessment(@RequestParam Long userId, 
                                     @RequestParam Long scaleId, 
                                     @RequestParam(required = false) Long enterpriseId) {
        // 检查用户是否可以测评
        Map<String, Object> checkResult = assessmentRecordService.checkUserCanAssess(userId, scaleId);
        if (!(Boolean) checkResult.get("canAssess")) {
            return error("无法开始测评：" + checkResult.get("errors"));
        }
        
        Long recordId = assessmentRecordService.startAssessment(userId, scaleId, enterpriseId);
        return success( recordId);
    }

    /**
     * 完成测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:complete')")
    @Log(title = "测评记录管理", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{recordId}")
    public AjaxResult completeAssessment(@PathVariable Long recordId,
                                        @RequestParam BigDecimal totalScore,
                                        @RequestBody(required = false) Map<String, BigDecimal> dimensionScores) {
        return toAjax(assessmentRecordService.completeAssessment(recordId, totalScore, dimensionScores));
    }

    /**
     * 暂停测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:pause')")
    @Log(title = "测评记录管理", businessType = BusinessType.UPDATE)
    @PostMapping("/pause/{recordId}")
    public AjaxResult pauseAssessment(@PathVariable Long recordId) {
        return toAjax(assessmentRecordService.pauseAssessment(recordId));
    }

    /**
     * 恢复测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:resume')")
    @Log(title = "测评记录管理", businessType = BusinessType.UPDATE)
    @PostMapping("/resume/{recordId}")
    public AjaxResult resumeAssessment(@PathVariable Long recordId) {
        return toAjax(assessmentRecordService.resumeAssessment(recordId));
    }

    /**
     * 取消测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:cancel')")
    @Log(title = "测评记录管理", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{recordId}")
    public AjaxResult cancelAssessment(@PathVariable Long recordId) {
        return toAjax(assessmentRecordService.cancelAssessment(recordId));
    }

    /**
     * 查询测评结果
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:result')")
    @GetMapping("/result/{recordId}")
    public AjaxResult getAssessmentResult(@PathVariable Long recordId) {
        Map<String, Object> result = assessmentRecordService.selectAssessmentResult(recordId);
        return success(result);
    }

    /**
     * 生成测评报告
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:report')")
    @GetMapping("/report/{recordId}")
    public AjaxResult generateReport(@PathVariable Long recordId) {
        Map<String, Object> report = assessmentRecordService.generateAssessmentReport(recordId);
        return success(report);
    }

    /**
     * 根据用户ID查询测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult recordsByUser(@PathVariable Long userId) {
        List<PsyTAssessmentRecord> list = assessmentRecordService.selectRecordsByUserId(userId);
        return success(list);
    }

    /**
     * 根据量表ID查询测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/scale/{scaleId}")
    public AjaxResult recordsByScale(@PathVariable Long scaleId) {
        List<PsyTAssessmentRecord> list = assessmentRecordService.selectRecordsByScaleId(scaleId);
        return success(list);
    }

    /**
     * 查询用户最近的测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/user/{userId}/recent")
    public AjaxResult recentRecords(@PathVariable Long userId, @RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTAssessmentRecord> list = assessmentRecordService.selectRecentRecordsByUserId(userId, limit);
        return success(list);
    }

    /**
     * 查询未完成的测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/user/{userId}/incomplete")
    public AjaxResult incompleteRecords(@PathVariable Long userId) {
        List<PsyTAssessmentRecord> list = assessmentRecordService.selectIncompleteRecordsByUserId(userId);
        return success(list);
    }

    /**
     * 查询已完成的测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/user/{userId}/completed")
    public AjaxResult completedRecords(@PathVariable Long userId) {
        List<PsyTAssessmentRecord> list = assessmentRecordService.selectCompletedRecordsByUserId(userId);
        return success(list);
    }

    /**
     * 查询测评统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/stats")
    public AjaxResult assessmentStats() {
        Map<String, Object> stats = assessmentRecordService.selectAssessmentStats();
        return success(stats);
    }

    /**
     * 查询用户测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/stats/user/{userId}")
    public AjaxResult userStats(@PathVariable Long userId) {
        Map<String, Object> stats = assessmentRecordService.selectUserAssessmentStats(userId);
        return success(stats);
    }

    /**
     * 查询量表测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/stats/scale/{scaleId}")
    public AjaxResult scaleStats(@PathVariable Long scaleId) {
        Map<String, Object> stats = assessmentRecordService.selectScaleAssessmentStats(scaleId);
        return success(stats);
    }

    /**
     * 查询企业测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/stats/enterprise/{enterpriseId}")
    public AjaxResult enterpriseStats(@PathVariable Long enterpriseId) {
        Map<String, Object> stats = assessmentRecordService.selectEnterpriseAssessmentStats(enterpriseId);
        return success(stats);
    }

    /**
     * 查询测评趋势
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/trend")
    public AjaxResult assessmentTrend(@RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> trend = assessmentRecordService.selectAssessmentTrend(days);
        return success(trend);
    }

    /**
     * 查询热门量表排行
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/ranking/hot")
    public AjaxResult hotScaleRanking(@RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> ranking = assessmentRecordService.selectHotScaleRanking(limit);
        return success(ranking);
    }

    /**
     * 查询测评时长分析
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/analysis/duration/{scaleId}")
    public AjaxResult durationAnalysis(@PathVariable Long scaleId) {
        Map<String, Object> analysis = assessmentRecordService.selectDurationAnalysis(scaleId);
        return success(analysis);
    }

    /**
     * 查询测评完成率分析
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/analysis/completion/{scaleId}")
    public AjaxResult completionRateAnalysis(@PathVariable Long scaleId) {
        Map<String, Object> analysis = assessmentRecordService.selectCompletionRateAnalysis(scaleId);
        return success(analysis);
    }

    /**
     * 查询测评分数分布
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:stats')")
    @GetMapping("/analysis/score-distribution/{scaleId}")
    public AjaxResult scoreDistribution(@PathVariable Long scaleId) {
        List<Map<String, Object>> distribution = assessmentRecordService.selectScoreDistribution(scaleId);
        return success(distribution);
    }

    /**
     * 重新计算测评分数
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:recalculate')")
    @Log(title = "测评记录管理", businessType = BusinessType.UPDATE)
    @PostMapping("/recalculate/{recordId}")
    public AjaxResult recalculateScore(@PathVariable Long recordId) {
        return toAjax(assessmentRecordService.recalculateAssessmentScore(recordId));
    }

    /**
     * 查询异常测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:abnormal')")
    @GetMapping("/abnormal")
    public AjaxResult abnormalRecords() {
        List<PsyTAssessmentRecord> list = assessmentRecordService.selectAbnormalRecords();
        return success(list);
    }

    /**
     * 批量更新测评状态
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "测评记录管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestParam Long[] ids, @RequestParam Integer status) {
        return toAjax(assessmentRecordService.batchUpdateAssessmentStatus(ids, status));
    }

    /**
     * 检查用户是否可以测评
     */
    @GetMapping("/check-can-assess")
    public AjaxResult checkCanAssess(@RequestParam Long userId, @RequestParam Long scaleId) {
        Map<String, Object> result = assessmentRecordService.checkUserCanAssess(userId, scaleId);
        return success(result);
    }
}
