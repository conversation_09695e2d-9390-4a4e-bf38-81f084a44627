package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.dto.SearchResultDTO;
import com.xihuan.common.core.domain.entity.PsyHotSearch;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.IPsySearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序搜索Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/search")
public class MiniAppSearchController extends BaseController {

    @Autowired
    private IPsySearchService searchService;

    @Autowired
    private TokenService tokenService;

    /**
     * 全局搜索
     */
    @GetMapping("/global")
    public AjaxResult globalSearch(@RequestParam String keyword,
                                   @RequestParam(defaultValue = "all") String type,
                                   @RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "20") Integer pageSize,
                                   HttpServletRequest request) {
        try {
            // 参数验证
            if (StringUtils.isEmpty(keyword) || keyword.trim().length() < 1) {
                return error("搜索关键词不能为空");
            }

            if (keyword.length() > 100) {
                return error("搜索关键词过长");
            }

            // 获取用户信息
            LoginUser loginUser = null;
            try {
                loginUser = tokenService.getLoginUser(request);
            } catch (Exception e) {
                // 允许未登录用户搜索
            }

            Long userId = loginUser != null ? loginUser.getUserId() : null;
//            String ipAddress = ServletUtils.getClientIP(request);

            // 执行搜索
            SearchResultDTO result = searchService.globalSearch(
                    keyword.trim(), type, pageNum, pageSize, userId, null);

            return success(result);

        } catch (Exception e) {
            logger.error("全局搜索失败", e);
            return error("搜索失败，请稍后重试");
        }
    }

    /**
     * 获取搜索建议
     */
    @GetMapping("/suggestions")
    public AjaxResult getSearchSuggestions(@RequestParam String keyword, HttpServletRequest request) {
        try {
            if (StringUtils.isEmpty(keyword) || keyword.trim().length() < 1) {
                return success(new java.util.ArrayList<>());
            }

            // 获取用户信息
            LoginUser loginUser = null;
            try {
                loginUser = tokenService.getLoginUser(request);
            } catch (Exception e) {
                // 允许未登录用户获取建议
            }

            Long userId = loginUser != null ? loginUser.getUserId() : null;

            List<String> suggestions = searchService.getSearchSuggestions(keyword.trim(), userId);
            return success(suggestions);

        } catch (Exception e) {
            logger.error("获取搜索建议失败", e);
            return error("获取搜索建议失败");
        }
    }

    /**
     * 获取热门搜索
     */
    @GetMapping("/hot")
    public AjaxResult getHotSearches(@RequestParam(defaultValue = "all") String type,
                                     @RequestParam(defaultValue = "10") Integer limit) {
        try {
            if (limit > 50) {
                limit = 50; // 限制最大数量
            }

            List<PsyHotSearch> hotSearches = searchService.getHotSearches(type, limit);
            return success(hotSearches);

        } catch (Exception e) {
            logger.error("获取热门搜索失败", e);
            return error("获取热门搜索失败");
        }
    }

    /**
     * 获取用户搜索历史
     */
    @GetMapping("/history")
    public AjaxResult getSearchHistory(@RequestParam(defaultValue = "10") Integer limit,
                                       HttpServletRequest request) {
        try {
            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }

            List<String> history = searchService.getUserSearchHistory(loginUser.getUserId(), limit);
            return success(history);

        } catch (Exception e) {
            logger.error("获取搜索历史失败", e);
            return error("获取搜索历史失败");
        }
    }

    /**
     * 清除用户搜索历史
     */
    @DeleteMapping("/history")
    public AjaxResult clearSearchHistory(HttpServletRequest request) {
        try {
            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }

            boolean success = searchService.clearUserSearchHistory(loginUser.getUserId());
            return success ? success() : error("清除搜索历史失败");

        } catch (Exception e) {
            logger.error("清除搜索历史失败", e);
            return error("清除搜索历史失败");
        }
    }

    /**
     * 搜索统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getSearchStatistics(@RequestParam(defaultValue = "all") String type) {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 获取热门搜索
            List<PsyHotSearch> hotSearches = searchService.getHotSearches(type, 5);
            statistics.put("hotSearches", hotSearches);

            // 可以添加更多统计信息
            statistics.put("totalHotSearches", hotSearches.size());

            return success(statistics);

        } catch (Exception e) {
            logger.error("获取搜索统计失败", e);
            return error("获取搜索统计失败");
        }
    }

    /**
     * 快速搜索（简化版搜索，只返回标题匹配的结果）
     */
    @GetMapping("/quick")
    public AjaxResult quickSearch(@RequestParam String keyword,
                                  @RequestParam(defaultValue = "all") String type,
                                  @RequestParam(defaultValue = "5") Integer limit,
                                  HttpServletRequest request) {
        try {
            if (StringUtils.isEmpty(keyword) || keyword.trim().length() < 1) {
                return error("搜索关键词不能为空");
            }

            // 获取用户信息
            LoginUser loginUser = null;
            try {
                loginUser = tokenService.getLoginUser(request);
            } catch (Exception e) {
                // 允许未登录用户搜索
            }

            Long userId = loginUser != null ? loginUser.getUserId() : null;
//            String ipAddress = ServletUtils.getClientIP(request);

            // 执行快速搜索（只返回第一页，限制数量）
            SearchResultDTO result = searchService.globalSearch(
                    keyword.trim(), type, 1, limit, userId, null);

            return success(result);

        } catch (Exception e) {
            logger.error("快速搜索失败", e);
            return error("搜索失败，请稍后重试");
        }
    }

    /**
     * 搜索类型列表
     */
    @GetMapping("/types")
    public AjaxResult getSearchTypes() {
        try {
            Map<String, String> types = new HashMap<>();
            types.put("all", "全部");
            types.put("consultant", "咨询师");
            types.put("course", "课程");
            types.put("meditation", "冥想");
            types.put("assessment", "测评");

            return success(types);

        } catch (Exception e) {
            logger.error("获取搜索类型失败", e);
            return error("获取搜索类型失败");
        }
    }

    /**
     * 搜索关键词联想
     */
    @GetMapping("/associate")
    public AjaxResult getAssociateKeywords(@RequestParam String keyword,
                                           @RequestParam(defaultValue = "5") Integer limit) {
        try {
            if (StringUtils.isEmpty(keyword) || keyword.trim().length() < 1) {
                return success(new java.util.ArrayList<>());
            }

            // 这里可以实现更复杂的联想逻辑
            List<String> suggestions = searchService.getSearchSuggestions(keyword.trim(), null);

            // 限制返回数量
            if (suggestions.size() > limit) {
                suggestions = suggestions.subList(0, limit);
            }

            return success(suggestions);

        } catch (Exception e) {
            logger.error("获取关键词联想失败", e);
            return error("获取关键词联想失败");
        }
    }
}
