package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyUserMeditationRecord;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyUserMeditationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户冥想记录表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/meditationRecord")
public class PsyUserMeditationRecordController extends BaseController {
    
    @Autowired
    private IPsyUserMeditationRecordService recordService;

    /**
     * 查询冥想记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyUserMeditationRecord record) {
        startPage();
        List<PsyUserMeditationRecord> list = recordService.selectRecordList(record);
        return getDataTable(list);
    }

    /**
     * 导出冥想记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:export')")
    @Log(title = "冥想记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyUserMeditationRecord record) {
        List<PsyUserMeditationRecord> list = recordService.selectRecordList(record);
        ExcelUtil<PsyUserMeditationRecord> util = new ExcelUtil<PsyUserMeditationRecord>(PsyUserMeditationRecord.class);
        util.exportExcel(response, list, "冥想记录数据");
    }

    /**
     * 获取冥想记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(recordService.selectRecordById(id));
    }

    /**
     * 新增冥想记录
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:add')")
    @Log(title = "冥想记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyUserMeditationRecord record) {
        return toAjax(recordService.insertRecord(record));
    }

    /**
     * 修改冥想记录
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:edit')")
    @Log(title = "冥想记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyUserMeditationRecord record) {
        return toAjax(recordService.updateRecord(record));
    }

    /**
     * 删除冥想记录
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:remove')")
    @Log(title = "冥想记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(recordService.deleteRecordByIds(ids));
    }

    /**
     * 根据用户ID查询冥想记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getRecordsByUser(@PathVariable Long userId) {
        List<PsyUserMeditationRecord> list = recordService.selectRecordsByUserId(userId);
        return success(list);
    }

    /**
     * 根据冥想ID查询用户记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:list')")
    @GetMapping("/meditation/{meditationId}")
    public AjaxResult getRecordsByMeditation(@PathVariable Long meditationId) {
        PsyUserMeditationRecord queryRecord = new PsyUserMeditationRecord();
        queryRecord.setMeditationId(meditationId);
        List<PsyUserMeditationRecord> list = recordService.selectRecordList(queryRecord);
        return success(list);
    }

    /**
     * 获取用户冥想统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:list')")
    @GetMapping("/statistics/user/{userId}")
    public AjaxResult getUserStatistics(@PathVariable Long userId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总冥想时长
        int totalDuration = recordService.sumUserMeditationDuration(userId);
        statistics.put("totalDuration", totalDuration);
        
        // 总冥想次数
        int totalTimes = recordService.countUserMeditationTimes(userId);
        statistics.put("totalTimes", totalTimes);
        
        // 完成次数
        int completedTimes = recordService.countUserCompletedMeditations(userId);
        statistics.put("completedTimes", completedTimes);
        
        // 完成率
        double completionRate = totalTimes > 0 ? (double) completedTimes / totalTimes * 100 : 0;
        statistics.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        // 平均每次时长
        int avgDuration = totalTimes > 0 ? totalDuration / totalTimes : 0;
        statistics.put("avgDuration", avgDuration);
        
        return success(statistics);
    }

    /**
     * 获取冥想使用统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:list')")
    @GetMapping("/statistics/meditation/{meditationId}")
    public AjaxResult getMeditationStatistics(@PathVariable Long meditationId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 查询该冥想的所有记录
        PsyUserMeditationRecord queryRecord = new PsyUserMeditationRecord();
        queryRecord.setMeditationId(meditationId);
        List<PsyUserMeditationRecord> records = recordService.selectRecordList(queryRecord);
        
        // 总播放次数
        statistics.put("totalPlayTimes", records.size());
        
        // 完成次数
        long completedTimes = records.stream().filter(r -> r.getIsCompleted() != null && r.getIsCompleted() == 1).count();
        statistics.put("completedTimes", completedTimes);
        
        // 完成率
        double completionRate = records.size() > 0 ? (double) completedTimes / records.size() * 100 : 0;
        statistics.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        
        // 平均播放时长
        double avgDuration = records.stream()
            .filter(r -> r.getDurationPlayed() != null)
            .mapToInt(PsyUserMeditationRecord::getDurationPlayed)
            .average()
            .orElse(0);
        statistics.put("avgDuration", Math.round(avgDuration));
        
        // 心情改善统计
        List<PsyUserMeditationRecord> moodRecords = records.stream()
            .filter(r -> r.getMoodBefore() != null && r.getMoodAfter() != null)
            .collect(java.util.stream.Collectors.toList());
        
        if (!moodRecords.isEmpty()) {
            double avgMoodBefore = moodRecords.stream().mapToInt(PsyUserMeditationRecord::getMoodBefore).average().orElse(0);
            double avgMoodAfter = moodRecords.stream().mapToInt(PsyUserMeditationRecord::getMoodAfter).average().orElse(0);
            double avgMoodImprovement = avgMoodAfter - avgMoodBefore;
            
            statistics.put("avgMoodBefore", Math.round(avgMoodBefore * 100.0) / 100.0);
            statistics.put("avgMoodAfter", Math.round(avgMoodAfter * 100.0) / 100.0);
            statistics.put("avgMoodImprovement", Math.round(avgMoodImprovement * 100.0) / 100.0);
            
            // 心情改善次数
            long improvementTimes = moodRecords.stream()
                .filter(r -> r.getMoodAfter() > r.getMoodBefore())
                .count();
            statistics.put("improvementTimes", improvementTimes);
            
            // 心情改善率
            double improvementRate = moodRecords.size() > 0 ? (double) improvementTimes / moodRecords.size() * 100 : 0;
            statistics.put("improvementRate", Math.round(improvementRate * 100.0) / 100.0);
        }
        
        return success(statistics);
    }

    /**
     * 获取用户最新冥想记录
     */
    @PreAuthorize("@ss.hasPermi('system:meditationRecord:query')")
    @GetMapping("/latest/{userId}/{meditationId}")
    public AjaxResult getLatestRecord(@PathVariable Long userId, @PathVariable Long meditationId) {
        PsyUserMeditationRecord record = recordService.selectLatestRecordByUserAndMeditation(userId, meditationId);
        return success(record);
    }
}
