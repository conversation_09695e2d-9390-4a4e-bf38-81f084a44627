package com.xihuan.web.controller.miniapp;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyCourseOrder;
import com.xihuan.common.core.domain.entity.PsyMeditationOrder;
import com.xihuan.common.core.domain.entity.SysUser;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.domain.dto.WxPayDTO;
import com.xihuan.system.service.IOrderPaymentService;
import com.xihuan.system.service.IPsyCourseOrderService;
import com.xihuan.system.service.IPsyMeditationOrderService;
import com.xihuan.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 小程序订单Controller
 */
@RestController
@RequestMapping("/miniapp/order")
public class MiniAppOrderController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(MiniAppOrderController.class);

    @Autowired
    private IOrderPaymentService orderPaymentService;

    @Autowired
    private IPsyCourseOrderService courseOrderService;

    @Autowired
    private IPsyMeditationOrderService meditationOrderService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysUserService userService;

    /**
     * 创建课程订单
     */
    @PostMapping("/course/create")
    @Log(title = "创建课程订单", businessType = BusinessType.INSERT)
    public AjaxResult createCourseOrder(@RequestBody CreateOrderRequest request, HttpServletRequest httpRequest) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 创建课程订单
            PsyCourseOrder order = new PsyCourseOrder();
            order.setUserId(loginUser.getUserId());
            order.setCourseId(request.getProductId());
            order.setPaymentAmount(request.getPaymentAmount());
            order.setOriginalPrice(request.getOriginalPrice());
            order.setCouponId(request.getCouponId());
            order.setCouponDiscount(request.getCouponDiscount());
            order.setPointsUsed(request.getPointsUsed());
            order.setPointsDiscount(request.getPointsDiscount());

            // 生成订单号
            String orderNo = orderPaymentService.generateOrderNo("course");
            order.setOrderNo(orderNo);

            // 保存订单
            courseOrderService.insertOrder(order);

            return AjaxResult.success("创建课程订单成功", order);

        } catch (Exception e) {
            log.error("创建课程订单失败", e);
            return AjaxResult.error("创建课程订单失败: " + e.getMessage());
        }
    }

    /**
     * 创建冥想订单
     */
    @PostMapping("/meditation/create")
    @Log(title = "创建冥想订单", businessType = BusinessType.INSERT)
    public AjaxResult createMeditationOrder(@RequestBody CreateOrderRequest request, HttpServletRequest httpRequest) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 创建冥想订单
            PsyMeditationOrder order = new PsyMeditationOrder();
            order.setUserId(loginUser.getUserId());
            order.setMeditationId(request.getProductId());
            order.setPaymentAmount(request.getPaymentAmount());
            order.setOriginalPrice(request.getOriginalPrice());
            order.setCouponId(request.getCouponId());
            order.setCouponDiscount(request.getCouponDiscount());
            order.setPointsUsed(request.getPointsUsed());
            order.setPointsDiscount(request.getPointsDiscount());

            // 生成订单号
            String orderNo = orderPaymentService.generateOrderNo("meditation");
            order.setOrderNo(orderNo);

            // 保存订单
            meditationOrderService.insertOrder(order);

            return AjaxResult.success("创建冥想订单成功", order);

        } catch (Exception e) {
            log.error("创建冥想订单失败", e);
            return AjaxResult.error("创建冥想订单失败: " + e.getMessage());
        }
    }

    /**
     * 支付订单
     */
    @PostMapping("/pay")
    @Log(title = "支付订单", businessType = BusinessType.UPDATE)
    public AjaxResult payOrder(@RequestBody PayOrderRequest request, HttpServletRequest httpRequest) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 解析订单类型
            String orderType = orderPaymentService.parseOrderType(request.getOrderNo());
            if (StringUtils.isEmpty(orderType)) {
                return AjaxResult.error("无效的订单号");
            }
            // 获取用户id
            Long userId = loginUser.getUserId();
            SysUser sysUser = userService.selectUserById(userId);
            // 获取用户openid
            String openid = String.valueOf(sysUser.getWxOpenId());
            if (StringUtils.isEmpty(openid)) {
                return AjaxResult.error("用户openid不存在，请重新登录");
            }

            // 获取客户端IP
//            String clientIp = ServletUtils.getClientIP(httpRequest);

            // 创建支付订单
            WxPayDTO.PayOrderResponse response = orderPaymentService.createPayOrder(
                    orderType, request.getOrderNo(), openid, null);

            return AjaxResult.success("创建支付订单成功", response);

        } catch (Exception e) {
            log.error("支付订单失败", e);
            return AjaxResult.error("支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询订单支付状态
     */
    @GetMapping("/pay/status/{orderNo}")
    public AjaxResult queryPayStatus(@PathVariable String orderNo, HttpServletRequest httpRequest) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 解析订单类型
            String orderType = orderPaymentService.parseOrderType(orderNo);
            if (StringUtils.isEmpty(orderType)) {
                return AjaxResult.error("无效的订单号");
            }

            // 查询支付状态
            WxPayDTO.QueryOrderResponse response = orderPaymentService.queryPayOrder(orderType, orderNo);

            return AjaxResult.success("查询支付状态成功", response);

        } catch (Exception e) {
            log.error("查询订单支付状态失败", e);
            return AjaxResult.error("查询支付状态失败: " + e.getMessage());
        }
    }

    /**
     * 申请退款
     */
    @PostMapping("/refund")
    @Log(title = "申请退款", businessType = BusinessType.UPDATE)
    public AjaxResult refundOrder(@RequestBody RefundOrderRequest request, HttpServletRequest httpRequest) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 解析订单类型
            String orderType = orderPaymentService.parseOrderType(request.getOrderNo());
            if (StringUtils.isEmpty(orderType)) {
                return AjaxResult.error("无效的订单号");
            }

            // TODO: 验证用户是否有权限退款该订单

            // 申请退款
            WxPayDTO.RefundResponse response = orderPaymentService.refundOrder(
                    orderType, request.getOrderNo(), request.getRefundAmount(), request.getReason());

            return AjaxResult.success("申请退款成功", response);

        } catch (Exception e) {
            log.error("申请退款失败", e);
            return AjaxResult.error("申请退款失败: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel/{orderNo}")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE)
    public AjaxResult cancelOrder(@PathVariable String orderNo, HttpServletRequest httpRequest) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 解析订单类型
            String orderType = orderPaymentService.parseOrderType(orderNo);
            if (StringUtils.isEmpty(orderType)) {
                return AjaxResult.error("无效的订单号");
            }

            // TODO: 验证用户是否有权限取消该订单

            // 关闭订单
            boolean result = orderPaymentService.closeOrder(orderType, orderNo);

            if (result) {
                return AjaxResult.success("取消订单成功");
            } else {
                return AjaxResult.error("取消订单失败");
            }

        } catch (Exception e) {
            log.error("取消订单失败", e);
            return AjaxResult.error("取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 创建订单请求
     */
    public static class CreateOrderRequest {
        @NotNull(message = "产品ID不能为空")
        private Long productId;

        @NotNull(message = "支付金额不能为空")
        @Positive(message = "支付金额必须大于0")
        private BigDecimal paymentAmount;

        @NotNull(message = "原价不能为空")
        @Positive(message = "原价必须大于0")
        private BigDecimal originalPrice;

        private Long couponId;
        private BigDecimal couponDiscount;
        private Integer pointsUsed;
        private BigDecimal pointsDiscount;

        // getters and setters
        public Long getProductId() { return productId; }
        public void setProductId(Long productId) { this.productId = productId; }
        public BigDecimal getPaymentAmount() { return paymentAmount; }
        public void setPaymentAmount(BigDecimal paymentAmount) { this.paymentAmount = paymentAmount; }
        public BigDecimal getOriginalPrice() { return originalPrice; }
        public void setOriginalPrice(BigDecimal originalPrice) { this.originalPrice = originalPrice; }
        public Long getCouponId() { return couponId; }
        public void setCouponId(Long couponId) { this.couponId = couponId; }
        public BigDecimal getCouponDiscount() { return couponDiscount; }
        public void setCouponDiscount(BigDecimal couponDiscount) { this.couponDiscount = couponDiscount; }
        public Integer getPointsUsed() { return pointsUsed; }
        public void setPointsUsed(Integer pointsUsed) { this.pointsUsed = pointsUsed; }
        public BigDecimal getPointsDiscount() { return pointsDiscount; }
        public void setPointsDiscount(BigDecimal pointsDiscount) { this.pointsDiscount = pointsDiscount; }
    }

    /**
     * 支付订单请求
     */
    public static class PayOrderRequest {
        @NotBlank(message = "订单号不能为空")
        private String orderNo;

        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
    }

    /**
     * 退款订单请求
     */
    public static class RefundOrderRequest {
        @NotBlank(message = "订单号不能为空")
        private String orderNo;

        @NotNull(message = "退款金额不能为空")
        @Positive(message = "退款金额必须大于0")
        private BigDecimal refundAmount;

        @NotBlank(message = "退款原因不能为空")
        private String reason;

        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
        public BigDecimal getRefundAmount() { return refundAmount; }
        public void setRefundAmount(BigDecimal refundAmount) { this.refundAmount = refundAmount; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
