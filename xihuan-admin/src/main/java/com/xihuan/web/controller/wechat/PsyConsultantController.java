package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.wxService.PsyCategoryService.PsyConsultantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 心理咨询师管理接口
 */
@RestController
@RequestMapping("/system/consultant")
public class PsyConsultantController extends BaseController {
    
    @Autowired
    private PsyConsultantService consultantService;

    /**
     * 获取咨询师列表（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:list')")
    @GetMapping("/admin/list")
    public TableDataInfo adminList(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String workStatus,
            @RequestParam(required = false) String auditStatus,
            @RequestParam(required = false) Integer minPrice,
            @RequestParam(required = false) Integer maxPrice) {
        startPage();
        List<PsyConsultant> list = consultantService.listConsultantsByPage(name, workStatus, auditStatus, minPrice, maxPrice);
        return getDataTable(list);
    }

    /**
     * 获取咨询师详细信息（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:query')")
    @GetMapping(value = "/admin/{id}")
    public AjaxResult getAdminInfo(@PathVariable("id") Long id) {
        return success(consultantService.getConsultantWithUserById(id));
    }

    /**
     * 导出咨询师列表（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:export')")
    @Log(title = "咨询师管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyConsultant consultant) {
        List<PsyConsultant> list = consultantService.listConsultantsByPage(
                consultant.getName(),
                consultant.getWorkStatus(),
                consultant.getAuditStatus(),
                consultant.getMinFee(),
                consultant.getMaxFee());
        ExcelUtil<PsyConsultant> util = new ExcelUtil<>(PsyConsultant.class);
        util.exportExcel(response, list, "咨询师数据");
    }

    /**
     * 新增咨询师（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:add')")
    @Log(title = "咨询师管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody PsyConsultant consultant) {
        return toAjax(consultantService.insertConsultant(consultant));
    }

    /**
     * 修改咨询师信息（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "咨询师管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody PsyConsultant consultant) {
        return toAjax(consultantService.updateConsultant(consultant));
    }

    /**
     * 删除咨询师（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:remove')")
    @Log(title = "咨询师管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(consultantService.deleteConsultants(ids));
    }

    /**
     * 更新咨询师工作状态（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "咨询师管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/workStatus/{status}")
    public AjaxResult updateWorkStatus(@PathVariable("id") Long id, @PathVariable("status") String status) {
        return toAjax(consultantService.updateConsultantWorkStatus(id, status));
    }

    /**
     * 更新咨询师审核状态（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:audit')")
    @Log(title = "咨询师管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/auditStatus/{status}")
    public AjaxResult updateAuditStatus(@PathVariable("id") Long id, @PathVariable("status") String status) {
        return toAjax(consultantService.updateConsultantAuditStatus(id, status));
    }

    /**
     * 分步保存 - 基本信息（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "咨询师管理", businessType = BusinessType.UPDATE)
    @PutMapping("/basic")
    public AjaxResult saveBasicInfo(@Validated @RequestBody PsyConsultant consultant) {
        return toAjax(consultantService.saveBasicInfo(consultant));
    }

    /**
     * 分步保存 - 服务信息（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "咨询师管理", businessType = BusinessType.UPDATE)
    @PutMapping("/service")
    public AjaxResult saveServiceInfo(@Validated @RequestBody PsyConsultant consultant) {
        return toAjax(consultantService.saveServiceInfo(consultant));
    }

    /**
     * 分步保存 - 资质信息（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "咨询师管理", businessType = BusinessType.UPDATE)
    @PutMapping("/qualification")
    public AjaxResult saveQualificationInfo(@Validated @RequestBody PsyConsultant consultant) {
        return toAjax(consultantService.saveQualificationInfo(consultant));
    }

    /**
     * 分步保存 - 教育培训信息（管理员接口）
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "咨询师管理", businessType = BusinessType.UPDATE)
    @PutMapping("/education")
    public AjaxResult saveEducationInfo(@Validated @RequestBody PsyConsultant consultant) {
        return toAjax(consultantService.saveEducationAndTraining(consultant));
    }

    /**
     * 获取可用的咨询师列表（小程序接口）
     */
    @GetMapping("/list")
    public AjaxResult list() {
        List<PsyConsultant> list = consultantService.listAllAvailableConsultants();
        return success(list);
    }

    /**
     * 按条件搜索咨询师（小程序接口）
     */
    @GetMapping("/search")
    public AjaxResult search(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String field) {
        List<PsyConsultant> list = consultantService.listConsultants(name, field);
        return success(list);
    }

    /**
     * 获取咨询师详情（小程序接口）
     */
    @GetMapping("/detail/{id}")
    public AjaxResult getDetail(@PathVariable("id") Long id) {
        return success(consultantService.getConsultantFullDetails(id));
    }

    /**
     * 获取所有咨询师简单信息（仅id和name,头像，无分页，适合下拉框等场景）
     */
    @GetMapping("/allSimpleList")
    public AjaxResult allSimpleList() {
        return success(consultantService.listAllSimple());
    }
} 