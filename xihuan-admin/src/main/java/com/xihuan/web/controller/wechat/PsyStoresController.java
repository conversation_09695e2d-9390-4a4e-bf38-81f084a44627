package com.xihuan.web.controller.wechat;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.store.PsyStoreBusinessDays;
import com.xihuan.common.core.domain.entity.store.PsyStoreBusinessHours;
import com.xihuan.common.core.domain.entity.store.PsyStoreContacts;
import com.xihuan.common.core.domain.entity.store.PsyStores;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.system.service.wxService.IPsyStoresService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 心理咨询门店管理Controller
 */
@RestController
@RequestMapping("/wechat/store")
public class PsyStoresController extends BaseController {
    @Autowired
    private IPsyStoresService psyStoresService;

    /**
     * 获取门店列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PsyStores psyStores) {
        startPage();
        List<PsyStores> list = psyStoresService.selectPsyStoresList(psyStores);
        return getDataTable(list);
    }

    /**
     * 获取门店详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        Map<String, Object> data = new HashMap<>();
        // 获取门店基本信息
        PsyStores store = psyStoresService.selectPsyStoresById(id);
        data.put("store", store);

        // 获取门店联系方式
        List<PsyStoreContacts> contacts = psyStoresService.getStoreContacts(id);
        data.put("contacts", contacts);

        // 获取门店营业日配置
        PsyStoreBusinessDays businessDays = psyStoresService.getStoreBusinessDays(id);
        data.put("businessDays", businessDays);

        // 获取门店营业时间段
        List<PsyStoreBusinessHours> businessHours = psyStoresService.getStoreBusinessHours(id);
        data.put("businessHours", businessHours);

        return AjaxResult.success(data);
    }

    /**
     * 新增门店
     */
    @PostMapping
    public AjaxResult add(@RequestBody StoreRequest request) {
        return toAjax(psyStoresService.insertPsyStores(request.getStore(), request.getContacts(),
                request.getBusinessDays(), request.getBusinessHours()));
    }

    /**
     * 修改门店
     */
    @PutMapping
    public AjaxResult edit(@RequestBody StoreRequest request) {
        return toAjax(psyStoresService.updatePsyStores(request.getStore(), request.getContacts(),
                request.getBusinessDays(), request.getBusinessHours()));
    }

    /**
     * 删除门店
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(psyStoresService.deletePsyStoresByIds(ids));
    }

    /**
     * 更新门店状态
     */
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody StatusRequest request) {
        return toAjax(psyStoresService.updatePsyStoresStatus(request.getId(), request.getStatus()));
    }

    /**
     * 门店请求对象
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    static class StoreRequest {
        /** 门店基本信息 */
        private PsyStores store;

        /** 联系方式列表 */
        private List<PsyStoreContacts> contacts;

        /** 营业日配置 */
        private PsyStoreBusinessDays businessDays;

        /** 营业时间段列表 */
        private List<PsyStoreBusinessHours> businessHours;
    }

    @Data
    static class StatusRequest {
        private Long id;
        private String status;
    }
}

