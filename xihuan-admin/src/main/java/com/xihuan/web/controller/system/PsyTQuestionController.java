package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTQuestion;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 题目管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/question")
public class PsyTQuestionController extends BaseController {
    
    @Autowired
    private IPsyTQuestionService questionService;

    /**
     * 查询题目列表
     */
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTQuestion question) {
        startPage();
        List<PsyTQuestion> list = questionService.selectQuestionList(question);
        return getDataTable(list);
    }

    /**
     * 导出题目列表
     */
    @PreAuthorize("@ss.hasPermi('system:question:export')")
    @Log(title = "题目管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTQuestion question) {
        List<PsyTQuestion> list = questionService.selectQuestionList(question);
        ExcelUtil<PsyTQuestion> util = new ExcelUtil<PsyTQuestion>(PsyTQuestion.class);
        util.exportExcel(response, list, "题目数据");
    }

    /**
     * 获取题目详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:question:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(questionService.selectQuestionById(id));
    }

    /**
     * 获取题目详情（包含选项信息）
     */
    @PreAuthorize("@ss.hasPermi('system:question:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(questionService.selectQuestionWithOptions(id));
    }

    /**
     * 新增题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:add')")
    @Log(title = "题目管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PsyTQuestion question) {
        return toAjax(questionService.insertQuestion(question));
    }

    /**
     * 修改题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:edit')")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PsyTQuestion question) {
        return toAjax(questionService.updateQuestion(question));
    }

    /**
     * 删除题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:remove')")
    @Log(title = "题目管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(questionService.deleteQuestionByIds(ids));
    }

    /**
     * 根据量表ID查询题目列表
     */
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/scale/{scaleId}")
    public AjaxResult questionsByScale(@PathVariable Long scaleId) {
        List<PsyTQuestion> list = questionService.selectQuestionsByScaleId(scaleId);
        return success(list);
    }

    /**
     * 根据量表ID查询题目列表（包含选项）
     */
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/scale/{scaleId}/with-options")
    public AjaxResult questionsWithOptionsByScale(@PathVariable Long scaleId) {
        List<PsyTQuestion> list = questionService.selectQuestionsWithOptionsByScaleId(scaleId);
        return success(list);
    }

    /**
     * 根据题目序号查询题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:query')")
    @GetMapping("/scale/{scaleId}/no/{questionNo}")
    public AjaxResult questionByNo(@PathVariable Long scaleId, @PathVariable Integer questionNo) {
        PsyTQuestion question = questionService.selectQuestionByNo(scaleId, questionNo);
        return success(question);
    }

    /**
     * 查询题目统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:question:stats')")
    @GetMapping("/stats/{scaleId}")
    public AjaxResult questionStats(@PathVariable Long scaleId) {
        Map<String, Object> stats = questionService.selectQuestionStats(scaleId);
        return success(stats);
    }

    /**
     * 复制题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:copy')")
    @Log(title = "题目管理", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public AjaxResult copyQuestions(@RequestParam Long sourceScaleId, @RequestParam Long targetScaleId) {
        return toAjax(questionService.copyQuestions(sourceScaleId, targetScaleId));
    }

    /**
     * 验证题目配置
     */
    @PreAuthorize("@ss.hasPermi('system:question:validate')")
    @GetMapping("/validate/{scaleId}")
    public AjaxResult validateQuestions(@PathVariable Long scaleId) {
        Map<String, Object> result = questionService.validateQuestionConfig(scaleId);
        return success(result);
    }

    /**
     * 自动生成题目序号
     */
    @PreAuthorize("@ss.hasPermi('system:question:edit')")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PostMapping("/generate-numbers/{scaleId}")
    public AjaxResult generateQuestionNumbers(@PathVariable Long scaleId) {
        int count = questionService.generateQuestionNumbers(scaleId);
        return success("成功生成 " + count + " 个题目序号");
    }

    /**
     * 重新排序题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:edit')")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PostMapping("/reorder/{scaleId}")
    public AjaxResult reorderQuestions(@PathVariable Long scaleId) {
        int count = questionService.reorderQuestions(scaleId);
        return success("成功重排序 " + count + " 个题目");
    }

    /**
     * 更新题目显示顺序
     */
    @PreAuthorize("@ss.hasPermi('system:question:edit')")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping("/order")
    public AjaxResult updateOrder(@RequestParam Long id, @RequestParam Integer orderNum) {
        return toAjax(questionService.updateQuestionOrder(id, orderNum));
    }

    /**
     * 批量更新题目状态
     */
    @PreAuthorize("@ss.hasPermi('system:question:edit')")
    @Log(title = "题目管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestParam Long[] ids, @RequestParam Integer status) {
        return toAjax(questionService.batchUpdateQuestionStatus(ids, status));
    }

    /**
     * 根据分量表查询题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/subscale/{subscaleId}")
    public AjaxResult questionsBySubscale(@PathVariable Long subscaleId) {
        List<PsyTQuestion> list = questionService.selectQuestionsBySubscaleId(subscaleId);
        return success(list);
    }

    /**
     * 查询必答题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/required/{scaleId}")
    public AjaxResult requiredQuestions(@PathVariable Long scaleId) {
        List<PsyTQuestion> list = questionService.selectRequiredQuestions(scaleId);
        return success(list);
    }

    /**
     * 查询选答题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/optional/{scaleId}")
    public AjaxResult optionalQuestions(@PathVariable Long scaleId) {
        List<PsyTQuestion> list = questionService.selectOptionalQuestions(scaleId);
        return success(list);
    }

    /**
     * 随机获取题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/random/{scaleId}")
    public AjaxResult randomQuestions(@PathVariable Long scaleId, @RequestParam Integer count) {
        List<PsyTQuestion> list = questionService.selectRandomQuestions(scaleId, count);
        return success(list);
    }

    /**
     * 导入题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:import')")
    @Log(title = "题目管理", businessType = BusinessType.IMPORT)
    @PostMapping("/import/{scaleId}")
    public AjaxResult importQuestions(@PathVariable Long scaleId, MultipartFile file) throws Exception {
        ExcelUtil<PsyTQuestion> util = new ExcelUtil<PsyTQuestion>(PsyTQuestion.class);
        List<PsyTQuestion> questionList = util.importExcel(file.getInputStream());
        Map<String, Object> result = questionService.importQuestions(scaleId, questionList);
        return success(result);
    }

    /**
     * 导出题目
     */
    @PreAuthorize("@ss.hasPermi('system:question:export')")
    @Log(title = "题目管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export/{scaleId}")
    public void exportQuestions(HttpServletResponse response, @PathVariable Long scaleId) {
        List<PsyTQuestion> list = questionService.exportQuestions(scaleId);
        ExcelUtil<PsyTQuestion> util = new ExcelUtil<PsyTQuestion>(PsyTQuestion.class);
        util.exportExcel(response, list, "题目数据");
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PsyTQuestion> util = new ExcelUtil<PsyTQuestion>(PsyTQuestion.class);
        util.importTemplateExcel(response, "题目数据");
    }
}
