package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.migration.PsyDataMigrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 心理咨询数据迁移Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/migration")
public class PsyDataMigrationController extends BaseController {
    
    @Autowired
    private PsyDataMigrationService migrationService;

    /**
     * 执行完整的数据迁移
     */
    @PreAuthorize("@ss.hasPermi('system:migration:execute')")
    @Log(title = "数据迁移", businessType = BusinessType.OTHER)
    @PostMapping("/execute")
    public AjaxResult executeMigration() {
        try {
            PsyDataMigrationService.MigrationResult result = migrationService.executeFullMigration();
            
            if (result.isSuccess()) {
                return AjaxResult.success("数据迁移成功", result);
            } else {
                return AjaxResult.error("数据迁移失败：" + result.getMessage());
            }
        } catch (Exception e) {
            logger.error("数据迁移执行失败", e);
            return AjaxResult.error("数据迁移执行失败：" + e.getMessage());
        }
    }

    /**
     * 获取迁移状态信息
     */
    @PreAuthorize("@ss.hasPermi('system:migration:query')")
    @GetMapping("/status")
    public AjaxResult getMigrationStatus() {
        // 这里可以实现检查迁移状态的逻辑
        return AjaxResult.success("迁移状态检查功能待实现");
    }
}
