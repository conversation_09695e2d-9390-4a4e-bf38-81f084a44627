package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsyTimeCounselorScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 咨询师排班管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/counselor/schedule")
public class PsyTimeCounselorScheduleController extends BaseController {
    
    @Autowired
    private IPsyTimeCounselorScheduleService scheduleService;

    /**
     * 查询咨询师排班列表
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTimeCounselorSchedule schedule) {
        startPage();
        List<PsyTimeCounselorSchedule> list = scheduleService.selectScheduleList(schedule);
        return getDataTable(list);
    }

    /**
     * 获取咨询师排班详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(scheduleService.selectScheduleById(id));
    }

    /**
     * 根据咨询师和日期查询排班
     */
    @GetMapping("/counselor/{counselorId}")
    public AjaxResult getByCounselorAndDate(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate scheduleDate) {
        PsyTimeCounselorSchedule schedule = scheduleService.selectScheduleByCounselorAndDate(counselorId, scheduleDate);
        return success(schedule);
    }

    /**
     * 查询咨询师在日期范围内的排班
     */
    @GetMapping("/counselor/{counselorId}/range")
    public AjaxResult getByDateRange(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<PsyTimeCounselorSchedule> list = scheduleService.selectSchedulesByDateRange(counselorId, startDate, endDate);
        return success(list);
    }

    /**
     * 查询指定日期的所有咨询师排班
     */
    @GetMapping("/date/{scheduleDate}")
    public AjaxResult getByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate scheduleDate,
            @RequestParam(required = false) Long centerId) {
        List<PsyTimeCounselorSchedule> list = scheduleService.selectSchedulesByDate(scheduleDate, centerId);
        return success(list);
    }

    /**
     * 新增咨询师排班
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:add')")
    @Log(title = "咨询师排班", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTimeCounselorSchedule schedule) {
        schedule.setCreateBy(getUsername());
        return toAjax(scheduleService.insertSchedule(schedule));
    }

    /**
     * 批量新增咨询师排班
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:add')")
    @Log(title = "咨询师排班", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<PsyTimeCounselorSchedule> schedules) {
        String username = getUsername();
        schedules.forEach(schedule -> schedule.setCreateBy(username));
        int count = scheduleService.batchInsertSchedules(schedules);
        return AjaxResult.success("成功添加 " + count + " 条排班记录");
    }

    /**
     * 修改咨询师排班
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:edit')")
    @Log(title = "咨询师排班", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTimeCounselorSchedule schedule) {
        schedule.setUpdateBy(getUsername());
        return toAjax(scheduleService.updateSchedule(schedule));
    }

    /**
     * 删除咨询师排班
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:remove')")
    @Log(title = "咨询师排班", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(scheduleService.deleteScheduleByIds(ids));
    }

    /**
     * 删除指定日期范围的排班
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:remove')")
    @Log(title = "咨询师排班", businessType = BusinessType.DELETE)
    @DeleteMapping("/counselor/{counselorId}/range")
    public AjaxResult removeByDateRange(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        int count = scheduleService.deleteSchedulesByDateRange(counselorId, startDate, endDate);
        return AjaxResult.success("成功删除 " + count + " 条排班记录");
    }

    /**
     * 为咨询师生成默认排班（工作日 9:00-18:00）
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:add')")
    @Log(title = "咨询师排班", businessType = BusinessType.INSERT)
    @PostMapping("/generate/{counselorId}")
    public AjaxResult generateDefaultSchedule(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false, defaultValue = "1") Long centerId) {
        
        int count = scheduleService.generateDefaultSchedule(counselorId, startDate, endDate, centerId);
        return AjaxResult.success("成功生成 " + count + " 条排班记录");
    }

    /**
     * 为所有咨询师生成默认排班
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:add')")
    @Log(title = "咨询师排班", businessType = BusinessType.INSERT)
    @PostMapping("/generateAll")
    public AjaxResult generateAllSchedule(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false, defaultValue = "1") Long centerId) {
        
        int count = scheduleService.generateAllCounselorSchedule(startDate, endDate, centerId);
        return AjaxResult.success("成功生成 " + count + " 条排班记录");
    }

    /**
     * 检查排班是否存在
     */
    @GetMapping("/check")
    public AjaxResult checkScheduleExists(
            @RequestParam Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate scheduleDate) {
        boolean exists = scheduleService.checkScheduleExists(counselorId, scheduleDate);
        return success(exists);
    }

    /**
     * 清理重复的排班记录
     */
    @PreAuthorize("@ss.hasPermi('system:schedule:remove')")
    @Log(title = "咨询师排班", businessType = BusinessType.DELETE)
    @PostMapping("/cleanup")
    public AjaxResult cleanupDuplicates() {
        int count = scheduleService.cleanupDuplicateSchedules();
        return AjaxResult.success("成功清理 " + count + " 条重复排班记录");
    }
}
