package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantInterruption;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyConsultantInterruptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 咨询中断记录表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/consultantInterruption")
public class PsyConsultantInterruptionController extends BaseController {
    
    @Autowired
    private IPsyConsultantInterruptionService interruptionService;

    /**
     * 查询中断记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyConsultantInterruption interruption) {
        startPage();
        List<PsyConsultantInterruption> list = interruptionService.selectInterruptionList(interruption);
        return getDataTable(list);
    }

    /**
     * 导出中断记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:export')")
    @Log(title = "咨询中断记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyConsultantInterruption interruption) {
        List<PsyConsultantInterruption> list = interruptionService.selectInterruptionList(interruption);
        ExcelUtil<PsyConsultantInterruption> util = new ExcelUtil<PsyConsultantInterruption>(PsyConsultantInterruption.class);
        util.exportExcel(response, list, "咨询中断记录数据");
    }

    /**
     * 获取中断记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(interruptionService.selectInterruptionById(id));
    }

    /**
     * 新增中断记录
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:add')")
    @Log(title = "咨询中断记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyConsultantInterruption interruption) {
        return toAjax(interruptionService.insertInterruption(interruption));
    }

    /**
     * 修改中断记录
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:edit')")
    @Log(title = "咨询中断记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyConsultantInterruption interruption) {
        return toAjax(interruptionService.updateInterruption(interruption));
    }

    /**
     * 删除中断记录（支持单个删除和批量删除）
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:remove')")
    @Log(title = "咨询中断记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String ids) {
        // 解析ID字符串，支持单个ID或多个ID（用逗号分隔）
        String[] idArray = ids.split(",");
        Long[] idLongs = new Long[idArray.length];

        try {
            for (int i = 0; i < idArray.length; i++) {
                idLongs[i] = Long.parseLong(idArray[i].trim());
            }
        } catch (NumberFormatException e) {
            return error("无效的中断记录ID格式");
        }

        // 如果只有一个ID，调用单个删除方法
        if (idLongs.length == 1) {
            return toAjax(interruptionService.deleteInterruptionById(idLongs[0]));
        } else {
            // 多个ID，调用批量删除方法
            return toAjax(interruptionService.deleteInterruptionByIds(idLongs));
        }
    }

    /**
     * 根据咨询记录ID查询中断记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:list')")
    @GetMapping("/record/{recordId}")
    public AjaxResult getInterruptionsByRecord(@PathVariable Long recordId) {
        List<PsyConsultantInterruption> list = interruptionService.selectInterruptionsByRecordId(recordId);
        return success(list);
    }

    /**
     * 统计咨询记录的中断次数
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:list')")
    @GetMapping("/count/{recordId}")
    public AjaxResult countInterruptions(@PathVariable Long recordId) {
        int count = interruptionService.countInterruptionsByRecordId(recordId);
        return success(count);
    }

    /**
     * 统计咨询记录的总中断时长
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:list')")
    @GetMapping("/duration/{recordId}")
    public AjaxResult sumInterruptionDuration(@PathVariable Long recordId) {
        int duration = interruptionService.sumInterruptionDuration(recordId);
        return success(duration);
    }

    /**
     * 根据中断类型统计
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:list')")
    @GetMapping("/countByType/{recordId}")
    public AjaxResult countInterruptionsByType(@PathVariable Long recordId, @RequestParam String interruptType) {
        int count = interruptionService.countInterruptionsByType(recordId, interruptType);
        return success(count);
    }

    /**
     * 获取咨询记录的中断统计
     */
    @PreAuthorize("@ss.hasPermi('system:consultantInterruption:list')")
    @GetMapping("/statistics/{recordId}")
    public AjaxResult getInterruptionStats(@PathVariable Long recordId) {
        return success(interruptionService.getInterruptionStats(recordId));
    }
}
