package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyMeditation;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyMeditationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 冥想主表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/meditation")
public class PsyMeditationController extends BaseController {
    
    @Autowired
    private IPsyMeditationService meditationService;

    /**
     * 查询冥想列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyMeditation meditation) {
        startPage();
        List<PsyMeditation> list = meditationService.selectMeditationList(meditation);
        return getDataTable(list);
    }

    /**
     * 导出冥想列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:export')")
    @Log(title = "冥想", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyMeditation meditation) {
        List<PsyMeditation> list = meditationService.selectMeditationList(meditation);
        ExcelUtil<PsyMeditation> util = new ExcelUtil<PsyMeditation>(PsyMeditation.class);
        util.exportExcel(response, list, "冥想数据");
    }

    /**
     * 获取冥想详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(meditationService.selectMeditationById(id));
    }

    /**
     * 获取冥想详细信息（包含分类等）
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(meditationService.selectMeditationWithDetails(id));
    }

    /**
     * 新增冥想
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:add')")
    @Log(title = "冥想", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyMeditation meditation) {
        return toAjax(meditationService.insertMeditation(meditation));
    }

    /**
     * 修改冥想
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:edit')")
    @Log(title = "冥想", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyMeditation meditation) {
        return toAjax(meditationService.updateMeditation(meditation));
    }

    /**
     * 删除冥想
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:remove')")
    @Log(title = "冥想", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(meditationService.deleteMeditationByIds(ids));
    }

    /**
     * 发布冥想
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:edit')")
    @Log(title = "冥想发布", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{id}")
    public AjaxResult publish(@PathVariable Long id) {
        return toAjax(meditationService.publishMeditation(id));
    }

    /**
     * 下架冥想
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:edit')")
    @Log(title = "冥想下架", businessType = BusinessType.UPDATE)
    @PutMapping("/unpublish/{id}")
    public AjaxResult unpublish(@PathVariable Long id) {
        return toAjax(meditationService.unpublishMeditation(id));
    }

    /**
     * 更新冥想评分信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:edit')")
    @Log(title = "更新冥想评分", businessType = BusinessType.UPDATE)
    @PutMapping("/updateRating/{id}")
    public AjaxResult updateRating(@PathVariable Long id) {
        return toAjax(meditationService.updateMeditationRating(id));
    }

    /**
     * 根据分类ID查询冥想列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:list')")
    @GetMapping("/category/{categoryId}")
    public AjaxResult getMeditationsByCategory(@PathVariable Long categoryId) {
        List<PsyMeditation> list = meditationService.selectMeditationsByCategoryId(categoryId);
        return success(list);
    }

    /**
     * 获取冥想播放统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditation:list')")
    @GetMapping("/statistics/{id}")
    public AjaxResult getMeditationStatistics(@PathVariable Long id) {
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();

        // 获取冥想基本信息
        PsyMeditation meditation = meditationService.selectMeditationById(id);
        if (meditation != null) {
            statistics.put("playCount", meditation.getPlayCount());
            statistics.put("ratingAvg", meditation.getRatingAvg());
            statistics.put("ratingCount", meditation.getRatingCount());
        }

        return success(statistics);
    }
}
