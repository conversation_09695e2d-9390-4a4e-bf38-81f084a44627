package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantOrder;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.IPsyConsultantOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序咨询师订单管理Controller（咨询师端）
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/consultant/orderManage")
public class MiniAppConsultantOrderManageController extends BaseController {
    
    @Autowired
    private IPsyConsultantOrderService orderService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取咨询师的订单列表
     */
    @GetMapping("/myOrders")
    public AjaxResult getMyOrders(@RequestParam(required = false) String status, HttpServletRequest request) {
        try {
            // 获取当前咨询师
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            // 验证是否为咨询师
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            // 获取咨询师ID（这里假设咨询师ID就是用户ID，实际可能需要查询咨询师表）
            Long consultantId = loginUser.getUserId();
            
            List<PsyConsultantOrder> orders = orderService.selectOrdersByConsultantId(consultantId);
            
            // 如果指定了状态，进行过滤
            if (status != null && !status.trim().isEmpty()) {
                orders = orders.stream()
                    .filter(order -> status.equals(order.getStatus()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            return success(orders);
        } catch (Exception e) {
            logger.error("获取咨询师订单失败", e);
            return error("获取订单列表失败");
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{orderId}")
    public AjaxResult getOrderDetails(@PathVariable Long orderId, HttpServletRequest request) {
        try {
            // 获取当前咨询师
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            PsyConsultantOrder order = orderService.selectOrderWithDetails(orderId);
            if (order == null) {
                return error("订单不存在");
            }
            
            // 验证订单归属（咨询师只能查看自己的订单）
            Long consultantId = loginUser.getUserId();
            if (!order.getConsultantId().equals(consultantId)) {
                return error("无权限访问该订单");
            }
            
            return success(order);
        } catch (Exception e) {
            logger.error("获取订单详情失败", e);
            return error("获取订单详情失败");
        }
    }

    /**
     * 确认订单（咨询师确认接单）
     */
    @PostMapping("/{orderId}/confirm")
    public AjaxResult confirmOrder(@PathVariable Long orderId, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            // 验证订单归属
            PsyConsultantOrder order = orderService.selectOrderById(orderId);
            if (order == null || !order.getConsultantId().equals(loginUser.getUserId())) {
                return error("订单不存在或无权限操作");
            }
            
            // 更新订单状态为待咨询
            int result = orderService.updateOrderStatus(orderId, "待咨询");
            return toAjax(result);
        } catch (Exception e) {
            logger.error("确认订单失败", e);
            return error("确认订单失败");
        }
    }

    /**
     * 拒绝订单
     */
    @PostMapping("/{orderId}/reject")
    public AjaxResult rejectOrder(@PathVariable Long orderId, 
                                @RequestParam String rejectReason, 
                                HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            // 验证订单归属
            PsyConsultantOrder order = orderService.selectOrderById(orderId);
            if (order == null || !order.getConsultantId().equals(loginUser.getUserId())) {
                return error("订单不存在或无权限操作");
            }
            
            // 取消订单
            int result = orderService.cancelOrder(orderId, "咨询师拒绝：" + rejectReason);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("拒绝订单失败", e);
            return error("拒绝订单失败");
        }
    }

    /**
     * 获取今日订单
     */
    @GetMapping("/today")
    public AjaxResult getTodayOrders(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantOrder> allOrders = orderService.selectOrdersByConsultantId(consultantId);
            
            // 过滤今日订单
            java.time.LocalDate today = java.time.LocalDate.now();
            List<PsyConsultantOrder> todayOrders = allOrders.stream()
                .filter(order -> {
                    if (order.getScheduledTime() != null) {
                        java.time.LocalDate orderDate = order.getScheduledTime().toInstant()
                            .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                        return orderDate.equals(today);
                    }
                    return false;
                })
                .collect(java.util.stream.Collectors.toList());
            
            return success(todayOrders);
        } catch (Exception e) {
            logger.error("获取今日订单失败", e);
            return error("获取今日订单失败");
        }
    }

    /**
     * 获取咨询师订单统计
     */
    @GetMapping("/statistics")
    public AjaxResult getOrderStatistics(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            Map<String, Object> statistics = orderService.getConsultantOrderStats(consultantId);
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取订单统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取不同状态的订单数量
     */
    @GetMapping("/statusCount")
    public AjaxResult getOrderStatusCount(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantOrder> allOrders = orderService.selectOrdersByConsultantId(consultantId);
            
            Map<String, Long> statusCount = new HashMap<>();
            statusCount.put("待确认", allOrders.stream().filter(o -> "已支付".equals(o.getStatus())).count());
            statusCount.put("待咨询", allOrders.stream().filter(o -> "待咨询".equals(o.getStatus())).count());
            statusCount.put("咨询中", allOrders.stream().filter(o -> "咨询中".equals(o.getStatus())).count());
            statusCount.put("已完成", allOrders.stream().filter(o -> "已完成".equals(o.getStatus())).count());
            statusCount.put("已取消", allOrders.stream().filter(o -> "已取消".equals(o.getStatus())).count());
            
            return success(statusCount);
        } catch (Exception e) {
            logger.error("获取订单状态统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取即将开始的咨询
     */
    @GetMapping("/upcoming")
    public AjaxResult getUpcomingConsultations(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantOrder> allOrders = orderService.selectOrdersByConsultantId(consultantId);
            
            // 过滤即将开始的咨询（2小时内）
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            java.time.LocalDateTime twoHoursLater = now.plusHours(2);
            
            List<PsyConsultantOrder> upcomingOrders = allOrders.stream()
                .filter(order -> "待咨询".equals(order.getStatus()) && order.getScheduledTime() != null)
                .filter(order -> {
                    java.time.LocalDateTime orderTime = order.getScheduledTime().toInstant()
                        .atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                    return orderTime.isAfter(now) && orderTime.isBefore(twoHoursLater);
                })
                .collect(java.util.stream.Collectors.toList());
            
            return success(upcomingOrders);
        } catch (Exception e) {
            logger.error("获取即将开始的咨询失败", e);
            return error("获取即将开始的咨询失败");
        }
    }

    /**
     * 更新咨询师状态
     */
    @PostMapping("/updateStatus")
    public AjaxResult updateConsultantStatus(@RequestParam String status, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            // 这里可以更新咨询师状态到数据库
            // 暂时返回成功
            Map<String, Object> result = new HashMap<>();
            result.put("consultantId", loginUser.getUserId());
            result.put("status", status);
            result.put("updateTime", new java.util.Date());
            
            return success(result);
        } catch (Exception e) {
            logger.error("更新咨询师状态失败", e);
            return error("更新状态失败");
        }
    }

    /**
     * 根据价格和日期筛选咨询订单
     */
    @GetMapping("/filterByPriceAndDate")
    public AjaxResult filterOrdersByPriceAndDate(
            @RequestParam(required = false) java.math.BigDecimal minPrice,
            @RequestParam(required = false) java.math.BigDecimal maxPrice,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "createTime") String orderBy,
            @RequestParam(defaultValue = "desc") String sortOrder,
            HttpServletRequest request) {
        try {
            // 获取当前咨询师
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }

            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }

            Long consultantId = loginUser.getUserId();

            // 参数验证
            if (minPrice != null && minPrice.compareTo(java.math.BigDecimal.ZERO) < 0) {
                return error("最小价格不能小于0");
            }
            if (maxPrice != null && maxPrice.compareTo(java.math.BigDecimal.ZERO) < 0) {
                return error("最大价格不能小于0");
            }
            if (minPrice != null && maxPrice != null && minPrice.compareTo(maxPrice) > 0) {
                return error("最小价格不能大于最大价格");
            }

            // 日期格式验证和转换
            java.util.Date startDateTime = null;
            java.util.Date endDateTime = null;

            if (startDate != null && !startDate.trim().isEmpty()) {
                try {
                    java.time.LocalDate localStartDate = java.time.LocalDate.parse(startDate);
                    startDateTime = java.util.Date.from(localStartDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
                } catch (Exception e) {
                    return error("开始日期格式错误，请使用 yyyy-MM-dd 格式");
                }
            }

            if (endDate != null && !endDate.trim().isEmpty()) {
                try {
                    java.time.LocalDate localEndDate = java.time.LocalDate.parse(endDate);
                    endDateTime = java.util.Date.from(localEndDate.atTime(23, 59, 59).atZone(java.time.ZoneId.systemDefault()).toInstant());
                } catch (Exception e) {
                    return error("结束日期格式错误，请使用 yyyy-MM-dd 格式");
                }
            }

            if (startDateTime != null && endDateTime != null && startDateTime.after(endDateTime)) {
                return error("开始日期不能晚于结束日期");
            }

            // 调用服务层方法进行筛选
            Map<String, Object> filterParams = new HashMap<>();
            filterParams.put("consultantId", consultantId);
            filterParams.put("minPrice", minPrice);
            filterParams.put("maxPrice", maxPrice);
            filterParams.put("startDate", startDateTime);
            filterParams.put("endDate", endDateTime);
            filterParams.put("status", status);
            filterParams.put("pageNum", pageNum);
            filterParams.put("pageSize", pageSize);
            filterParams.put("orderBy", orderBy);
            filterParams.put("sortOrder", sortOrder);

            Map<String, Object> result = orderService.filterOrdersByPriceAndDate(filterParams);

            return success(result);
        } catch (Exception e) {
            logger.error("根据价格和日期筛选订单失败", e);
            return error("筛选订单失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否为咨询师
     */
    private boolean isConsultant(LoginUser loginUser) {
        return loginUser.getUser().getDeptId() != null &&
               loginUser.getUser().getDeptId().equals(201L);
    }
}
