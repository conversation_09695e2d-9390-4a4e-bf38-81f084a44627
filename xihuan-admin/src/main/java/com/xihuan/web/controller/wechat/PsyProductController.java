package com.xihuan.web.controller.wechat;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyProduct;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.system.domain.dto.PsyProductDTO;
import com.xihuan.system.service.wxService.PsyProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

// PsyProductController.java
@RestController
@RequestMapping("/psy/product")
public class PsyProductController extends BaseController {

    @Autowired
    private PsyProductService productService;

    /**
     * 分页查询产品列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PsyProduct product) {
        startPage();
        List<PsyProduct> list = productService.selectProductList(product);
        return getDataTable(list);
    }

    /**
     * 获取产品详细信息（包含关联内容）
     */
    @GetMapping(value = "/{productId}")
    public AjaxResult getInfo(@PathVariable("productId") Long productId) {
        return AjaxResult.success(productService.selectProductWithDetails(productId));
    }

    /**
     * 新增心理咨询产品
     */
//    @Log(title = "心理咨询产品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody PsyProductDTO productDTO) {
        return toAjax(productService.insertProduct(productDTO));
    }

    /**
     * 修改心理咨询产品
     */
//    @Log(title = "心理咨询产品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody PsyProductDTO productDTO) {
        return toAjax(productService.updateProduct(productDTO));
    }

    /**
     * 删除心理咨询产品
     */
//    @Log(title = "心理咨询产品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{productIds}")
    public AjaxResult remove(@PathVariable Long[] productIds) {
        return toAjax(productService.deleteProductByIds(productIds));
    }
}