package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.dto.PsyCounselorScheduleDTO;
import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsyTimeCounselorScheduleService;
import com.xihuan.system.service.wxService.IPsyCounselorScheduleWxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 咨询师排期管理接口（小程序端）
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wechat/counselor/schedule")
public class PsyCounselorScheduleController extends BaseController {
    
    @Autowired
    private IPsyTimeCounselorScheduleService scheduleService;

    @Autowired
    private IPsyCounselorScheduleWxService wxScheduleService;

    /**
     * 获取咨询师排期列表（小程序接口）
     * 支持按咨询师ID、日期范围等条件查询
     */
    @GetMapping("/list")
    public AjaxResult list(
            @RequestParam(required = false) Long counselorId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long centerId,
            @RequestParam(required = false, defaultValue = "1") Integer isWorking) {
        
        PsyTimeCounselorSchedule schedule = new PsyTimeCounselorSchedule();
        schedule.setCounselorId(counselorId);
        schedule.setCenterId(centerId);
        schedule.setIsWorking(isWorking);
        
        List<PsyTimeCounselorSchedule> list;
        
        // 如果指定了日期范围，使用日期范围查询
        if (counselorId != null && startDate != null && endDate != null) {
            list = scheduleService.selectSchedulesByDateRange(counselorId, startDate, endDate);
        } else {
            list = scheduleService.selectScheduleList(schedule);
        }
        
        return success(list);
    }

    /**
     * 获取咨询师排期详情（小程序接口）
     */
    @GetMapping("/detail/{id}")
    public AjaxResult getDetail(@PathVariable("id") Long id) {
        PsyTimeCounselorSchedule schedule = scheduleService.selectScheduleById(id);
        if (schedule == null) {
            return error("排期信息不存在");
        }
        return success(schedule);
    }

    /**
     * 根据咨询师ID和日期查询排期（小程序接口）
     */
    @GetMapping("/counselor/{counselorId}")
    public AjaxResult getByCounselorAndDate(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate scheduleDate) {
        
        PsyTimeCounselorSchedule schedule = scheduleService.selectScheduleByCounselorAndDate(counselorId, scheduleDate);
        return success(schedule);
    }

    /**
     * 获取咨询师在指定日期范围内的排期（小程序接口）
     */
    @GetMapping("/counselor/{counselorId}/range")
    public AjaxResult getByDateRange(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<PsyTimeCounselorSchedule> list = scheduleService.selectSchedulesByDateRange(counselorId, startDate, endDate);
        return success(list);
    }

    /**
     * 获取指定日期的所有咨询师排期（小程序接口）
     */
    @GetMapping("/date/{scheduleDate}")
    public AjaxResult getByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate scheduleDate,
            @RequestParam(required = false) Long centerId) {
        
        List<PsyTimeCounselorSchedule> list = scheduleService.selectSchedulesByDate(scheduleDate, centerId);
        return success(list);
    }

    /**
     * 咨询师创建自己的排期（小程序接口）
     * 咨询师可以为自己创建排期安排
     */
    @Log(title = "咨询师排期", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult create(@Validated @RequestBody PsyTimeCounselorSchedule schedule) {
        // 设置创建者信息
        schedule.setCreateBy(getUsername());
        
        // 验证排期数据
        if (schedule.getCounselorId() == null) {
            return error("咨询师ID不能为空");
        }
        if (schedule.getScheduleDate() == null) {
            return error("排期日期不能为空");
        }
        if (schedule.getStartTime() == null || schedule.getEndTime() == null) {
            return error("排期时间不能为空");
        }
        if (schedule.getStartTime().isAfter(schedule.getEndTime())) {
            return error("开始时间不能晚于结束时间");
        }
        
        // 检查是否已存在相同日期的排期
        PsyTimeCounselorSchedule existingSchedule = scheduleService.selectScheduleByCounselorAndDate(
            schedule.getCounselorId(), schedule.getScheduleDate());
        if (existingSchedule != null) {
            return error("该日期已存在排期，请修改现有排期或选择其他日期");
        }
        
        int result = scheduleService.insertSchedule(schedule);
        return toAjax(result);
    }

    /**
     * 咨询师修改自己的排期（小程序接口）
     */
    @Log(title = "咨询师排期", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@Validated @RequestBody PsyTimeCounselorSchedule schedule) {
        // 设置更新者信息
        schedule.setUpdateBy(getUsername());
        
        // 验证排期数据
        if (schedule.getId() == null) {
            return error("排期ID不能为空");
        }
        if (schedule.getStartTime() != null && schedule.getEndTime() != null 
            && schedule.getStartTime().isAfter(schedule.getEndTime())) {
            return error("开始时间不能晚于结束时间");
        }
        
        int result = scheduleService.updateSchedule(schedule);
        return toAjax(result);
    }

    /**
     * 咨询师删除自己的排期（小程序接口）
     */
    @Log(title = "咨询师排期", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {
        int result = scheduleService.deleteScheduleById(id);
        return toAjax(result);
    }

    /**
     * 咨询师批量删除排期（小程序接口）
     */
    @Log(title = "咨询师排期", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/batch")
    public AjaxResult deleteBatch(@RequestBody List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return error("请选择要删除的排期");
        }

        int result = scheduleService.deleteScheduleByIds(ids.toArray(new Long[0]));
        return AjaxResult.success("成功删除 " + result + " 条排期记录");
    }

    /**
     * 咨询师批量创建排期（小程序接口）
     * 支持一次性创建多个日期的排期
     */
    @Log(title = "咨询师排期", businessType = BusinessType.INSERT)
    @PostMapping("/create/batch")
    public AjaxResult createBatch(@Validated @RequestBody List<PsyTimeCounselorSchedule> schedules) {
        if (schedules == null || schedules.isEmpty()) {
            return error("排期数据不能为空");
        }

        String username = getUsername();

        // 验证每个排期数据
        for (PsyTimeCounselorSchedule schedule : schedules) {
            schedule.setCreateBy(username);

            if (schedule.getCounselorId() == null) {
                return error("咨询师ID不能为空");
            }
            if (schedule.getScheduleDate() == null) {
                return error("排期日期不能为空");
            }
            if (schedule.getStartTime() == null || schedule.getEndTime() == null) {
                return error("排期时间不能为空");
            }
            if (schedule.getStartTime().isAfter(schedule.getEndTime())) {
                return error("开始时间不能晚于结束时间");
            }
        }

        int result = scheduleService.batchInsertSchedules(schedules);
        return AjaxResult.success("成功创建 " + result + " 条排期记录");
    }

    /**
     * 咨询师生成默认排期（小程序接口）
     * 为指定日期范围生成默认的工作日排期
     */
    @Log(title = "咨询师排期", businessType = BusinessType.INSERT)
    @PostMapping("/generate/default")
    public AjaxResult generateDefault(
            @RequestParam Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false, defaultValue = "1") Long centerId) {

        if (counselorId == null) {
            return error("咨询师ID不能为空");
        }
        if (startDate == null || endDate == null) {
            return error("日期范围不能为空");
        }
        if (startDate.isAfter(endDate)) {
            return error("开始日期不能晚于结束日期");
        }

        int result = scheduleService.generateDefaultSchedule(counselorId, startDate, endDate, centerId);
        return AjaxResult.success("成功生成 " + result + " 条默认排期记录");
    }

    /**
     * 获取咨询师的工作状态统计（小程序接口）
     * 统计指定时间范围内的工作天数、休假天数等
     */
    @GetMapping("/statistics/{counselorId}")
    public AjaxResult getStatistics(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        List<PsyTimeCounselorSchedule> schedules = scheduleService.selectSchedulesByDateRange(counselorId, startDate, endDate);

        // 统计数据
        long totalDays = schedules.size();
        long workingDays = schedules.stream().filter(s -> s.getIsWorking() == 1).count();
        long restDays = totalDays - workingDays;

        // 构建统计结果
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("totalDays", totalDays);
        statistics.put("workingDays", workingDays);
        statistics.put("restDays", restDays);
        statistics.put("workingRate", totalDays > 0 ? (double) workingDays / totalDays * 100 : 0);
        statistics.put("startDate", startDate);
        statistics.put("endDate", endDate);

        return success(statistics);
    }

    /**
     * 复制排期到其他日期（小程序接口）
     * 将某个日期的排期复制到其他日期
     */
    @Log(title = "咨询师排期", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public AjaxResult copySchedule(
            @RequestParam Long sourceScheduleId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) List<LocalDate> targetDates) {

        if (sourceScheduleId == null) {
            return error("源排期ID不能为空");
        }
        if (targetDates == null || targetDates.isEmpty()) {
            return error("目标日期不能为空");
        }

        // 获取源排期
        PsyTimeCounselorSchedule sourceSchedule = scheduleService.selectScheduleById(sourceScheduleId);
        if (sourceSchedule == null) {
            return error("源排期不存在");
        }

        // 创建新的排期列表
        List<PsyTimeCounselorSchedule> newSchedules = new java.util.ArrayList<>();
        String username = getUsername();

        for (LocalDate targetDate : targetDates) {
            // 检查目标日期是否已存在排期
            PsyTimeCounselorSchedule existingSchedule = scheduleService.selectScheduleByCounselorAndDate(
                sourceSchedule.getCounselorId(), targetDate);
            if (existingSchedule != null) {
                continue; // 跳过已存在的日期
            }

            // 创建新排期
            PsyTimeCounselorSchedule newSchedule = new PsyTimeCounselorSchedule();
            newSchedule.setCounselorId(sourceSchedule.getCounselorId());
            newSchedule.setScheduleDate(targetDate);
            newSchedule.setStartTime(sourceSchedule.getStartTime());
            newSchedule.setEndTime(sourceSchedule.getEndTime());
            newSchedule.setCenterId(sourceSchedule.getCenterId());
            newSchedule.setIsWorking(sourceSchedule.getIsWorking());
            newSchedule.setIsTemplate(0); // 复制的排期不是模板生成
            newSchedule.setRemark("复制自 " + sourceSchedule.getScheduleDate() + " 的排期");
            newSchedule.setCreateBy(username);

            newSchedules.add(newSchedule);
        }

        if (newSchedules.isEmpty()) {
            return error("所有目标日期都已存在排期，无需复制");
        }

        int result = scheduleService.batchInsertSchedules(newSchedules);
        return AjaxResult.success("成功复制 " + result + " 条排期记录");
    }

    /**
     * 切换排期工作状态（小程序接口）
     * 快速切换工作/休假状态
     */
    @Log(title = "咨询师排期", businessType = BusinessType.UPDATE)
    @PutMapping("/toggle/working/{id}")
    public AjaxResult toggleWorking(@PathVariable("id") Long id) {
        int result = wxScheduleService.toggleWorkingStatus(id, getUsername());
        return result > 0 ?
            AjaxResult.success("工作状态切换成功") :
            AjaxResult.error("状态切换失败");
    }

    /**
     * 获取排期日历视图（小程序接口）
     */
    @GetMapping("/calendar/{counselorId}")
    public AjaxResult getCalendar(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam(required = false, defaultValue = "0") int year,
            @RequestParam(required = false, defaultValue = "0") int month) {

        // 如果没有指定年月，使用当前年月
        if (year == 0 || month == 0) {
            LocalDate now = LocalDate.now();
            year = now.getYear();
            month = now.getMonthValue();
        }

        PsyCounselorScheduleDTO.ScheduleCalendarDTO calendar = wxScheduleService.getScheduleCalendar(counselorId, year, month);
        return success(calendar);
    }

    /**
     * 获取近期排期概览（小程序接口）
     */
    @GetMapping("/overview/{counselorId}")
    public AjaxResult getOverview(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam(required = false, defaultValue = "7") int days) {

        List<PsyCounselorScheduleDTO> overview = wxScheduleService.getRecentScheduleOverview(counselorId, days);
        return success(overview);
    }

    /**
     * 获取排期时间段分析（小程序接口）
     */
    @GetMapping("/timerange/analysis/{counselorId}")
    public AjaxResult getTimeRangeAnalysis(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {

        List<PsyCounselorScheduleDTO.ScheduleTimeRange> analysis = wxScheduleService.getScheduleTimeRangeAnalysis(counselorId, date);
        return success(analysis);
    }

    /**
     * 检查排期冲突（小程序接口）
     */
    @GetMapping("/conflict/check")
    public AjaxResult checkConflict(
            @RequestParam Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate scheduleDate,
            @RequestParam(required = false) Long excludeScheduleId) {

        PsyCounselorScheduleDTO.ConflictCheckResult result = wxScheduleService.checkScheduleConflict(counselorId, scheduleDate, excludeScheduleId);
        return success(result);
    }

    /**
     * 获取排期建议（小程序接口）
     */
    @GetMapping("/suggestions/{counselorId}")
    public AjaxResult getSuggestions(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {

        List<String> suggestions = wxScheduleService.getScheduleSuggestions(counselorId, date);
        return success(suggestions);
    }

    /**
     * 批量操作排期（小程序接口）
     */
    @Log(title = "咨询师排期", businessType = BusinessType.OTHER)
    @PostMapping("/batch/operate")
    public AjaxResult batchOperate(@RequestBody PsyCounselorScheduleDTO.BatchOperationRequest request) {
        int result = wxScheduleService.batchOperateSchedules(request, getUsername());
        return AjaxResult.success("批量操作完成，处理了 " + result + " 条记录");
    }

    /**
     * 验证排期数据（小程序接口）
     */
    @PostMapping("/validate")
    public AjaxResult validateSchedule(@RequestBody PsyTimeCounselorSchedule schedule) {
        String validationMessage = wxScheduleService.validateScheduleData(schedule);
        if (validationMessage != null) {
            return error(validationMessage);
        }
        return success("排期数据验证通过");
    }
}
