package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.task.PsyTimeSlotTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 时间槽定时任务Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/timeTask")
public class PsyTimeTaskController extends BaseController {
    
    @Autowired
    private PsyTimeSlotTaskService taskService;

    /**
     * 获取定时任务状态
     */
    @PreAuthorize("@ss.hasPermi('system:timeTask:query')")
    @GetMapping("/status")
    public AjaxResult getStatus() {
        String status = taskService.getTaskStatus();
        return AjaxResult.success(status);
    }

    /**
     * 手动触发生成时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:timeTask:generate')")
    @Log(title = "时间槽定时任务", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult manualGenerate(@RequestParam(defaultValue = "7") int days) {
        int count = taskService.manualGenerateTimeSlots(days);
        return AjaxResult.success("成功生成 " + count + " 个时间槽");
    }

    /**
     * 手动触发清理过期时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:timeTask:clean')")
    @Log(title = "时间槽定时任务", businessType = BusinessType.DELETE)
    @PostMapping("/clean")
    public AjaxResult manualClean(@RequestParam(defaultValue = "7") int days) {
        int count = taskService.manualCleanExpiredSlots(days);
        return AjaxResult.success("成功清理 " + count + " 个过期时间槽");
    }

    /**
     * 手动触发更新过期时间槽状态
     */
    @PreAuthorize("@ss.hasPermi('system:timeTask:update')")
    @Log(title = "时间槽定时任务", businessType = BusinessType.UPDATE)
    @PostMapping("/updateExpired")
    public AjaxResult manualUpdateExpired() {
        // 这里直接调用服务层方法
        // int count = timeSlotService.updateExpiredSlotStatus();
        return AjaxResult.success("手动更新过期状态功能待实现");
    }
}
