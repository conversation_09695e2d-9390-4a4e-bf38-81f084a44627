package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.common.core.domain.entity.PsyTScoringConfig;
import com.xihuan.system.service.IPsyTConfigurableScoringService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 计分配置管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/scoring-config")
public class PsyTScoringConfigController extends BaseController {

    @Autowired
    private IPsyTConfigurableScoringService configurableScoringService;

    /**
     * 查询计分配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTScoringConfig scoringConfig) {
        startPage();
        // 这里需要实现具体的查询逻辑
        List<PsyTScoringConfig> list = new ArrayList<>(); // 临时空列表
        return getDataTable(list);
    }

    /**
     * 导出计分配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:export')")
    @Log(title = "计分配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTScoringConfig scoringConfig) {
        List<PsyTScoringConfig> list = new ArrayList<>(); // 临时空列表
        ExcelUtil<PsyTScoringConfig> util = new ExcelUtil<>(PsyTScoringConfig.class);
        util.exportExcel(response, list, "计分配置数据");
    }

    /**
     * 获取计分配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:query')")
    @GetMapping(value = "/{scaleId}")
    public AjaxResult getInfo(@PathVariable("scaleId") Long scaleId) {
        try {
            Map<String, Object> config = configurableScoringService.getScaleConfig(scaleId);
            return success(config);
        } catch (Exception e) {
            logger.error("获取计分配置失败", e);
            return error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 新增或修改计分配置
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:edit')")
    @Log(title = "计分配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{scaleId}")
    public AjaxResult saveConfig(@PathVariable("scaleId") Long scaleId, 
                               @RequestBody Map<String, Object> config) {
        try {
            // 验证配置
            Map<String, Object> validation = configurableScoringService.validateConfig(config);
            if (!(Boolean) validation.get("valid")) {
                return error("配置验证失败").put("validation", validation);
            }
            
            // 保存配置
            boolean success = configurableScoringService.saveScaleConfig(scaleId, config);
            if (success) {
                return success("保存成功");
            } else {
                return error("保存失败");
            }
        } catch (Exception e) {
            logger.error("保存计分配置失败", e);
            return error("保存失败: " + e.getMessage());
        }
    }

    /**
     * 删除计分配置
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:remove')")
    @Log(title = "计分配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{scaleId}")
    public AjaxResult remove(@PathVariable Long scaleId) {
        try {
            // 这里需要实现删除逻辑
            return success("删除成功");
        } catch (Exception e) {
            logger.error("删除计分配置失败", e);
            return error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的计分方法
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:query')")
    @GetMapping("/methods")
    public AjaxResult getScoringMethods() {
        try {
            Map<String, String> methods = configurableScoringService.getSupportedScoringMethods();
            return success(methods);
        } catch (Exception e) {
            logger.error("获取计分方法失败", e);
            return error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 验证计分配置
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:query')")
    @PostMapping("/validate")
    public AjaxResult validateConfig(@RequestBody Map<String, Object> config) {
        try {
            Map<String, Object> validation = configurableScoringService.validateConfig(config);
            return success(validation);
        } catch (Exception e) {
            logger.error("验证计分配置失败", e);
            return error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 测试计分配置
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:test')")
    @PostMapping("/test/{scaleId}")
    public AjaxResult testConfig(@PathVariable Long scaleId, 
                               @RequestBody Map<String, Object> testData) {
        try {
            Map<String, Object> result = configurableScoringService.testScoringConfig(scaleId, testData);
            return success(result);
        } catch (Exception e) {
            logger.error("测试计分配置失败", e);
            return error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 生成计分规则说明
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:query')")
    @GetMapping("/description/{scaleId}")
    public AjaxResult getDescription(@PathVariable Long scaleId) {
        try {
            String description = configurableScoringService.generateScoringRuleDescription(scaleId);
            return success().put("description", description);
        } catch (Exception e) {
            logger.error("生成计分规则说明失败", e);
            return error("生成失败: " + e.getMessage());
        }
    }

    /**
     * 执行配置化计分
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:score')")
    @Log(title = "配置化计分", businessType = BusinessType.OTHER)
    @PostMapping("/execute/{recordId}")
    public AjaxResult executeScoring(@PathVariable Long recordId) {
        try {
            Map<String, Object> result = configurableScoringService.executeConfigurableScoring(recordId);
            return success(result);
        } catch (Exception e) {
            logger.error("执行配置化计分失败", e);
            return error("计分失败: " + e.getMessage());
        }
    }

    /**
     * 批量配置量表计分方法
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:edit')")
    @Log(title = "批量配置计分", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-config")
    public AjaxResult batchConfig(@RequestBody Map<String, Object> batchData) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> configs = (List<Map<String, Object>>) batchData.get("configs");
            int successCount = 0;
            int failCount = 0;

            for (Map<String, Object> configItem : configs) {
                Long scaleId = Long.valueOf(configItem.get("scaleId").toString());
                @SuppressWarnings("unchecked")
                Map<String, Object> config = (Map<String, Object>) configItem.get("config");
                
                try {
                    boolean success = configurableScoringService.saveScaleConfig(scaleId, config);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    failCount++;
                    logger.error("批量配置失败，scaleId: {}", scaleId, e);
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("total", configs.size());
            result.put("success", successCount);
            result.put("failed", failCount);
            
            return success(result);
        } catch (Exception e) {
            logger.error("批量配置计分方法失败", e);
            return error("批量配置失败: " + e.getMessage());
        }
    }

    /**
     * 复制计分配置
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:edit')")
    @Log(title = "复制计分配置", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{sourceScaleId}/{targetScaleId}")
    public AjaxResult copyConfig(@PathVariable Long sourceScaleId, 
                               @PathVariable Long targetScaleId) {
        try {
            // 获取源配置
            Map<String, Object> sourceConfig = configurableScoringService.getScaleConfig(sourceScaleId);
            if (sourceConfig.isEmpty()) {
                return error("源配置不存在");
            }
            
            // 移除不需要复制的字段
            sourceConfig.remove("scale_id");
            sourceConfig.remove("scale_code");
            sourceConfig.remove("scale_name");
            
            // 保存到目标量表
            boolean success = configurableScoringService.saveScaleConfig(targetScaleId, sourceConfig);
            if (success) {
                return success("复制成功");
            } else {
                return error("复制失败");
            }
        } catch (Exception e) {
            logger.error("复制计分配置失败", e);
            return error("复制失败: " + e.getMessage());
        }
    }

    /**
     * 重置计分配置为默认值
     */
    @PreAuthorize("@ss.hasPermi('system:scoring:edit')")
    @Log(title = "重置计分配置", businessType = BusinessType.UPDATE)
    @PostMapping("/reset/{scaleId}")
    public AjaxResult resetConfig(@PathVariable Long scaleId) {
        try {
            // 创建默认配置
            Map<String, Object> defaultConfig = new HashMap<>();
            defaultConfig.put("scoring_method", "SIMPLE_SUM");
            defaultConfig.put("has_reverse_items", 0);
            defaultConfig.put("has_standard_score", 0);
            defaultConfig.put("standard_score_multiplier", 1.0);
            defaultConfig.put("dimension_count", 1);
            
            boolean success = configurableScoringService.saveScaleConfig(scaleId, defaultConfig);
            if (success) {
                return success("重置成功");
            } else {
                return error("重置失败");
            }
        } catch (Exception e) {
            logger.error("重置计分配置失败", e);
            return error("重置失败: " + e.getMessage());
        }
    }
}
