package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.utils.TimeSlotDiagnosticUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统公共时间槽Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/systemTimeSlot")
public class PsySystemTimeSlotController extends BaseController {
    
    @Autowired
    private IPsySystemTimeSlotService systemTimeSlotService;

    @Autowired
    private IPsyTimeRangeService timeRangeService;

    /**
     * 查询系统时间槽列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsySystemTimeSlot systemTimeSlot,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        startPage();

        // 将日期范围参数添加到查询条件中
        if (startDate != null) {
            systemTimeSlot.getParams().put("startDate", startDate.toString());
        }
        if (endDate != null) {
            systemTimeSlot.getParams().put("endDate", endDate.toString());
        }

        // 使用统一的查询方法，支持所有查询条件
        List<PsySystemTimeSlot> list = systemTimeSlotService.selectSystemTimeSlotList(systemTimeSlot);

        return getDataTable(list);
    }

    /**
     * 获取系统时间槽详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(systemTimeSlotService.selectSystemTimeSlotById(id));
    }

    /**
     * 查询指定日期范围的系统时间槽
     */
    @GetMapping("/dateRange")
    public AjaxResult getSlotsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        List<PsySystemTimeSlot> list = systemTimeSlotService.selectSlotsByDateRange(startDate, endDate, centerId);
        return AjaxResult.success(list);
    }

    /**
     * 查询指定日期的系统时间槽
     */
    @GetMapping("/date/{date}")
    public AjaxResult getSlotsByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "1") Long centerId) {
        List<PsySystemTimeSlot> list = systemTimeSlotService.selectSlotsByDate(date, centerId);
        return AjaxResult.success(list);
    }

    /**
     * 查询有可用咨询师的时间槽
     */
    @GetMapping("/available")
    public AjaxResult getAvailableSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        List<PsySystemTimeSlot> list = systemTimeSlotService.selectAvailableSlots(startDate, endDate, centerId);
        return AjaxResult.success(list);
    }

    /**
     * 获取格式化的系统时间槽数据
     */
    @GetMapping("/formatted")
    public AjaxResult getFormattedTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        Map<String, Object> result = systemTimeSlotService.getFormattedSystemTimeSlots(startDate, endDate, centerId);
        return AjaxResult.success(result);
    }

    /**
     * 新增系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:add')")
    @Log(title = "系统时间槽", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsySystemTimeSlot systemTimeSlot) {
        return toAjax(systemTimeSlotService.insertSystemTimeSlot(systemTimeSlot));
    }

    /**
     * 修改系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:edit')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsySystemTimeSlot systemTimeSlot) {
        return toAjax(systemTimeSlotService.updateSystemTimeSlot(systemTimeSlot));
    }

    /**
     * 删除系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:remove')")
    @Log(title = "系统时间槽", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(systemTimeSlotService.deleteSystemTimeSlotByIds(ids));
    }

    /**
     * 生成系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:generate')")
    @Log(title = "系统时间槽", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult generateSystemTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, centerId);
        return AjaxResult.success("成功生成 " + count + " 个系统时间槽");
    }

    /**
     * 重新生成系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:regenerate')")
    @Log(title = "系统时间槽", businessType = BusinessType.INSERT)
    @PostMapping("/regenerate")
    public AjaxResult regenerateSystemTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.regenerateSystemTimeSlots(startDate, endDate, centerId);
        return AjaxResult.success("成功重新生成 " + count + " 个系统时间槽");
    }

    /**
     * 更新可用性统计
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:update')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStats")
    public AjaxResult updateAvailabilityStats(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.updateAvailabilityStats(date, centerId);
        return AjaxResult.success("成功更新 " + count + " 个时间槽的可用性统计");
    }

    /**
     * 清理过期的系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:clean')")
    @Log(title = "系统时间槽", businessType = BusinessType.DELETE)
    @DeleteMapping("/cleanExpired")
    public AjaxResult cleanExpiredSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate beforeDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.cleanExpiredSystemSlots(beforeDate, centerId);
        return AjaxResult.success("成功清理 " + count + " 个过期系统时间槽");
    }

    /**
     * 更新过期状态
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:update')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PutMapping("/updateExpiredStatus")
    public AjaxResult updateExpiredStatus(@RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.updateExpiredSlotStatusWithDelay(centerId);
        return AjaxResult.success("成功更新 " + count + " 个时间槽的过期状态");
    }

    /**
     * 批量更新时间槽状态
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:update')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> slotIds = (List<Long>) params.get("slotIds");
            Integer status = (Integer) params.get("status");

            if (slotIds == null || slotIds.isEmpty()) {
                return AjaxResult.error("时间槽ID列表不能为空");
            }

            if (status == null || (status != 0 && status != 2)) {
                return AjaxResult.error("状态值无效，只能是0(可用)或2(已过期)");
            }

            int count = systemTimeSlotService.batchUpdateSlotStatus(slotIds, status);
            String statusText = status == 0 ? "可用" : "已过期";
            return AjaxResult.success("成功将 " + count + " 个时间槽状态更新为：" + statusText);
        } catch (Exception e) {
            logger.error("批量更新时间槽状态失败", e);
            return AjaxResult.error("批量更新失败：" + e.getMessage());
        }
    }

    /**
     * 诊断时间槽生成问题
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:query')")
    @GetMapping("/diagnose")
    public AjaxResult diagnoseTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "1") Long centerId) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 诊断时间段配置
            List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();
            Map<String, Object> timeRangeDiagnosis = TimeSlotDiagnosticUtil.diagnoseTimeRanges(timeRanges);

            // 诊断系统时间槽
            List<PsySystemTimeSlot> timeSlots = systemTimeSlotService.selectSlotsByDate(date, centerId);
            Map<String, Object> timeSlotDiagnosis = TimeSlotDiagnosticUtil.diagnoseSystemTimeSlots(timeSlots, date);

            // 生成修复建议
            List<String> fixSuggestions = TimeSlotDiagnosticUtil.generateFixSuggestions(
                timeRangeDiagnosis, timeSlotDiagnosis);

            result.put("date", date);
            result.put("centerId", centerId);
            result.put("timeRangeDiagnosis", timeRangeDiagnosis);
            result.put("timeSlotDiagnosis", timeSlotDiagnosis);
            result.put("fixSuggestions", fixSuggestions);

            // 打印诊断报告到日志
            TimeSlotDiagnosticUtil.printDiagnosticReport(timeRangeDiagnosis, timeSlotDiagnosis, date);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("诊断时间槽问题失败", e);
            return AjaxResult.error("诊断失败：" + e.getMessage());
        }
    }

    /**
     * 检查和修复时间段配置连续性
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:generate')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PostMapping("/fixTimeRangeContinuity")
    public AjaxResult fixTimeRangeContinuity() {
        try {
            int addedCount = systemTimeSlotService.checkAndFixTimeRangeContinuity();
            if (addedCount > 0) {
                return AjaxResult.success("成功修复时间段连续性，添加了 " + addedCount + " 个缺失的时间段");
            } else {
                return AjaxResult.success("时间段配置正常，无需修复");
            }
        } catch (Exception e) {
            logger.error("修复时间段连续性失败", e);
            return AjaxResult.error("修复失败：" + e.getMessage());
        }
    }

    /**
     * 一键修复时间槽问题
     * 包括：检查时间段连续性 -> 重新生成系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:generate')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PostMapping("/oneClickFix")
    public AjaxResult oneClickFix(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 步骤1：检查和修复时间段连续性
            int fixedRanges = systemTimeSlotService.checkAndFixTimeRangeContinuity();
            result.put("fixedTimeRanges", fixedRanges);

            // 步骤2：重新生成系统时间槽
            int generatedSlots = systemTimeSlotService.regenerateSystemTimeSlots(startDate, endDate, centerId);
            result.put("generatedSlots", generatedSlots);

            // 步骤3：再次诊断验证修复效果
            List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();
            List<PsySystemTimeSlot> timeSlots = systemTimeSlotService.selectSlotsByDateRange(startDate, endDate, centerId);

            Map<String, Object> finalDiagnosis = new HashMap<>();
            finalDiagnosis.put("timeRangeCount", timeRanges.size());
            finalDiagnosis.put("timeSlotCount", timeSlots.size());

            result.put("finalStatus", finalDiagnosis);
            result.put("message", String.format("一键修复完成！修复了 %d 个时间段，重新生成了 %d 个系统时间槽",
                fixedRanges, generatedSlots));

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("一键修复时间槽问题失败", e);
            return AjaxResult.error("一键修复失败：" + e.getMessage());
        }
    }
}
