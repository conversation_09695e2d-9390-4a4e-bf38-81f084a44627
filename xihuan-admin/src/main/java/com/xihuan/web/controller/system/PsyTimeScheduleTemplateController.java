package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTimeScheduleTemplate;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTimeScheduleTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * 排班模板Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/schedule/template")
public class PsyTimeScheduleTemplateController extends BaseController {
    
    @Autowired
    private IPsyTimeScheduleTemplateService templateService;

    /**
     * 查询排班模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:template:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTimeScheduleTemplate template) {
        startPage();
        List<PsyTimeScheduleTemplate> list = templateService.selectTemplateList(template);
        return getDataTable(list);
    }

    /**
     * 导出排班模板列表
     */
    @PreAuthorize("@ss.hasPermi('system:template:export')")
    @Log(title = "排班模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTimeScheduleTemplate template) {
        List<PsyTimeScheduleTemplate> list = templateService.selectTemplateList(template);
        ExcelUtil<PsyTimeScheduleTemplate> util = new ExcelUtil<PsyTimeScheduleTemplate>(PsyTimeScheduleTemplate.class);
        util.exportExcel(response, list, "排班模板数据");
    }

    /**
     * 获取排班模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:template:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(templateService.selectTemplateById(id));
    }

    /**
     * 根据咨询师ID查询排班模板
     */
    @GetMapping("/counselor/{counselorId}")
    public AjaxResult getByCounselorId(@PathVariable("counselorId") Long counselorId) {
        List<PsyTimeScheduleTemplate> list = templateService.selectTemplatesByCounselorId(counselorId);
        return success(list);
    }

    /**
     * 查询咨询师的默认模板
     */
    @GetMapping("/counselor/{counselorId}/default")
    public AjaxResult getDefaultTemplate(@PathVariable("counselorId") Long counselorId) {
        PsyTimeScheduleTemplate template = templateService.selectDefaultTemplateByCounselorId(counselorId);
        return success(template);
    }

    /**
     * 查询在指定日期有效的模板
     */
    @GetMapping("/counselor/{counselorId}/effective")
    public AjaxResult getEffectiveTemplate(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam("date") String dateStr) {
        LocalDate date = LocalDate.parse(dateStr);
        PsyTimeScheduleTemplate template = templateService.selectEffectiveTemplate(counselorId, date);
        return success(template);
    }

    /**
     * 新增排班模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "排班模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTimeScheduleTemplate template) {
        template.setCreateBy(getUsername());
        return toAjax(templateService.insertTemplate(template));
    }

    /**
     * 修改排班模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "排班模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTimeScheduleTemplate template) {
        template.setUpdateBy(getUsername());
        return toAjax(templateService.updateTemplate(template));
    }

    /**
     * 设置默认模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "排班模板", businessType = BusinessType.UPDATE)
    @PutMapping("/setDefault/{counselorId}/{templateId}")
    public AjaxResult setDefaultTemplate(
            @PathVariable("counselorId") Long counselorId,
            @PathVariable("templateId") Long templateId) {
        int result = templateService.setDefaultTemplate(counselorId, templateId);
        return toAjax(result);
    }

    /**
     * 删除排班模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:remove')")
    @Log(title = "排班模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(templateService.deleteTemplateByIds(ids));
    }

    /**
     * 校验模板名称是否唯一
     */
    @PostMapping("/checkTemplateNameUnique")
    public AjaxResult checkTemplateNameUnique(@RequestBody PsyTimeScheduleTemplate template) {
        String result = templateService.checkTemplateNameUnique(template);
        return AjaxResult.success("0".equals(result));
    }

    /**
     * 为咨询师创建默认排班模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "排班模板", businessType = BusinessType.INSERT)
    @PostMapping("/createDefault/{counselorId}")
    public AjaxResult createDefaultTemplate(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam(value = "centerId", defaultValue = "1") Long centerId) {
        int result = templateService.createDefaultTemplate(counselorId, centerId);
        return toAjax(result);
    }

    /**
     * 复制排班模板
     */
    @PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "排班模板", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{templateId}")
    public AjaxResult copyTemplate(
            @PathVariable("templateId") Long templateId,
            @RequestParam("newName") String newName) {
        try {
            Long newTemplateId = templateService.copyTemplate(templateId, newName);
            return AjaxResult.success("复制成功", newTemplateId);
        } catch (Exception e) {
            return AjaxResult.error("复制失败：" + e.getMessage());
        }
    }
}
