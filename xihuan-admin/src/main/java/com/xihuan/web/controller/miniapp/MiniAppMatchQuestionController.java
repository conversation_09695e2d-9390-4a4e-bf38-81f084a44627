package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.dto.MatchQuestionDTO;
import com.xihuan.common.core.domain.entity.PsyMatchQuestion;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.wxService.IPsyMatchQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 小程序极速匹配问题Controller（用户端）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/match/question")
public class MiniAppMatchQuestionController extends BaseController {
    
    @Autowired
    private IPsyMatchQuestionService questionService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取问题列表（小程序端）
     */
    @GetMapping("/list")
    public AjaxResult list(@RequestParam(required = false) String title) {
        List<PsyMatchQuestion> list = questionService.selectQuestionList(title);
        return success(list);
    }

    /**
     * 获取问题详情（小程序端）
     */
    @GetMapping("/{questionId}")
    public AjaxResult getInfo(@PathVariable Long questionId) {
        PsyMatchQuestion question = questionService.getQuestion(questionId);
        if (question == null) {
            return error("问题不存在");
        }
        return success(question);
    }

    /**
     * 根据选项筛选咨询师（小程序端核心功能）
     */
    @PostMapping("/match")
    public AjaxResult matchConsultants(@RequestBody List<MatchQuestionDTO> questions, HttpServletRequest request) {
        try {
            // 获取当前用户（可选，用于记录匹配历史）
            LoginUser loginUser = null;
            try {
                loginUser = tokenService.getLoginUser(request);
            } catch (Exception e) {
                // 允许未登录用户使用匹配功能
            }
            
            List<PsyConsultant> consultants = questionService.matchConsultants(questions);
            
            // 可以在这里记录用户的匹配历史
            if (loginUser != null) {
                // TODO: 记录用户匹配历史
                logger.info("用户 {} 进行了咨询师匹配，匹配到 {} 位咨询师", 
                    loginUser.getUsername(), consultants.size());
            }
            
            return success(consultants);
        } catch (Exception e) {
            logger.error("咨询师匹配失败", e);
            return error("匹配失败，请稍后重试");
        }
    }

    /**
     * 获取所有问题（用于匹配流程）
     */
    @GetMapping("/all")
    public AjaxResult getAllQuestions() {
        List<PsyMatchQuestion> list = questionService.selectQuestionList(null);
        return success(list);
    }

    /**
     * 获取问题选项关联的咨询师（小程序端）
     */
    @GetMapping("/option/{optionId}/consultants")
    public AjaxResult getOptionConsultants(@PathVariable Long optionId) {
        try {
            List<Long> consultants = questionService.getOptionConsultants(optionId);
            return success(consultants);
        } catch (Exception e) {
            logger.error("获取选项关联咨询师失败", e);
            return error("获取咨询师信息失败");
        }
    }

    /**
     * 根据问题类型获取问题列表
     */
    @GetMapping("/type/{type}")
    public AjaxResult getQuestionsByType(@PathVariable String type) {
        try {
            // 这里可以根据问题类型筛选
            List<PsyMatchQuestion> list = questionService.selectQuestionList(null);
            // 可以添加类型过滤逻辑
            return success(list);
        } catch (Exception e) {
            logger.error("根据类型获取问题失败", e);
            return error("获取问题失败");
        }
    }

    /**
     * 获取推荐问题（小程序首页推荐）
     */
    @GetMapping("/recommend")
    public AjaxResult getRecommendQuestions() {
        try {
            List<PsyMatchQuestion> list = questionService.selectQuestionList(null);
            // 可以添加推荐逻辑，比如取前几个问题
            if (list.size() > 5) {
                list = list.subList(0, 5);
            }
            return success(list);
        } catch (Exception e) {
            logger.error("获取推荐问题失败", e);
            return error("获取推荐问题失败");
        }
    }

    /**
     * 快速匹配（简化版匹配）
     */
    @PostMapping("/quickMatch")
    public AjaxResult quickMatch(@RequestParam Long questionId, @RequestParam Long optionId) {
        try {
            List<Long> consultants = questionService.getOptionConsultants(optionId);
            return success(consultants);
        } catch (Exception e) {
            logger.error("快速匹配失败", e);
            return error("快速匹配失败");
        }
    }

    /**
     * 获取匹配统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getMatchStatistics() {
        try {
            // 可以返回一些统计信息，比如总问题数、总咨询师数等
            List<PsyMatchQuestion> questions = questionService.selectQuestionList(null);
            
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("totalQuestions", questions.size());
            statistics.put("totalOptions", questions.stream()
                .mapToInt(q -> q.getOptions() != null ? q.getOptions().size() : 0)
                .sum());
            
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取匹配统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 搜索问题（小程序端搜索）
     */
    @GetMapping("/search")
    public AjaxResult searchQuestions(@RequestParam String keyword) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return error("搜索关键词不能为空");
            }
            
            List<PsyMatchQuestion> list = questionService.selectQuestionList(keyword.trim());
            return success(list);
        } catch (Exception e) {
            logger.error("搜索问题失败", e);
            return error("搜索失败");
        }
    }

    /**
     * 获取热门问题
     */
    @GetMapping("/hot")
    public AjaxResult getHotQuestions() {
        try {
            // 可以根据使用频率或其他指标获取热门问题
            List<PsyMatchQuestion> list = questionService.selectQuestionList(null);
            // 这里可以添加热门排序逻辑
            return success(list);
        } catch (Exception e) {
            logger.error("获取热门问题失败", e);
            return error("获取热门问题失败");
        }
    }
}
