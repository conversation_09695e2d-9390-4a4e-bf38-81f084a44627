package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyCourseReview;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyCourseReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 课程评价表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/review")
public class PsyCourseReviewController extends BaseController {
    
    @Autowired
    private IPsyCourseReviewService reviewService;

    /**
     * 查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:review:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyCourseReview review) {
        startPage();
        List<PsyCourseReview> list = reviewService.selectReviewList(review);
        return getDataTable(list);
    }

    /**
     * 导出评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:review:export')")
    @Log(title = "评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyCourseReview review) {
        List<PsyCourseReview> list = reviewService.selectReviewList(review);
        ExcelUtil<PsyCourseReview> util = new ExcelUtil<PsyCourseReview>(PsyCourseReview.class);
        util.exportExcel(response, list, "评价数据");
    }

    /**
     * 获取评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:review:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(reviewService.selectReviewById(id));
    }

    /**
     * 获取评价详细信息（包含课程和用户信息）
     */
    @PreAuthorize("@ss.hasPermi('system:review:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(reviewService.selectReviewWithDetails(id));
    }

    /**
     * 新增评价
     */
    @PreAuthorize("@ss.hasPermi('system:review:add')")
    @Log(title = "评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyCourseReview review) {
        return toAjax(reviewService.insertReview(review));
    }

    /**
     * 修改评价
     */
    @PreAuthorize("@ss.hasPermi('system:review:edit')")
    @Log(title = "评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyCourseReview review) {
        return toAjax(reviewService.updateReview(review));
    }

    /**
     * 删除评价
     */
    @PreAuthorize("@ss.hasPermi('system:review:remove')")
    @Log(title = "评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(reviewService.deleteReviewByIds(ids));
    }

    /**
     * 根据课程ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:review:list')")
    @GetMapping("/course/{courseId}")
    public AjaxResult getReviewsByCourse(@PathVariable Long courseId) {
        List<PsyCourseReview> list = reviewService.selectReviewsByCourseId(courseId);
        return success(list);
    }

    /**
     * 根据用户ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:review:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getReviewsByUser(@PathVariable Long userId) {
        List<PsyCourseReview> list = reviewService.selectReviewsByUserId(userId);
        return success(list);
    }
}
