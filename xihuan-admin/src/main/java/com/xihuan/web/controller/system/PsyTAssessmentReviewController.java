package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTAssessmentReview;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTAssessmentReviewService;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 测评评价Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/assessment/review")
public class PsyTAssessmentReviewController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(PsyTAssessmentReviewController.class);

    @Autowired
    private IPsyTAssessmentReviewService reviewService;

    /**
     * 查询测评评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTAssessmentReview review) {
        startPage();
        List<PsyTAssessmentReview> list = reviewService.selectReviewList(review);
        return getDataTable(list);
    }

    /**
     * 导出测评评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:export')")
    @Log(title = "测评评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTAssessmentReview review) {
        List<PsyTAssessmentReview> list = reviewService.selectReviewList(review);
        ExcelUtil<PsyTAssessmentReview> util = new ExcelUtil<PsyTAssessmentReview>(PsyTAssessmentReview.class);
        util.exportExcel(response, list, "测评评价数据");
    }

    /**
     * 获取测评评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(reviewService.selectReviewById(id));
    }

    /**
     * 获取评价详细信息（包含量表和用户信息）
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(reviewService.selectReviewWithDetails(id));
    }

    /**
     * 新增测评评价
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:add')")
    @Log(title = "测评评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTAssessmentReview review) {
        return toAjax(reviewService.insertReview(review));
    }

    /**
     * 修改测评评价
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:edit')")
    @Log(title = "测评评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTAssessmentReview review) {
        return toAjax(reviewService.updateReview(review));
    }

    /**
     * 删除测评评价
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:remove')")
    @Log(title = "测评评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(reviewService.deleteReviewByIds(ids));
    }

    /**
     * 审核评价
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:audit')")
    @Log(title = "测评评价审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{id}")
    public AjaxResult auditReview(@PathVariable Long id, 
                                @RequestParam Integer status, 
                                @RequestParam(required = false) String auditRemark) {
        int result = reviewService.auditReview(id, status, auditRemark);
        return toAjax(result);
    }

    /**
     * 批量审核评价
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:audit')")
    @Log(title = "测评评价批量审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/batch")
    public AjaxResult batchAuditReviews(@RequestParam Long[] ids, 
                                      @RequestParam Integer status, 
                                      @RequestParam(required = false) String auditRemark) {
        int result = reviewService.batchAuditReviews(ids, status, auditRemark);
        return toAjax(result);
    }

    /**
     * 置顶评价
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:top')")
    @Log(title = "测评评价置顶", businessType = BusinessType.UPDATE)
    @PutMapping("/top/{id}")
    public AjaxResult topReview(@PathVariable Long id, @RequestParam Integer isTop) {
        int result = reviewService.topReview(id, isTop);
        return toAjax(result);
    }

    /**
     * 根据量表ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/scale/{scaleId}")
    public AjaxResult getReviewsByScale(@PathVariable Long scaleId) {
        List<PsyTAssessmentReview> list = reviewService.selectReviewsByScaleId(scaleId);
        return success(list);
    }

    /**
     * 根据用户ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getReviewsByUser(@PathVariable Long userId) {
        List<PsyTAssessmentReview> list = reviewService.selectReviewsByUserId(userId);
        return success(list);
    }

    /**
     * 查询待审核评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/pending")
    public AjaxResult getPendingReviews() {
        List<PsyTAssessmentReview> list = reviewService.selectPendingReviews();
        return success(list);
    }

    /**
     * 查询置顶评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/top/{scaleId}")
    public AjaxResult getTopReviews(@PathVariable Long scaleId) {
        List<PsyTAssessmentReview> list = reviewService.selectTopReviews(scaleId);
        return success(list);
    }

    /**
     * 查询热门评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/hot")
    public AjaxResult getHotReviews(@RequestParam(required = false) Long scaleId,
                                  @RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTAssessmentReview> list = reviewService.selectHotReviews(scaleId, limit);
        return success(list);
    }

    /**
     * 搜索评价
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/search")
    public TableDataInfo searchReviews(@RequestParam(required = false) String keyword,
                                     @RequestParam(required = false) Long scaleId,
                                     @RequestParam(required = false) Long userId,
                                     @RequestParam(required = false) Integer status,
                                     @RequestParam(required = false) Integer rating) {
        startPage();
        List<PsyTAssessmentReview> list = reviewService.searchReviews(keyword, scaleId, userId, status, rating);
        return getDataTable(list);
    }

    /**
     * 查询评价统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/statistics")
    public AjaxResult getReviewStatistics() {
        List<Map<String, Object>> stats = reviewService.selectReviewStats();
        return success(stats);
    }

    /**
     * 查询量表评价统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/statistics/scale/{scaleId}")
    public AjaxResult getScaleReviewStats(@PathVariable Long scaleId) {
        Map<String, Object> stats = reviewService.selectScaleReviewStats(scaleId);
        return success(stats);
    }

    /**
     * 查询用户评价统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/statistics/user/{userId}")
    public AjaxResult getUserReviewStats(@PathVariable Long userId) {
        Map<String, Object> stats = reviewService.selectUserReviewStats(userId);
        return success(stats);
    }

    /**
     * 查询评分分布
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/distribution/{scaleId}")
    public AjaxResult getRatingDistribution(@PathVariable Long scaleId) {
        List<Map<String, Object>> distribution = reviewService.selectRatingDistribution(scaleId);
        return success(distribution);
    }

    /**
     * 查询评价排行榜
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:list')")
    @GetMapping("/ranking")
    public AjaxResult getReviewRanking(@RequestParam String type, 
                                     @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> ranking = reviewService.selectReviewRanking(type, limit);
        return success(ranking);
    }

    /**
     * 导出评价统计报告
     */
    @PreAuthorize("@ss.hasPermi('system:assessmentReview:export')")
    @Log(title = "评价统计报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export/statistics")
    public void exportStatistics(HttpServletResponse response) {
        // 获取统计数据
        List<Map<String, Object>> stats = reviewService.selectReviewStats();

        // 创建工作簿
        try {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("评价统计报告");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("量表ID");
            headerRow.createCell(1).setCellValue("量表名称");
            headerRow.createCell(2).setCellValue("总评价数");
            headerRow.createCell(3).setCellValue("平均评分");
            headerRow.createCell(4).setCellValue("待审核数");
            headerRow.createCell(5).setCellValue("已通过数");
            headerRow.createCell(6).setCellValue("已拒绝数");

            // 填充数据
            int rowNum = 1;
            for (Map<String, Object> stat : stats) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(stat.get("scaleId") != null ? stat.get("scaleId").toString() : "");
                row.createCell(1).setCellValue(stat.get("scaleName") != null ? stat.get("scaleName").toString() : "");
                row.createCell(2).setCellValue(stat.get("totalReviews") != null ? stat.get("totalReviews").toString() : "0");
                row.createCell(3).setCellValue(stat.get("averageRating") != null ? stat.get("averageRating").toString() : "0");
                row.createCell(4).setCellValue(stat.get("pendingReviews") != null ? stat.get("pendingReviews").toString() : "0");
                row.createCell(5).setCellValue(stat.get("approvedReviews") != null ? stat.get("approvedReviews").toString() : "0");
                row.createCell(6).setCellValue(stat.get("rejectedReviews") != null ? stat.get("rejectedReviews").toString() : "0");
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=review_statistics.xlsx");

            // 输出到响应流
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            log.error("导出评价统计报告失败", e);
        }
    }
}
