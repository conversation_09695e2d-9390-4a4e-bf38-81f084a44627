package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.common.core.domain.entity.PsySearchRecord;
import com.xihuan.common.core.domain.entity.PsyHotSearch;
import com.xihuan.common.core.domain.entity.PsySearchSuggestion;
import com.xihuan.system.service.IPsySearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 搜索管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/search")
public class PsySearchController extends BaseController {
    
    @Autowired
    private IPsySearchService searchService;

    /**
     * 查询搜索记录列表
     */
    @PreAuthorize("@ss.hasPermi('search:record:list')")
    @GetMapping("/record/list")
    public TableDataInfo list(PsySearchRecord searchRecord) {
        startPage();
        List<PsySearchRecord> list = searchService.selectSearchRecordList(searchRecord);
        return getDataTable(list);
    }

    /**
     * 导出搜索记录列表
     */
    @PreAuthorize("@ss.hasPermi('search:record:export')")
    @Log(title = "搜索记录", businessType = BusinessType.EXPORT)
    @PostMapping("/record/export")
    public void export(HttpServletResponse response, PsySearchRecord searchRecord) {
        List<PsySearchRecord> list = searchService.selectSearchRecordList(searchRecord);
        ExcelUtil<PsySearchRecord> util = new ExcelUtil<PsySearchRecord>(PsySearchRecord.class);
        util.exportExcel(response, list, "搜索记录数据");
    }

    /**
     * 新增搜索记录
     */
    @PreAuthorize("@ss.hasPermi('search:record:add')")
    @Log(title = "搜索记录", businessType = BusinessType.INSERT)
    @PostMapping("/record")
    public AjaxResult add(@RequestBody PsySearchRecord searchRecord) {
        return toAjax(searchService.insertSearchRecord(searchRecord));
    }

    /**
     * 修改搜索记录
     */
    @PreAuthorize("@ss.hasPermi('search:record:edit')")
    @Log(title = "搜索记录", businessType = BusinessType.UPDATE)
    @PutMapping("/record")
    public AjaxResult edit(@RequestBody PsySearchRecord searchRecord) {
        return toAjax(searchService.updateSearchRecord(searchRecord));
    }

    /**
     * 删除搜索记录
     */
    @PreAuthorize("@ss.hasPermi('search:record:remove')")
    @Log(title = "搜索记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/record/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(searchService.deleteSearchRecordByIds(ids));
    }

    /**
     * 查询热门搜索列表
     */
    @PreAuthorize("@ss.hasPermi('search:hot:list')")
    @GetMapping("/hot/list")
    public TableDataInfo hotList(PsyHotSearch hotSearch) {
        startPage();
        List<PsyHotSearch> list = searchService.selectHotSearchList(hotSearch);
        return getDataTable(list);
    }

    /**
     * 导出热门搜索列表
     */
    @PreAuthorize("@ss.hasPermi('search:hot:export')")
    @Log(title = "热门搜索", businessType = BusinessType.EXPORT)
    @PostMapping("/hot/export")
    public void exportHot(HttpServletResponse response, PsyHotSearch hotSearch) {
        List<PsyHotSearch> list = searchService.selectHotSearchList(hotSearch);
        ExcelUtil<PsyHotSearch> util = new ExcelUtil<PsyHotSearch>(PsyHotSearch.class);
        util.exportExcel(response, list, "热门搜索数据");
    }

    /**
     * 新增热门搜索
     */
    @PreAuthorize("@ss.hasPermi('search:hot:add')")
    @Log(title = "热门搜索", businessType = BusinessType.INSERT)
    @PostMapping("/hot")
    public AjaxResult addHot(@RequestBody PsyHotSearch hotSearch) {
        return toAjax(searchService.insertHotSearch(hotSearch));
    }

    /**
     * 修改热门搜索
     */
    @PreAuthorize("@ss.hasPermi('search:hot:edit')")
    @Log(title = "热门搜索", businessType = BusinessType.UPDATE)
    @PutMapping("/hot")
    public AjaxResult editHot(@RequestBody PsyHotSearch hotSearch) {
        return toAjax(searchService.updateHotSearch(hotSearch));
    }

    /**
     * 删除热门搜索
     */
    @PreAuthorize("@ss.hasPermi('search:hot:remove')")
    @Log(title = "热门搜索", businessType = BusinessType.DELETE)
    @DeleteMapping("/hot/{ids}")
    public AjaxResult removeHot(@PathVariable Long[] ids) {
        return toAjax(searchService.deleteHotSearchByIds(ids));
    }

    /**
     * 查询搜索建议列表
     */
    @PreAuthorize("@ss.hasPermi('search:suggestion:list')")
    @GetMapping("/suggestion/list")
    public TableDataInfo suggestionList(PsySearchSuggestion searchSuggestion) {
        startPage();
        List<PsySearchSuggestion> list = searchService.selectSearchSuggestionList(searchSuggestion);
        return getDataTable(list);
    }

    /**
     * 导出搜索建议列表
     */
    @PreAuthorize("@ss.hasPermi('search:suggestion:export')")
    @Log(title = "搜索建议", businessType = BusinessType.EXPORT)
    @PostMapping("/suggestion/export")
    public void exportSuggestion(HttpServletResponse response, PsySearchSuggestion searchSuggestion) {
        List<PsySearchSuggestion> list = searchService.selectSearchSuggestionList(searchSuggestion);
        ExcelUtil<PsySearchSuggestion> util = new ExcelUtil<PsySearchSuggestion>(PsySearchSuggestion.class);
        util.exportExcel(response, list, "搜索建议数据");
    }

    /**
     * 新增搜索建议
     */
    @PreAuthorize("@ss.hasPermi('search:suggestion:add')")
    @Log(title = "搜索建议", businessType = BusinessType.INSERT)
    @PostMapping("/suggestion")
    public AjaxResult addSuggestion(@RequestBody PsySearchSuggestion searchSuggestion) {
        return toAjax(searchService.insertSearchSuggestion(searchSuggestion));
    }

    /**
     * 修改搜索建议
     */
    @PreAuthorize("@ss.hasPermi('search:suggestion:edit')")
    @Log(title = "搜索建议", businessType = BusinessType.UPDATE)
    @PutMapping("/suggestion")
    public AjaxResult editSuggestion(@RequestBody PsySearchSuggestion searchSuggestion) {
        return toAjax(searchService.updateSearchSuggestion(searchSuggestion));
    }

    /**
     * 删除搜索建议
     */
    @PreAuthorize("@ss.hasPermi('search:suggestion:remove')")
    @Log(title = "搜索建议", businessType = BusinessType.DELETE)
    @DeleteMapping("/suggestion/{ids}")
    public AjaxResult removeSuggestion(@PathVariable Long[] ids) {
        return toAjax(searchService.deleteSearchSuggestionByIds(ids));
    }
}
