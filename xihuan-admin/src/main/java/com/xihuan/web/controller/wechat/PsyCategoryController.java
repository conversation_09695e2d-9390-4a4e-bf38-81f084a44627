package com.xihuan.web.controller.wechat;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyCategory;
import com.xihuan.system.mapper.PsyCategoryMapper;
import com.xihuan.system.service.wxService.PsyCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/psy/category")
public class PsyCategoryController extends BaseController {

    @Autowired
    private PsyCategoryService categoryService;

    @Autowired
    private PsyCategoryMapper categoryMapper;

    /**
     * 获取分类列表(树形结构)
     */
    @GetMapping("/list")
    public AjaxResult list(PsyCategory category) {
        List<PsyCategory> categories = categoryService.selectCategoryList(category);
        return success(categories);
    }

    /**
     * 获取分类树形结构(包含产品)
     */
    @GetMapping("/tree")
    public AjaxResult tree() {
        List<PsyCategory> categories = categoryService.selectCategoryListWithProducts();
        return success(categories);
    }

    @GetMapping("/treeWithProducts")
    public AjaxResult getCategoryTreeWithProducts() {
        Map<String, Object> result = categoryService.selectCategoryTreeWithAllProducts();
        return success(result);
    }

    /**
     * 新增分类
     * @param category 分类实体（JSON格式）
     */
    @PostMapping
    public AjaxResult add(@RequestBody PsyCategory category) {
        return toAjax(categoryService.insertCategory(category));
    }

    /**
     * 修改分类信息
     * @param category 分类实体（需包含ID）
     */
    @PutMapping
    public AjaxResult edit(@RequestBody PsyCategory category) {
        return toAjax(categoryService.updateCategory(category));
    }

    /**
     * 获取子分类列表
     * @param parentId 父分类ID（0表示根分类）
     */
    @GetMapping("/children/{parentId}")
    public AjaxResult getChildren(@PathVariable Long parentId) {
        return success(categoryService.getChildrenCategories(parentId));
    }

    /**
     * 获取分类详情（增强版）
     * @param categoryId 分类ID（路径参数）
     */
    @GetMapping("/{categoryId}")
    public AjaxResult getDetail(@PathVariable Long categoryId) {
        return success(categoryService.getCategoryWithProducts(categoryId));
    }

    /**
     * 删除分类（补充校验逻辑）
     * @param categoryId 分类ID（路径参数）
     */
    @DeleteMapping("/{categoryId}")
    public AjaxResult remove(@PathVariable Long categoryId) {
        return toAjax(categoryService.deleteCategoryById(categoryId));
    }
}