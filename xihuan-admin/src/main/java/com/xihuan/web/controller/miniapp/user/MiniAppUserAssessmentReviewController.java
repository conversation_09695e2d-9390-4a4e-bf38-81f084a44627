package com.xihuan.web.controller.miniapp.user;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTAssessmentReview;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.IPsyTAssessmentReviewService;
import com.xihuan.system.service.IPsyTAssessmentRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 小程序测评评价Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/assessment")
public class MiniAppUserAssessmentReviewController extends BaseController {
    
    @Autowired
    private IPsyTAssessmentReviewService reviewService;
    
    @Autowired
    private IPsyTAssessmentRecordService recordService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 提交测评评价
     */
    @Log(title = "测评评价", businessType = BusinessType.INSERT)
    @PostMapping("/review")
    public AjaxResult submitReview(@RequestBody PsyTAssessmentReview review, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }

        // 检查用户是否已评价
        boolean reviewed = reviewService.checkUserReviewed(loginUser.getUserId(), review.getScaleId(), review.getRecordId());
        if (reviewed) {
            return error("您已评价过该测评");
        }

        // 验证测评记录是否存在且属于当前用户
        Map<String, Object> validation = reviewService.validateReviewPermission(
            loginUser.getUserId(), review.getScaleId(), review.getRecordId());
        if (!(Boolean) validation.get("success")) {
            return error(validation.get("message").toString());
        }

        // 设置评价信息
        review.setUserId(loginUser.getUserId());
        review.setCreateBy(loginUser.getUsername());
        review.setCreateTime(DateUtils.getNowDate());
        review.setDelFlag("0");
        review.setStatus(0); // 待审核状态
        review.setLikeCount(0);
        review.setReplyCount(0);
        review.setIsTop(0);

        // 保存评价
        int result = reviewService.insertReview(review);
        if (result > 0) {
            return success("评价提交成功，等待审核");
        } else {
            return error("提交评价失败");
        }
    }

    /**
     * 获取量表评价列表
     */
    @GetMapping("/reviews/{scaleId}")
    public AjaxResult getReviews(@PathVariable("scaleId") Long scaleId) {
        List<PsyTAssessmentReview> reviews = reviewService.selectReviewsByScaleId(scaleId);
        return success(reviews);
    }

    /**
     * 获取用户评价列表
     */
    @GetMapping("/reviews/user")
    public AjaxResult getUserReviews(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        List<PsyTAssessmentReview> reviews = reviewService.selectReviewsByUserId(loginUser.getUserId());
        return success(reviews);
    }

    /**
     * 获取评价详情
     */
    @GetMapping("/review/{id}")
    public AjaxResult getReviewDetail(@PathVariable Long id, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        PsyTAssessmentReview review = reviewService.selectReviewById(id);
        if (review == null) {
            return error("评价不存在");
        }
        
        // 验证评价归属（只有评价者本人可以查看详情）
        if (!review.getUserId().equals(loginUser.getUserId())) {
            return error("无权限查看该评价");
        }
        
        return success(review);
    }

    /**
     * 根据测评记录ID获取评价
     */
    @GetMapping("/review/record/{recordId}")
    public AjaxResult getReviewByRecord(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        PsyTAssessmentReview review = reviewService.selectReviewByRecordId(recordId);
        if (review == null) {
            return error("该测评记录暂无评价");
        }
        
        // 验证评价归属
        if (!review.getUserId().equals(loginUser.getUserId())) {
            return error("无权限查看该评价");
        }
        
        return success(review);
    }

    /**
     * 检查用户是否可以评价
     */
    @GetMapping("/review/check")
    public AjaxResult checkCanReview(@RequestParam Long scaleId, 
                                   @RequestParam Long recordId, 
                                   HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        Map<String, Object> result = reviewService.validateReviewPermission(
            loginUser.getUserId(), scaleId, recordId);
        return success(result);
    }

    /**
     * 获取量表评价统计
     */
    @GetMapping("/review/stats/{scaleId}")
    public AjaxResult getReviewStats(@PathVariable Long scaleId) {
        Map<String, Object> stats = reviewService.selectScaleReviewStats(scaleId);
        return success(stats);
    }

    /**
     * 获取用户评价统计
     */
    @GetMapping("/review/stats/user")
    public AjaxResult getUserReviewStats(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        Map<String, Object> stats = reviewService.selectUserReviewStats(loginUser.getUserId());
        return success(stats);
    }

    /**
     * 获取评价摘要
     */
    @GetMapping("/review/summary/{scaleId}")
    public AjaxResult getReviewSummary(@PathVariable Long scaleId) {
        Map<String, Object> summary = reviewService.getReviewSummary(scaleId);
        return success(summary);
    }

    /**
     * 获取热门评价
     */
    @GetMapping("/review/hot")
    public AjaxResult getHotReviews(@RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTAssessmentReview> hotReviews = reviewService.selectHotReviews(null, limit);
        return success(hotReviews);
    }

    /**
     * 搜索评价
     */
    @GetMapping("/review/search")
    public AjaxResult searchReviews(@RequestParam(required = false) String keyword,
                                  @RequestParam(required = false) Long scaleId,
                                  @RequestParam(required = false) Integer rating) {
        List<PsyTAssessmentReview> list = reviewService.searchReviews(keyword, scaleId, null, 1, rating);
        return success(list);
    }
}
