package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyCourse;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 心理咨询课程主表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/course")
public class PsyCourseController extends BaseController {
    
    @Autowired
    private IPsyCourseService courseService;

    /**
     * 查询课程列表
     */
    @PreAuthorize("@ss.hasPermi('system:course:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyCourse course) {
        startPage();
        List<PsyCourse> list = courseService.selectCourseList(course);
        return getDataTable(list);
    }

    /**
     * 导出课程列表
     */
    @PreAuthorize("@ss.hasPermi('system:course:export')")
    @Log(title = "课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyCourse course) {
        List<PsyCourse> list = courseService.selectCourseList(course);
        ExcelUtil<PsyCourse> util = new ExcelUtil<PsyCourse>(PsyCourse.class);
        util.exportExcel(response, list, "课程数据");
    }

    /**
     * 获取课程详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:course:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(courseService.selectCourseById(id));
    }

    /**
     * 获取课程详细信息（包含章节、讲师等）
     */
    @PreAuthorize("@ss.hasPermi('system:course:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(courseService.selectCourseWithDetails(id));
    }

    /**
     * 新增课程
     */
    @PreAuthorize("@ss.hasPermi('system:course:add')")
    @Log(title = "课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyCourse course) {
        return toAjax(courseService.insertCourse(course));
    }

    /**
     * 修改课程
     */
    @PreAuthorize("@ss.hasPermi('system:course:edit')")
    @Log(title = "课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyCourse course) {
        return toAjax(courseService.updateCourse(course));
    }

    /**
     * 删除课程
     */
    @PreAuthorize("@ss.hasPermi('system:course:remove')")
    @Log(title = "课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(courseService.deleteCourseByIds(ids));
    }

    /**
     * 发布课程
     */
    @PreAuthorize("@ss.hasPermi('system:course:edit')")
    @Log(title = "课程发布", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{id}")
    public AjaxResult publish(@PathVariable Long id) {
        return toAjax(courseService.publishCourse(id));
    }

    /**
     * 下架课程
     */
    @PreAuthorize("@ss.hasPermi('system:course:edit')")
    @Log(title = "课程下架", businessType = BusinessType.UPDATE)
    @PutMapping("/unpublish/{id}")
    public AjaxResult unpublish(@PathVariable Long id) {
        return toAjax(courseService.unpublishCourse(id));
    }

    /**
     * 更新课程统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:course:edit')")
    @Log(title = "更新课程统计", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatistics/{id}")
    public AjaxResult updateStatistics(@PathVariable Long id) {
        return toAjax(courseService.updateCourseStatistics(id));
    }

    /**
     * 更新课程评分信息
     */
    @PreAuthorize("@ss.hasPermi('system:course:edit')")
    @Log(title = "更新课程评分", businessType = BusinessType.UPDATE)
    @PutMapping("/updateRating/{id}")
    public AjaxResult updateRating(@PathVariable Long id) {
        return toAjax(courseService.updateCourseRating(id));
    }

    /**
     * 根据分类ID查询课程列表
     */
    @PreAuthorize("@ss.hasPermi('system:course:list')")
    @GetMapping("/category/{categoryId}")
    public AjaxResult getCoursesByCategory(@PathVariable Long categoryId) {
        List<PsyCourse> list = courseService.selectCoursesByCategoryId(categoryId);
        return success(list);
    }

    /**
     * 根据讲师ID查询课程列表
     */
    @PreAuthorize("@ss.hasPermi('system:course:list')")
    @GetMapping("/instructor/{instructorId}")
    public AjaxResult getCoursesByInstructor(@PathVariable Long instructorId) {
        List<PsyCourse> list = courseService.selectCoursesByInstructorId(instructorId);
        return success(list);
    }
}
