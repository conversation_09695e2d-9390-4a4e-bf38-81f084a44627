package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import com.xihuan.common.core.domain.dto.PsyTimeSlotDTO;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsyTimeSlotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 时间槽Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/timeSlot")
public class PsyTimeSlotController extends BaseController {
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;

    /**
     * 查询时间槽列表
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTimeSlot timeSlot) {
        startPage();
        List<PsyTimeSlot> list = timeSlotService.selectTimeSlotList(timeSlot);
        return getDataTable(list);
    }

    /**
     * 获取时间槽详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(timeSlotService.selectTimeSlotById(id));
    }

    /**
     * 查询咨询师在指定日期范围内的时间槽
     */
    @GetMapping("/counselor/{counselorId}")
    public AjaxResult getByCounselorAndDateRange(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<PsyTimeSlot> list = timeSlotService.selectSlotsByCounselorAndDateRange(counselorId, startDate, endDate);
        return AjaxResult.success(list);
    }

    /**
     * 获取格式化的时间槽数据（按日期和时间段分组）
     */
    @GetMapping("/formatted/{counselorId}")
    public AjaxResult getFormattedTimeSlots(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<PsyTimeSlotDTO> list = timeSlotService.getFormattedTimeSlots(counselorId, startDate, endDate);
        return AjaxResult.success(list);
    }

    /**
     * 查询指定日期的可用时间槽
     */
    @GetMapping("/available")
    public AjaxResult getAvailableSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam Long centerId,
            @RequestParam(required = false) Long counselorId) {
        List<PsyTimeSlot> list = timeSlotService.selectAvailableSlotsByDate(date, centerId, counselorId);
        return AjaxResult.success(list);
    }

    /**
     * 查询公开时间槽
     */
    @GetMapping("/public")
    public AjaxResult getPublicSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam Long centerId) {
        List<PsyTimeSlot> list = timeSlotService.selectPublicSlots(startDate, endDate, centerId);
        return AjaxResult.success(list);
    }

    /**
     * 新增时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:add')")
    @Log(title = "时间槽", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTimeSlot timeSlot) {
        return toAjax(timeSlotService.insertTimeSlot(timeSlot));
    }

    /**
     * 修改时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:edit')")
    @Log(title = "时间槽", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTimeSlot timeSlot) {
        return toAjax(timeSlotService.updateTimeSlot(timeSlot));
    }

    /**
     * 批量更新时间槽状态
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:edit')")
    @Log(title = "时间槽", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestBody List<Long> slotIds, @RequestParam Integer status) {
        return toAjax(timeSlotService.batchUpdateSlotStatus(slotIds, status));
    }

    /**
     * 删除时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:remove')")
    @Log(title = "时间槽", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(timeSlotService.deleteTimeSlotByIds(ids));
    }

    /**
     * 为咨询师生成指定日期范围的时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:generate')")
    @Log(title = "时间槽", businessType = BusinessType.INSERT)
    @PostMapping("/generate/{counselorId}")
    public AjaxResult generateSlots(
            @PathVariable("counselorId") Long counselorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        int count = timeSlotService.generateSlotsForCounselor(counselorId, startDate, endDate);
        return AjaxResult.success("成功生成 " + count + " 个时间槽");
    }

    /**
     * 为所有咨询师生成指定日期范围的时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:generateAll')")
    @Log(title = "时间槽", businessType = BusinessType.INSERT)
    @PostMapping("/generateAll")
    public AjaxResult generateSlotsForAll(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        int count = timeSlotService.generateSlotsForAllCounselors(startDate, endDate);
        return AjaxResult.success("成功生成 " + count + " 个时间槽");
    }

    /**
     * 清理过期的时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:clean')")
    @Log(title = "时间槽", businessType = BusinessType.DELETE)
    @DeleteMapping("/cleanExpired")
    public AjaxResult cleanExpiredSlots(@RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate beforeDate) {
        int count = timeSlotService.cleanExpiredSlots(beforeDate);
        return AjaxResult.success("成功清理 " + count + " 个过期时间槽");
    }

    /**
     * 更新过期时间槽的状态
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlot:updateExpired')")
    @Log(title = "时间槽", businessType = BusinessType.UPDATE)
    @PutMapping("/updateExpired")
    public AjaxResult updateExpiredSlotStatus() {
        int count = timeSlotService.updateExpiredSlotStatus();
        return AjaxResult.success("成功更新 " + count + " 个过期时间槽状态");
    }
}
