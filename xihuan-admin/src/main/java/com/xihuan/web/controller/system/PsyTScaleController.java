package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 量表管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/scale")
public class PsyTScaleController extends BaseController {
    
    @Autowired
    private IPsyTScaleService scaleService;

    /**
     * 查询量表列表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTScale scale) {
        startPage();
        List<PsyTScale> list = scaleService.selectScaleList(scale);
        return getDataTable(list);
    }

    /**
     * 导出量表列表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:export')")
    @Log(title = "量表管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTScale scale) {
        List<PsyTScale> list = scaleService.selectScaleList(scale);
        ExcelUtil<PsyTScale> util = new ExcelUtil<PsyTScale>(PsyTScale.class);
        util.exportExcel(response, list, "量表数据");
    }

    /**
     * 获取量表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:scale:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(scaleService.selectScaleById(id));
    }

    /**
     * 获取量表详情（包含题目、分量表等信息）
     */
    @PreAuthorize("@ss.hasPermi('system:scale:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(scaleService.selectScaleWithDetails(id));
    }

    /**
     * 新增量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:add')")
    @Log(title = "量表管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody PsyTScale scale) {
        // 检查量表编码是否重复
        if (scaleService.selectScaleByCode(scale.getCode()) != null) {
            return error("量表编码已存在");
        }
        return toAjax(scaleService.insertScale(scale));
    }

    /**
     * 修改量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:edit')")
    @Log(title = "量表管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody PsyTScale scale) {
        // 检查量表编码是否重复（排除自己）
        PsyTScale existScale = scaleService.selectScaleByCode(scale.getCode());
        if (existScale != null && !existScale.getId().equals(scale.getId())) {
            return error("量表编码已存在");
        }
        return toAjax(scaleService.updateScale(scale));
    }

    /**
     * 删除量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:remove')")
    @Log(title = "量表管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(scaleService.deleteScaleByIds(ids));
    }

    /**
     * 发布量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:publish')")
    @Log(title = "量表管理", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{id}")
    public AjaxResult publish(@PathVariable Long id) {
        // 验证量表配置完整性
        Map<String, Object> validation = scaleService.validateScaleConfig(id);
        if (!(Boolean) validation.get("valid")) {
            return error("量表配置不完整，无法发布：" + validation.get("errors"));
        }
        return toAjax(scaleService.publishScale(id));
    }

    /**
     * 下架量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:offline')")
    @Log(title = "量表管理", businessType = BusinessType.UPDATE)
    @PutMapping("/offline/{id}")
    public AjaxResult offline(@PathVariable Long id) {
        return toAjax(scaleService.offlineScale(id));
    }

    /**
     * 复制量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:copy')")
    @Log(title = "量表管理", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{id}")
    public AjaxResult copy(@PathVariable Long id, @RequestParam String newScaleName, @RequestParam String newScaleCode) {
        // 检查新编码是否重复
        if (scaleService.selectScaleByCode(newScaleCode) != null) {
            return error("新量表编码已存在");
        }
        return toAjax(scaleService.copyScale(id, newScaleName, newScaleCode));
    }

    /**
     * 验证量表配置
     */
    @PreAuthorize("@ss.hasPermi('system:scale:validate')")
    @GetMapping("/validate/{id}")
    public AjaxResult validate(@PathVariable Long id) {
        Map<String, Object> result = scaleService.validateScaleConfig(id);
        return success(result);
    }

    /**
     * 查询量表统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:scale:stats')")
    @GetMapping("/stats")
    public AjaxResult stats() {
        Map<String, Object> stats = scaleService.selectScaleStats();
        return success(stats);
    }

    /**
     * 查询量表测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:scale:stats')")
    @GetMapping("/stats/{id}")
    public AjaxResult scaleStats(@PathVariable Long id) {
        Map<String, Object> stats = scaleService.selectScaleTestStats(id);
        return success(stats);
    }

    /**
     * 查询启用的量表列表
     */
    @GetMapping("/enabled")
    public AjaxResult enabledScales() {
        List<PsyTScale> list = scaleService.selectEnabledScales();
        return success(list);
    }

    /**
     * 查询热门量表
     */
    @GetMapping("/hot")
    public AjaxResult hotScales(@RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectHotScales(limit);
        return success(list);
    }

    /**
     * 查询最新量表
     */
    @GetMapping("/latest")
    public AjaxResult latestScales(@RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectLatestScales(limit);
        return success(list);
    }

    /**
     * 根据分类查询量表
     */
    @GetMapping("/category/{categoryId}")
    public AjaxResult scalesByCategory(@PathVariable Integer categoryId) {
        List<PsyTScale> list = scaleService.selectScalesByCategory(categoryId);
        return success(list);
    }

    /**
     * 搜索量表
     */
    @GetMapping("/search")
    public AjaxResult searchScales(@RequestParam(required = false) String keyword,
                                  @RequestParam(required = false) Integer categoryId,
                                  @RequestParam(required = false) Integer status,
                                  @RequestParam(required = false) Long enterpriseId) {
        List<PsyTScale> list = scaleService.searchScales(keyword, categoryId, status, enterpriseId);
        return success(list);
    }

    /**
     * 导入量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:import')")
    @Log(title = "量表管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<PsyTScale> util = new ExcelUtil<PsyTScale>(PsyTScale.class);
        List<PsyTScale> scaleList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = scaleService.importScale(scaleList, updateSupport, operName);
        return success(message);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PsyTScale> util = new ExcelUtil<PsyTScale>(PsyTScale.class);
        util.importTemplateExcel(response, "量表数据");
    }

    /**
     * 批量更新量表状态
     */
    @PreAuthorize("@ss.hasPermi('system:scale:edit')")
    @Log(title = "量表管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestParam Long[] ids, @RequestParam Integer status) {
        return toAjax(scaleService.batchUpdateScaleStatus(ids, status));
    }

    /**
     * 增加查看次数
     */
    @PostMapping("/view/{id}")
    public AjaxResult increaseViewCount(@PathVariable Long id) {
        return toAjax(scaleService.increaseViewCount(id));
    }

    /**
     * 增加搜索次数
     */
    @PostMapping("/search/{id}")
    public AjaxResult increaseSearchCount(@PathVariable Long id) {
        return toAjax(scaleService.increaseSearchCount(id));
    }
}
