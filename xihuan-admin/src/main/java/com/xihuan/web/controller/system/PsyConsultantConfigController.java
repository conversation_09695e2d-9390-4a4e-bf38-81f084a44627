package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantCenterConfig;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsyConsultantCenterConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 咨询师配置Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/consultant/config")
public class PsyConsultantConfigController extends BaseController {
    
    @Autowired
    private IPsyConsultantCenterConfigService configService;

    /**
     * 查询咨询师配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyConsultantCenterConfig config) {
        startPage();
        List<PsyConsultantCenterConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    /**
     * 获取咨询师配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(configService.selectConfigById(id));
    }

    /**
     * 根据咨询师ID查询配置
     */
    @GetMapping("/consultant/{consultantId}")
    public AjaxResult getByConsultantId(@PathVariable("consultantId") Long consultantId) {
        List<PsyConsultantCenterConfig> list = configService.selectConfigsByConsultantId(consultantId);
        return success(list);
    }

    /**
     * 根据咨询师ID和中心ID查询配置
     */
    @GetMapping("/consultant/{consultantId}/center/{centerId}")
    public AjaxResult getByConsultantAndCenter(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId) {
        PsyConsultantCenterConfig config = configService.selectConfigByConsultantAndCenter(consultantId, centerId);
        return success(config);
    }

    /**
     * 新增咨询师配置
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:add')")
    @Log(title = "咨询师配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyConsultantCenterConfig config) {
        config.setCreateBy(getUsername());
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改咨询师配置
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "咨询师配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyConsultantCenterConfig config) {
        config.setUpdateBy(getUsername());
        return toAjax(configService.updateConfig(config));
    }

    /**
     * 删除咨询师配置
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:remove')")
    @Log(title = "咨询师配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(configService.deleteConfigByIds(ids));
    }

    /**
     * 设置咨询师到店时间
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "设置到店时间", businessType = BusinessType.UPDATE)
    @PutMapping("/arrivalTime/{consultantId}/{centerId}")
    public AjaxResult setArrivalTime(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId,
            @RequestParam("hours") Double hours) {
        int result = configService.setArrivalTime(consultantId, centerId, hours);
        return toAjax(result);
    }

    /**
     * 设置咨询师是否启用到店时间过滤
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:edit')")
    @Log(title = "设置到店时间过滤", businessType = BusinessType.UPDATE)
    @PutMapping("/arrivalFilter/{consultantId}/{centerId}")
    public AjaxResult setArrivalFilterEnabled(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId,
            @RequestParam("enabled") boolean enabled) {
        int result = configService.setArrivalFilterEnabled(consultantId, centerId, enabled);
        String action = enabled ? "启用" : "禁用";
        return result > 0 ? 
            AjaxResult.success("成功" + action + "到店时间过滤功能") : 
            AjaxResult.error("操作失败");
    }

    /**
     * 获取咨询师到店时间
     */
    @GetMapping("/arrivalTime/{consultantId}/{centerId}")
    public AjaxResult getArrivalTime(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId) {
        Double arrivalTime = configService.getArrivalTime(consultantId, centerId);
        return success(arrivalTime);
    }

    /**
     * 检查咨询师是否启用到店时间过滤
     */
    @GetMapping("/arrivalFilter/{consultantId}/{centerId}")
    public AjaxResult isArrivalFilterEnabled(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId) {
        boolean enabled = configService.isArrivalFilterEnabled(consultantId, centerId);
        return success(enabled);
    }

    /**
     * 为咨询师创建默认配置
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:add')")
    @Log(title = "创建默认配置", businessType = BusinessType.INSERT)
    @PostMapping("/createDefault/{consultantId}/{centerId}")
    public AjaxResult createDefaultConfig(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId) {
        int result = configService.createDefaultConfig(consultantId, centerId);
        return toAjax(result);
    }

    /**
     * 批量为咨询师创建默认配置
     */
    @PreAuthorize("@ss.hasPermi('system:consultant:add')")
    @Log(title = "批量创建默认配置", businessType = BusinessType.INSERT)
    @PostMapping("/batchCreateDefault/{centerId}")
    public AjaxResult batchCreateDefaultConfig(
            @PathVariable("centerId") Long centerId,
            @RequestBody List<Long> consultantIds) {
        int result = configService.batchCreateDefaultConfig(consultantIds, centerId);
        return AjaxResult.success("成功为 " + result + " 个咨询师创建默认配置");
    }

    /**
     * 咨询师个人设置页面 - 获取自己的配置
     */
    @GetMapping("/my/{centerId}")
    public AjaxResult getMyConfig(@PathVariable("centerId") Long centerId) {
        // 这里需要从当前登录用户获取咨询师ID
        // Long consultantId = getCurrentConsultantId();
        // 暂时使用参数传递
        return success("请在URL中指定咨询师ID");
    }

    /**
     * 咨询师个人设置 - 更新自己的配置
     */
    @PutMapping("/my/{consultantId}/{centerId}")
    public AjaxResult updateMyConfig(
            @PathVariable("consultantId") Long consultantId,
            @PathVariable("centerId") Long centerId,
            @RequestBody PsyConsultantCenterConfig config) {
        
        // 验证是否为当前登录咨询师
        // if (!isCurrentConsultant(consultantId)) {
        //     return AjaxResult.error("只能修改自己的配置");
        // }
        
        config.setConsultantId(consultantId);
        config.setCenterId(centerId);
        config.setUpdateBy(getUsername());
        
        return toAjax(configService.updateConfig(config));
    }
}
