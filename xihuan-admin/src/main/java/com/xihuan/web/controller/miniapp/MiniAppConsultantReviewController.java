package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantReview;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.IPsyConsultantReviewService;
import com.xihuan.system.service.IPsyConsultationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序咨询师评价Controller（用户端）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/consultant/review")
public class MiniAppConsultantReviewController extends BaseController {
    
    @Autowired
    private IPsyConsultantReviewService reviewService;
    
    @Autowired
    private IPsyConsultationRecordService recordService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 提交咨询评价
     */
    @PostMapping("/submit")
    public AjaxResult submitReview(@RequestBody PsyConsultantReview review, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            // 检查是否已评价
            boolean reviewed = reviewService.checkUserReviewed(loginUser.getUserId(), review.getRecordId());
            if (reviewed) {
                return error("您已评价过该咨询");
            }
            
            // 检查是否有权限评价
            if (!recordService.canRate(review.getRecordId(), loginUser.getUserId())) {
                return error("无权限评价或咨询未完成");
            }
            
            // 设置评价信息
            review.setUserId(loginUser.getUserId());
            review.setCreateBy(loginUser.getUsername());
            review.setReviewTime(DateUtils.getNowDate());
            
            int result = reviewService.insertReview(review);
            if (result > 0) {
                // 更新咨询师评分信息
                reviewService.updateConsultantRating(review.getConsultantId());
                return success("评价提交成功");
            } else {
                return error("提交评价失败");
            }
        } catch (Exception e) {
            logger.error("提交评价失败", e);
            return error("提交评价失败");
        }
    }

    /**
     * 获取我的评价列表
     */
    @GetMapping("/myReviews")
    public AjaxResult getMyReviews(HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            List<PsyConsultantReview> reviews = reviewService.selectReviewsByUserId(loginUser.getUserId());
            return success(reviews);
        } catch (Exception e) {
            logger.error("获取我的评价失败", e);
            return error("获取评价列表失败");
        }
    }

    /**
     * 获取咨询师评价列表
     */
    @GetMapping("/consultant/{consultantId}")
    public AjaxResult getConsultantReviews(@PathVariable Long consultantId) {
        try {
            List<PsyConsultantReview> reviews = reviewService.selectApprovedReviews(consultantId);
            return success(reviews);
        } catch (Exception e) {
            logger.error("获取咨询师评价失败", e);
            return error("获取评价列表失败");
        }
    }

    /**
     * 获取评价详情
     */
    @GetMapping("/{reviewId}")
    public AjaxResult getReviewDetails(@PathVariable Long reviewId, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            PsyConsultantReview review = reviewService.selectReviewWithDetails(reviewId);
            if (review == null) {
                return error("评价不存在");
            }
            
            // 验证评价归属（只有评价者本人可以查看详情）
            if (!review.getUserId().equals(loginUser.getUserId())) {
                return error("无权限查看该评价");
            }
            
            return success(review);
        } catch (Exception e) {
            logger.error("获取评价详情失败", e);
            return error("获取评价详情失败");
        }
    }

    /**
     * 检查是否可以评价咨询记录
     */
    @GetMapping("/canRate/{recordId}")
    public AjaxResult checkCanRate(@PathVariable Long recordId, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            boolean canRate = recordService.canRate(recordId, loginUser.getUserId());
            boolean hasReviewed = false;
            
            if (canRate) {
                // 检查是否已评价
                hasReviewed = reviewService.checkUserReviewed(loginUser.getUserId(), recordId);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("canRate", canRate && !hasReviewed);
            result.put("hasReviewed", hasReviewed);
            
            return success(result);
        } catch (Exception e) {
            logger.error("检查评价权限失败", e);
            return error("检查评价权限失败");
        }
    }

    /**
     * 获取咨询师评价统计
     */
    @GetMapping("/statistics/{consultantId}")
    public AjaxResult getReviewStatistics(@PathVariable Long consultantId) {
        try {
            // 计算平均评分
            java.math.BigDecimal avgRating = reviewService.calculateAverageRating(consultantId);
            
            // 统计评价数量
            int totalCount = reviewService.countReviewsByConsultantId(consultantId);
            
            // 获取评分分布
            Map<String, Object> distribution = reviewService.getRatingDistribution(consultantId);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("avgRating", avgRating);
            statistics.put("totalCount", totalCount);
            statistics.put("distribution", distribution);
            
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取评价统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取最新评价
     */
    @GetMapping("/latest")
    public AjaxResult getLatestReviews(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            PsyConsultantReview queryReview = new PsyConsultantReview();
            queryReview.setAdminCheck(1); // 只获取已审核通过的评价
            
            List<PsyConsultantReview> reviews = reviewService.selectReviewList(queryReview);
            
            // 限制返回数量
            if (reviews.size() > limit) {
                reviews = reviews.subList(0, limit);
            }
            
            return success(reviews);
        } catch (Exception e) {
            logger.error("获取最新评价失败", e);
            return error("获取最新评价失败");
        }
    }

    /**
     * 获取高分评价
     */
    @GetMapping("/highRating")
    public AjaxResult getHighRatingReviews(@RequestParam(defaultValue = "4.0") Double minRating,
                                         @RequestParam(defaultValue = "10") Integer limit) {
        try {
            PsyConsultantReview queryReview = new PsyConsultantReview();
            queryReview.setAdminCheck(1); // 只获取已审核通过的评价
            
            List<PsyConsultantReview> allReviews = reviewService.selectReviewList(queryReview);
            
            // 过滤高分评价
            List<PsyConsultantReview> highRatingReviews = allReviews.stream()
                .filter(review -> review.getRating() != null && 
                    review.getRating().doubleValue() >= minRating)
                .limit(limit)
                .collect(java.util.stream.Collectors.toList());
            
            return success(highRatingReviews);
        } catch (Exception e) {
            logger.error("获取高分评价失败", e);
            return error("获取高分评价失败");
        }
    }

    /**
     * 根据咨询记录ID获取评价
     */
    @GetMapping("/record/{recordId}")
    public AjaxResult getReviewByRecord(@PathVariable Long recordId, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            PsyConsultantReview review = reviewService.selectReviewByRecordId(recordId);
            if (review == null) {
                return error("该咨询记录暂无评价");
            }
            
            // 验证评价归属
            if (!review.getUserId().equals(loginUser.getUserId())) {
                return error("无权限查看该评价");
            }
            
            return success(review);
        } catch (Exception e) {
            logger.error("根据咨询记录获取评价失败", e);
            return error("获取评价失败");
        }
    }

    /**
     * 获取评价类型统计
     */
    @GetMapping("/typeStatistics/{consultantId}")
    public AjaxResult getReviewTypeStatistics(@PathVariable Long consultantId) {
        try {
            List<PsyConsultantReview> reviews = reviewService.selectReviewsByConsultantId(consultantId);
            
            Map<String, Long> typeCount = new HashMap<>();
            typeCount.put("语音咨询", reviews.stream().filter(r -> "语音咨询".equals(r.getConsultType())).count());
            typeCount.put("视频咨询", reviews.stream().filter(r -> "视频咨询".equals(r.getConsultType())).count());
            typeCount.put("线下咨询", reviews.stream().filter(r -> "线下咨询".equals(r.getConsultType())).count());
            
            return success(typeCount);
        } catch (Exception e) {
            logger.error("获取评价类型统计失败", e);
            return error("获取统计信息失败");
        }
    }
}
