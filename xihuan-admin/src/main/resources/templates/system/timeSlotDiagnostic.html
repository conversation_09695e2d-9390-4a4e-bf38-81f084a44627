<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间槽诊断工具</title>
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/animate.css}" rel="stylesheet"/>
    <link th:href="@{/css/style.css}" rel="stylesheet"/>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>时间槽系统诊断工具</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12">
                            <h4>系统状态</h4>
                            <div id="systemStatus" class="alert alert-info">
                                <i class="fa fa-spinner fa-spin"></i> 正在加载系统状态...
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-sm-6">
                            <h4>诊断操作</h4>
                            <div class="btn-group-vertical" style="width: 100%;">
                                <button type="button" class="btn btn-primary" onclick="diagnoseSystem()">
                                    <i class="fa fa-search"></i> 执行完整诊断
                                </button>
                                <button type="button" class="btn btn-success" onclick="generateTimeSlots()">
                                    <i class="fa fa-play"></i> 手动生成时间槽
                                </button>
                                <button type="button" class="btn btn-warning" onclick="initTimeRanges()">
                                    <i class="fa fa-cog"></i> 初始化时间段配置
                                </button>
                                <button type="button" class="btn btn-info" onclick="refreshStatus()">
                                    <i class="fa fa-refresh"></i> 刷新状态
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-sm-6">
                            <h4>操作日志</h4>
                            <div id="operationLog" style="height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9;">
                                <p><span class="text-muted">[系统]</span> 诊断工具已加载</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" style="margin-top: 20px;">
                        <div class="col-sm-12">
                            <h4>诊断结果</h4>
                            <div id="diagnosticResult" style="display: none;">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <pre id="diagnosticData" style="max-height: 400px; overflow-y: auto;"></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script th:src="@{/js/jquery.min.js}"></script>
<script th:src="@{/js/bootstrap.min.js}"></script>

<script>
$(document).ready(function() {
    refreshStatus();
});

function addLog(message, type = 'info') {
    const logDiv = $('#operationLog');
    const timestamp = new Date().toLocaleTimeString();
    const typeClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
    
    logDiv.append(`<p><span class="text-muted">[${timestamp}]</span> <span class="${typeClass}">${message}</span></p>`);
    logDiv.scrollTop(logDiv[0].scrollHeight);
}

function refreshStatus() {
    addLog('正在获取系统状态...');
    
    $.get('/system/timeSlotDiagnostic/status')
        .done(function(response) {
            if (response.code === 200) {
                const data = response.data;
                $('#systemStatus').html(`
                    <i class="fa fa-check-circle text-success"></i>
                    时间段配置: ${data.timeRangeCount} 个 | 
                    咨询师数量: ${data.consultantCount} 个
                `);
                addLog('系统状态获取成功', 'success');
            } else {
                $('#systemStatus').html(`<i class="fa fa-exclamation-triangle text-warning"></i> 获取状态失败: ${response.msg}`);
                addLog('获取系统状态失败: ' + response.msg, 'error');
            }
        })
        .fail(function() {
            $('#systemStatus').html('<i class="fa fa-times-circle text-danger"></i> 无法连接到服务器');
            addLog('无法连接到服务器', 'error');
        });
}

function diagnoseSystem() {
    addLog('开始执行系统诊断...');
    
    $.get('/system/timeSlotDiagnostic/diagnose')
        .done(function(response) {
            if (response.code === 200) {
                addLog('系统诊断完成', 'success');
                $('#diagnosticResult').show();
                $('#diagnosticData').text(JSON.stringify(response.data, null, 2));
                
                // 更新状态显示
                const data = response.data;
                $('#systemStatus').html(`
                    <i class="fa fa-check-circle text-success"></i>
                    时间段: ${data.timeRangeCount} 个 | 
                    咨询师: ${data.consultantCount} 个 | 
                    排班记录: ${data.sampleScheduleCount} 个 | 
                    测试时间槽: ${data.testSlotGeneration} 个
                `);
            } else {
                addLog('系统诊断失败: ' + response.msg, 'error');
                $('#diagnosticResult').show();
                $('#diagnosticData').text(JSON.stringify(response, null, 2));
            }
        })
        .fail(function() {
            addLog('诊断请求失败', 'error');
        });
}

function generateTimeSlots() {
    addLog('开始手动生成时间槽...');
    
    $.post('/system/timeSlotDiagnostic/generateTimeSlots')
        .done(function(response) {
            if (response.code === 200) {
                addLog('时间槽生成任务已触发，请查看服务器日志了解详细结果', 'success');
            } else {
                addLog('生成时间槽失败: ' + response.msg, 'error');
            }
        })
        .fail(function() {
            addLog('生成时间槽请求失败', 'error');
        });
}

function initTimeRanges() {
    addLog('开始初始化时间段配置...');
    
    $.post('/system/timeSlotDiagnostic/initTimeRanges')
        .done(function(response) {
            if (response.code === 200) {
                addLog('时间段配置初始化成功: ' + response.msg, 'success');
                refreshStatus();
            } else {
                addLog('初始化时间段配置失败: ' + response.msg, 'error');
            }
        })
        .fail(function() {
            addLog('初始化时间段配置请求失败', 'error');
        });
}
</script>

</body>
</html>
