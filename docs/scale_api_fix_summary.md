# 量表列表API数据不全问题修复总结

## 🎯 问题描述

`/miniapp/user/assessment/scale/list` 接口返回的数据中很多字段为 `null`：

```json
{
  "author": null,
  "categories": null,
  "categoryId": null,
  "categoryName": null,
  "coverImage": null,
  "scaleName": null,
  "scaleCode": null,
  "tags": null,
  // ... 其他字段也是null
}
```

## 🔍 问题分析

1. **字段名不匹配**：`PsyAssessmentScale` 实体类字段名与 `ScaleDTO` 字段名不一致
2. **BeanUtils.copyBeanProp** 无法正确复制不同名称的字段
3. **缺少字段转换逻辑**：某些字段需要计算或转换

## ✅ 修复方案

### 1. 字段映射修复

| ScaleDTO字段 | PsyAssessmentScale字段 | 修复方式 |
|-------------|----------------------|---------|
| scaleName | name | 直接映射 |
| scaleCode | code | 直接映射 |
| instruction | introduction | 直接映射 |
| categoryId | categoryId | 类型转换(Integer→Long) |
| author | createBy | 字段名映射 |
| version | - | 默认值"1.0" |
| timeLimit | duration | 解析字符串提取数字 |
| difficultyLevel | questionCount | 根据题目数量计算 |
| coverImage | imageUrl | 字段名映射 |
| tags | searchKeywords | 字段名映射 |
| testCount | searchCount | 字段名映射 |

### 2. 核心修复代码

```java
// 手动设置字段映射（解决字段名不匹配问题）
dto.setId(scale.getId());
dto.setScaleName(scale.getName());
dto.setScaleCode(scale.getCode());
dto.setDescription(scale.getDescription());
dto.setInstruction(scale.getIntroduction());
dto.setCategoryId(scale.getCategoryId() != null ? scale.getCategoryId().longValue() : null);
dto.setAuthor(scale.getCreateBy());
dto.setVersion("1.0");
dto.setQuestionCount(scale.getQuestionCount());
dto.setTimeLimit(parseTimeLimit(scale.getDuration()));
dto.setDifficultyLevel(calculateDifficultyLevel(scale.getQuestionCount()));
dto.setPrice(scale.getPrice());
dto.setIsFree(scale.getPayMode() != null && scale.getPayMode() == 0 ? 1 : 0);
dto.setCoverImage(scale.getImageUrl());
dto.setTags(scale.getSearchKeywords());
dto.setStatus(scale.getStatus());
dto.setViewCount(scale.getViewCount());
dto.setTestCount(scale.getSearchCount());
```

### 3. 辅助方法

```java
/**
 * 解析时长为时间限制（分钟）
 */
private Integer parseTimeLimit(String duration) {
    if (StringUtils.isEmpty(duration)) {
        return null;
    }
    try {
        String numStr = duration.replaceAll("[^0-9]", "");
        if (StringUtils.isNotEmpty(numStr)) {
            return Integer.parseInt(numStr);
        }
    } catch (Exception e) {
        logger.warn("解析时长失败: {}", duration);
    }
    return null;
}

/**
 * 根据题目数量计算难度等级
 */
private Integer calculateDifficultyLevel(Integer questionCount) {
    if (questionCount == null) {
        return 2; // 默认中等
    }
    if (questionCount <= 20) {
        return 1; // 简单
    } else if (questionCount <= 50) {
        return 2; // 中等
    } else {
        return 3; // 困难
    }
}

/**
 * 获取难度描述
 */
private String getDifficultyDesc(Integer difficultyLevel) {
    if (difficultyLevel == null) {
        return "未知";
    }
    switch (difficultyLevel) {
        case 1: return "简单";
        case 2: return "中等";
        case 3: return "困难";
        default: return "未知";
    }
}

/**
 * 获取状态描述
 */
private String getStatusDesc(Integer status) {
    if (status == null) {
        return "未知";
    }
    switch (status) {
        case 0: return "停用";
        case 1: return "已发布";
        case 2: return "草稿";
        default: return "未知";
    }
}

/**
 * 获取价格描述
 */
private String getPriceDesc(BigDecimal price, Integer payMode) {
    if (payMode != null && payMode == 0) {
        return "免费";
    }
    if (price == null || price.compareTo(BigDecimal.ZERO) == 0) {
        return "免费";
    }
    return "¥" + price.toString();
}
```

## 🧪 验证方法

### 1. 数据库验证
```bash
mysql -u username -p database_name < sql/test_scale_data_mapping.sql
```

### 2. API测试
```bash
# 测试量表列表接口
curl "http://localhost:8080/miniapp/user/assessment/scale/list?pageNum=1&pageSize=10"

# 测试量表详情接口
curl "http://localhost:8080/miniapp/user/assessment/scale/8"
```

### 3. 期望结果
```json
{
  "id": 8,
  "scaleName": "PRCA-24人际交往焦虑量表",
  "scaleCode": "PRCA24",
  "description": "PRCA-24表可能是评估一般交际恐惧的最佳工具...",
  "instruction": "请根据您的实际情况选择最符合的选项",
  "categoryId": 1,
  "categoryName": "焦虑测评",
  "author": "admin",
  "version": "1.0",
  "questionCount": 24,
  "timeLimit": 20,
  "difficultyLevel": 2,
  "difficultyDesc": "中等",
  "price": 0,
  "isFree": 1,
  "priceDesc": "免费",
  "coverImage": "/images/prca24.jpg",
  "tags": "焦虑,社交,人际交往",
  "status": 1,
  "statusDesc": "已发布",
  "viewCount": 1,
  "testCount": 0,
  "createTime": "2025-07-19 17:59:01"
}
```

## 📋 修复文件清单

- ✅ `PsyAssessmentScaleServiceImpl.java` - 修复字段映射逻辑
- ✅ `sql/test_scale_data_mapping.sql` - 数据验证脚本
- ✅ `docs/scale_api_fix_summary.md` - 本修复文档

## 🚀 部署步骤

1. **重新编译应用**
   ```bash
   mvn clean compile
   ```

2. **重启服务**
   ```bash
   ./restart.sh
   ```

3. **验证修复**
   ```bash
   curl "http://localhost:8080/miniapp/user/assessment/scale/list?pageNum=1&pageSize=10"
   ```

## 🎯 修复效果

修复后，API接口将返回完整的数据，所有字段都会有正确的值：

- ✅ **scaleName** - 量表名称
- ✅ **scaleCode** - 量表编码  
- ✅ **description** - 量表描述
- ✅ **instruction** - 使用说明
- ✅ **author** - 创建者
- ✅ **difficultyDesc** - 难度描述
- ✅ **priceDesc** - 价格描述
- ✅ **statusDesc** - 状态描述
- ✅ **coverImage** - 封面图片
- ✅ **tags** - 标签
- ✅ **所有其他字段** - 正确映射

现在量表列表API将返回完整、正确的数据！
