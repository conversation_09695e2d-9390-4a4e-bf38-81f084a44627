# SQL错误修复说明

## 🔍 遇到的错误

### **1. MySQL语法错误**
```sql
-- ❌ 错误语法
ALTER TABLE `psy_consultants` 
ADD COLUMN IF NOT EXISTS `search_keywords` TEXT COMMENT '搜索关键词';
```
**错误原因**: MySQL不支持 `ADD COLUMN IF NOT EXISTS` 语法

### **2. 字段不存在错误**
```sql
-- ❌ 错误：instructor字段不存在
IFNULL(`instructor`, '')
```
**错误原因**: `psy_course` 表中没有 `instructor` 字段

### **3. 索引语法错误**
```sql
-- ❌ 错误语法
CREATE INDEX IF NOT EXISTS `idx_consultant_search_count` ON `psy_consultants`(`search_count`, `view_count`);
```
**错误原因**: 部分MySQL版本不支持 `CREATE INDEX IF NOT EXISTS`

## ✅ 修复方案

### **方案1: 使用动态SQL检查字段存在性**
```sql
-- 检查字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'psy_consultants' 
     AND COLUMN_NAME = 'search_keywords') = 0,
    'ALTER TABLE `psy_consultants` ADD COLUMN `search_keywords` TEXT',
    'SELECT ''search_keywords already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
```

### **方案2: 简化版本（推荐）**
```sql
-- 直接添加字段，忽略已存在的错误
ALTER TABLE `psy_consultants` ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词';
-- 如果字段已存在，会报错但不影响后续执行
```

## 🔧 修复后的脚本特点

### **1. 兼容性更好**
- 移除了不支持的 `IF NOT EXISTS` 语法
- 使用标准的 `ALTER TABLE` 语句
- 兼容MySQL 5.7+版本

### **2. 字段名修正**
- 移除了不存在的 `instructor` 字段引用
- 使用实际存在的字段名
- 添加了字段存在性检查

### **3. 索引创建优化**
- 使用标准的 `ALTER TABLE ADD INDEX` 语法
- 避免了 `CREATE INDEX IF NOT EXISTS` 问题
- 为搜索关键词创建前缀索引

## 📋 使用方法

### **推荐使用简化版脚本**
```bash
# 执行简化版脚本（兼容性最好）
source 简化版搜索字段添加脚本.sql;
```

### **如果遇到字段已存在错误**
这是正常的，可以忽略。错误信息类似：
```
ERROR 1060 (42S21): Duplicate column name 'search_keywords'
```
这表示字段已经存在，脚本会继续执行后续的UPDATE语句。

### **验证执行结果**
```sql
-- 检查字段是否添加成功
DESCRIBE psy_consultants;
DESCRIBE psy_course;
DESCRIBE psy_meditation;
DESCRIBE psy_t_scale;

-- 检查关键词是否生成
SELECT name, search_keywords FROM psy_consultants LIMIT 3;
SELECT title, search_keywords FROM psy_course LIMIT 3;
SELECT title, search_keywords FROM psy_meditation LIMIT 3;
SELECT name, search_keywords FROM psy_t_scale LIMIT 3;
```

## 🎯 预期结果

执行成功后：

### **1. 字段添加完成**
- ✅ `search_keywords` - 搜索关键词字段
- ✅ `search_count` - 搜索次数统计字段

### **2. 关键词自动生成**
- ✅ **咨询师**: 姓名+专业+地区+特征标签
- ✅ **课程**: 标题+简介+分类标签
- ✅ **冥想**: 标题+描述+功能标签
- ✅ **测评**: 量表名+编码+专业标签

### **3. 搜索索引创建**
- ✅ 为 `search_keywords` 字段创建前缀索引
- ✅ 为 `search_count` 字段创建索引
- ✅ 优化搜索性能

## 🔍 测试搜索功能

执行脚本后，可以测试搜索：

```sql
-- 测试咨询师搜索
SELECT name, search_keywords FROM psy_consultants 
WHERE search_keywords LIKE '%心理咨询师%' LIMIT 5;

-- 测试课程搜索
SELECT title, search_keywords FROM psy_course 
WHERE search_keywords LIKE '%心理课程%' LIMIT 5;

-- 测试冥想搜索
SELECT title, search_keywords FROM psy_meditation 
WHERE search_keywords LIKE '%冥想%' LIMIT 5;

-- 测试测评搜索
SELECT name, search_keywords FROM psy_t_scale 
WHERE search_keywords LIKE '%焦虑%' LIMIT 5;
```

## ⚠️ 注意事项

1. **字段已存在错误可以忽略** - 这是正常现象
2. **备份数据库** - 执行前建议备份重要数据
3. **分步执行** - 如果遇到问题，可以分段执行脚本
4. **检查字段类型** - 确保现有字段类型与脚本中的类型匹配

现在您可以使用修复后的脚本，搜索功能就能正常工作了！
