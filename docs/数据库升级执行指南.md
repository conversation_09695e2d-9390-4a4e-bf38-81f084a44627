# 数据库升级执行指南

## 问题解决

原始的 `ADD COLUMN IF NOT EXISTS` 语法在某些MySQL版本中不支持，我已经创建了兼容性更好的版本。

## 执行步骤

### 1. 执行结构升级脚本
```sql
-- 在数据库管理工具中执行
source 测评系统升级脚本_兼容版.sql;
```

或者复制粘贴 `测评系统升级脚本_兼容版.sql` 文件内容到数据库管理工具中执行。

### 2. 执行配置数据更新脚本
```sql
-- 在数据库管理工具中执行
source 测评系统配置数据更新.sql;
```

或者复制粘贴 `测评系统配置数据更新.sql` 文件内容到数据库管理工具中执行。

## 兼容性说明

### 新版本特点
- ✅ 使用动态SQL检查字段是否存在
- ✅ 避免重复添加字段的错误
- ✅ 兼容MySQL 5.7及以上版本
- ✅ 提供详细的执行反馈

### 执行逻辑
```sql
-- 示例：检查字段是否存在，不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_scale' AND COLUMN_NAME = 'scoring_method') = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN scoring_method varchar(50) COMMENT ''计分方法''',
    'SELECT ''scoring_method字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
```

## 验证方法

### 检查字段是否添加成功
```sql
-- 检查psy_t_scale表新增字段
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale' 
  AND COLUMN_NAME IN ('scoring_method', 'has_reverse_items', 'has_standard_score', 'standard_score_multiplier');

-- 检查psy_t_assessment_record表新增字段
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_assessment_record' 
  AND COLUMN_NAME IN ('dimension_scores', 'standard_score', 'report_generated', 'report_content');
```

### 检查配置数据是否更新
```sql
-- 检查量表配置
SELECT code, name, scoring_method, has_reverse_items, has_standard_score, standard_score_multiplier
FROM psy_t_scale 
WHERE code IN ('PRCA-24', 'STAI', 'SAD', 'PDQ-4+', 'FNE', 'SAS', 'BAI');

-- 检查反向计分题目配置
SELECT s.code, COUNT(*) as reverse_question_count
FROM psy_t_question q
JOIN psy_t_scale s ON q.scale_id = s.id
WHERE q.is_reverse = 1 AND s.code IN ('STAI', 'SAS', 'FNE')
GROUP BY s.code;
```

## 预期结果

### 字段添加结果
- `psy_t_scale` 表：7个新字段
- `psy_t_subscale` 表：1个新字段
- `psy_t_subscale_question_rel` 表：1个新字段
- `psy_t_question` 表：1个新字段
- `psy_t_interpretation` 表：8个新字段
- `psy_t_assessment_record` 表：4个新字段
- 新建 `psy_t_scoring_config` 表

### 配置数据结果
- PRCA-24：FORMULA_WITH_BASE，4个维度
- STAI：REVERSE_SCORING，19个反向题目
- SAD：SPECIAL_BINARY，2个维度
- PDQ-4+：COMPOSITE_SCORING
- FNE：REVERSE_SCORING，全部反向
- SAS：STANDARD_SCORE，×1.25，5个反向题目
- BAI：STANDARD_SCORE，×1.19

## 错误处理

如果执行过程中遇到错误：

1. **字段已存在错误**：正常情况，脚本会跳过
2. **表不存在错误**：检查表名是否正确
3. **权限错误**：确保数据库用户有ALTER权限
4. **语法错误**：检查MySQL版本兼容性

## 回滚方案

如果需要回滚（谨慎操作）：
```sql
-- 删除新增字段（示例）
ALTER TABLE psy_t_scale DROP COLUMN scoring_method;
ALTER TABLE psy_t_scale DROP COLUMN has_reverse_items;
-- ... 其他字段

-- 删除新建表
DROP TABLE IF EXISTS psy_t_scoring_config;
```

## 完成确认

执行完成后，您应该看到：
- ✅ 所有字段添加成功
- ✅ 配置数据更新完成
- ✅ 验证查询返回预期结果
- ✅ 没有错误信息

现在可以重新编译和部署应用程序了！
