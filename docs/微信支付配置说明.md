# 微信支付配置说明

## 📋 概述

本文档说明如何配置微信小程序支付功能，包括微信商户平台配置和系统配置。

## 🏪 微信商户平台配置

### 1. 申请微信支付

1. **登录微信商户平台**: https://pay.weixin.qq.com/
2. **申请接入微信支付**: 提交相关资质材料
3. **获取商户信息**:
   - 商户号（mch_id）
   - API密钥（mch_key）
   - 商户证书

### 2. 配置支付参数

#### A. 基础配置
- **商户号**: 微信支付分配的商户号
- **API密钥**: 在商户平台设置的32位密钥
- **APPID**: 小程序的APPID

#### B. 证书配置
- **商户证书**: 下载apiclient_cert.p12文件
- **证书密码**: 通常是商户号
- **API证书**: 用于V3接口的证书文件

#### C. 回调配置
- **支付回调URL**: `https://yourdomain.com/miniapp/payment/notify/pay`
- **退款回调URL**: `https://yourdomain.com/miniapp/payment/notify/refund`

### 3. 小程序配置

#### A. 小程序后台配置
1. 登录小程序管理后台
2. 进入"微信支付" -> "商户号管理"
3. 绑定已申请的商户号
4. 配置支付目录（如：https://yourdomain.com/）

#### B. 域名配置
在小程序后台配置服务器域名：
- **request合法域名**: `https://yourdomain.com`
- **uploadFile合法域名**: `https://yourdomain.com`
- **downloadFile合法域名**: `https://yourdomain.com`

## ⚙️ 系统配置

### 1. application.yml配置

```yaml
# 微信小程序配置
wx:
  app:
    # 小程序APPID
    appid: wx8df6fcafd17d7348
    # 小程序密钥
    secret: your_app_secret_here
    grant_type: authorization_code
    session_url: https://api.weixin.qq.com/sns/jscode2session
  
  # 微信支付配置
  pay:
    # 微信支付商户号
    mch-id: 1234567890
    # 微信支付商户API密钥（V2版本，32位）
    mch-key: your_mch_key_here_32_characters_long
    # 微信支付API V3密钥（32位）
    api-v3-key: your_api_v3_key_here_32_characters_long
    # 微信支付回调地址
    notify-url: https://yourdomain.com/miniapp/payment/notify/pay
    # 微信小程序APPID（与上面的appid保持一致）
    app-id: wx8df6fcafd17d7348
    # 是否使用沙箱环境
    use-sandbox: false
    # 是否使用V3版本接口（推荐）
    use-v3: true
    # 私钥路径（V3版本需要）
    private-key-path: classpath:cert/apiclient_key.pem
    # 平台证书序列号（V3版本需要）
    cert-serial-no: your_cert_serial_no_here
    # 商户证书路径
    mch-cert-path: classpath:cert/apiclient_cert.p12
    # 商户证书密码（通常是商户号）
    mch-cert-password: 1234567890
    # 超时时间，单位分钟
    timeout-minutes: 5
```

### 2. 证书文件配置

#### A. 证书文件位置
将证书文件放在 `src/main/resources/cert/` 目录下：

```
src/main/resources/cert/
├── apiclient_cert.p12      # 商户证书（V2版本）
├── apiclient_key.pem       # 商户私钥（V3版本）
└── apiclient_cert.pem      # 商户证书（V3版本）
```

#### B. 证书文件获取
1. **V2版本证书**: 在商户平台下载apiclient_cert.p12
2. **V3版本证书**: 在商户平台下载证书压缩包，解压得到pem文件

### 3. 环境配置

#### A. 开发环境
```yaml
wx:
  pay:
    use-sandbox: true  # 使用沙箱环境
    notify-url: https://dev.yourdomain.com/miniapp/payment/notify/pay
```

#### B. 生产环境
```yaml
wx:
  pay:
    use-sandbox: false  # 使用正式环境
    notify-url: https://yourdomain.com/miniapp/payment/notify/pay
```

## 🔒 安全配置

### 1. 密钥管理

#### A. 环境变量配置
```bash
# 设置环境变量
export WX_MCH_KEY=your_mch_key_here_32_characters_long
export WX_API_V3_KEY=your_api_v3_key_here_32_characters_long
```

#### B. 配置文件引用
```yaml
wx:
  pay:
    mch-key: ${WX_MCH_KEY}
    api-v3-key: ${WX_API_V3_KEY}
```

### 2. HTTPS配置

#### A. SSL证书
- 确保服务器配置了有效的SSL证书
- 微信支付回调必须使用HTTPS

#### B. 域名配置
- 配置正确的域名解析
- 确保回调地址可以正常访问

### 3. 防火墙配置

#### A. 端口开放
- 开放443端口（HTTPS）
- 开放80端口（HTTP重定向）

#### B. IP白名单
- 配置微信支付服务器IP白名单
- 限制回调接口的访问来源

## 🧪 测试配置

### 1. 沙箱环境

#### A. 申请沙箱
1. 登录微信商户平台
2. 进入"开发配置" -> "沙箱环境"
3. 获取沙箱商户号和密钥

#### B. 沙箱配置
```yaml
wx:
  pay:
    mch-id: 沙箱商户号
    mch-key: 沙箱密钥
    use-sandbox: true
```

### 2. 测试用例

#### A. 支付测试
```javascript
// 测试支付金额（分）
const testAmounts = {
  success: 1,      // 1分 - 支付成功
  fail: 2,         // 2分 - 支付失败
  cancel: 3        // 3分 - 用户取消
};
```

#### B. 回调测试
使用工具模拟微信支付回调：
- 使用Postman或curl发送回调请求
- 验证签名和数据解密功能
- 测试订单状态更新逻辑

## 📝 配置检查清单

### 1. 微信商户平台
- [ ] 商户号已申请并审核通过
- [ ] API密钥已设置（32位）
- [ ] 商户证书已下载
- [ ] 小程序已绑定商户号
- [ ] 支付目录已配置
- [ ] 回调URL已配置

### 2. 小程序后台
- [ ] 服务器域名已配置
- [ ] 业务域名已配置
- [ ] 支付功能已开通

### 3. 系统配置
- [ ] application.yml配置正确
- [ ] 证书文件已放置
- [ ] 环境变量已设置
- [ ] HTTPS已配置
- [ ] 防火墙已配置

### 4. 功能测试
- [ ] 创建订单功能正常
- [ ] 支付调起功能正常
- [ ] 支付回调功能正常
- [ ] 订单状态更新正常
- [ ] 退款功能正常

## ⚠️ 常见问题

### 1. 支付失败
- **问题**: 调起支付失败
- **解决**: 检查APPID、商户号、签名是否正确

### 2. 回调失败
- **问题**: 支付成功但回调失败
- **解决**: 检查回调URL是否可访问，签名验证是否正确

### 3. 证书问题
- **问题**: 证书加载失败
- **解决**: 检查证书文件路径和密码是否正确

### 4. 网络问题
- **问题**: 网络请求超时
- **解决**: 检查网络连接和防火墙配置

通过以上配置，可以完整地搭建微信小程序支付功能。建议先在沙箱环境中测试，确认功能正常后再切换到生产环境。
