# 系统时间槽状态管理功能说明

## 功能概述

为系统公共时间槽添加了状态管理功能，支持**可用**和**已过期**两种状态，让小程序能够准确显示时间槽的当前状态。

## 状态定义

### 状态枚举
- **0 - 可用**: 时间槽当前可以被预约
- **2 - 已过期**: 时间槽已经过期，不能再被预约

### 状态判断逻辑
```java
// 过期判断
boolean isExpired = slotEndTime.isBefore(cutoffTime);

// cutoffTime 计算：
// - 未启用延后：当前时间
// - 启用延后：当前时间 - 延后小时数
```

## 数据库变更

### 1. 表结构更新
```sql
-- 添加状态字段
ALTER TABLE `psy_system_time_slot` 
ADD COLUMN `status` int DEFAULT '0' COMMENT '状态（0-可用，2-已过期）' 
AFTER `has_available`;

-- 创建状态索引
CREATE INDEX `idx_status` ON `psy_system_time_slot` (`status`);
CREATE INDEX `idx_status_date_center` ON `psy_system_time_slot` (`status`, `date_key`, `center_id`);
```

### 2. 初始化现有数据
```sql
-- 根据时间判断现有数据的状态
UPDATE `psy_system_time_slot` 
SET `status` = CASE 
    WHEN STR_TO_DATE(CONCAT(date_key, ' ', end_time), '%Y-%m-%d %H:%i:%s') < NOW() THEN 2  -- 已过期
    ELSE 0  -- 可用
END
WHERE `del_flag` = 0;
```

## 新增功能

### 1. 状态管理方法

#### 批量更新状态
```java
/**
 * 批量更新系统时间槽状态
 */
int batchUpdateSlotStatus(List<Long> slotIds, Integer status);
```

#### 更新过期状态
```java
/**
 * 更新过期的系统时间槽状态（支持延后配置）
 */
int updateExpiredSlotStatusWithDelay(Long centerId);
```

### 2. 状态查询增强

#### API响应增加状态信息
```json
{
  "slotId": 1,
  "startTime": "09:00",
  "endTime": "09:15",
  "status": 0,
  "statusText": "可用",
  "availableCounselors": 3,
  "totalCounselors": 5,
  "hasAvailable": true
}
```

### 3. 定时任务增强

#### 自动状态更新
- 每30分钟更新可用性统计时，同时更新过期状态
- 支持延后过期配置
- 记录详细的更新日志

## 使用方式

### 1. 查询可用时间槽
```bash
# 只查询可用状态的时间槽
GET /miniapp/timeSlot/available?startDate=2025-07-10&endDate=2025-07-16&centerId=1
```

### 2. 手动更新状态
```bash
# 更新指定中心的过期状态
POST /system/systemTimeSlot/updateExpiredStatus?centerId=1
```

### 3. 批量状态操作
```java
// 批量设置时间槽为过期状态
List<Long> slotIds = Arrays.asList(1L, 2L, 3L);
systemTimeSlotService.batchUpdateSlotStatus(slotIds, 2);
```

## 状态流转

### 正常流转
```
创建时间槽 → 可用(0) → 时间过期 → 已过期(2) → 清理删除
```

### 延后过期流转
```
创建时间槽 → 可用(0) → 时间过期+延后时间 → 已过期(2) → 清理删除
```

## 前端展示

### 1. 状态样式
```css
.slot-available {
  background-color: #e8f5e8;
  color: #28a745;
}

.slot-expired {
  background-color: #f8d7da;
  color: #dc3545;
}
```

### 2. 状态文本
- **可用**: 显示绿色，用户可以点击预约
- **已过期**: 显示灰色，禁用点击

### 3. 过滤功能
```javascript
// 只显示可用的时间槽
const availableSlots = allSlots.filter(slot => slot.status === 0);

// 显示所有状态，但区分样式
const slotsWithStatus = allSlots.map(slot => ({
  ...slot,
  className: slot.status === 0 ? 'slot-available' : 'slot-expired',
  disabled: slot.status !== 0
}));
```

## 监控和统计

### 1. 状态统计查询
```sql
-- 查看各状态的时间槽数量
SELECT 
    status,
    CASE status 
        WHEN 0 THEN '可用'
        WHEN 2 THEN '已过期'
        ELSE '未知'
    END as status_name,
    COUNT(*) as count
FROM `psy_system_time_slot` 
WHERE `del_flag` = 0
GROUP BY status;
```

### 2. 过期率统计
```sql
-- 查看今天的过期率
SELECT 
    date_key,
    COUNT(*) as total_slots,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as expired_slots,
    ROUND(SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as expired_rate
FROM `psy_system_time_slot` 
WHERE `del_flag` = 0 
  AND date_key = DATE_FORMAT(NOW(), '%Y-%m-%d')
GROUP BY date_key;
```

## 配置管理

### 延后过期配置
- **配置项**: `psy.slot.delay.expiration.enabled`
- **延后时间**: `psy.slot.delay.expiration.hours`
- **管理接口**: `/system/timeSlotConfig/delayExpiration`

### 状态更新频率
- **定时任务**: 每30分钟自动更新
- **手动触发**: 通过管理接口立即更新

## 注意事项

### ⚠️ 重要提醒
1. **状态一致性**: 确保系统时间槽状态与咨询师时间槽状态保持一致
2. **延后配置**: 延后过期功能影响状态判断，需要合理配置
3. **性能考虑**: 大量时间槽的状态更新可能影响性能
4. **前端缓存**: 前端需要及时刷新状态信息

### 🔧 最佳实践
1. **定期清理**: 及时清理过期的时间槽数据
2. **状态监控**: 监控状态更新的执行情况
3. **用户提示**: 在前端明确显示时间槽状态
4. **异常处理**: 处理状态更新失败的情况

现在系统公共时间槽具备了完整的状态管理功能，用户可以清楚地看到哪些时间可用，哪些已经过期！🎯
