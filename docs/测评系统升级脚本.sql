-- =====================================================
-- 测评系统升级脚本 - 支持复杂计分和报告生成
-- =====================================================

-- 1. 扩展 psy_t_scale 表 - 添加计分配置
ALTER TABLE psy_t_scale 
ADD COLUMN IF NOT EXISTS scoring_method varchar(50) COMMENT '计分方法';

ALTER TABLE psy_t_scale 
ADD COLUMN IF NOT EXISTS has_reverse_items tinyint DEFAULT 0 COMMENT '是否有反向计分题';

ALTER TABLE psy_t_scale 
ADD COLUMN IF NOT EXISTS has_standard_score tinyint DEFAULT 0 COMMENT '是否需要标准分转换';

ALTER TABLE psy_t_scale 
ADD COLUMN IF NOT EXISTS standard_score_multiplier decimal(5,2) DEFAULT 1.0 COMMENT '标准分转换系数';

ALTER TABLE psy_t_scale 
ADD COLUMN IF NOT EXISTS dimension_count int DEFAULT 1 COMMENT '维度数量';

ALTER TABLE psy_t_scale 
ADD COLUMN IF NOT EXISTS raw_score_range varchar(20) COMMENT '原始分范围';

ALTER TABLE psy_t_scale 
ADD COLUMN IF NOT EXISTS standard_score_range varchar(20) COMMENT '标准分范围';

-- 2. 扩展 psy_t_subscale 表 - 添加基础分数支持
ALTER TABLE psy_t_subscale 
ADD COLUMN IF NOT EXISTS base_score int DEFAULT 0 COMMENT '基础分数';

-- 3. 扩展 psy_t_subscale_question_rel 表 - 添加系数支持
ALTER TABLE psy_t_subscale_question_rel 
ADD COLUMN IF NOT EXISTS coefficient int DEFAULT 1 COMMENT '系数(+1或-1)';

-- 4. 扩展 psy_t_question 表 - 完善反向计分和特殊规则
ALTER TABLE psy_t_question 
MODIFY COLUMN reverse_value int COMMENT '反向计分最大值(如5点量表反向计分用5)';

ALTER TABLE psy_t_question 
ADD COLUMN IF NOT EXISTS scoring_rule json COMMENT '特殊计分规则配置';

-- 5. 扩展 psy_t_interpretation 表 - 支持详细解释
ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS score_type varchar(20) DEFAULT 'RAW' COMMENT '分数类型(RAW原始分/STANDARD标准分)';

ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS severity_level varchar(50) COMMENT '严重程度等级';

ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS fear_level varchar(20) COMMENT '恐惧等级';

ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS performance_desc text COMMENT '说明与表现';

ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS impact_analysis text COMMENT '可能影响';

ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS guidance_suggestions text COMMENT '建议与指导';

ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS clinical_significance text COMMENT '临床意义';

ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS recommendations text COMMENT '建议措施';

ALTER TABLE psy_t_interpretation 
ADD COLUMN IF NOT EXISTS risk_level varchar(20) COMMENT '风险等级';

-- 6. 创建计分配置表
CREATE TABLE IF NOT EXISTS psy_t_scoring_config (
    id bigint AUTO_INCREMENT PRIMARY KEY,
    scale_id bigint NOT NULL,
    scoring_type varchar(50) NOT NULL COMMENT 'SIMPLE_SUM, REVERSE_SCORING, FORMULA, STANDARD_SCORE',
    formula_config json COMMENT '存储复杂计分公式',
    reverse_items json COMMENT '反向计分题目',
    standard_multiplier decimal(5,2) COMMENT '标准分转换系数',
    create_time datetime DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_scale_id (scale_id)
) COMMENT '计分配置表';

-- 7. 扩展 psy_t_assessment_record 表 - 支持维度分数和报告
ALTER TABLE psy_t_assessment_record 
ADD COLUMN IF NOT EXISTS dimension_scores json COMMENT '各维度分数';

ALTER TABLE psy_t_assessment_record 
ADD COLUMN IF NOT EXISTS standard_score decimal(8,2) COMMENT '标准分';

ALTER TABLE psy_t_assessment_record 
ADD COLUMN IF NOT EXISTS report_generated tinyint DEFAULT 0 COMMENT '报告是否已生成';

ALTER TABLE psy_t_assessment_record 
ADD COLUMN IF NOT EXISTS report_content longtext COMMENT '完整报告内容';

-- =====================================================
-- 配置数据更新
-- =====================================================

-- 8. 更新量表基础配置
UPDATE psy_t_scale SET 
    scoring_method = 'FORMULA_WITH_BASE',
    has_reverse_items = 0,
    has_standard_score = 0,
    dimension_count = 4,
    raw_score_range = '24-120',
    standard_score_range = '24-120'
WHERE code = 'PRCA-24';

UPDATE psy_t_scale SET 
    scoring_method = 'REVERSE_SCORING',
    has_reverse_items = 1,
    has_standard_score = 0,
    dimension_count = 2,
    raw_score_range = '20-80',
    standard_score_range = '20-80'
WHERE code = 'STAI';

UPDATE psy_t_scale SET 
    scoring_method = 'SPECIAL_BINARY',
    has_reverse_items = 0,
    has_standard_score = 0,
    dimension_count = 2,
    raw_score_range = '0-28',
    standard_score_range = '0-28'
WHERE code = 'SAD';

UPDATE psy_t_scale SET 
    scoring_method = 'STANDARD_SCORE',
    has_reverse_items = 1,
    has_standard_score = 1,
    standard_score_multiplier = 1.25,
    dimension_count = 1,
    raw_score_range = '20-80',
    standard_score_range = '25-100'
WHERE code = 'SAS';

UPDATE psy_t_scale SET 
    scoring_method = 'STANDARD_SCORE',
    has_reverse_items = 0,
    has_standard_score = 1,
    standard_score_multiplier = 1.19,
    dimension_count = 1,
    raw_score_range = '21-84',
    standard_score_range = '25-100'
WHERE code = 'BAI';

-- 9. 更新分量表基础分数（PRCA-24）
UPDATE psy_t_subscale SET base_score = 18 
WHERE scale_id = 8 AND alias IN ('Group', 'Meeting', 'Interpersonal', 'Public');

-- 10. 配置反向计分题目（STAI）
UPDATE psy_t_question SET 
    is_reverse = 1, 
    reverse_value = 5 
WHERE scale_id = (SELECT id FROM psy_t_scale WHERE code = 'STAI' LIMIT 1)
  AND question_no IN (1,2,5,8,10,11,15,16,19,20,23,24,26,27,30,33,34,36,39);

-- 11. 配置反向计分题目（SAS）
UPDATE psy_t_question SET 
    is_reverse = 1, 
    reverse_value = 4 
WHERE scale_id = (SELECT id FROM psy_t_scale WHERE code = 'SAS' LIMIT 1)
  AND question_no IN (5,9,13,17,19);

-- =====================================================
-- 验证脚本
-- =====================================================

-- 检查升级结果
SELECT 'psy_t_scale扩展字段' as table_name, 
       COUNT(*) as updated_count 
FROM psy_t_scale 
WHERE scoring_method IS NOT NULL;

SELECT 'psy_t_subscale基础分数' as table_name, 
       COUNT(*) as updated_count 
FROM psy_t_subscale 
WHERE base_score > 0;

SELECT 'STAI反向计分题目' as table_name, 
       COUNT(*) as updated_count 
FROM psy_t_question 
WHERE is_reverse = 1 AND scale_id = (SELECT id FROM psy_t_scale WHERE code = 'STAI' LIMIT 1);

-- 升级完成提示
SELECT '测评系统升级完成！' as status, 
       NOW() as completion_time;
