# 后台管理接口完善和小程序搜索功能增强

## 概述

本次更新完善了后台管理系统的测评评价和订单管理功能，同时增强了小程序端的测评搜索功能。

## 1. 后台管理 - 测评评价管理

### 1.1 新增控制器
**文件**: `PsyAssessmentReviewController.java`

### 1.2 主要功能接口

#### 基础CRUD操作
- `GET /system/assessment/review/list` - 查询评价列表
- `GET /system/assessment/review/{id}` - 获取评价详细信息
- `POST /system/assessment/review` - 新增评价
- `PUT /system/assessment/review` - 修改评价
- `DELETE /system/assessment/review/{ids}` - 删除评价

#### 审核管理
- `PUT /system/assessment/review/audit/{id}` - 审核评价
- `PUT /system/assessment/review/audit/batch` - 批量审核评价
- `GET /system/assessment/review/pending/count` - 获取待审核评价数量

#### 查询功能
- `GET /system/assessment/review/user/{userId}` - 根据用户ID查询评价
- `GET /system/assessment/review/scale/{scaleId}` - 根据量表ID查询评价
- `GET /system/assessment/review/details/{id}` - 获取评价详情（包含回复）

#### 统计分析
- `GET /system/assessment/review/statistics` - 获取评价统计信息
- `GET /system/assessment/review/trend` - 获取评价趋势数据
- `POST /system/assessment/review/export/statistics` - 导出评价统计报告

#### 互动功能
- `PUT /system/assessment/review/top/{id}` - 置顶/取消置顶评价
- `POST /system/assessment/review/reply/{id}` - 回复评价

### 1.3 服务层增强

#### 新增接口方法
```java
// 后台管理专用方法
PsyAssessmentReview selectReviewWithDetails(Long id);
int auditReview(PsyAssessmentReview review);
int batchAuditReview(List<PsyAssessmentReview> reviews);
List<PsyAssessmentReview> selectReviewsByUserId(Long userId);
List<PsyAssessmentReview> selectReviewsByScaleId(Long scaleId);
Map<String, Object> getReviewStatistics();
List<Map<String, Object>> getReviewTrend();
int toggleTopReview(Long id);
int replyReview(Long id, String replyContent, Long replyUserId);
int countPendingReviews();
PsyAssessmentReview selectReviewWithReplies(Long id);
void exportReviewStatistics(HttpServletResponse response);
```

## 2. 后台管理 - 订单管理

### 2.1 控制器完善
**文件**: `PsyAssessmentOrderController.java` (已存在，功能完整)

### 2.2 主要功能接口

#### 基础CRUD操作
- `GET /system/assessment/order/list` - 查询订单列表
- `GET /system/assessment/order/{id}` - 获取订单详细信息
- `POST /system/assessment/order` - 新增订单
- `PUT /system/assessment/order` - 修改订单
- `DELETE /system/assessment/order/{ids}` - 删除订单

#### 订单状态管理
- `PUT /system/assessment/order/confirm/{id}` - 确认订单
- `PUT /system/assessment/order/cancel/{id}` - 取消订单
- `PUT /system/assessment/order/refund/{id}` - 退款订单
- `PUT /system/assessment/order/status/{id}` - 订单状态流转
- `PUT /system/assessment/order/sync/{id}` - 同步订单状态

#### 支付管理
- `GET /system/assessment/order/payment/{id}` - 获取订单支付信息
- `PUT /system/assessment/order/mark-paid/{id}` - 手动标记订单已支付

#### 查询功能
- `GET /system/assessment/order/user/{userId}` - 根据用户ID查询订单
- `GET /system/assessment/order/scale/{scaleId}` - 根据量表ID查询订单
- `GET /system/assessment/order/details/{id}` - 获取订单详情（包含支付记录）

#### 统计分析
- `GET /system/assessment/order/statistics` - 获取订单统计信息
- `GET /system/assessment/order/trend` - 获取订单趋势数据
- `GET /system/assessment/order/revenue` - 获取收入统计
- `GET /system/assessment/order/pending/count` - 获取待处理订单数量
- `POST /system/assessment/order/export/statistics` - 导出订单统计报告

#### 批量操作
- `PUT /system/assessment/order/batch` - 批量处理订单

### 2.3 服务层增强

#### 新增接口方法
```java
// 后台管理专用方法
PsyAssessmentOrder selectOrderWithDetails(Long id);
int confirmOrder(Long id);
int cancelOrder(Long id, String reason);
int refundOrder(Long id, String reason);
List<PsyAssessmentOrder> selectOrdersByUserId(Long userId);
List<PsyAssessmentOrder> selectOrdersByScaleId(Long scaleId);
Map<String, Object> getOrderStatistics();
List<Map<String, Object>> getOrderTrend(Integer days);
Map<String, Object> getRevenueStatistics(Integer days);
int batchProcessOrder(List<PsyAssessmentOrder> orders, String action);
int countPendingOrders();
int updateOrderStatus(Long id, Integer status, String remark);
Map<String, Object> getOrderPaymentInfo(Long id);
void exportOrderStatistics(HttpServletResponse response, String startDate, String endDate);
int syncOrderStatus(Long id);
PsyAssessmentOrder selectOrderWithPaymentRecords(Long id);
int markOrderPaid(Long id, String paymentMethod, String remark);
```

## 3. 小程序端 - 测评搜索功能增强

### 3.1 搜索服务增强

#### 新增测评搜索功能
在 `PsySearchServiceImpl` 中添加了测评搜索支持：

```java
// 在综合搜索中添加测评类型
if ("all".equals(searchType) || "assessment".equals(searchType)) {
    SearchResultDTO.CategoryResult assessmentResult = searchAssessments(keyword, pageNum, pageSize);
    if (assessmentResult.getCount() > 0) {
        categories.add(assessmentResult);
        totalCount += assessmentResult.getCount();
    }
}
```

#### 测评搜索实现
```java
private SearchResultDTO.CategoryResult searchAssessments(String keyword, Integer pageNum, Integer pageSize) {
    // 执行搜索
    List<PsyAssessmentScale> assessments = assessmentScaleMapper.searchScales(keyword);
    
    // 转换为搜索结果
    List<SearchResultDTO.SearchItem> items = new ArrayList<>();
    for (PsyAssessmentScale assessment : assessments) {
        SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
        item.setId(assessment.getId());
        item.setTitle(assessment.getScaleName());
        item.setDescription(assessment.getDescription());
        item.setImageUrl(assessment.getCoverImage());
        item.setType("assessment");
        item.setUrl("/pages/assessment/detail?id=" + assessment.getId());
        
        // 计算相关度分数
        double score = calculateRelevanceScore(keyword, assessment.getScaleName(), assessment.getDescription());
        item.setScore(score);
        
        // 添加额外信息
        Map<String, Object> extra = new HashMap<>();
        extra.put("scaleCode", assessment.getScaleCode());
        extra.put("questionCount", assessment.getQuestionCount());
        extra.put("duration", assessment.getDuration());
        extra.put("price", assessment.getPrice());
        extra.put("payMode", assessment.getPayMode());
        extra.put("testCount", assessment.getTestCount());
        extra.put("avgScore", assessment.getAvgScore());
        item.setExtra(extra);
        
        items.add(item);
    }
    
    // 按相关度排序和分页处理
    items.sort((a, b) -> Double.compare(b.getScore(), a.getScore()));
    int start = (pageNum - 1) * pageSize;
    int end = Math.min(start + pageSize, items.size());
    List<SearchResultDTO.SearchItem> pagedItems = items.subList(start, end);
    
    return new SearchResultDTO.CategoryResult("assessment", "心理测评", items.size(), pagedItems);
}
```

### 3.2 Mapper层增强

#### 新增搜索方法
在 `PsyAssessmentScaleMapper` 中添加了简化的搜索方法：

```java
/**
 * 简单搜索量表（只根据关键词）
 * 
 * @param keyword 关键词
 * @return 量表集合
 */
List<PsyAssessmentScale> searchScales(@Param("keyword") String keyword);
```

#### XML映射配置
在 `PsyAssessmentScaleMapper.xml` 中添加了对应的SQL查询：

```xml
<!-- 简单搜索量表（只根据关键词） -->
<select id="searchScales" resultMap="ScaleResultMap">
    SELECT * FROM psy_t_scale s
    WHERE s.del_flag = 0 AND s.status = 1
    <if test="keyword != null and keyword != ''">
        AND (
            s.scale_name LIKE CONCAT('%', #{keyword}, '%')
            OR s.description LIKE CONCAT('%', #{keyword}, '%')
            OR s.search_keywords LIKE CONCAT('%', #{keyword}, '%')
        )
    </if>
    ORDER BY 
    <if test="keyword != null and keyword != ''">
        CASE 
            WHEN s.scale_name LIKE CONCAT(#{keyword}, '%') THEN 1
            WHEN s.scale_name LIKE CONCAT('%', #{keyword}, '%') THEN 2
            ELSE 3
        END,
    </if>
    s.test_count DESC, s.view_count DESC
    LIMIT 50
</select>
```

## 4. 搜索功能特性

### 4.1 搜索类型支持
- `all` - 综合搜索（包含测评）
- `assessment` - 专门搜索测评
- `consultant` - 搜索咨询师
- `course` - 搜索课程
- `meditation` - 搜索冥想

### 4.2 搜索结果特性
- **相关度排序**: 根据关键词匹配度计算分数
- **智能排序**: 标题完全匹配 > 标题包含 > 描述包含
- **丰富信息**: 返回测评的详细信息（价格、题目数量、时长等）
- **分页支持**: 支持分页查询
- **统计信息**: 返回搜索结果数量

### 4.3 搜索字段
- 量表名称 (`scale_name`)
- 量表描述 (`description`)
- 搜索关键词 (`search_keywords`)

## 5. 权限配置

### 5.1 评价管理权限
```
system:review:list     - 查看评价列表
system:review:query    - 查看评价详情
system:review:add      - 新增评价
system:review:edit     - 修改评价
system:review:remove   - 删除评价
system:review:audit    - 审核评价
system:review:reply    - 回复评价
system:review:export   - 导出评价数据
```

### 5.2 订单管理权限
```
system:order:list      - 查看订单列表
system:order:query     - 查看订单详情
system:order:add       - 新增订单
system:order:edit      - 修改订单
system:order:remove    - 删除订单
system:order:confirm   - 确认订单
system:order:cancel    - 取消订单
system:order:refund    - 退款订单
system:order:pay       - 支付管理
system:order:export    - 导出订单数据
```

## 6. 使用示例

### 6.1 小程序搜索测评
```javascript
// 搜索测评
GET /miniapp/search?keyword=焦虑&searchType=assessment&pageNum=1&pageSize=10

// 响应
{
  "code": 200,
  "data": {
    "keyword": "焦虑",
    "totalCount": 5,
    "categories": [
      {
        "type": "assessment",
        "name": "心理测评",
        "count": 5,
        "items": [
          {
            "id": 1,
            "title": "焦虑自评量表",
            "description": "用于评估焦虑程度的专业量表",
            "imageUrl": "/images/sas.jpg",
            "type": "assessment",
            "url": "/pages/assessment/detail?id=1",
            "score": 95.5,
            "extra": {
              "scaleCode": "SAS",
              "questionCount": 20,
              "duration": 10,
              "price": 9.9,
              "payMode": 1,
              "testCount": 1250,
              "avgScore": 4.5
            }
          }
        ]
      }
    ]
  }
}
```

### 6.2 后台管理评价审核
```javascript
// 审核评价
PUT /system/assessment/review/audit/1
{
  "status": 1,
  "auditRemark": "内容合规，审核通过"
}

// 批量审核
PUT /system/assessment/review/audit/batch
[
  {
    "id": 1,
    "status": 1,
    "auditRemark": "审核通过"
  },
  {
    "id": 2,
    "status": 2,
    "auditRemark": "内容不当，审核拒绝"
  }
]
```

### 6.3 后台管理订单处理
```javascript
// 确认订单
PUT /system/assessment/order/confirm/1

// 取消订单
PUT /system/assessment/order/cancel/1?reason=用户申请取消

// 手动标记已支付
PUT /system/assessment/order/mark-paid/1?paymentMethod=微信支付&remark=线下确认支付
```

## 7. 总结

本次更新完善了：

1. **后台管理功能**：
   - ✅ 测评评价管理（审核、回复、统计）
   - ✅ 订单管理（状态流转、支付管理、统计分析）
   - ✅ 数据导出和统计报告

2. **小程序搜索功能**：
   - ✅ 测评搜索支持
   - ✅ 智能排序和相关度计算
   - ✅ 丰富的搜索结果信息

3. **系统完整性**：
   - ✅ 完整的权限控制
   - ✅ 统一的接口规范
   - ✅ 完善的错误处理

现在系统具备了完整的后台管理功能和强大的搜索能力，可以满足心理测评平台的各种业务需求。
