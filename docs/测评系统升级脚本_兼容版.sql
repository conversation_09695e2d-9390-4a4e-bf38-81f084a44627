-- =====================================================
-- 测评系统升级脚本 - 兼容版本（支持复杂计分和报告生成）
-- =====================================================

-- 1. 扩展 psy_t_scale 表 - 添加计分配置
-- 使用动态SQL确保兼容性

-- 添加 scoring_method 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_scale' AND COLUMN_NAME = 'scoring_method') = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN scoring_method varchar(50) COMMENT ''计分方法''',
    'SELECT ''scoring_method字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 has_reverse_items 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_scale' AND COLUMN_NAME = 'has_reverse_items') = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN has_reverse_items tinyint DEFAULT 0 COMMENT ''是否有反向计分题''',
    'SELECT ''has_reverse_items字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 has_standard_score 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_scale' AND COLUMN_NAME = 'has_standard_score') = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN has_standard_score tinyint DEFAULT 0 COMMENT ''是否需要标准分转换''',
    'SELECT ''has_standard_score字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 standard_score_multiplier 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_scale' AND COLUMN_NAME = 'standard_score_multiplier') = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN standard_score_multiplier decimal(5,2) DEFAULT 1.0 COMMENT ''标准分转换系数''',
    'SELECT ''standard_score_multiplier字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 dimension_count 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_scale' AND COLUMN_NAME = 'dimension_count') = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN dimension_count int DEFAULT 1 COMMENT ''维度数量''',
    'SELECT ''dimension_count字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 raw_score_range 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_scale' AND COLUMN_NAME = 'raw_score_range') = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN raw_score_range varchar(20) COMMENT ''原始分范围''',
    'SELECT ''raw_score_range字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 standard_score_range 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_scale' AND COLUMN_NAME = 'standard_score_range') = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN standard_score_range varchar(20) COMMENT ''标准分范围''',
    'SELECT ''standard_score_range字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 扩展 psy_t_subscale 表 - 添加基础分数支持
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_subscale' AND COLUMN_NAME = 'base_score') = 0,
    'ALTER TABLE psy_t_subscale ADD COLUMN base_score int DEFAULT 0 COMMENT ''基础分数''',
    'SELECT ''base_score字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 扩展 psy_t_subscale_question_rel 表 - 添加系数支持
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_subscale_question_rel' AND COLUMN_NAME = 'coefficient') = 0,
    'ALTER TABLE psy_t_subscale_question_rel ADD COLUMN coefficient int DEFAULT 1 COMMENT ''系数(+1或-1)''',
    'SELECT ''coefficient字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 扩展 psy_t_question 表 - 完善反向计分和特殊规则
-- 修改 reverse_value 字段注释
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_question' AND COLUMN_NAME = 'reverse_value') > 0,
    'ALTER TABLE psy_t_question MODIFY COLUMN reverse_value int COMMENT ''反向计分最大值(如5点量表反向计分用5)''',
    'SELECT ''reverse_value字段不存在，跳过修改'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 scoring_rule 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_question' AND COLUMN_NAME = 'scoring_rule') = 0,
    'ALTER TABLE psy_t_question ADD COLUMN scoring_rule json COMMENT ''特殊计分规则配置''',
    'SELECT ''scoring_rule字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 扩展 psy_t_interpretation 表 - 支持详细解释
-- 添加 score_type 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'score_type') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN score_type varchar(20) DEFAULT ''RAW'' COMMENT ''分数类型(RAW原始分/STANDARD标准分)''',
    'SELECT ''score_type字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 severity_level 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'severity_level') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN severity_level varchar(50) COMMENT ''严重程度等级''',
    'SELECT ''severity_level字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 fear_level 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'fear_level') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN fear_level varchar(20) COMMENT ''恐惧等级''',
    'SELECT ''fear_level字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 performance_desc 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'performance_desc') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN performance_desc text COMMENT ''说明与表现''',
    'SELECT ''performance_desc字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 impact_analysis 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'impact_analysis') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN impact_analysis text COMMENT ''可能影响''',
    'SELECT ''impact_analysis字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 guidance_suggestions 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'guidance_suggestions') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN guidance_suggestions text COMMENT ''建议与指导''',
    'SELECT ''guidance_suggestions字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 clinical_significance 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'clinical_significance') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN clinical_significance text COMMENT ''临床意义''',
    'SELECT ''clinical_significance字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 recommendations 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'recommendations') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN recommendations text COMMENT ''建议措施''',
    'SELECT ''recommendations字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 risk_level 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_interpretation' AND COLUMN_NAME = 'risk_level') = 0,
    'ALTER TABLE psy_t_interpretation ADD COLUMN risk_level varchar(20) COMMENT ''风险等级''',
    'SELECT ''risk_level字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 创建计分配置表
CREATE TABLE IF NOT EXISTS psy_t_scoring_config (
    id bigint AUTO_INCREMENT PRIMARY KEY,
    scale_id bigint NOT NULL,
    scoring_type varchar(50) NOT NULL COMMENT 'SIMPLE_SUM, REVERSE_SCORING, FORMULA, STANDARD_SCORE',
    formula_config json COMMENT '存储复杂计分公式',
    reverse_items json COMMENT '反向计分题目',
    standard_multiplier decimal(5,2) COMMENT '标准分转换系数',
    create_time datetime DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_scale_id (scale_id)
) COMMENT '计分配置表';

-- 7. 扩展 psy_t_assessment_record 表 - 支持维度分数和报告
-- 添加 dimension_scores 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_assessment_record' AND COLUMN_NAME = 'dimension_scores') = 0,
    'ALTER TABLE psy_t_assessment_record ADD COLUMN dimension_scores json COMMENT ''各维度分数''',
    'SELECT ''dimension_scores字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 standard_score 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_assessment_record' AND COLUMN_NAME = 'standard_score') = 0,
    'ALTER TABLE psy_t_assessment_record ADD COLUMN standard_score decimal(8,2) COMMENT ''标准分''',
    'SELECT ''standard_score字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 report_generated 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_assessment_record' AND COLUMN_NAME = 'report_generated') = 0,
    'ALTER TABLE psy_t_assessment_record ADD COLUMN report_generated tinyint DEFAULT 0 COMMENT ''报告是否已生成''',
    'SELECT ''report_generated字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 report_content 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'psy_t_assessment_record' AND COLUMN_NAME = 'report_content') = 0,
    'ALTER TABLE psy_t_assessment_record ADD COLUMN report_content longtext COMMENT ''完整报告内容''',
    'SELECT ''report_content字段已存在'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT '数据库结构升级完成！' as message;
