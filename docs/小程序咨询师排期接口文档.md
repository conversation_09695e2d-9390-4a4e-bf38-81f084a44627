# 小程序咨询师排期接口文档

## 概述

本文档描述了小程序端咨询师排期管理的所有接口，包括查询、创建、修改、删除等功能。

## 基础信息

- **基础路径**: `/wechat/counselor/schedule`
- **返回格式**: JSON
- **认证方式**: 基于Token的身份认证

## 接口列表

### 1. 查询接口

#### 1.1 获取咨询师排期列表
```
GET /wechat/counselor/schedule/list
```

**参数**:
- `counselorId` (可选): 咨询师ID
- `startDate` (可选): 开始日期 (YYYY-MM-DD)
- `endDate` (可选): 结束日期 (YYYY-MM-DD)
- `centerId` (可选): 咨询中心ID
- `isWorking` (可选): 工作状态 (1=工作, 0=休假, 默认=1)

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "counselorId": 1,
      "counselorName": "张医生",
      "scheduleDate": "2024-01-15",
      "dateDisplay": "2024年1月15日",
      "weekDayDisplay": "周一",
      "isToday": false,
      "startTime": "09:00:00",
      "endTime": "18:00:00",
      "timeDisplay": "09:00-18:00",
      "isWorking": 1,
      "workingStatusDisplay": "工作"
    }
  ]
}
```

#### 1.2 获取排期详情
```
GET /wechat/counselor/schedule/detail/{id}
```

#### 1.3 根据咨询师和日期查询排期
```
GET /wechat/counselor/schedule/counselor/{counselorId}?scheduleDate=2024-01-15
```

#### 1.4 获取日期范围内的排期
```
GET /wechat/counselor/schedule/counselor/{counselorId}/range?startDate=2024-01-15&endDate=2024-01-21
```

#### 1.5 获取指定日期的所有咨询师排期
```
GET /wechat/counselor/schedule/date/{scheduleDate}?centerId=1
```

### 2. 创建接口

#### 2.1 创建单个排期
```
POST /wechat/counselor/schedule/create
```

**请求体**:
```json
{
  "counselorId": 1,
  "scheduleDate": "2024-01-15",
  "startTime": "09:00:00",
  "endTime": "18:00:00",
  "centerId": 1,
  "isWorking": 1,
  "remark": "正常工作日"
}
```

#### 2.2 批量创建排期
```
POST /wechat/counselor/schedule/create/batch
```

**请求体**:
```json
[
  {
    "counselorId": 1,
    "scheduleDate": "2024-01-15",
    "startTime": "09:00:00",
    "endTime": "18:00:00",
    "centerId": 1,
    "isWorking": 1
  },
  {
    "counselorId": 1,
    "scheduleDate": "2024-01-16",
    "startTime": "09:00:00",
    "endTime": "18:00:00",
    "centerId": 1,
    "isWorking": 1
  }
]
```

#### 2.3 生成默认排期
```
POST /wechat/counselor/schedule/generate/default?counselorId=1&startDate=2024-01-15&endDate=2024-01-21&centerId=1
```

### 3. 修改接口

#### 3.1 更新排期
```
PUT /wechat/counselor/schedule/update
```

**请求体**:
```json
{
  "id": 1,
  "startTime": "10:00:00",
  "endTime": "19:00:00",
  "remark": "调整工作时间"
}
```

#### 3.2 切换工作状态
```
PUT /wechat/counselor/schedule/toggle/working/{id}
```

### 4. 删除接口

#### 4.1 删除单个排期
```
DELETE /wechat/counselor/schedule/delete/{id}
```

#### 4.2 批量删除排期
```
DELETE /wechat/counselor/schedule/delete/batch
```

**请求体**:
```json
[1, 2, 3, 4, 5]
```

### 5. 高级功能接口

#### 5.1 复制排期
```
POST /wechat/counselor/schedule/copy?sourceScheduleId=1&targetDates=2024-01-16,2024-01-17
```

#### 5.2 获取排期日历视图
```
GET /wechat/counselor/schedule/calendar/{counselorId}?year=2024&month=1
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "yearMonth": "2024-01",
    "monthDisplay": "2024年1月",
    "days": [
      {
        "date": "2024-01-15",
        "dayDisplay": "15",
        "isToday": false,
        "isCurrentMonth": true,
        "hasSchedule": true,
        "workingStatus": 1,
        "statusType": "work",
        "timeDisplay": "09:00-18:00",
        "scheduleId": 1
      }
    ]
  }
}
```

#### 5.3 获取排期统计
```
GET /wechat/counselor/schedule/statistics/{counselorId}?startDate=2024-01-01&endDate=2024-01-31
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "totalDays": 31,
    "workingDays": 22,
    "restDays": 9,
    "workingRate": 70.97,
    "totalWorkingHours": 176.0,
    "averageDailyHours": 8.0
  }
}
```

#### 5.4 获取近期排期概览
```
GET /wechat/counselor/schedule/overview/{counselorId}?days=7
```

#### 5.5 获取时间段分析
```
GET /wechat/counselor/schedule/timerange/analysis/{counselorId}?date=2024-01-15
```

#### 5.6 检查排期冲突
```
GET /wechat/counselor/schedule/conflict/check?counselorId=1&scheduleDate=2024-01-15&excludeScheduleId=1
```

#### 5.7 获取排期建议
```
GET /wechat/counselor/schedule/suggestions/{counselorId}?date=2024-01-15
```

#### 5.8 批量操作排期
```
POST /wechat/counselor/schedule/batch/operate
```

**请求体示例（复制操作）**:
```json
{
  "operationType": "copy",
  "sourceScheduleId": 1,
  "targetDates": ["2024-01-16", "2024-01-17", "2024-01-18"],
  "remark": "批量复制排期"
}
```

#### 5.9 验证排期数据
```
POST /wechat/counselor/schedule/validate
```

**请求体**:
```json
{
  "counselorId": 1,
  "scheduleDate": "2024-01-15",
  "startTime": "09:00:00",
  "endTime": "18:00:00"
}
```

## 错误码说明

- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用示例

### 创建一周的工作排期
```javascript
// 1. 先生成默认排期
const generateResponse = await fetch('/wechat/counselor/schedule/generate/default', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    counselorId: 1,
    startDate: '2024-01-15',
    endDate: '2024-01-21',
    centerId: 1
  })
});

// 2. 获取生成的排期列表
const listResponse = await fetch('/wechat/counselor/schedule/list?counselorId=1&startDate=2024-01-15&endDate=2024-01-21');
const schedules = await listResponse.json();
```

### 获取月度排期日历
```javascript
const calendarResponse = await fetch('/wechat/counselor/schedule/calendar/1?year=2024&month=1');
const calendar = await calendarResponse.json();
```

## 注意事项

1. 所有日期参数格式为 `YYYY-MM-DD`
2. 所有时间参数格式为 `HH:mm:ss`
3. 创建排期前建议先调用验证接口检查数据
4. 批量操作时注意数据量限制
5. 删除操作不可恢复，请谨慎使用
