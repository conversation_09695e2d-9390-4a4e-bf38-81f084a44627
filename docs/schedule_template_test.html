<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排班模板功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .api-section {
            margin-bottom: 30px;
        }
        .api-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        .api-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
        }
        .api-method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .method-get { background: #28a745; }
        .method-post { background: #007bff; }
        .method-put { background: #ffc107; color: #333; }
        .method-delete { background: #dc3545; }
        .api-content {
            padding: 15px;
            display: none;
        }
        .api-content.active {
            display: block;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>排班模板功能测试页面</h1>
        <p>此页面用于测试排班模板相关的API接口功能。</p>
        
        <div class="api-section">
            <h2>排班模板管理接口</h2>
            
            <div class="api-item">
                <div class="api-header" onclick="toggleApi(this)">
                    <span class="api-method method-get">GET</span>
                    <span>/system/schedule/template/list - 查询模板列表</span>
                </div>
                <div class="api-content">
                    <p>查询排班模板列表，支持分页和条件查询。</p>
                    <pre>curl -X GET "http://localhost:8080/system/schedule/template/list?pageNum=1&pageSize=10&counselorId=1"</pre>
                    <button class="test-button" onclick="testApi('GET', '/system/schedule/template/list?pageNum=1&pageSize=10', null, this)">测试接口</button>
                    <div class="result" id="result-1" style="display:none;"></div>
                </div>
            </div>
            
            <div class="api-item">
                <div class="api-header" onclick="toggleApi(this)">
                    <span class="api-method method-get">GET</span>
                    <span>/system/schedule/template/{id} - 获取模板详情</span>
                </div>
                <div class="api-content">
                    <p>根据ID获取排班模板详细信息，包括模板明细。</p>
                    <pre>curl -X GET "http://localhost:8080/system/schedule/template/1"</pre>
                    <button class="test-button" onclick="testApi('GET', '/system/schedule/template/1', null, this)">测试接口</button>
                    <div class="result" id="result-2" style="display:none;"></div>
                </div>
            </div>
            
            <div class="api-item">
                <div class="api-header" onclick="toggleApi(this)">
                    <span class="api-method method-get">GET</span>
                    <span>/system/schedule/template/counselor/{counselorId} - 查询咨询师模板</span>
                </div>
                <div class="api-content">
                    <p>根据咨询师ID查询所有排班模板。</p>
                    <pre>curl -X GET "http://localhost:8080/system/schedule/template/counselor/1"</pre>
                    <button class="test-button" onclick="testApi('GET', '/system/schedule/template/counselor/1', null, this)">测试接口</button>
                    <div class="result" id="result-3" style="display:none;"></div>
                </div>
            </div>
            
            <div class="api-item">
                <div class="api-header" onclick="toggleApi(this)">
                    <span class="api-method method-get">GET</span>
                    <span>/system/schedule/template/counselor/{counselorId}/default - 查询默认模板</span>
                </div>
                <div class="api-content">
                    <p>查询咨询师的默认排班模板。</p>
                    <pre>curl -X GET "http://localhost:8080/system/schedule/template/counselor/1/default"</pre>
                    <button class="test-button" onclick="testApi('GET', '/system/schedule/template/counselor/1/default', null, this)">测试接口</button>
                    <div class="result" id="result-4" style="display:none;"></div>
                </div>
            </div>
            
            <div class="api-item">
                <div class="api-header" onclick="toggleApi(this)">
                    <span class="api-method method-post">POST</span>
                    <span>/system/schedule/template - 新增模板</span>
                </div>
                <div class="api-content">
                    <p>创建新的排班模板，包括模板明细。</p>
                    <pre>{
  "counselorId": 1,
  "name": "测试模板",
  "isDefault": 0,
  "effectiveStart": "2025-07-01",
  "effectiveEnd": "2025-12-31",
  "templateItems": [
    {
      "dayOfWeek": 1,
      "startTime": "09:00",
      "endTime": "12:00",
      "centerId": 1
    },
    {
      "dayOfWeek": 1,
      "startTime": "14:00",
      "endTime": "18:00",
      "centerId": 1
    }
  ]
}</pre>
                    <button class="test-button" onclick="testCreateTemplate(this)">测试接口</button>
                    <div class="result" id="result-5" style="display:none;"></div>
                </div>
            </div>
            
            <div class="api-item">
                <div class="api-header" onclick="toggleApi(this)">
                    <span class="api-method method-post">POST</span>
                    <span>/system/schedule/template/createDefault/{counselorId} - 创建默认模板</span>
                </div>
                <div class="api-content">
                    <p>为咨询师创建默认排班模板（周一到周五 9:00-18:00）。</p>
                    <pre>curl -X POST "http://localhost:8080/system/schedule/template/createDefault/1?centerId=1"</pre>
                    <button class="test-button" onclick="testApi('POST', '/system/schedule/template/createDefault/1?centerId=1', null, this)">测试接口</button>
                    <div class="result" id="result-6" style="display:none;"></div>
                </div>
            </div>
        </div>
        
        <div class="api-section">
            <h2>模板明细管理接口</h2>
            
            <div class="api-item">
                <div class="api-header" onclick="toggleApi(this)">
                    <span class="api-method method-get">GET</span>
                    <span>/system/schedule/template/item/template/{templateId} - 查询模板明细</span>
                </div>
                <div class="api-content">
                    <p>根据模板ID查询所有明细记录。</p>
                    <pre>curl -X GET "http://localhost:8080/system/schedule/template/item/template/1"</pre>
                    <button class="test-button" onclick="testApi('GET', '/system/schedule/template/item/template/1', null, this)">测试接口</button>
                    <div class="result" id="result-7" style="display:none;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基础URL配置
        const BASE_URL = 'http://localhost:8080';
        
        // 切换API详情显示
        function toggleApi(header) {
            const content = header.nextElementSibling;
            content.classList.toggle('active');
        }
        
        // 通用API测试函数
        async function testApi(method, url, data, button) {
            const resultDiv = button.nextElementSibling;
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '请求中...';
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(BASE_URL + url, options);
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `<strong>成功:</strong><br><pre>${JSON.stringify(result, null, 2)}</pre>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>错误:</strong><br><pre>${JSON.stringify(result, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>网络错误:</strong><br>${error.message}`;
            }
        }
        
        // 测试创建模板
        function testCreateTemplate(button) {
            const data = {
                counselorId: 1,
                name: "测试模板_" + new Date().getTime(),
                isDefault: 0,
                effectiveStart: "2025-07-01",
                effectiveEnd: "2025-12-31",
                templateItems: [
                    {
                        dayOfWeek: 1,
                        startTime: "09:00",
                        endTime: "12:00",
                        centerId: 1
                    },
                    {
                        dayOfWeek: 1,
                        startTime: "14:00",
                        endTime: "18:00",
                        centerId: 1
                    }
                ]
            };
            
            testApi('POST', '/system/schedule/template', data, button);
        }
        
        // 页面加载完成后展开第一个API
        document.addEventListener('DOMContentLoaded', function() {
            const firstApi = document.querySelector('.api-content');
            if (firstApi) {
                firstApi.classList.add('active');
            }
        });
    </script>
</body>
</html>
