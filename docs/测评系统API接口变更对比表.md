# 测评系统API接口变更对比表

## 概述

本文档对比了测评系统新旧API接口的差异，帮助前端开发人员快速了解需要修改的接口。

## 接口变更对比

### 1. 量表相关接口

| 功能 | 旧接口 | 新接口 | 变更说明 |
|------|--------|--------|----------|
| 查询量表列表 | `GET /miniapp/user/assessment/scale/list` | `GET /miniapp/user/assessment/scales` | 路径简化 |
| 获取量表详情 | `GET /miniapp/user/assessment/scale/{id}` | `GET /miniapp/user/assessment/scales/{id}` | 路径简化 |
| 搜索量表 | `GET /miniapp/user/assessment/scale/search` | `GET /miniapp/user/assessment/scales/search` | 路径简化 |
| 热门量表 | `GET /miniapp/user/assessment/scale/hot` | `GET /miniapp/user/assessment/scales/hot` | 路径简化 |
| 最新量表 | `GET /miniapp/user/assessment/scale/latest` | `GET /miniapp/user/assessment/scales/latest` | 路径简化 |
| 分类量表 | 无 | `GET /miniapp/user/assessment/scales/category/{categoryId}` | 新增接口 |
| 收藏量表 | 无 | `GET /miniapp/user/assessment/favorites/{userId}` | 新增接口 |
| 推荐量表 | 无 | `GET /miniapp/user/assessment/recommendations/{scaleId}` | 新增接口 |

### 2. 测评流程接口

| 功能 | 旧接口 | 新接口 | 变更说明 |
|------|--------|--------|----------|
| 检查测评权限 | 无 | `GET /miniapp/user/assessment/check-can-start` | 新增接口 |
| 开始测评 | `POST /miniapp/user/assessment/test/start` | `POST /miniapp/user/assessment/start` | 路径简化，参数变更 |
| 获取题目 | 无 | `GET /miniapp/user/assessment/questions/{recordId}` | 新增接口 |
| 获取下一题 | 无 | `GET /miniapp/user/assessment/next-question/{recordId}` | 新增接口 |
| 获取上一题 | 无 | `GET /miniapp/user/assessment/previous-question/{recordId}` | 新增接口 |
| 提交答案 | `POST /miniapp/user/assessment/test/submit` | `POST /miniapp/user/assessment/answer` | 路径简化，参数变更 |
| 查询进度 | 无 | `GET /miniapp/user/assessment/progress/{recordId}` | 新增接口 |
| 暂停测评 | 无 | `POST /miniapp/user/assessment/pause/{recordId}` | 新增接口 |
| 恢复测评 | 无 | `POST /miniapp/user/assessment/resume/{recordId}` | 新增接口 |
| 完成测评 | `POST /miniapp/user/assessment/test/complete` | `POST /miniapp/user/assessment/complete/{recordId}` | 路径变更，参数变更 |
| 取消测评 | 无 | `POST /miniapp/user/assessment/cancel/{recordId}` | 新增接口 |

### 3. 测评结果接口

| 功能 | 旧接口 | 新接口 | 变更说明 |
|------|--------|--------|----------|
| 获取测评结果 | `GET /miniapp/user/assessment/test/result/{sessionId}` | `GET /miniapp/user/assessment/result/{recordId}` | 参数从sessionId改为recordId |
| 生成测评报告 | 无 | `GET /miniapp/user/assessment/report/{recordId}` | 新增接口 |

### 4. 测评记录接口

| 功能 | 旧接口 | 新接口 | 变更说明 |
|------|--------|--------|----------|
| 用户测评记录 | `GET /miniapp/user/assessment/record/list` | `GET /miniapp/user/assessment/records/{userId}` | 路径变更，增加userId参数 |
| 最近测评记录 | 无 | `GET /miniapp/user/assessment/records/{userId}/recent` | 新增接口 |
| 未完成记录 | 无 | `GET /miniapp/user/assessment/records/{userId}/incomplete` | 新增接口 |
| 已完成记录 | 无 | `GET /miniapp/user/assessment/records/{userId}/completed` | 新增接口 |
| 用户测评统计 | 无 | `GET /miniapp/user/assessment/stats/{userId}` | 新增接口 |

### 5. 测评订单接口

| 功能 | 旧接口 | 新接口 | 变更说明 |
|------|--------|--------|----------|
| 查询订单列表 | 无 | `GET /miniapp/user/order/list` | 新增接口 |
| 查询订单详情 | 无 | `GET /miniapp/user/order/{id}` | 新增接口 |
| 创建订单 | 无 | `POST /miniapp/user/order/create` | 新增接口 |
| 支付订单 | 无 | `POST /miniapp/user/order/pay` | 新增接口 |
| 取消订单 | 无 | `POST /miniapp/user/order/cancel` | 新增接口 |
| 申请退款 | 无 | `POST /miniapp/user/order/refund` | 新增接口 |
| 待支付订单 | 无 | `GET /miniapp/user/order/pending` | 新增接口 |
| 已支付订单 | 无 | `GET /miniapp/user/order/paid` | 新增接口 |

### 6. 测评评价接口

| 功能 | 旧接口 | 新接口 | 变更说明 |
|------|--------|--------|----------|
| 提交评价 | 无 | `POST /miniapp/user/assessment/review` | 新增接口 |
| 量表评价列表 | 无 | `GET /miniapp/user/assessment/reviews/{scaleId}` | 新增接口 |
| 用户评价列表 | 无 | `GET /miniapp/user/assessment/reviews/user` | 新增接口 |
| 评价详情 | 无 | `GET /miniapp/user/assessment/review/{id}` | 新增接口 |
| 根据记录获取评价 | 无 | `GET /miniapp/user/assessment/review/record/{recordId}` | 新增接口 |
| 检查评价权限 | 无 | `GET /miniapp/user/assessment/review/check` | 新增接口 |
| 评价统计 | 无 | `GET /miniapp/user/assessment/review/stats/{scaleId}` | 新增接口 |
| 用户评价统计 | 无 | `GET /miniapp/user/assessment/review/stats/user` | 新增接口 |
| 评价摘要 | 无 | `GET /miniapp/user/assessment/review/summary/{scaleId}` | 新增接口 |
| 热门评价 | 无 | `GET /miniapp/user/assessment/review/hot` | 新增接口 |
| 搜索评价 | 无 | `GET /miniapp/user/assessment/review/search` | 新增接口 |

## 参数变更对比

### 1. 开始测评接口

**旧接口参数：**
```json
{
  "scaleId": 1,
  "isAnonymous": 0
}
```

**新接口参数：**
```
GET参数: userId=1001&scaleId=1
```

### 2. 提交答案接口

**旧接口参数：**
```json
{
  "sessionId": "session_001",
  "questionId": 1,
  "optionId": 2,
  "timeSpent": 30
}
```

**新接口参数：**
```
GET参数: recordId=123&questionId=1&optionId=2&answerContent=&responseTime=30
```

### 3. 完成测评接口

**旧接口参数：**
```json
{
  "sessionId": "session_001"
}
```

**新接口参数：**
```
路径参数: /complete/{recordId}
```

## 响应数据变更对比

### 1. 量表数据结构

**旧数据结构：**
```json
{
  "id": 1,
  "scaleName": "焦虑自评量表",
  "scaleCode": "SAS",
  "description": "量表描述",
  "questionCount": 20,
  "timeLimit": 15,
  "price": 0.00,
  "isFree": 1,
  "status": 1
}
```

**新数据结构：**
```json
{
  "id": 1,
  "name": "焦虑自评量表",
  "code": "SAS",
  "alias": "焦虑量表",
  "description": "量表描述",
  "instruction": "测评说明",
  "author": "Zung",
  "version": "1.0",
  "questionCount": 20,
  "timeLimit": 1800,
  "coverImage": "https://example.com/cover.jpg",
  "tags": "焦虑,情绪,心理健康",
  "payMode": 0,
  "originalPrice": 0.00,
  "currentPrice": 0.00,
  "viewCount": 150,
  "testCount": 89,
  "ratingAvg": 4.5,
  "status": 1
}
```

### 2. 测评记录数据结构

**旧数据结构：**
```json
{
  "id": 1,
  "sessionId": "session_001",
  "scaleId": 1,
  "startTime": "2024-12-01 10:00:00",
  "status": 0
}
```

**新数据结构：**
```json
{
  "id": 123,
  "scaleId": 1,
  "userId": 1001,
  "sessionId": "uuid-session-id",
  "startTime": "2024-12-01 10:00:00",
  "endTime": "2024-12-01 10:15:00",
  "totalScore": 35.0,
  "resultLevel": "正常",
  "currentQuestionNo": 20,
  "answeredQuestions": 20,
  "totalQuestions": 20,
  "progress": 100.0,
  "duration": 900,
  "status": 2
}
```

## 新增功能接口

### 1. 测评权限检查
- **接口**: `GET /miniapp/user/assessment/check-can-start`
- **功能**: 检查用户是否可以开始测评
- **返回**: 权限状态、剩余次数、冷却时间等

### 2. 测评进度管理
- **接口**: `GET /miniapp/user/assessment/progress/{recordId}`
- **功能**: 查询当前测评进度
- **返回**: 当前题目、总题数、已答题数、进度百分比等

### 3. 测评状态控制
- **暂停**: `POST /miniapp/user/assessment/pause/{recordId}`
- **恢复**: `POST /miniapp/user/assessment/resume/{recordId}`
- **取消**: `POST /miniapp/user/assessment/cancel/{recordId}`

### 4. 题目导航
- **下一题**: `GET /miniapp/user/assessment/next-question/{recordId}`
- **上一题**: `GET /miniapp/user/assessment/previous-question/{recordId}`

### 5. 用户统计
- **接口**: `GET /miniapp/user/assessment/stats/{userId}`
- **功能**: 查询用户测评统计信息
- **返回**: 总测评数、完成数、平均分等

## 迁移建议

1. **优先级排序**：
   - 高优先级：量表列表、开始测评、提交答案、完成测评
   - 中优先级：测评记录、测评结果
   - 低优先级：新增功能接口

2. **分阶段迁移**：
   - 第一阶段：核心测评流程接口
   - 第二阶段：辅助功能接口
   - 第三阶段：新增功能接口

3. **兼容性处理**：
   - 保留旧接口调用逻辑作为备用
   - 通过配置开关控制新旧接口使用
   - 逐步验证新接口稳定性后完全切换

4. **测试验证**：
   - 每个接口修改后进行单独测试
   - 完整流程测试
   - 异常情况测试
