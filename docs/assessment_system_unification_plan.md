# 测评系统统一整合方案

## 🎯 整合目标

将当前混乱的两套测评体系统一为一套完整、准确的系统，确保数据库字段与实体类完全匹配。

## 📋 推荐方案：统一到 PsyT 系列

### 理由
1. **PsyT 系列更完整**：包含企业版功能
2. **命名更规范**：符合表名前缀 `psy_t_`
3. **架构更清晰**：分层更明确
4. **扩展性更好**：支持更多业务场景

## 🔧 具体实施步骤

### 第一阶段：修复当前错误（紧急）

#### 1. 修复 PsyTAnswerRecord 实体类
```java
// 确保字段与数据库完全匹配
public class PsyTAnswerRecord extends BaseEntity {
    private Long id;
    private Long recordId;
    private Long questionId;
    private Long optionId;
    private String answerContent;        // ✅ 对应 answer_content
    private BigDecimal answerScore;      // ✅ 对应 answer_score  
    private Date answerTime;             // ✅ 对应 answer_time
    private Integer responseTime;        // ✅ 对应 response_time
    private Integer delFlag;             // ✅ 对应 del_flag
    // BaseEntity 提供: createBy, createTime, updateBy, updateTime
}
```

#### 2. 修复 PsyTAnswerRecordMapper.xml
```xml
<!-- ✅ 正确的字段映射 -->
<resultMap id="AnswerRecordResultMap" type="PsyTAnswerRecord">
    <result property="answerContent" column="answer_content"/>
    <result property="answerScore" column="answer_score"/>
    <result property="responseTime" column="response_time"/>
</resultMap>

<!-- ✅ 正确的表名 -->
<select id="selectAnswerRecordList">
    SELECT * FROM psy_t_answer_record WHERE del_flag = 0
</select>
```

#### 3. 完善 PsyTScale 实体类
```java
public class PsyTScale extends BaseEntity {
    // 基础字段
    private Long id;
    private String name;
    private String code;
    private Integer categoryId;          // ✅ 添加分类ID
    private String description;
    private String introduction;
    
    // 测评详细信息字段
    private String testNotice;           // ✅ 添加
    private String testPurpose;          // ✅ 添加
    private String testObject;           // ✅ 添加
    private String testPreparation;      // ✅ 添加
    private String testProcessing;       // ✅ 添加
    private String testAttention;        // ✅ 添加
    private String testTheory;           // ✅ 添加
    private String testApplication;      // ✅ 添加
    private String referenceLiterature;  // ✅ 添加
    
    // 量表配置字段
    private Integer questionCount;
    private String scoringType;
    private String duration;
    private BigDecimal normMean;
    private BigDecimal normSd;
    private String applicableAge;
    private String imageUrl;
    private BigDecimal price;
    private Integer payMode;
    private Integer payPhase;
    private Integer freeVipLevel;
    private Integer freeReportLevel;
    private Integer paidReportLevel;
    private Long enterpriseId;
    private Integer status;
    private Integer sort;
    private String searchKeywords;
    private Integer searchCount;
    private Integer viewCount;
    
    // 删除多余字段
    // ❌ 删除 author, version, alias, originalPrice, currentPrice
}
```

### 第二阶段：删除冗余文件

#### 1. 删除 PsyAssessment 系列实体类
```
❌ PsyAssessmentScale.java
❌ PsyAssessmentQuestion.java  
❌ PsyAssessmentAnswer.java
❌ PsyAssessmentRecord.java
❌ PsyAssessmentOrder.java
❌ PsyAssessmentOption.java
❌ PsyAssessmentInterpretation.java
```

#### 2. 删除 PsyAssessment 系列 Mapper
```
❌ PsyAssessmentScaleMapper.java
❌ PsyAssessmentQuestionMapper.java
❌ PsyAssessmentAnswerMapper.java
❌ PsyAssessmentRecordMapper.java
❌ PsyAssessmentOrderMapper.java
```

#### 3. 删除 PsyAssessment 系列 XML
```
❌ PsyAssessmentScaleMapper.xml
❌ PsyAssessmentQuestionMapper.xml
❌ PsyAssessmentAnswerMapper.xml
❌ PsyAssessmentRecordMapper.xml
❌ PsyAssessmentOrderMapper.xml
```

#### 4. 删除 PsyAssessment 系列服务
```
❌ PsyAssessmentScaleService.java
❌ PsyAssessmentScaleServiceImpl.java
❌ PsyAssessmentQuestionService.java
❌ PsyAssessmentQuestionServiceImpl.java
```

### 第三阶段：完善 PsyT 系列

#### 1. 创建缺失的实体类
```java
// 基于数据库表创建完整的实体类
PsyTQuestion.java           // ✅ 已存在，需完善
PsyTQuestionOption.java     // ✅ 需创建
PsyTSubscale.java          // ✅ 已存在
PsyTScoringRule.java       // ✅ 需创建
PsyTInterpretation.java    // ✅ 需创建
```

#### 2. 创建缺失的 Mapper
```java
PsyTQuestionOptionMapper.java
PsyTScoringRuleMapper.java
PsyTInterpretationMapper.java
```

#### 3. 创建缺失的服务
```java
PsyTQuestionOptionService.java
PsyTScoringRuleService.java
PsyTInterpretationService.java
```

### 第四阶段：统一控制器

#### 1. 重命名控制器
```java
// 统一控制器命名
PsyTScaleController.java        // 量表管理
PsyTQuestionController.java     // 题目管理
PsyTAnswerRecordController.java // 答题记录
PsyTAssessmentRecordController.java // 测评记录
```

#### 2. 统一 API 路径
```java
// 管理后台 API
@RequestMapping("/system/scale")        // 量表管理
@RequestMapping("/system/question")     // 题目管理
@RequestMapping("/system/answer")       // 答题记录
@RequestMapping("/system/record")       // 测评记录

// 小程序 API  
@RequestMapping("/miniapp/user/assessment/scale")    // 量表列表
@RequestMapping("/miniapp/user/assessment/question") // 题目获取
@RequestMapping("/miniapp/user/assessment/answer")   // 答题提交
@RequestMapping("/miniapp/user/assessment/record")   // 测评记录
```

## 📋 数据迁移计划

### 1. 备份现有数据
```sql
-- 备份所有测评相关表
CREATE TABLE psy_t_scale_backup AS SELECT * FROM psy_t_scale;
CREATE TABLE psy_t_question_backup AS SELECT * FROM psy_t_question;
CREATE TABLE psy_t_answer_record_backup AS SELECT * FROM psy_t_answer_record;
CREATE TABLE psy_t_assessment_record_backup AS SELECT * FROM psy_t_assessment_record;
```

### 2. 验证数据完整性
```sql
-- 验证关键数据
SELECT COUNT(*) FROM psy_t_scale WHERE del_flag = '0';
SELECT COUNT(*) FROM psy_t_question WHERE del_flag = '0';
SELECT COUNT(*) FROM psy_t_answer_record WHERE del_flag = 0;
SELECT COUNT(*) FROM psy_t_assessment_record WHERE del_flag = '0';
```

### 3. 测试新系统
```bash
# 运行完整的测试套件
mvn test -Dtest=PsyT*Test
```

## 🚀 部署计划

### 阶段一：修复当前错误（1天）
1. 修复 PsyTAnswerRecord 字段映射
2. 修复 XML 文件错误
3. 验证基础功能

### 阶段二：删除冗余文件（1天）
1. 逐步删除 PsyAssessment 系列文件
2. 更新依赖引用
3. 测试编译通过

### 阶段三：完善功能（2-3天）
1. 完善 PsyT 系列实体类
2. 创建缺失的组件
3. 统一 API 接口

### 阶段四：全面测试（1-2天）
1. 单元测试
2. 集成测试
3. 功能测试
4. 性能测试

## ✅ 验收标准

1. **编译通过**：无任何编译错误
2. **字段匹配**：所有实体类字段与数据库完全匹配
3. **功能完整**：所有测评功能正常工作
4. **性能稳定**：响应时间在可接受范围内
5. **数据一致**：新旧系统数据完全一致

## ⚠️ 风险控制

1. **分支管理**：在独立分支进行整合
2. **灰度发布**：先在测试环境验证
3. **回滚方案**：保留原有代码备份
4. **监控告警**：实时监控系统状态
5. **应急预案**：准备快速回滚机制

## 🎉 预期收益

1. **代码简化**：减少50%的冗余代码
2. **维护效率**：提升开发和维护效率
3. **系统稳定**：消除字段映射错误
4. **功能完整**：支持完整的测评流程
5. **扩展性强**：便于后续功能扩展
