-- 为现有表添加搜索关键字字段和初始化数据

-- ==================== 1. 检查并添加搜索字段 ====================

-- 1.1 咨询师表添加搜索字段
ALTER TABLE `psy_consultants` 
ADD COLUMN IF NOT EXISTS `search_keywords` TEXT COMMENT '搜索关键词（姓名、专长、地区、标签等）',
ADD COLUMN IF NOT EXISTS `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数',
ADD COLUMN IF NOT EXISTS `view_count` INT(11) DEFAULT 0 COMMENT '查看次数';

-- 1.2 课程表添加搜索字段
ALTER TABLE `psy_course` 
ADD COLUMN IF NOT EXISTS `search_keywords` TEXT COMMENT '搜索关键词（课程名、讲师、标签、简介等）',
ADD COLUMN IF NOT EXISTS `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数';

-- 1.3 冥想表添加搜索字段（检查是否已存在）
ALTER TABLE `psy_meditation` 
ADD COLUMN IF NOT EXISTS `search_keywords` TEXT COMMENT '搜索关键词（标题、引导师、标签、描述等）',
ADD COLUMN IF NOT EXISTS `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数';

-- 1.4 测评量表添加搜索字段
ALTER TABLE `psy_t_scale` 
ADD COLUMN IF NOT EXISTS `search_keywords` TEXT COMMENT '搜索关键词（量表名、描述、标签等）',
ADD COLUMN IF NOT EXISTS `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数';

-- ==================== 2. 初始化咨询师搜索关键词 ====================
UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`name`, ''),
        IFNULL(`personal_intro`, ''),
        IFNULL(`personal_title`, ''),
        IFNULL(`location`, ''),
        IFNULL(`province`, ''),
        IFNULL(`city`, ''),
        IFNULL(`district`, ''),
        CASE 
            WHEN `gender` = '0' THEN '男咨询师,男性咨询师'
            WHEN `gender` = '1' THEN '女咨询师,女性咨询师'
            ELSE ''
        END,
        CASE 
            WHEN `can_teach` = 1 THEN '可讲课,培训师'
            ELSE ''
        END,
        CASE 
            WHEN `can_travel` = 1 THEN '可出差,上门服务'
            ELSE ''
        END,
        CASE 
            WHEN `work_status` = '0' THEN '可预约,在线咨询师'
            WHEN `work_status` = '1' THEN '休息中'
            ELSE ''
        END,
        '心理咨询师,心理医生,心理专家,咨询师',
        CASE 
            WHEN `price` = 0 THEN '免费咨询'
            WHEN `price` > 0 AND `price` <= 200 THEN '经济实惠,低价咨询'
            WHEN `price` > 200 AND `price` <= 500 THEN '中等价位'
            WHEN `price` > 500 THEN '高端咨询,资深专家'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- ==================== 3. 初始化课程搜索关键词 ====================
UPDATE `psy_course` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`title`, ''),
        IFNULL(`summary`, ''),
        IFNULL(`instructor`, ''),
        IFNULL(`tags`, ''),
        CASE 
            WHEN `difficulty_level` = 1 THEN '入门课程,基础课程,新手课程'
            WHEN `difficulty_level` = 2 THEN '进阶课程,中级课程'
            WHEN `difficulty_level` = 3 THEN '高级课程,专业课程,深度课程'
            ELSE ''
        END,
        CASE 
            WHEN `is_free` = 1 THEN '免费课程,公开课'
            WHEN `is_free` = 0 THEN '付费课程,精品课程'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '已发布,可学习'
            WHEN `status` = 0 THEN '未发布'
            WHEN `status` = 2 THEN '已下架'
            ELSE ''
        END,
        '心理学课程,心理课程,在线课程,心理健康课程',
        CASE 
            WHEN `duration` < 1800 THEN '短课程,快速学习'
            WHEN `duration` BETWEEN 1800 AND 7200 THEN '中等时长'
            WHEN `duration` > 7200 THEN '长课程,深度学习'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- ==================== 4. 初始化冥想搜索关键词 ====================
UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`title`, ''),
        IFNULL(`description`, ''),
        IFNULL(`narrator`, ''),
        IFNULL(`tags`, ''),
        CASE 
            WHEN `difficulty_level` = 1 THEN '入门冥想,基础冥想,新手冥想'
            WHEN `difficulty_level` = 2 THEN '进阶冥想,中级冥想'
            WHEN `difficulty_level` = 3 THEN '高级冥想,专业冥想,深度冥想'
            ELSE ''
        END,
        CASE 
            WHEN `is_free` = 1 THEN '免费冥想,公开冥想'
            WHEN `is_free` = 0 THEN '付费冥想,精品冥想'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '已发布,可播放'
            WHEN `status` = 0 THEN '未发布'
            WHEN `status` = 2 THEN '已下架'
            ELSE ''
        END,
        '冥想,冥想音频,引导冥想,正念冥想',
        CASE 
            WHEN `duration` < 300 THEN '短时冥想,快速冥想,5分钟冥想'
            WHEN `duration` BETWEEN 300 AND 1200 THEN '中时冥想,标准冥想,10分钟冥想'
            WHEN `duration` > 1200 THEN '长时冥想,深度冥想,20分钟冥想'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- ==================== 5. 初始化测评量表搜索关键词 ====================
UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`name`, ''),
        IFNULL(`code`, ''),
        IFNULL(`description`, ''),
        IFNULL(`introduction`, ''),
        CASE 
            WHEN `pay_mode` = 0 THEN '免费测评,免费测试'
            WHEN `pay_mode` = 1 THEN '付费测评,专业测评'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '可测评,已发布'
            WHEN `status` = 0 THEN '未发布'
            ELSE ''
        END,
        '心理测评,心理测试,性格测试,情绪测评,心理量表',
        CASE 
            WHEN `question_count` <= 20 THEN '简短测评,快速测试'
            WHEN `question_count` BETWEEN 21 AND 50 THEN '标准测评'
            WHEN `question_count` > 50 THEN '详细测评,专业测评'
            ELSE ''
        END,
        CASE 
            WHEN `code` LIKE '%SAS%' THEN '焦虑测试,焦虑自评,SAS量表'
            WHEN `code` LIKE '%SDS%' THEN '抑郁测试,抑郁自评,SDS量表'
            WHEN `code` LIKE '%MBTI%' THEN 'MBTI,人格测试,性格分析'
            WHEN `code` LIKE '%SCL%' THEN 'SCL-90,症状自评,心理症状'
            WHEN `code` LIKE '%PDQ%' THEN 'PDQ-4,人格障碍,人格测评'
            WHEN `code` LIKE '%STAI%' THEN 'STAI,状态焦虑,特质焦虑'
            WHEN `code` LIKE '%BAI%' THEN 'BAI,贝克焦虑,焦虑量表'
            WHEN `code` LIKE '%FNE%' THEN 'FNE,负面评价,社交恐惧'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- ==================== 6. 创建搜索索引 ====================
-- 为搜索关键词创建全文索引（MySQL 5.7+支持中文）
ALTER TABLE `psy_consultants` ADD INDEX `idx_search_keywords` (`search_keywords`(500));
ALTER TABLE `psy_course` ADD INDEX `idx_search_keywords` (`search_keywords`(500));
ALTER TABLE `psy_meditation` ADD INDEX `idx_search_keywords` (`search_keywords`(500));
ALTER TABLE `psy_t_scale` ADD INDEX `idx_search_keywords` (`search_keywords`(500));

-- 为搜索次数创建索引
CREATE INDEX IF NOT EXISTS `idx_consultant_search_count` ON `psy_consultants`(`search_count`, `view_count`);
CREATE INDEX IF NOT EXISTS `idx_course_search_count` ON `psy_course`(`search_count`, `view_count`);
CREATE INDEX IF NOT EXISTS `idx_meditation_search_count` ON `psy_meditation`(`search_count`, `play_count`);
CREATE INDEX IF NOT EXISTS `idx_scale_search_count` ON `psy_t_scale`(`search_count`);

-- ==================== 7. 添加一些示例数据的关键词 ====================

-- 为咨询师添加专业领域关键词
UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',婚姻咨询,情感问题,夫妻关系')
WHERE `name` LIKE '%婚姻%' OR `personal_intro` LIKE '%婚姻%' OR `personal_intro` LIKE '%夫妻%';

UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',青少年心理,儿童心理,亲子关系')
WHERE `personal_intro` LIKE '%青少年%' OR `personal_intro` LIKE '%儿童%' OR `personal_intro` LIKE '%亲子%';

UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',职场心理,工作压力,职业规划')
WHERE `personal_intro` LIKE '%职场%' OR `personal_intro` LIKE '%工作%' OR `personal_intro` LIKE '%职业%';

-- 为课程添加主题关键词
UPDATE `psy_course` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',情绪管理,情绪调节,情绪控制')
WHERE `title` LIKE '%情绪%' OR `summary` LIKE '%情绪%';

UPDATE `psy_course` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',压力管理,减压,放松')
WHERE `title` LIKE '%压力%' OR `summary` LIKE '%压力%' OR `summary` LIKE '%减压%';

UPDATE `psy_course` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',人际关系,社交技巧,沟通技巧')
WHERE `title` LIKE '%人际%' OR `summary` LIKE '%人际%' OR `summary` LIKE '%沟通%';

-- 为冥想添加功能关键词
UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',睡眠冥想,助眠,改善睡眠')
WHERE `title` LIKE '%睡眠%' OR `description` LIKE '%睡眠%' OR `description` LIKE '%助眠%';

UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',减压冥想,放松冥想,缓解压力')
WHERE `title` LIKE '%减压%' OR `description` LIKE '%减压%' OR `description` LIKE '%放松%';

UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',专注力,注意力,集中精神')
WHERE `title` LIKE '%专注%' OR `description` LIKE '%专注%' OR `description` LIKE '%注意力%';

-- 清理多余的逗号
UPDATE `psy_consultants` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(`search_keywords`, ',,', ',')) WHERE `search_keywords` IS NOT NULL;
UPDATE `psy_course` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(`search_keywords`, ',,', ',')) WHERE `search_keywords` IS NOT NULL;
UPDATE `psy_meditation` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(`search_keywords`, ',,', ',')) WHERE `search_keywords` IS NOT NULL;
UPDATE `psy_t_scale` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(`search_keywords`, ',,', ',')) WHERE `search_keywords` IS NOT NULL;
