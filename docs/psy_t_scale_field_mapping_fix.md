# PsyTScale 字段映射修正文档

## 📋 概述

本文档记录了将心理测评量表系统从旧的 `psy_assessment_scale` 表结构迁移到新的 `psy_t_scale` 表结构时进行的字段映射修正。

## 🗃️ 数据库表结构变更

### 新表结构 (psy_t_scale)
```sql
create table psy_t_scale
(
    id                bigint auto_increment comment '量表唯一ID' primary key,
    name              varchar(100)                             not null comment '量表名称',
    code              varchar(50)                              not null comment '量表编码',
    category_id       int                                      null comment '分类id',
    description       text                                     null comment '量表描述',
    introduction      text                                     null comment '量表介绍',
    question_count    int                                      not null comment '总题数',
    scoring_type      enum ('LIKERT', 'BINARY', 'COMPOSITE')   not null comment '计分类型',
    duration          varchar(20)                              null comment '预估时长',
    norm_mean         decimal(5, 2)                            null comment '常模均值',
    norm_sd           decimal(5, 2)                            null comment '常模标准差',
    applicable_age    varchar(50)                              null comment '适用年龄',
    image_url         varchar(500)                             null comment '量表封面图片',
    price             decimal(10, 2) default 0.00              null comment '价格',
    pay_mode          tinyint(1)     default 0                 null comment '付费模式(0免费 1付费)',
    pay_phase         tinyint(1)     default 0                 null comment '付费阶段(0测试 1报告)',
    free_vip_level    int            default 0                 null comment '免费VIP等级',
    free_report_level int            default 1                 null comment '免费报告层级',
    paid_report_level int            default 3                 null comment '付费报告层级',
    enterprise_id     bigint                                   null comment '企业ID',
    status            tinyint(1)     default 1                 null comment '状态(0停用 1启用)',
    sort              int            default 0                 null comment '排序',
    search_keywords   text                                     null comment '搜索关键词',
    search_count      int            default 0                 null comment '被搜索次数',
    view_count        int            default 0                 null comment '查看次数',
    del_flag          char           default '0'               null comment '删除标志',
    create_by         varchar(64)                              null comment '创建者',
    create_time       datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by         varchar(64)                              null comment '更新者',
    update_time       datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间',
    remark            varchar(500)                             null comment '备注'
);
```

## 🔄 字段映射变更

### 主要字段变更
| 旧字段名 | 新字段名 | 说明 |
|---------|---------|------|
| `scale_name` | `name` | 量表名称 |
| `scale_code` | `code` | 量表编码 |
| `instruction` | `introduction` | 量表介绍 |
| `cover_image` | `image_url` | 封面图片 |
| `is_free` | `pay_mode` | 付费模式(逻辑相反：0=免费,1=付费) |
| `difficulty_level` | ❌ 删除 | 难度等级字段已移除 |
| `time_limit` | `duration` | 时间限制改为预估时长 |
| `author` | ❌ 删除 | 作者字段已移除 |
| `version` | ❌ 删除 | 版本字段已移除 |
| `tags` | ❌ 删除 | 标签字段已移除 |
| `test_count` | ❌ 删除 | 测试次数字段已移除 |
| `rating_avg` | ❌ 删除 | 平均评分字段已移除 |
| `rating_count` | ❌ 删除 | 评分人数字段已移除 |

### 新增字段
| 字段名 | 类型 | 说明 |
|-------|------|------|
| `scoring_type` | enum | 计分类型(LIKERT/BINARY/COMPOSITE) |
| `norm_mean` | decimal(5,2) | 常模均值 |
| `norm_sd` | decimal(5,2) | 常模标准差 |
| `applicable_age` | varchar(50) | 适用年龄 |
| `pay_phase` | tinyint(1) | 付费阶段(0=测试,1=报告) |
| `free_vip_level` | int | 免费VIP等级 |
| `free_report_level` | int | 免费报告层级 |
| `paid_report_level` | int | 付费报告层级 |
| `enterprise_id` | bigint | 企业ID |
| `sort` | int | 排序 |

## 📝 修正的文件列表

### 1. XML映射文件
- `xihuan-system/src/main/resources/mapper/system/PsyAssessmentScaleMapper.xml`
  - 更新 resultMap 字段映射
  - 修正所有 SQL 查询中的字段名
  - 更新表名为 `psy_t_scale`

### 2. 实体类
- `xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentScale.java`
  - 更新字段名和注解
  - 更新常量定义
  - 移除已删除的字段

### 3. Mapper接口
- `xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentScaleMapper.java`
  - 更新方法参数名
  - 移除不存在的方法

### 4. Service层
- `xihuan-system/src/main/java/com/xihuan/system/service/IPsyAssessmentScaleService.java`
- `xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyAssessmentScaleServiceImpl.java`
  - 更新方法参数和字段引用
  - 移除已删除字段的相关方法
  - 更新常量使用

### 5. Controller层
- `xihuan-admin/src/main/java/com/xihuan/web/controller/system/PsyAssessmentScaleController.java`
- `xihuan-admin/src/main/java/com/xihuan/web/controller/miniapp/user/MiniAppUserAssessmentController.java`
  - 更新搜索方法参数
  - 修正字段名引用

### 6. 其他服务
- `xihuan-system/src/main/java/com/xihuan/system/service/impl/PsySearchServiceImpl.java`
  - 更新搜索条件字段名

## ✅ 修正完成的功能

1. **基础CRUD操作** - 增删改查功能已适配新表结构
2. **字段映射** - 所有字段名已更新为新表结构
3. **搜索功能** - 搜索参数已更新(difficulty_level → payMode, isFree → payMode)
4. **统计功能** - 统计查询已适配新字段
5. **常量定义** - 实体类常量已更新为新的业务逻辑

## 🔧 主要业务逻辑变更

### 付费模式变更
- **旧逻辑**: `is_free` (0=付费, 1=免费)
- **新逻辑**: `pay_mode` (0=免费, 1=付费)

### 状态字段变更
- **旧逻辑**: `status` (0=未发布, 1=已发布, 2=下架)
- **新逻辑**: `status` (0=停用, 1=启用)

### 移除的功能
- 难度等级相关功能
- 评分统计功能
- 测试次数统计功能
- 作者和版本信息

## 🚀 后续工作建议

1. **数据迁移脚本** - 编写从旧表到新表的数据迁移脚本
2. **前端适配** - 更新前端页面以适应新的字段结构
3. **API文档更新** - 更新相关API文档
4. **测试验证** - 全面测试所有相关功能
5. **企业功能开发** - 实现企业相关的量表管理功能

## ⚠️ 注意事项

1. 新表结构中的 `pay_mode` 逻辑与旧的 `is_free` 相反
2. 状态字段的含义发生了变化
3. 部分统计功能已移除，如需要请重新实现
4. 企业ID字段为新增，需要在业务逻辑中正确处理
