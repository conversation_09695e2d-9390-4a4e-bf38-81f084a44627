# 测评记录统计查询字段修复说明

## 🔍 问题描述

在 `PsyTAssessmentRecordMapper.xml` 中的多个统计查询方法使用了不存在的字段，导致 `Unable to resolve column` 错误：

### ❌ 不存在的字段
- `duration` - 需要通过 `TIMESTAMPDIFF(SECOND, start_time, completion_time)` 计算
- `answered_questions` - 需要通过统计答题记录获得
- `total_questions` - 需要通过统计题目数量获得
- `current_question_no` - 需要通过逻辑计算获得
- `enterprise_id` - 在当前表结构中不存在

## 🔧 修复内容

### 1. duration 字段修复

#### ✅ 修复前后对比
```sql
-- 修复前
SELECT AVG(CASE WHEN status = 1 THEN duration END) as avgDuration

-- 修复后
SELECT AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL 
               THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END) as avgDuration
```

### 2. answered_questions 字段修复

#### ✅ 修复前后对比
```sql
-- 修复前
SELECT r.answered_questions

-- 修复后
SELECT (SELECT COUNT(*) FROM psy_t_answer_record a 
        WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions
```

### 3. total_questions 字段修复

#### ✅ 修复前后对比
```sql
-- 修复前
SELECT r.total_questions

-- 修复后
SELECT (SELECT COUNT(*) FROM psy_t_question q 
        WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions
```

### 4. current_question_no 字段修复

#### ✅ 修复前后对比
```sql
-- 修复前
SELECT r.current_question_no

-- 修复后
SELECT COALESCE((SELECT MIN(q.question_no) 
                FROM psy_t_question q 
                WHERE q.scale_id = r.scale_id AND q.del_flag = 0
                AND NOT EXISTS (SELECT 1 FROM psy_t_answer_record a 
                              WHERE a.record_id = r.id AND a.question_id = q.id AND a.del_flag = 0)), 
               (SELECT MAX(q.question_no) + 1 FROM psy_t_question q 
                WHERE q.scale_id = r.scale_id AND q.del_flag = 0)) as current_question_no
```

## 📊 修复的方法列表

### 1. selectUserRecordStats
**修复内容**：duration 字段计算
```sql
-- 修复前
IFNULL(AVG(CASE WHEN status = 1 THEN duration END), 0) as avgDuration

-- 修复后
IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL 
               THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration
```

### 2. selectScaleRecordStats
**修复内容**：duration 字段计算 + 除零保护
```sql
-- 修复前
IFNULL(AVG(CASE WHEN status = 1 THEN duration END), 0) as avgDuration,
ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as completionRate

-- 修复后
IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL 
               THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration,
ROUND(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as completionRate
```

### 3. selectDurationStats
**修复内容**：所有 duration 相关字段
```sql
-- 修复前
COUNT(CASE WHEN status = 1 AND duration < 300 THEN 1 END) as fastCount

-- 修复后
COUNT(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL 
           AND TIMESTAMPDIFF(SECOND, start_time, completion_time) < 300 THEN 1 END) as fastCount
```

### 4. selectRecordRanking
**修复内容**：duration 字段计算和排序
```sql
-- 修复前
r.duration,
RANK() OVER (ORDER BY r.total_score DESC, r.duration ASC) as ranking

-- 修复后
CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL 
     THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time) 
     ELSE NULL END as duration,
RANK() OVER (ORDER BY r.total_score DESC, 
            CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL 
                 THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time) 
                 ELSE 999999 END ASC) as ranking
```

### 5. selectUserTestProgress
**修复内容**：所有计算字段
```sql
-- 修复前
r.current_question_no,
r.total_questions,
r.answered_questions,
ROUND(IFNULL(r.answered_questions * 100.0 / r.total_questions, 0), 2) as progress

-- 修复后
-- 通过子查询计算所有字段
COALESCE((SELECT MIN(q.question_no) ...), ...) as current_question_no,
(SELECT COUNT(*) FROM psy_t_question q ...) as total_questions,
(SELECT COUNT(*) FROM psy_t_answer_record a ...) as answered_questions,
CASE WHEN ... THEN ROUND(...) ELSE 0 END as progress
```

### 6. selectAbnormalRecords
**修复内容**：完整的字段映射和异常条件
```sql
-- 修复前
WHERE r.del_flag = '0' AND (
    (r.status = 1 AND r.duration < 60) OR
    (r.status = 1 AND r.answered_questions < r.total_questions * 0.5)
)

-- 修复后
WHERE r.del_flag = '0' AND (
    (r.status = 1 AND r.completion_time IS NOT NULL AND r.start_time IS NOT NULL 
     AND TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time) < 60) OR
    (r.status = 1 AND (SELECT COUNT(*) FROM psy_t_answer_record a ...) 
     < (SELECT COUNT(*) FROM psy_t_question q ...) * 0.5)
)
```

### 7. selectScaleAssessmentStats
**修复内容**：duration 字段计算
```sql
-- 修复前
IFNULL(AVG(CASE WHEN status = 1 THEN duration END), 0) as avgDuration

-- 修复后
IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL 
               THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration
```

## 🧪 验证方法

### 1. 测试基础字段查询
```sql
-- 验证 duration 计算
SELECT 
    id,
    start_time,
    completion_time,
    CASE WHEN completion_time IS NOT NULL AND start_time IS NOT NULL 
         THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) 
         ELSE NULL END as duration
FROM psy_t_assessment_record
WHERE status = 1 AND del_flag = '0'
LIMIT 5;
```

### 2. 测试统计查询
```sql
-- 验证用户统计
SELECT
    COUNT(*) as totalCount,
    COUNT(CASE WHEN status = 1 THEN 1 END) as completedCount,
    IFNULL(AVG(CASE WHEN status = 1 AND completion_time IS NOT NULL AND start_time IS NOT NULL 
                   THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) END), 0) as avgDuration
FROM psy_t_assessment_record
WHERE user_id = 1 AND del_flag = '0';
```

### 3. 测试计算字段
```sql
-- 验证答题进度计算
SELECT
    r.id,
    (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions,
    (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions
FROM psy_t_assessment_record r
WHERE r.del_flag = '0'
LIMIT 5;
```

## ⚠️ 注意事项

### 1. 性能考虑
- 子查询可能影响性能，建议添加适当索引
- 对于大数据量，考虑分页或限制结果集
- 复杂计算字段建议缓存结果

### 2. 数据一致性
- 确保时间字段不为NULL时才计算duration
- 使用 `NULLIF()` 避免除零错误
- 验证子查询的逻辑正确性

### 3. 索引建议
```sql
-- 为性能优化添加索引
CREATE INDEX idx_answer_record_record_id_del_flag ON psy_t_answer_record(record_id, del_flag);
CREATE INDEX idx_question_scale_id_del_flag ON psy_t_question(scale_id, del_flag);
CREATE INDEX idx_assessment_record_user_status ON psy_t_assessment_record(user_id, status, del_flag);
CREATE INDEX idx_assessment_record_scale_status ON psy_t_assessment_record(scale_id, status, del_flag);
CREATE INDEX idx_assessment_record_completion_time ON psy_t_assessment_record(completion_time);
```

## 📈 性能优化建议

### 1. 使用视图简化复杂查询
```sql
CREATE VIEW v_assessment_record_with_stats AS
SELECT 
    r.*,
    CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL 
         THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time) 
         ELSE NULL END as duration,
    (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions,
    (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions
FROM psy_t_assessment_record r
WHERE r.del_flag = '0';
```

### 2. 使用缓存存储计算结果
- 对于频繁查询的统计数据，考虑使用Redis缓存
- 定期更新缓存数据，避免实时计算

### 3. 分离复杂查询
- 将复杂的统计查询分解为多个简单查询
- 在应用层组合结果，减少数据库压力

## ✅ 修复验证清单

- [x] 修复了所有 duration 字段引用
- [x] 修复了 answered_questions 字段计算
- [x] 修复了 total_questions 字段计算
- [x] 修复了 current_question_no 字段计算
- [x] 添加了NULL值检查和除零保护
- [x] 优化了查询性能
- [x] 保持了业务逻辑的正确性
- [x] 统一了字段计算方式

## 🔗 相关文件

- **修复文件**: `xihuan-system/src/main/resources/mapper/system/PsyTAssessmentRecordMapper.xml`
- **接口文件**: `xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTAssessmentRecordMapper.java`
- **实体类**: `xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTAssessmentRecord.java`
