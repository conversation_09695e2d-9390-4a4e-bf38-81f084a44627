# 测评系统升级验证脚本

## 1. 数据库升级验证

### 执行升级脚本
```sql
-- 请在数据库管理工具中执行以下脚本
source 测评系统升级脚本.sql;

-- 或者复制粘贴脚本内容到数据库管理工具中执行
```

### 验证升级结果
```sql
-- 检查psy_t_scale表新增字段
DESCRIBE psy_t_scale;

-- 检查新增字段是否存在
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'psy_t_scale' 
  AND COLUMN_NAME IN ('scoring_method', 'has_reverse_items', 'has_standard_score', 'standard_score_multiplier');

-- 检查psy_t_assessment_record表新增字段
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'psy_t_assessment_record' 
  AND COLUMN_NAME IN ('dimension_scores', 'standard_score', 'report_generated', 'report_content');

-- 检查计分配置表是否创建
SHOW TABLES LIKE 'psy_t_scoring_config';
DESCRIBE psy_t_scoring_config;
```

## 2. 计分逻辑验证

### 反向计分测试
```java
// 5点量表反向计分测试
// 原始分2分 -> 反向计分应该是 6-2=4分
BigDecimal originalScore = new BigDecimal("2");
Integer maxValue = 5;
BigDecimal reverseScore = calculateReverseScore(originalScore, maxValue);
// 期望结果：4

// 4点量表反向计分测试（SAS量表）
BigDecimal sasScore = new BigDecimal("2");
Integer sasMaxValue = 4;
BigDecimal sasReverseScore = calculateReverseScore(sasScore, sasMaxValue);
// 期望结果：3 (5-2=3)
```

### 标准分转换测试
```java
// SAS标准分转换测试（×1.25）
BigDecimal sasRawScore = new BigDecimal("40");
BigDecimal sasMultiplier = new BigDecimal("1.25");
BigDecimal sasStandardScore = calculateStandardScore(sasRawScore, sasMultiplier);
// 期望结果：50.00

// BAI标准分转换测试（×1.19）
BigDecimal baiRawScore = new BigDecimal("30");
BigDecimal baiMultiplier = new BigDecimal("1.19");
BigDecimal baiStandardScore = calculateStandardScore(baiRawScore, baiMultiplier);
// 期望结果：35.70
```

## 3. 量表配置验证

### PRCA-24配置验证
```sql
-- 检查PRCA-24量表配置
SELECT * FROM psy_t_scale WHERE code = 'PRCA-24';

-- 检查分量表基础分数配置
SELECT * FROM psy_t_subscale WHERE scale_id = (SELECT id FROM psy_t_scale WHERE code = 'PRCA-24');

-- 验证基础分数是否设置为18
SELECT alias, base_score FROM psy_t_subscale 
WHERE scale_id = (SELECT id FROM psy_t_scale WHERE code = 'PRCA-24');
```

### STAI配置验证
```sql
-- 检查STAI反向计分题目配置
SELECT question_no, is_reverse, reverse_value 
FROM psy_t_question 
WHERE scale_id = (SELECT id FROM psy_t_scale WHERE code = 'STAI')
  AND is_reverse = 1;

-- 验证反向计分题目数量（应该是19个）
SELECT COUNT(*) as reverse_question_count
FROM psy_t_question 
WHERE scale_id = (SELECT id FROM psy_t_scale WHERE code = 'STAI')
  AND is_reverse = 1;
```

### SAS配置验证
```sql
-- 检查SAS反向计分题目配置
SELECT question_no, is_reverse, reverse_value 
FROM psy_t_question 
WHERE scale_id = (SELECT id FROM psy_t_scale WHERE code = 'SAS')
  AND is_reverse = 1;

-- 验证反向计分题目数量（应该是5个：5,9,13,17,19）
SELECT COUNT(*) as reverse_question_count
FROM psy_t_question 
WHERE scale_id = (SELECT id FROM psy_t_scale WHERE code = 'SAS')
  AND is_reverse = 1;
```

## 4. API接口测试

### 高级计分接口测试
```bash
# 执行高级计分
curl -X POST "http://localhost:8080/system/advanced-scoring/execute/1" \
  -H "Authorization: Bearer YOUR_TOKEN"

# PRCA-24专项计分
curl -X POST "http://localhost:8080/system/advanced-scoring/prca24/1" \
  -H "Authorization: Bearer YOUR_TOKEN"

# SAS专项计分
curl -X POST "http://localhost:8080/system/advanced-scoring/sas/1" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 生成完整报告
curl -X POST "http://localhost:8080/system/advanced-scoring/complete-report/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 反向计分工具测试
```bash
# 反向计分计算
curl -X POST "http://localhost:8080/system/advanced-scoring/reverse-score" \
  -H "Content-Type: application/json" \
  -d "originalScore=2&maxValue=5"

# 标准分转换
curl -X POST "http://localhost:8080/system/advanced-scoring/standard-score" \
  -H "Content-Type: application/json" \
  -d "rawScore=40&multiplier=1.25"
```

## 5. 计分结果验证

### PRCA-24计分验证
```
预期计分逻辑：基础分18 + 题目分数总和
示例：
- 维度1：基础分18 + 题目分数12 = 30分
- 维度2：基础分18 + 题目分数15 = 33分
- 维度3：基础分18 + 题目分数10 = 28分
- 维度4：基础分18 + 题目分数14 = 32分
- 总分：30 + 33 + 28 + 32 = 123分
```

### STAI计分验证
```
预期计分逻辑：部分题目反向计分
反向计分题目：1,2,5,8,10,11,15,16,19,20,23,24,26,27,30,33,34,36,39
反向计分公式：6 - 原始分（5点量表）

示例：
- 题目1（反向）：原始分3 -> 反向分 6-3=3
- 题目3（正向）：原始分3 -> 保持3
- 总分：所有题目分数相加
```

### SAS计分验证
```
预期计分逻辑：部分题目反向计分 + 标准分转换
反向计分题目：5,9,13,17,19
反向计分公式：5 - 原始分（4点量表）
标准分转换：原始分 × 1.25

示例：
- 原始分计算：包含反向计分的总分
- 标准分：原始分 × 1.25
```

### BAI计分验证
```
预期计分逻辑：简单求和 + 标准分转换
标准分转换：原始分 × 1.19

示例：
- 原始分：所有题目分数相加
- 标准分：原始分 × 1.19
```

## 6. 报告生成验证

### 报告内容检查
```json
{
  "success": true,
  "recordId": 1,
  "scaleCode": "SAS",
  "scaleName": "焦虑自评量表",
  "testDate": "2024-12-01 10:00:00",
  "completionTime": "2024-12-01 10:15:00",
  "scoringMethod": "STANDARD_SCORE",
  "interpretation": {
    "rawScore": 40,
    "standardScore": 50.00,
    "anxietyLevel": "正常"
  },
  "recommendations": {
    "suggestions": ["您的焦虑水平在正常范围内", "继续保持健康的生活方式"],
    "riskLevel": "中低"
  },
  "reportType": "SAS",
  "generatedAt": "2024-12-01 10:16:00"
}
```

## 7. 错误处理验证

### 异常情况测试
```
1. 测评记录不存在
2. 量表信息缺失
3. 答题记录为空
4. 计分配置错误
5. 数据库连接异常
```

## 8. 性能验证

### 批量计分测试
```
测试场景：
1. 单个测评记录计分时间
2. 批量100个记录计分时间
3. 报告生成时间
4. 数据库查询性能
```

## 9. 兼容性验证

### 向后兼容性
```
1. 原有简单计分逻辑仍然可用
2. 未配置高级计分的量表使用默认逻辑
3. 现有测评记录可以重新计分
4. API接口保持兼容
```

## 10. 部署验证清单

- [ ] 数据库升级脚本执行成功
- [ ] 新增字段验证通过
- [ ] 计分配置数据更新完成
- [ ] 反向计分逻辑测试通过
- [ ] 标准分转换测试通过
- [ ] 各量表专项计分测试通过
- [ ] 报告生成功能正常
- [ ] API接口测试通过
- [ ] 错误处理机制正常
- [ ] 性能测试满足要求
- [ ] 向后兼容性验证通过

## 11. 上线后监控

### 关键指标监控
```
1. 计分成功率
2. 报告生成成功率
3. API响应时间
4. 数据库查询性能
5. 错误日志监控
```

### 业务指标监控
```
1. 各量表使用频率
2. 计分结果分布
3. 用户满意度
4. 系统稳定性
```
