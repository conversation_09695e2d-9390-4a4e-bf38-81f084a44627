# 预约排班系统定时任务配置说明

## 概述

系统已将定时任务从代码中的 `@Scheduled` 注解迁移到数据库配置，使用 Quartz 框架进行管理。这样可以通过管理后台动态配置和监控定时任务。

## 需要配置的定时任务

### 1. 生成咨询师时间槽
- **任务名称**: `generateFutureTimeSlots`
- **任务分组**: `SYSTEM`
- **调用目标**: `psyTimeSlotTaskService.generateFutureTimeSlots()`
- **cron表达式**: `0 0 2 * * ?` (每天凌晨2点执行)
- **任务描述**: 自动生成未来7天的咨询师时间槽

### 2. 清理过期咨询师时间槽
- **任务名称**: `cleanExpiredTimeSlots`
- **任务分组**: `SYSTEM`
- **调用目标**: `psyTimeSlotTaskService.cleanExpiredTimeSlots()`
- **cron表达式**: `0 0 3 * * ?` (每天凌晨3点执行)
- **任务描述**: 清理7天前的过期咨询师时间槽

### 3. 更新过期时间槽状态
- **任务名称**: `updateExpiredSlotStatus`
- **任务分组**: `SYSTEM`
- **调用目标**: `psyTimeSlotTaskService.updateExpiredSlotStatus()`
- **cron表达式**: `0 0 * * * ?` (每小时执行)
- **任务描述**: 更新过期时间槽的状态为已过期

### 4. 生成系统公共时间槽
- **任务名称**: `generateSystemTimeSlots`
- **任务分组**: `SYSTEM`
- **调用目标**: `psyTimeSlotTaskService.generateSystemTimeSlots()`
- **cron表达式**: `0 5 2 * * ?` (每天凌晨2点05分执行)
- **任务描述**: 生成系统公共时间槽，在咨询师时间槽生成后执行

### 5. 更新过期时间槽状态（支持延后配置）
- **任务名称**: `updateExpiredSlotStatusWithDelay`
- **任务分组**: `SYSTEM`
- **调用目标**: `psyTimeSlotTaskService.updateExpiredSlotStatusWithDelay()`
- **cron表达式**: `0 0 * * * ?` (每小时执行)
- **任务描述**: 更新过期时间槽状态，支持延后2小时的容错配置

### 6. 更新系统时间槽可用性统计
- **任务名称**: `updateSystemSlotAvailability`
- **任务分组**: `SYSTEM`
- **调用目标**: `psyTimeSlotTaskService.updateSystemSlotAvailability()`
- **cron表达式**: `0 */30 * * * ?` (每30分钟执行)
- **任务描述**: 更新系统时间槽的可用咨询师数量统计

### 7. 更新系统公共时间槽过期状态
- **任务名称**: `updateSystemSlotExpiredStatus`
- **任务分组**: `SYSTEM`
- **调用目标**: `psyTimeSlotTaskService.updateSystemSlotExpiredStatus()`
- **cron表达式**: `0 */15 * * * ?` (每15分钟执行)
- **任务描述**: 更新系统公共时间槽的过期状态，支持延后配置

### 8. 清理过期系统时间槽
- **任务名称**: `cleanExpiredSystemSlots`
- **任务分组**: `SYSTEM`
- **调用目标**: `psyTimeSlotTaskService.cleanExpiredSystemSlots()`
- **cron表达式**: `0 10 3 * * ?` (每天凌晨3点10分执行)
- **任务描述**: 清理7天前的过期系统时间槽

## 数据库配置SQL

```sql
-- 插入定时任务配置到 sys_job 表

-- 1. 生成咨询师时间槽
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark) 
VALUES (1, 'generateFutureTimeSlots', 'SYSTEM', 'psyTimeSlotTaskService.generateFutureTimeSlots()', '0 0 2 * * ?', '1', '1', '0', 'admin', now(), '自动生成未来7天的咨询师时间槽');

-- 2. 清理过期咨询师时间槽
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark) 
VALUES (2, 'cleanExpiredTimeSlots', 'SYSTEM', 'psyTimeSlotTaskService.cleanExpiredTimeSlots()', '0 0 3 * * ?', '1', '1', '0', 'admin', now(), '清理7天前的过期咨询师时间槽');

-- 3. 更新过期时间槽状态（支持延后配置）
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark)
VALUES (3, 'updateExpiredSlotStatusWithDelay', 'SYSTEM', 'psyTimeSlotTaskService.updateExpiredSlotStatusWithDelay()', '0 0 * * * ?', '1', '1', '0', 'admin', now(), '更新过期时间槽状态，支持延后2小时的容错配置');

-- 4. 生成系统公共时间槽
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark) 
VALUES (4, 'generateSystemTimeSlots', 'SYSTEM', 'psyTimeSlotTaskService.generateSystemTimeSlots()', '0 5 2 * * ?', '1', '1', '0', 'admin', now(), '生成系统公共时间槽');

-- 5. 更新系统时间槽可用性统计
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark)
VALUES (5, 'updateSystemSlotAvailability', 'SYSTEM', 'psyTimeSlotTaskService.updateSystemSlotAvailability()', '0 */30 * * * ?', '1', '1', '0', 'admin', now(), '更新系统时间槽的可用咨询师数量统计');

-- 6. 更新系统公共时间槽过期状态
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark)
VALUES (6, 'updateSystemSlotExpiredStatus', 'SYSTEM', 'psyTimeSlotTaskService.updateSystemSlotExpiredStatus()', '0 */15 * * * ?', '1', '1', '0', 'admin', now(), '更新系统公共时间槽的过期状态，支持延后配置');

-- 7. 清理过期系统时间槽
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark)
VALUES (7, 'cleanExpiredSystemSlots', 'SYSTEM', 'psyTimeSlotTaskService.cleanExpiredSystemSlots()', '0 10 3 * * ?', '1', '1', '0', 'admin', now(), '清理7天前的过期系统时间槽');
```

## 任务执行顺序

为了避免数据冲突，任务按以下顺序执行：

1. **凌晨2:00** - 生成咨询师时间槽
2. **凌晨2:05** - 生成系统公共时间槽（依赖咨询师时间槽）
3. **凌晨3:00** - 清理过期咨询师时间槽
4. **凌晨3:10** - 清理过期系统时间槽
5. **每小时** - 更新咨询师时间槽过期状态
6. **每30分钟** - 更新系统时间槽可用性统计
7. **每15分钟** - 更新系统公共时间槽过期状态

## 管理方式

### 通过管理后台
- 访问：`/monitor/job`
- 可以启动/停止/暂停任务
- 查看任务执行日志
- 手动执行任务

### 通过API接口
```bash
# 手动生成时间槽
POST /system/timeTask/generate?days=7

# 手动清理过期时间槽
POST /system/timeTask/clean?days=7

# 获取任务状态
GET /system/timeTask/status
```

## 注意事项

1. **执行顺序**: 确保生成任务在清理任务之前执行
2. **并发控制**: 所有任务设置为不允许并发执行
3. **错误处理**: 任务失败时会记录详细日志
4. **性能监控**: 建议监控任务执行时间和资源使用情况
5. **数据备份**: 在清理任务执行前建议备份重要数据

## 监控建议

1. 监控任务执行状态和耗时
2. 设置任务执行失败告警
3. 定期检查生成的数据量是否正常
4. 监控数据库存储空间使用情况
