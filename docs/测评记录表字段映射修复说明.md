# 测评记录表字段映射修复说明

## 🔍 问题描述

`PsyTAssessmentRecordMapper.xml` 文件中的字段映射与实际数据库表结构不匹配，包含了很多不存在的字段，导致查询可能失败。

## 📊 数据库表结构对比

### 实际数据库字段（psy_t_assessment_record）
```sql
CREATE TABLE psy_t_assessment_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    scale_id BIGINT NOT NULL,
    user_id BIGINT,
    session_id VARCHAR(64) NOT NULL,
    start_time DATETIME NOT NULL,
    completion_time DATETIME,           -- ✅ 存在
    total_score DECIMAL(8,2),
    result_level VARCHAR(50),
    result_description TEXT,
    suggestions TEXT,
    status TINYINT(1) DEFAULT 0,
    ip_address VARCHAR(50),
    user_agent VARCHAR(500),
    del_flag CHAR(1) DEFAULT '0',
    create_by VARCHAR(64),
    create_time DATETIME,
    update_by VARCHAR(64),
    update_time DATETIME
);
```

### XML中映射的不存在字段
❌ **删除的字段**：
- `end_time` - 应该使用 `completion_time`
- `current_question_no` - 需要通过计算获得
- `answered_questions` - 需要通过计算获得
- `total_questions` - 需要通过计算获得
- `progress` - 需要通过计算获得
- `duration` - 需要通过计算获得
- `enterprise_id` - 不存在
- `assessment_no` - 不存在
- `subscale_scores` - 不存在
- `max_score` - 不存在
- `score_percentage` - 不存在
- `remark` - 不存在

## 🔧 修复内容

### 1. ResultMap 字段映射修复

#### ✅ 修复前后对比
```xml
<!-- 修复前 -->
<resultMap id="RecordResultMap" type="PsyTAssessmentRecord">
    <result property="endTime" column="end_time"/>                    <!-- ❌ 不存在 -->
    <result property="enterpriseId" column="enterprise_id"/>          <!-- ❌ 不存在 -->
    <result property="assessmentNo" column="assessment_no"/>          <!-- ❌ 不存在 -->
    <result property="subscaleScores" column="subscale_scores"/>      <!-- ❌ 不存在 -->
    <result property="maxScore" column="max_score"/>                  <!-- ❌ 不存在 -->
    <result property="scorePercentage" column="score_percentage"/>    <!-- ❌ 不存在 -->
    <result property="remark" column="remark"/>                       <!-- ❌ 不存在 -->
</resultMap>

<!-- 修复后 -->
<resultMap id="RecordResultMap" type="PsyTAssessmentRecord">
    <!-- 只映射实际存在的字段 -->
    <result property="completionTime" column="completion_time"/>      <!-- ✅ 存在 -->
    <!-- 计算字段通过SQL计算获得 -->
    <result property="duration" column="duration"/>
    <result property="currentQuestionNo" column="current_question_no"/>
    <result property="answeredQuestions" column="answered_questions"/>
    <result property="totalQuestions" column="total_questions"/>
    <result property="progress" column="progress"/>
</resultMap>
```

### 2. 查询语句修复

#### ✅ 基础字段查询
```xml
<!-- 修复前 -->
SELECT r.*, s.name as scale_name, s.code as scale_code, u.nick_name

<!-- 修复后 -->
SELECT 
    r.id, r.scale_id, r.user_id, r.session_id, r.start_time, r.completion_time,
    r.total_score, r.result_level, r.result_description, r.suggestions,
    r.status, r.ip_address, r.user_agent, r.del_flag,
    r.create_by, r.create_time, r.update_by, r.update_time,
    s.name as scale_name, s.code as scale_code, u.nick_name
```

#### ✅ 计算字段实现
```xml
-- 计算测评时长（秒）
CASE WHEN r.completion_time IS NOT NULL AND r.start_time IS NOT NULL 
     THEN TIMESTAMPDIFF(SECOND, r.start_time, r.completion_time) 
     ELSE NULL END as duration,

-- 计算已答题数
(SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) as answered_questions,

-- 计算总题数
(SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) as total_questions,

-- 计算答题进度百分比
CASE WHEN (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0) > 0
     THEN ROUND((SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = r.id AND a.del_flag = 0) * 100.0 / 
               (SELECT COUNT(*) FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0), 2)
     ELSE 0 END as progress,

-- 计算当前题目序号（下一个未答题目）
COALESCE((SELECT MIN(q.question_no) 
         FROM psy_t_question q 
         WHERE q.scale_id = r.scale_id AND q.del_flag = 0
         AND NOT EXISTS (SELECT 1 FROM psy_t_answer_record a 
                       WHERE a.record_id = r.id AND a.question_id = q.id AND a.del_flag = 0)), 
        (SELECT MAX(q.question_no) + 1 FROM psy_t_question q WHERE q.scale_id = r.scale_id AND q.del_flag = 0)) as current_question_no
```

### 3. 新增简化查询方法

#### ✅ 简化查询（避免复杂计算）
```xml
<select id="selectAssessmentRecordSimple" parameterType="Long" resultType="PsyTAssessmentRecord">
    SELECT 
        id, scale_id, user_id, session_id, start_time, completion_time,
        total_score, result_level, result_description, suggestions,
        status, ip_address, user_agent, del_flag,
        create_by, create_time, update_by, update_time
    FROM psy_t_assessment_record
    WHERE id = #{id} AND del_flag = '0'
</select>
```

## 📋 字段映射对照表

### 基础字段映射
| 实体属性 | 数据库字段 | 状态 | 说明 |
|----------|------------|------|------|
| id | id | ✅ | 主键 |
| scaleId | scale_id | ✅ | 量表ID |
| userId | user_id | ✅ | 用户ID |
| sessionId | session_id | ✅ | 会话ID |
| startTime | start_time | ✅ | 开始时间 |
| completionTime | completion_time | ✅ | 完成时间 |
| totalScore | total_score | ✅ | 总分 |
| resultLevel | result_level | ✅ | 结果等级 |
| resultDescription | result_description | ✅ | 结果描述 |
| suggestions | suggestions | ✅ | 建议 |
| status | status | ✅ | 状态 |
| ipAddress | ip_address | ✅ | IP地址 |
| userAgent | user_agent | ✅ | 用户代理 |
| delFlag | del_flag | ✅ | 删除标志 |
| createBy | create_by | ✅ | 创建者 |
| createTime | create_time | ✅ | 创建时间 |
| updateBy | update_by | ✅ | 更新者 |
| updateTime | update_time | ✅ | 更新时间 |

### 计算字段映射
| 实体属性 | 计算方式 | 说明 |
|----------|----------|------|
| duration | TIMESTAMPDIFF(SECOND, start_time, completion_time) | 测评时长（秒） |
| answeredQuestions | COUNT(答题记录) | 已答题数 |
| totalQuestions | COUNT(题目) | 总题数 |
| progress | (已答题数/总题数)*100 | 答题进度百分比 |
| currentQuestionNo | MIN(未答题目序号) | 当前题目序号 |

### 扩展字段映射
| 实体属性 | 来源表 | 说明 |
|----------|--------|------|
| scaleName | psy_t_scale.name | 量表名称 |
| scaleCode | psy_t_scale.code | 量表编码 |
| nickName | sys_user.nick_name | 用户昵称 |

## 🧪 验证方法

### 1. 数据库字段验证
```sql
-- 验证表结构
DESCRIBE psy_t_assessment_record;

-- 验证字段存在性
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_assessment_record'
ORDER BY ORDINAL_POSITION;
```

### 2. 查询测试
```sql
-- 测试基础查询
SELECT 
    id, scale_id, user_id, session_id, start_time, completion_time,
    total_score, result_level, result_description, suggestions,
    status, ip_address, user_agent, del_flag,
    create_by, create_time, update_by, update_time
FROM psy_t_assessment_record
WHERE del_flag = '0'
LIMIT 5;

-- 测试计算字段
SELECT 
    id,
    CASE WHEN completion_time IS NOT NULL AND start_time IS NOT NULL 
         THEN TIMESTAMPDIFF(SECOND, start_time, completion_time) 
         ELSE NULL END as duration,
    (SELECT COUNT(*) FROM psy_t_answer_record a WHERE a.record_id = psy_t_assessment_record.id AND a.del_flag = 0) as answered_questions
FROM psy_t_assessment_record
WHERE del_flag = '0'
LIMIT 5;
```

## ⚠️ 注意事项

### 1. 性能考虑
- 计算字段的子查询可能影响性能
- 建议为大数据量查询使用简化版本
- 考虑添加适当的索引

### 2. 数据一致性
- 确保计算字段的逻辑正确
- 验证NULL值处理
- 测试边界情况

### 3. 向后兼容
- 保持实体类属性名不变
- 通过SQL别名实现字段映射
- 避免破坏现有业务逻辑

## 📈 性能优化建议

### 1. 索引建议
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_assessment_record_user_id ON psy_t_assessment_record(user_id);
CREATE INDEX idx_assessment_record_scale_id ON psy_t_assessment_record(scale_id);
CREATE INDEX idx_assessment_record_session_id ON psy_t_assessment_record(session_id);
CREATE INDEX idx_assessment_record_status ON psy_t_assessment_record(status, del_flag);
CREATE INDEX idx_assessment_record_create_time ON psy_t_assessment_record(create_time);
```

### 2. 查询优化
- 对于列表查询，使用简化版本避免复杂计算
- 对于详情查询，可以使用完整版本
- 考虑使用缓存存储计算结果

## ✅ 修复验证清单

- [x] 删除了不存在的字段映射
- [x] 修复了基础字段查询
- [x] 实现了计算字段逻辑
- [x] 添加了简化查询方法
- [x] 优化了查询性能
- [x] 保持了向后兼容性
- [x] 添加了NULL值处理
- [x] 统一了字段命名规范
