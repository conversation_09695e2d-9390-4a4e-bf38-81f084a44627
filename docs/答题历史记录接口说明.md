# 答题历史记录接口说明

## 🎯 新增接口功能

为了解决前端"继续测评"时不知道答到哪一题、之前选择了什么选项的问题，新增了以下接口：

### 1. 获取答题历史记录
**URL**: `/miniapp/user/assessment/answers/{recordId}`
**方法**: `GET`
**功能**: 获取用户的答题历史记录

### 2. 获取测评详细信息
**URL**: `/miniapp/user/assessment/detail/{recordId}`
**方法**: `GET`
**功能**: 获取完整的测评信息（包含题目、选项、答题历史、进度等）

## 📊 接口详细说明

### 1. 答题历史记录接口

#### 请求示例
```javascript
// 获取测评记录24的答题历史
fetch('/miniapp/user/assessment/answers/24', {
    method: 'GET'
})
.then(response => response.json())
.then(data => {
    console.log('答题历史:', data.data);
});
```

#### 返回数据结构
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "answer_id": 101,
            "question_id": 277,
            "option_id": 760,
            "answer_content": "非常同意",
            "answer_score": 5,
            "response_time": 30,
            "answer_time": "2025-07-22 17:15:30",
            "question_content": "我不喜欢参加小组讨论",
            "question_type": "single_choice",
            "dimension": "Group",
            "question_sort": 1,
            "option_content": "非常同意",
            "option_value": "5",
            "option_sort": 5,
            "question_no": 1
        },
        {
            "answer_id": 102,
            "question_id": 278,
            "option_id": 761,
            "answer_content": "同意",
            "answer_score": 4,
            "response_time": 25,
            "answer_time": "2025-07-22 17:16:00",
            "question_content": "通常在参加小组讨论时我感到自然",
            "question_type": "single_choice",
            "dimension": "Group",
            "question_sort": 2,
            "option_content": "同意",
            "option_value": "4",
            "option_sort": 4,
            "question_no": 2
        }
    ]
}
```

#### 字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| answer_id | Long | 答题记录ID |
| question_id | Long | 题目ID |
| option_id | Long | 选项ID（选择题） |
| answer_content | String | 答案内容 |
| answer_score | BigDecimal | 答题得分 |
| response_time | Integer | 答题耗时（秒） |
| answer_time | String | 答题时间 |
| question_content | String | 题目内容 |
| question_type | String | 题目类型 |
| dimension | String | 所属维度 |
| question_sort | Integer | 题目排序 |
| option_content | String | 选项内容 |
| option_value | String | 选项分值 |
| option_sort | Integer | 选项排序 |
| question_no | Integer | 题目序号 |

### 2. 测评详细信息接口

#### 请求示例
```javascript
// 获取测评记录24的详细信息
fetch('/miniapp/user/assessment/detail/24', {
    method: 'GET'
})
.then(response => response.json())
.then(data => {
    console.log('测评详细信息:', data.data);
});
```

#### 返回数据结构
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "record": {
            "id": 24,
            "scaleId": 8,
            "userId": 136,
            "sessionId": "USER_136_SCALE_8_TIME_1753175353597",
            "status": 0,
            "startTime": "2025-07-22 17:09:14",
            "totalScore": 0
        },
        "progress": {
            "record_id": 24,
            "answered_questions": 15,
            "total_questions": 24,
            "progress": 62.50,
            "current_question_no": 16,
            "status_text": "进行中"
        },
        "answerHistory": [
            // 答题历史记录数组（同上）
        ],
        "scale": {
            "id": 8,
            "name": "交流恐惧自陈量表(PRCA-24)",
            "code": "PRCA24",
            "description": "评估个体在不同交流情境中的恐惧程度"
        },
        "questions": [
            // 所有题目和选项信息
        ]
    }
}
```

## 🔧 前端使用场景

### 1. 继续测评功能

```javascript
// 用户点击"继续测评"时
async function continueAssessment(recordId) {
    try {
        // 获取答题历史
        const historyResponse = await fetch(`/miniapp/user/assessment/answers/${recordId}`);
        const historyData = await historyResponse.json();
        
        // 获取进度信息
        const progressResponse = await fetch(`/miniapp/user/assessment/progress/${recordId}`);
        const progressData = await progressResponse.json();
        
        if (historyData.code === 200 && progressData.code === 200) {
            const answerHistory = historyData.data;
            const progress = progressData.data;
            
            // 恢复答题状态
            restoreAnswerState(answerHistory, progress);
            
            // 跳转到当前题目
            goToQuestion(progress.current_question_no);
        }
    } catch (error) {
        console.error('获取答题历史失败:', error);
    }
}

// 恢复答题状态
function restoreAnswerState(answerHistory, progress) {
    // 将答题历史保存到本地状态
    const answeredQuestions = {};
    answerHistory.forEach(answer => {
        answeredQuestions[answer.question_id] = {
            optionId: answer.option_id,
            answerContent: answer.answer_content,
            answerScore: answer.answer_score
        };
    });
    
    // 更新UI状态
    updateProgressBar(progress.progress);
    updateQuestionStatus(answeredQuestions);
    
    console.log(`已恢复 ${progress.answered_questions}/${progress.total_questions} 题的答题状态`);
}
```

### 2. 答题状态显示

```javascript
// 显示题目的答题状态
function renderQuestionStatus(questionId, answerHistory) {
    const answer = answerHistory.find(a => a.question_id === questionId);
    
    if (answer) {
        // 题目已答
        return {
            status: 'answered',
            selectedOption: answer.option_id,
            answerContent: answer.answer_content,
            score: answer.answer_score,
            answerTime: answer.answer_time
        };
    } else {
        // 题目未答
        return {
            status: 'unanswered'
        };
    }
}
```

### 3. 进度恢复

```javascript
// 恢复测评进度
function restoreProgress(progress) {
    // 更新进度条
    const progressBar = document.getElementById('progress-bar');
    progressBar.style.width = `${progress.progress}%`;
    progressBar.textContent = `${progress.answered_questions}/${progress.total_questions}`;
    
    // 更新状态文本
    const statusText = document.getElementById('status-text');
    statusText.textContent = progress.status_text;
    
    // 设置当前题目
    const currentQuestionNo = progress.current_question_no;
    highlightCurrentQuestion(currentQuestionNo);
}
```

## 📋 业务流程

### 继续测评完整流程

1. **用户点击继续测评**
2. **获取答题历史** → `/answers/{recordId}`
3. **获取当前进度** → `/progress/{recordId}`
4. **恢复答题状态**：
   - 标记已答题目
   - 恢复选择的选项
   - 更新进度条
5. **跳转到当前题目**
6. **用户继续答题**

### 数据同步

```javascript
// 答题后更新本地状态
function onAnswerSubmitted(questionId, optionId, answerContent, answerScore) {
    // 更新本地答题历史
    const newAnswer = {
        question_id: questionId,
        option_id: optionId,
        answer_content: answerContent,
        answer_score: answerScore,
        answer_time: new Date().toISOString()
    };
    
    // 添加到答题历史
    answerHistory.push(newAnswer);
    
    // 更新进度
    updateProgress();
}
```

## ⚠️ 注意事项

### 1. 权限验证
- 所有接口都会验证用户登录状态
- 验证测评记录是否属于当前用户
- 确保数据安全

### 2. 数据一致性
- 答题历史按题目排序返回
- 包含完整的题目和选项信息
- 分数计算准确

### 3. 性能考虑
- 答题历史可能较多，注意分页
- 考虑缓存常用数据
- 避免频繁请求

### 4. 错误处理
```javascript
// 处理接口错误
function handleApiError(error, operation) {
    console.error(`${operation}失败:`, error);
    
    // 显示用户友好的错误信息
    showErrorMessage(`${operation}失败，请重试`);
    
    // 记录错误日志
    logError(operation, error);
}
```

## 🧪 测试用例

### 1. 基础功能测试
```bash
# 测试获取答题历史
curl -X GET http://localhost:8080/miniapp/user/assessment/answers/24

# 测试获取测评详情
curl -X GET http://localhost:8080/miniapp/user/assessment/detail/24
```

### 2. 边界情况测试
- 测评记录不存在
- 用户无权限访问
- 没有答题历史
- 测评已完成

### 3. 数据验证
- 答题历史顺序正确
- 分数计算准确
- 进度计算正确
- 题目信息完整

现在前端可以完美地实现"继续测评"功能了！🎉
