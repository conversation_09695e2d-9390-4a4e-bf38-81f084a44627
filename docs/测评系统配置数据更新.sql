-- =====================================================
-- 测评系统配置数据更新脚本
-- =====================================================

-- 8. 更新量表基础配置
-- 更新PRCA-24配置
UPDATE psy_t_scale SET 
    scoring_method = 'FORMULA_WITH_BASE',
    has_reverse_items = 0,
    has_standard_score = 0,
    dimension_count = 4,
    raw_score_range = '24-120',
    standard_score_range = '24-120'
WHERE code = 'PRCA-24';

-- 更新STAI配置
UPDATE psy_t_scale SET 
    scoring_method = 'REVERSE_SCORING',
    has_reverse_items = 1,
    has_standard_score = 0,
    dimension_count = 2,
    raw_score_range = '20-80',
    standard_score_range = '20-80'
WHERE code = 'STAI';

-- 更新SAD配置
UPDATE psy_t_scale SET 
    scoring_method = 'SPECIAL_BINARY',
    has_reverse_items = 0,
    has_standard_score = 0,
    dimension_count = 2,
    raw_score_range = '0-28',
    standard_score_range = '0-28'
WHERE code = 'SAD';

-- 更新PDQ-4+配置
UPDATE psy_t_scale SET 
    scoring_method = 'COMPOSITE_SCORING',
    has_reverse_items = 0,
    has_standard_score = 0,
    dimension_count = 1,
    raw_score_range = '0-100',
    standard_score_range = '0-100'
WHERE code = 'PDQ-4+';

-- 更新FNE配置
UPDATE psy_t_scale SET 
    scoring_method = 'REVERSE_SCORING',
    has_reverse_items = 1,
    has_standard_score = 0,
    dimension_count = 1,
    raw_score_range = '0-30',
    standard_score_range = '0-30'
WHERE code = 'FNE';

-- 更新SAS配置
UPDATE psy_t_scale SET 
    scoring_method = 'STANDARD_SCORE',
    has_reverse_items = 1,
    has_standard_score = 1,
    standard_score_multiplier = 1.25,
    dimension_count = 1,
    raw_score_range = '20-80',
    standard_score_range = '25-100'
WHERE code = 'SAS';

-- 更新BAI配置
UPDATE psy_t_scale SET 
    scoring_method = 'STANDARD_SCORE',
    has_reverse_items = 0,
    has_standard_score = 1,
    standard_score_multiplier = 1.19,
    dimension_count = 1,
    raw_score_range = '21-84',
    standard_score_range = '25-100'
WHERE code = 'BAI';

-- 9. 更新分量表基础分数（PRCA-24）
-- 检查PRCA-24量表是否存在
SET @prca24_scale_id = (SELECT id FROM psy_t_scale WHERE code = 'PRCA-24' LIMIT 1);

-- 如果PRCA-24存在，更新分量表基础分数
UPDATE psy_t_subscale SET base_score = 18 
WHERE scale_id = @prca24_scale_id 
  AND alias IN ('Group', 'Meeting', 'Interpersonal', 'Public');

-- 10. 配置反向计分题目（STAI）
-- 检查STAI量表是否存在
SET @stai_scale_id = (SELECT id FROM psy_t_scale WHERE code = 'STAI' LIMIT 1);

-- 如果STAI存在，配置反向计分题目
UPDATE psy_t_question SET 
    is_reverse = 1, 
    reverse_value = 5 
WHERE scale_id = @stai_scale_id
  AND question_no IN (1,2,5,8,10,11,15,16,19,20,23,24,26,27,30,33,34,36,39);

-- 11. 配置反向计分题目（SAS）
-- 检查SAS量表是否存在
SET @sas_scale_id = (SELECT id FROM psy_t_scale WHERE code = 'SAS' LIMIT 1);

-- 如果SAS存在，配置反向计分题目
UPDATE psy_t_question SET 
    is_reverse = 1, 
    reverse_value = 4 
WHERE scale_id = @sas_scale_id
  AND question_no IN (5,9,13,17,19);

-- 12. 配置反向计分题目（FNE）
-- 检查FNE量表是否存在
SET @fne_scale_id = (SELECT id FROM psy_t_scale WHERE code = 'FNE' LIMIT 1);

-- 如果FNE存在，配置所有题目为反向计分
UPDATE psy_t_question SET 
    is_reverse = 1, 
    reverse_value = 5 
WHERE scale_id = @fne_scale_id;

-- =====================================================
-- 验证脚本
-- =====================================================

-- 检查升级结果
SELECT 'psy_t_scale扩展字段' as table_name, 
       COUNT(*) as updated_count 
FROM psy_t_scale 
WHERE scoring_method IS NOT NULL;

SELECT 'psy_t_subscale基础分数' as table_name, 
       COUNT(*) as updated_count 
FROM psy_t_subscale 
WHERE base_score > 0;

-- 检查STAI反向计分题目
SELECT 'STAI反向计分题目' as table_name, 
       COUNT(*) as updated_count 
FROM psy_t_question 
WHERE is_reverse = 1 AND scale_id = @stai_scale_id;

-- 检查SAS反向计分题目
SELECT 'SAS反向计分题目' as table_name, 
       COUNT(*) as updated_count 
FROM psy_t_question 
WHERE is_reverse = 1 AND scale_id = @sas_scale_id;

-- 检查FNE反向计分题目
SELECT 'FNE反向计分题目' as table_name, 
       COUNT(*) as updated_count 
FROM psy_t_question 
WHERE is_reverse = 1 AND scale_id = @fne_scale_id;

-- 显示所有量表的配置情况
SELECT 
    code as '量表编码',
    name as '量表名称',
    scoring_method as '计分方法',
    has_reverse_items as '有反向题',
    has_standard_score as '需要标准分',
    standard_score_multiplier as '标准分系数',
    dimension_count as '维度数量',
    raw_score_range as '原始分范围',
    standard_score_range as '标准分范围'
FROM psy_t_scale 
WHERE code IN ('PRCA-24', 'STAI', 'SAD', 'PDQ-4+', 'FNE', 'SAS', 'BAI')
ORDER BY code;

-- 升级完成提示
SELECT '测评系统配置数据更新完成！' as status, 
       NOW() as completion_time;
