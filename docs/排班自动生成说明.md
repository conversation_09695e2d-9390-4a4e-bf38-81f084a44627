# 排班自动生成功能说明

## 问题背景

原有的时间槽生成逻辑存在以下问题：
1. 时间槽生成依赖于 `psy_time_counselor_schedule` 表中的排班记录
2. 如果咨询师没有排班记录或排班日期过期，就不会生成时间槽
3. 导致用户无法预约咨询服务

## 解决方案

### 1. 新增自动排班生成功能

在 `IPsyTimeCounselorScheduleService` 接口中新增两个方法：

- `ensureFutureSchedule(Long counselorId, int days, Long centerId)` - 确保单个咨询师有未来指定天数的排班
- `ensureAllCounselorsFutureSchedule(int days, Long centerId)` - 确保所有咨询师都有未来指定天数的排班

### 2. 修改定时任务逻辑

修改 `PsyTimeSlotTaskService.generateFutureTimeSlots()` 方法，执行流程如下：

1. **第一步**：确保所有咨询师都有未来7天的排班记录
2. **第二步**：基于排班记录生成时间槽
3. **第三步**：生成系统公共时间槽

### 3. 默认排班规则

当系统自动生成排班时，使用以下默认规则：

- **工作时间**：9:00 - 21:00（包含晚上时间段）
- **工作状态**：工作状态（is_working = 1）
- **生成标记**：标记为模板生成（is_template = 1）
- **备注**：系统自动生成的默认排班
- **适用范围**：所有日期（包括周末）

### 时间段划分

系统将一天的工作时间划分为4个时间段：
- **上午**：9:00 - 12:00
- **中午**：12:00 - 14:00
- **下午**：14:00 - 18:00
- **晚上**：18:00 - 21:00

每个时间段按15分钟间隔生成时间槽，总共48个时间槽/天。

## 功能特点

### 1. 智能检测缺失排班

- 系统会检查每个咨询师在未来7天内是否有排班记录
- 只为缺失的日期生成新的排班记录
- 避免重复生成已存在的排班

### 2. 批量处理

- 支持为所有咨询师批量生成排班
- 使用事务处理确保数据一致性
- 详细的日志记录便于问题排查

### 3. 容错处理

- 单个咨询师排班生成失败不影响其他咨询师
- 完整的异常处理和日志记录
- 返回详细的执行结果统计

## API 接口

### 1. 测试接口

新增 `ScheduleTestController` 提供以下测试接口：

```bash
# 为指定咨询师确保未来排班
POST /test/schedule/ensure/{counselorId}?days=7&centerId=1

# 为所有咨询师确保未来排班
POST /test/schedule/ensureAll?days=7&centerId=1

# 手动触发完整的时间槽生成流程
POST /test/schedule/generateComplete?days=7

# 手动触发定时任务
POST /test/schedule/task/future
```

### 2. 原有接口增强

原有的手动生成时间槽接口也已增强：

```bash
# 手动生成时间槽（现在会先确保排班存在）
POST /system/timeTask/generate?days=7
```

## 执行流程

### 1. 定时任务执行流程

```
每天凌晨2:00执行
├── 确保所有咨询师都有未来7天排班
│   ├── 获取所有启用的咨询师列表
│   ├── 检查每个咨询师的未来7天排班
│   ├── 为缺失的日期生成默认排班
│   └── 记录生成结果
├── 基于排班记录生成时间槽
│   ├── 遍历所有咨询师的排班
│   ├── 为每个工作日生成15分钟间隔的时间槽
│   └── 更新时间槽状态
└── 生成系统公共时间槽
    ├── 聚合所有咨询师的时间槽
    ├── 计算可用咨询师数量
    └── 生成系统级别的时间槽
```

### 2. 排班生成逻辑

```
ensureFutureSchedule(counselorId, days, centerId)
├── 参数验证
├── 计算日期范围（明天开始的N天）
├── 查询现有排班记录
├── 找出缺失的日期
├── 为缺失日期生成默认排班
│   ├── 工作时间：9:00-18:00
│   ├── 工作状态：工作
│   ├── 生成标记：模板生成
│   └── 备注：系统自动生成
└── 批量插入新排班记录
```

## 配置说明

### 1. 默认参数

- **未来天数**：7天
- **咨询中心ID**：1（默认）
- **工作开始时间**：9:00
- **工作结束时间**：21:00（包含晚上时间段）

### 2. 可配置项

这些参数可以根据实际需求进行调整：

- 修改 `PsyTimeCounselorScheduleServiceImpl.ensureFutureSchedule()` 方法中的默认时间
- 修改定时任务中的天数参数
- 修改默认咨询中心ID

## 监控和日志

### 1. 日志级别

- **INFO**：正常执行流程和结果统计
- **DEBUG**：详细的执行步骤
- **WARN**：非致命错误和警告
- **ERROR**：严重错误和异常

### 2. 关键日志

```
为咨询师 {counselorId} 自动生成了 {count} 条未来排班记录
为 {totalCounselors} 个咨询师确保了未来 {days} 天的排班，共生成 {totalCount} 条新记录
定时任务完成：生成 {scheduleCount} 条排班记录，{slotCount} 个咨询师时间槽和 {systemSlotCount} 个系统时间槽
```

## 注意事项

1. **数据库约束**：确保 `psy_time_counselor_schedule` 表的唯一约束 `uniq_counselor_date` 正常工作
2. **并发处理**：使用 `INSERT IGNORE` 或事务处理避免并发插入冲突
3. **性能考虑**：批量插入提高性能，避免逐条插入
4. **数据一致性**：确保排班生成和时间槽生成的原子性

## 测试建议

1. **功能测试**：使用测试接口验证排班生成功能
2. **边界测试**：测试无咨询师、无排班等边界情况
3. **并发测试**：测试多个定时任务同时执行的情况
4. **数据验证**：检查生成的排班数据是否正确
