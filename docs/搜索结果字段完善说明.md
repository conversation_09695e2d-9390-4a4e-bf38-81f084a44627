# 搜索结果字段完善说明

## 🎯 问题解决

您提到搜索结果返回的字段太少，需要按照列表页面的字段返回。我已经完善了测评搜索结果的字段，现在返回完整的测评信息。

## 📊 字段对比

### **修改前（字段较少）**
```json
{
  "id": 1,
  "title": "焦虑自评量表(SAS)",
  "description": "用于评估焦虑病人的主观感受",
  "coverImage": "/images/sas.jpg",
  "type": "assessment",
  "price": "0.00",
  "rating": 4.5,
  "createTime": "2025-07-17 15:18:24",
  "viewCount": 15,
  "extraData": {
    "scaleCode": "SAS",
    "questionCount": 20,
    "testCount": 0,
    "url": "/pages/assessment/detail?id=1"
  }
}
```

### **修改后（字段完整）**
```json
{
  "id": 1,
  "title": "焦虑自评量表(SAS)",
  "description": "用于评估焦虑病人的主观感受",
  "coverImage": "/images/sas.jpg",
  "type": "assessment",
  "price": "0.00",
  "rating": 4.5,
  "createTime": "2025-07-17 15:18:24",
  "updateTime": "2025-07-24 20:08:21",
  "viewCount": 15,
  "extraData": {
    // 基础信息
    "scaleCode": "SAS",
    "categoryId": 101,
    "introduction": "SAS是心理学专业量表...",
    
    // 测评相关信息
    "questionCount": 20,
    "scoringType": "LIKERT",
    "duration": "5~10分钟",
    "applicableAge": "成人",
    
    // 付费相关信息
    "payMode": 0,
    "payPhase": 0,
    "freeVipLevel": 0,
    "freeReportLevel": 1,
    "paidReportLevel": 3,
    
    // 状态信息
    "status": 1,
    "sort": 1,
    
    // 统计信息
    "searchCount": 0,
    "testCount": 15,
    
    // 计分相关信息
    "scoringMethod": "STANDARD_SCORE",
    "hasReverseItems": 0,
    "hasStandardScore": 1,
    "standardScoreMultiplier": 1.25,
    
    // 常模信息
    "normMean": null,
    "normSd": null,
    
    // 详细说明信息
    "testNotice": "请在专业人士的指导下使用",
    "testPurpose": "用于评出焦虑病人的主观感受",
    "testObject": "成人",
    "testPreparation": "在自评者评定之前，要让他把整个量表的填写方法...",
    "testProcessing": "1.评定的时间范围，应强调是\"现在或过去一周\"...",
    "testAttention": null,
    "testTheory": "1. 焦虑的症状维度模型...",
    "testApplication": "SAS被称为焦虑自评量表...",
    "referenceLiterature": "Zung, W. W. (1971). A rating instrument for anxiety disorders...",
    
    // 页面链接
    "url": "/pages/assessment/detail?id=1",
    "testUrl": "/pages/assessment/test?id=1"
  }
}
```

## 🔧 新增字段说明

### **1. 基础信息字段**
- `scaleCode` - 量表编码（如：SAS、STAI）
- `categoryId` - 分类ID
- `introduction` - 量表介绍
- `updateTime` - 更新时间

### **2. 测评相关字段**
- `questionCount` - 题目数量
- `scoringType` - 计分类型（如：LIKERT）
- `duration` - 测评时长（如：5~10分钟）
- `applicableAge` - 适用年龄（如：成人）

### **3. 付费相关字段**
- `payMode` - 付费模式（0=免费，1=付费）
- `payPhase` - 付费阶段（0=测评前，1=报告前）
- `freeVipLevel` - 免费VIP等级
- `freeReportLevel` - 免费报告等级
- `paidReportLevel` - 付费报告等级

### **4. 状态信息字段**
- `status` - 状态（0=禁用，1=启用）
- `sort` - 排序值

### **5. 统计信息字段**
- `searchCount` - 搜索次数
- `testCount` - 测试次数（与searchCount相同）

### **6. 计分相关字段**
- `scoringMethod` - 计分方法（如：STANDARD_SCORE）
- `hasReverseItems` - 是否有反向计分题（0=否，1=是）
- `hasStandardScore` - 是否需要标准分转换（0=否，1=是）
- `standardScoreMultiplier` - 标准分转换系数（如：1.25）

### **7. 常模信息字段**
- `normMean` - 常模均值
- `normSd` - 常模标准差

### **8. 详细说明字段**
- `testNotice` - 测评须知
- `testPurpose` - 测评目的
- `testObject` - 测评对象
- `testPreparation` - 测评准备
- `testProcessing` - 测评后的处理
- `testAttention` - 注意事项
- `testTheory` - 测评基础理论
- `testApplication` - 测评应用
- `referenceLiterature` - 引用文献

### **9. 页面链接字段**
- `url` - 详情页链接
- `testUrl` - 测评页链接

## 🎯 使用场景

### **前端列表展示**
现在搜索结果包含了所有列表页面需要的字段：
```javascript
// 显示基础信息
const title = item.title;
const description = item.description;
const price = item.price;
const questionCount = item.extraData.questionCount;
const duration = item.extraData.duration;

// 显示付费信息
const isFree = item.extraData.payMode === 0;
const payModeText = isFree ? "免费" : "付费";

// 显示状态信息
const isEnabled = item.extraData.status === 1;
const statusText = isEnabled ? "已启用" : "已禁用";
```

### **详情页跳转**
```javascript
// 跳转到详情页
const detailUrl = item.extraData.url;
// 跳转到测评页
const testUrl = item.extraData.testUrl;
```

### **筛选和排序**
```javascript
// 按价格筛选
const freeItems = items.filter(item => item.extraData.payMode === 0);
const paidItems = items.filter(item => item.extraData.payMode === 1);

// 按题目数量排序
items.sort((a, b) => a.extraData.questionCount - b.extraData.questionCount);

// 按更新时间排序
items.sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime));
```

## 📋 数据完整性

### **字段默认值处理**
- 数值字段：如果为null，设置为0
- 字符串字段：如果为null，保持null（前端可以判断显示）
- 价格字段：如果为null，设置为"0.00"
- 查看次数：如果为null，设置为0

### **字段验证**
所有字段都从数据库实体对象中获取，确保数据的一致性和完整性。

## ✅ 预期效果

修改后的搜索结果将：

1. **字段完整** - 包含列表页面的所有字段
2. **信息丰富** - 提供详细的测评信息
3. **便于展示** - 前端可以直接使用所有字段
4. **支持筛选** - 可以根据各种条件筛选
5. **便于跳转** - 包含详情页和测评页链接

现在搜索结果的字段与列表页面完全一致，前端可以使用相同的组件来展示搜索结果！
