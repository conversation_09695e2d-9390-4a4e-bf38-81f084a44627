# Mapper缺失问题修复说明

## 🚨 问题描述

在调用分数计算方法时出现错误：
```
org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.xihuan.system.mapper.PsyTQuestionOptionMapper.selectOptionById
```

## 🔍 问题原因

1. **Mapper接口存在**：`PsyTQuestionOptionMapper.java` 中定义了 `selectOptionById` 方法
2. **XML文件缺失**：`PsyTQuestionOptionMapper.xml` 文件不存在
3. **MyBatis无法找到SQL实现**：接口方法没有对应的SQL映射

## 🔧 修复内容

### 1. 创建了完整的 PsyTQuestionOptionMapper.xml

#### ✅ 文件位置
```
xihuan-system/src/main/resources/mapper/system/PsyTQuestionOptionMapper.xml
```

#### ✅ 核心方法实现

**selectOptionById 方法**：
```xml
<select id="selectOptionById" parameterType="Long" resultMap="OptionResultMap">
    SELECT o.id, o.question_id, o.option_text, o.option_value, o.sort, o.order_num,
           o.status, o.del_flag, o.create_by, o.create_time, o.update_by, o.update_time, o.remark
    FROM psy_t_question_option o
    WHERE o.id = #{id} AND o.del_flag = 0
</select>
```

**ResultMap 映射**：
```xml
<resultMap id="OptionResultMap" type="PsyTQuestionOption">
    <id property="id" column="id"/>
    <result property="questionId" column="question_id"/>
    <result property="optionText" column="option_text"/>
    <result property="optionValue" column="option_value"/>
    <result property="sort" column="sort"/>
    <result property="orderNum" column="order_num"/>
    <result property="status" column="status"/>
    <result property="delFlag" column="del_flag"/>
    <result property="createBy" column="create_by"/>
    <result property="createTime" column="create_time"/>
    <result property="updateBy" column="update_by"/>
    <result property="updateTime" column="update_time"/>
    <result property="remark" column="remark"/>
</resultMap>
```

### 2. 完整的CRUD操作支持

#### ✅ 查询方法
- `selectOptionById` - 根据ID查询单个选项
- `selectOptionList` - 查询选项列表
- `selectOptionsByQuestionId` - 根据题目ID查询选项
- `selectOptionsByQuestionIds` - 根据题目ID列表查询选项

#### ✅ 增删改方法
- `insertOption` - 新增选项
- `updateOption` - 修改选项
- `deleteOptionById` - 删除选项
- `deleteOptionByIds` - 批量删除选项
- `deleteOptionsByQuestionId` - 根据题目ID删除选项

#### ✅ 统计方法
- `countOptionsByQuestionId` - 统计题目选项数量
- `getMaxSortByQuestionId` - 获取最大排序号
- `checkOptionTextExists` - 检查选项文本重复

## 📊 数据库字段映射

### psy_t_question_option 表结构
```sql
CREATE TABLE psy_t_question_option (
    id           bigint auto_increment PRIMARY KEY,
    question_id  bigint NOT NULL,
    option_text  varchar(200) NOT NULL,
    option_value int NOT NULL,
    sort         int DEFAULT 0,
    order_num    int DEFAULT 0,
    status       int DEFAULT 1,
    del_flag     int DEFAULT 0,
    create_by    varchar(64) DEFAULT '',
    create_time  datetime DEFAULT CURRENT_TIMESTAMP,
    update_by    varchar(64) DEFAULT '',
    update_time  datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    remark       varchar(500) DEFAULT NULL
);
```

### 实体类字段映射
| 数据库字段 | 实体类属性 | 类型 | 说明 |
|------------|------------|------|------|
| id | id | Long | 选项ID |
| question_id | questionId | Long | 题目ID |
| option_text | optionText | String | 选项文本 |
| option_value | optionValue | Integer | 选项分值 |
| sort | sort | Integer | 排序 |
| order_num | orderNum | Integer | 显示顺序 |
| status | status | Integer | 状态 |
| del_flag | delFlag | Integer | 删除标志 |
| create_by | createBy | String | 创建者 |
| create_time | createTime | Date | 创建时间 |
| update_by | updateBy | String | 更新者 |
| update_time | updateTime | Date | 更新时间 |
| remark | remark | String | 备注 |

## 🧪 验证方法

### 1. 重启应用
```bash
# 重启Spring Boot应用以加载新的Mapper XML
```

### 2. 测试分数计算
```bash
# 重新提交答案，查看日志
curl -X POST http://localhost:8080/miniapp/user/assessment/answer \
  -H "Content-Type: application/json" \
  -d '{
    "recordId": 24,
    "questionId": 277,
    "optionId": 761,
    "responseTime": 30
  }'
```

### 3. 查看日志输出
应该看到类似的日志：
```
INFO - 计算分数 - questionId: 277, optionId: 761, answerContent: 同意
INFO - 选项信息 - optionText: 同意, optionValue: 4
INFO - 从选项获得分数: 4
INFO - 最终计算分数: questionId: 277, optionId: 761, score: 4
```

### 4. 数据库验证
```sql
-- 检查答题记录分数是否正确保存
SELECT id, question_id, option_id, answer_content, answer_score
FROM psy_t_answer_record 
WHERE record_id = 24 
ORDER BY create_time DESC 
LIMIT 5;
```

## 📋 相关接口方法

### PsyTQuestionOptionMapper 接口
```java
public interface PsyTQuestionOptionMapper {
    
    /**
     * 根据ID查询题目选项
     */
    PsyTQuestionOption selectOptionById(Long id);
    
    /**
     * 查询题目选项列表
     */
    List<PsyTQuestionOption> selectOptionList(PsyTQuestionOption option);
    
    /**
     * 根据题目ID查询选项列表
     */
    List<PsyTQuestionOption> selectOptionsByQuestionId(Long questionId);
    
    /**
     * 新增题目选项
     */
    int insertOption(PsyTQuestionOption option);
    
    /**
     * 修改题目选项
     */
    int updateOption(PsyTQuestionOption option);
    
    /**
     * 删除题目选项
     */
    int deleteOptionById(Long id);
}
```

## ⚠️ 注意事项

### 1. 文件命名规范
- Mapper接口：`PsyTQuestionOptionMapper.java`
- XML文件：`PsyTQuestionOptionMapper.xml`
- 命名必须完全一致

### 2. 命名空间配置
```xml
<mapper namespace="com.xihuan.system.mapper.PsyTQuestionOptionMapper">
```
命名空间必须与接口全限定名一致

### 3. 方法名匹配
- XML中的 `id` 属性必须与接口方法名完全一致
- 参数类型和返回类型必须匹配

### 4. 软删除处理
```xml
WHERE o.del_flag = 0
```
所有查询都添加了软删除条件

## 🚀 预期效果

修复后应该能够：
1. **正常查询选项**：`selectOptionById` 方法正常工作
2. **正确计算分数**：根据选项值计算答题分数
3. **保存分数**：答题记录中的 `answer_score` 不再是0
4. **完整日志**：看到详细的分数计算过程

## 📈 后续优化

### 1. 性能优化
- 添加选项查询缓存
- 批量查询选项信息

### 2. 功能扩展
- 支持选项图片
- 支持选项权重配置
- 支持动态分值计算

现在 Mapper XML 文件已经创建完成，重启应用后分数计算应该能正常工作了！🎉
