-- 检查时间段表数据和系统时间槽生成问题

-- ==================== 1. 检查 psy_time_range 表数据 ====================
SELECT * FROM psy_time_range WHERE del_flag = '0' ORDER BY start_hour;

-- 如果上面查询结果为空，执行下面的插入语句
INSERT INTO psy_time_range (start_hour, end_hour, del_flag, create_by, create_time) VALUES
(9, 10, '0', 'system', NOW()),
(10, 11, '0', 'system', NOW()),
(11, 12, '0', 'system', NOW()),
(14, 15, '0', 'system', NOW()),
(15, 16, '0', 'system', NOW()),
(16, 17, '0', 'system', NOW()),
(19, 20, '0', 'system', NOW()),
(20, 21, '0', 'system', NOW());

-- ==================== 2. 检查系统时间槽表结构 ====================
DESCRIBE psy_system_time_slot;

-- ==================== 3. 检查现有系统时间槽数据 ====================
SELECT * FROM psy_system_time_slot 
WHERE date_key BETWEEN '2025-07-25' AND '2025-07-31'
ORDER BY date_key, start_time;

-- ==================== 4. 检查系统时间槽生成服务是否正常调用 ====================
-- 查看最近的系统时间槽记录
SELECT * FROM psy_system_time_slot 
ORDER BY create_time DESC 
LIMIT 10;

-- ==================== 5. 手动测试时间段服务 ====================
-- 这个需要在应用中测试，检查 timeRangeService.selectAllActiveTimeRanges() 是否返回数据
