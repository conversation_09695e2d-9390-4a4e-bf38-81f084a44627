# 咨询师排班和时间槽生成功能修复总结

## 问题描述

系统中存在两个主要问题：

1. **`selectScalesByCategoryId` 方法缺失**：服务实现类中调用了这个方法，但 Mapper 接口和 XML 中没有定义
2. **咨询师排班接口没有被使用**：`PsyTimeCounselorScheduleMapper` 接口存在但缺少对应的服务层和控制器，导致时间槽不能生成

## 修复方案

### 1. 修复 `selectScalesByCategoryId` 方法缺失

#### 1.1 在 Mapper 接口中添加方法定义

**文件**: `xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentScaleMapper.java`

```java
/**
 * 根据分类ID查询量表列表
 * 
 * @param categoryId 分类ID
 * @return 量表集合
 */
List<PsyAssessmentScale> selectScalesByCategoryId(Long categoryId);
```

#### 1.2 在 XML 映射文件中添加 SQL 查询

**文件**: `xihuan-system/src/main/resources/mapper/system/PsyAssessmentScaleMapper.xml`

```xml
<!-- 根据分类ID查询量表列表 -->
<select id="selectScalesByCategoryId" parameterType="Long" resultMap="ScaleResultMap">
    <include refid="selectPsyAssessmentScaleVo"/>
    FROM psy_t_scale s
    WHERE s.category_id = #{categoryId} AND s.del_flag = '0' AND s.status = 1
    ORDER BY s.sort ASC, s.create_time DESC
</select>
```

### 2. 完善咨询师排班管理功能

#### 2.1 创建咨询师排班控制器

**文件**: `xihuan-admin/src/main/java/com/xihuan/web/controller/system/PsyTimeCounselorScheduleController.java`

**主要功能**:
- ✅ 查询排班列表和详情
- ✅ 根据咨询师和日期查询排班
- ✅ 新增和批量新增排班
- ✅ 修改和删除排班
- ✅ 生成默认排班（工作日 9:00-18:00）
- ✅ 为所有咨询师批量生成排班

**核心接口**:
```java
// 查询排班列表
GET /system/counselor/schedule/list

// 根据咨询师和日期查询排班
GET /system/counselor/schedule/counselor/{counselorId}?scheduleDate=2024-01-01

// 新增排班
POST /system/counselor/schedule

// 生成默认排班
POST /system/counselor/schedule/generate/{counselorId}?startDate=2024-01-01&endDate=2024-01-07

// 为所有咨询师生成排班
POST /system/counselor/schedule/generateAll?startDate=2024-01-01&endDate=2024-01-07
```

#### 2.2 创建咨询师排班服务接口

**文件**: `xihuan-system/src/main/java/com/xihuan/system/service/IPsyTimeCounselorScheduleService.java`

**主要方法**:
- `selectScheduleList()` - 查询排班列表
- `selectScheduleByCounselorAndDate()` - 根据咨询师和日期查询排班
- `insertSchedule()` - 新增排班
- `batchInsertSchedules()` - 批量新增排班
- `generateDefaultSchedule()` - 生成默认排班
- `generateAllCounselorSchedule()` - 为所有咨询师生成排班

#### 2.3 创建咨询师排班服务实现类

**文件**: `xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyTimeCounselorScheduleServiceImpl.java`

**核心功能**:

##### 默认排班生成逻辑
```java
// 工作日：9:00-18:00
if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
    schedule.setStartTime(LocalTime.of(9, 0));
    schedule.setEndTime(LocalTime.of(18, 0));
    schedule.setIsWorking(1);
} else {
    // 周末：休假
    schedule.setIsWorking(0);
}
```

##### 批量生成功能
- 检查排班是否已存在，避免重复生成
- 支持日期范围批量生成
- 自动区分工作日和周末
- 记录生成日志

## 系统架构完善

### 1. 完整的排班管理流程

```
1. 管理员创建咨询师排班
   ↓
2. 系统根据排班生成时间槽
   ↓
3. 用户可以预约可用的时间槽
   ↓
4. 咨询师查看自己的排班和预约
```

### 2. 时间槽生成依赖关系

```
咨询师排班数据 (PsyTimeCounselorSchedule)
   ↓
时间槽生成服务 (PsyTimeSlotService.generateSlotsForCounselor)
   ↓
可用时间槽 (PsyTimeSlot)
   ↓
用户预约 (PsyAppointment)
```

### 3. 数据流向

```
排班管理 → 时间槽生成 → 预约管理 → 咨询服务
```

## 使用指南

### 1. 创建咨询师排班

#### 手动创建单个排班
```bash
POST /system/counselor/schedule
{
    "counselorId": 1,
    "scheduleDate": "2024-01-15",
    "startTime": "09:00:00",
    "endTime": "18:00:00",
    "isWorking": 1,
    "centerId": 1
}
```

#### 批量生成默认排班
```bash
# 为指定咨询师生成一周排班
POST /system/counselor/schedule/generate/1?startDate=2024-01-15&endDate=2024-01-21&centerId=1

# 为所有咨询师生成排班
POST /system/counselor/schedule/generateAll?startDate=2024-01-15&endDate=2024-01-21&centerId=1
```

### 2. 生成时间槽

排班创建完成后，使用现有的时间槽生成接口：

```bash
# 为指定咨询师生成时间槽
POST /wechat/timeSlot/generate/1?startDate=2024-01-15&endDate=2024-01-21

# 为所有咨询师生成时间槽
POST /wechat/timeSlot/generateAll?startDate=2024-01-15&endDate=2024-01-21
```

### 3. 查询可用时间槽

```bash
# 查询咨询师的时间槽
GET /wechat/timeSlot/counselor/1?startDate=2024-01-15&endDate=2024-01-21

# 查询公共时间槽
GET /wechat/timeSlot/public?startDate=2024-01-15&endDate=2024-01-21&centerId=1
```

## 权限配置

需要在系统中配置以下权限：

```
system:schedule:list    - 查看排班列表
system:schedule:query   - 查看排班详情
system:schedule:add     - 新增排班
system:schedule:edit    - 修改排班
system:schedule:remove  - 删除排班
```

## 数据库依赖

确保以下表存在并有数据：

1. **psy_t_counselor_schedule** - 咨询师排班表
2. **psy_t_time_slot** - 时间槽表
3. **psy_t_consultant** - 咨询师表
4. **psy_t_center** - 咨询中心表

## 测试验证

### 1. 排班管理测试

```bash
# 1. 生成默认排班
curl -X POST "http://localhost:8080/system/counselor/schedule/generate/1?startDate=2024-01-15&endDate=2024-01-21&centerId=1"

# 2. 查询排班
curl -X GET "http://localhost:8080/system/counselor/schedule/counselor/1?scheduleDate=2024-01-15"

# 3. 查询日期范围内的排班
curl -X GET "http://localhost:8080/system/counselor/schedule/counselor/1/range?startDate=2024-01-15&endDate=2024-01-21"
```

### 2. 时间槽生成测试

```bash
# 1. 生成时间槽
curl -X POST "http://localhost:8080/wechat/timeSlot/generate/1?startDate=2024-01-15&endDate=2024-01-21"

# 2. 查询生成的时间槽
curl -X GET "http://localhost:8080/wechat/timeSlot/counselor/1?startDate=2024-01-15&endDate=2024-01-21"
```

## 注意事项

### 1. 数据一致性
- 排班数据必须先于时间槽数据创建
- 删除排班时需要同时清理相关的时间槽数据

### 2. 业务规则
- 默认工作时间：工作日 9:00-18:00
- 周末默认为休假状态
- 避免重复生成排班数据

### 3. 性能考虑
- 批量操作使用事务处理
- 大量数据生成时考虑分批处理
- 定期清理过期的排班和时间槽数据

## 总结

通过完善咨询师排班管理功能，现在系统具备了：

1. **✅ 完整的排班管理** - 支持排班的增删改查
2. **✅ 自动排班生成** - 支持默认排班模板生成
3. **✅ 批量操作支持** - 支持批量生成和管理
4. **✅ 时间槽生成基础** - 为时间槽生成提供数据支持
5. **✅ 完整的API接口** - 提供完整的管理接口
6. **✅ 权限控制** - 集成系统权限管理

现在时间槽生成功能可以正常工作，管理员可以通过排班管理来控制咨询师的可用时间，系统会根据排班数据自动生成对应的时间槽供用户预约。
