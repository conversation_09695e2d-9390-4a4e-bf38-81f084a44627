# 智能时间槽生成说明

## 概述

系统现在采用智能化的时间槽生成策略，**优先根据咨询师的实际排班信息生成时间槽**，只有在没有排班信息时才使用默认的时间段设置。

## 核心原则

### 1. 排班优先原则
- **第一优先级**：咨询师的实际排班时间
- **第二优先级**：咨询师的排班模板
- **第三优先级**：系统默认时间段设置

### 2. 智能匹配原则
- 时间槽的时间段ID不仅仅根据开始时间确定
- 考虑时间槽的完整时间范围进行智能匹配
- 使用时间槽的中点时间来确定最合适的时间段

## 时间段定义

系统预定义了4个标准时间段：

| ID | 名称 | 时间范围 | 说明 |
|----|------|----------|------|
| 20 | 上午 | 9:00-12:00 | 上午工作时间 |
| 21 | 中午 | 12:00-14:00 | 中午时间段 |
| 22 | 下午 | 14:00-18:00 | 下午工作时间 |
| 23 | 晚上 | 18:00-21:00 | 晚上工作时间 |

## 生成逻辑详解

### 1. 排班记录生成

#### A. 智能排班创建
```java
// 优先级1：检查咨询师是否有自定义排班模板
PsyTimeScheduleTemplate template = getEffectiveTemplateForDate(counselorId, date);

if (template != null) {
    // 使用咨询师的自定义模板
    applyTemplateToSchedule(schedule, template, date);
} else {
    // 使用系统默认设置（9:00-21:00）
    applyDefaultScheduleSettings(schedule);
}
```

#### B. 默认排班设置
- **工作时间**：9:00 - 21:00
- **覆盖时间段**：上午、中午、下午、晚上（完整覆盖）
- **工作状态**：工作状态（is_working = 1）

### 2. 时间槽生成

#### A. 基于排班生成
```java
// 完全基于咨询师的排班时间
LocalTime startTime = schedule.getStartTime(); // 咨询师的实际开始时间
LocalTime endTime = schedule.getEndTime();     // 咨询师的实际结束时间

// 按15分钟间隔生成时间槽
while (currentTime.isBefore(endTime)) {
    LocalTime slotEndTime = currentTime.plusMinutes(15);
    // 创建时间槽...
}
```

#### B. 智能时间段匹配
```java
// 计算时间槽的中点时间
int slotMidMinutes = (slotStartMinutes + slotEndMinutes) / 2;
int slotMidHour = slotMidMinutes / 60;

// 寻找最匹配的时间段
for (PsyTimeRange timeRange : allTimeRanges) {
    if (slotMidHour >= timeRange.getStartHour() && slotMidHour < timeRange.getEndHour()) {
        bestMatch = timeRange; // 找到最佳匹配
        break;
    }
}
```

## 匹配示例

### 时间槽与时间段的智能匹配

| 时间槽时间 | 中点时间 | 匹配时间段 | 时间段ID | 说明 |
|------------|----------|------------|----------|------|
| 9:00-9:15 | 9:07 (9时) | 上午 | 20 | 中点在上午时间段内 |
| 11:45-12:00 | 11:52 (11时) | 上午 | 20 | 中点仍在上午时间段内 |
| 12:00-12:15 | 12:07 (12时) | 中午 | 21 | 中点在中午时间段内 |
| 13:45-14:00 | 13:52 (13时) | 中午 | 21 | 中点在中午时间段内 |
| 14:00-14:15 | 14:07 (14时) | 下午 | 22 | 中点在下午时间段内 |
| 17:45-18:00 | 17:52 (17时) | 下午 | 22 | 中点在下午时间段内 |
| 18:00-18:15 | 18:07 (18时) | 晚上 | 23 | 中点在晚上时间段内 |
| 20:45-21:00 | 20:52 (20时) | 晚上 | 23 | 中点在晚上时间段内 |

## 实际应用场景

### 场景1：咨询师有完整排班
```
咨询师A的排班：9:00-21:00
生成结果：48个时间槽，覆盖所有4个时间段
```

### 场景2：咨询师有部分时间排班
```
咨询师B的排班：14:00-18:00
生成结果：16个时间槽，只覆盖下午时间段
```

### 场景3：咨询师有特殊时间排班
```
咨询师C的排班：19:00-22:00
生成结果：12个时间槽，主要覆盖晚上时间段
```

### 场景4：咨询师无排班记录
```
系统自动生成：9:00-21:00的默认排班
生成结果：48个时间槽，覆盖所有4个时间段
```

## 优势特点

### 1. 灵活性
- 完全尊重咨询师的个性化工作时间安排
- 支持非标准工作时间（如只做晚上咨询）
- 支持部分时间段工作

### 2. 智能性
- 智能匹配时间段，避免边界时间的错误分类
- 自动处理跨时间段的情况
- 容错机制，确保系统稳定运行

### 3. 一致性
- 时间槽的时间段分类准确
- 前端展示逻辑清晰
- 用户体验统一

## 测试接口

### 1. 基础测试
```bash
# 查看时间段匹配逻辑
GET /test/schedule/testTimeRangeMatching

# 测试时间槽生成范围
GET /test/schedule/testTimeRange
```

### 2. 排班测试
```bash
# 为指定咨询师确保排班
POST /test/schedule/ensure/{counselorId}?days=7

# 查看咨询师排班详情
GET /test/schedule/schedule/{counselorId}?startDate=2024-01-01&endDate=2024-01-07
```

### 3. 完整流程测试
```bash
# 完整的时间槽生成流程
POST /test/schedule/generateComplete?days=7
```

## 注意事项

1. **排班优先**：系统严格按照咨询师的实际排班时间生成时间槽
2. **时间段匹配**：使用智能算法确保时间槽正确分类到对应时间段
3. **向后兼容**：保持与现有系统的兼容性
4. **性能优化**：批量处理提高生成效率

## 配置建议

1. **咨询师培训**：建议咨询师根据实际工作时间设置排班
2. **定期检查**：定期检查时间槽生成是否符合预期
3. **灵活调整**：根据业务需求调整默认时间段设置
