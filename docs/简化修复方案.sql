-- 简化修复方案：快速为咨询师4、7、8创建排班记录

-- ==================== 1. 快速诊断 ====================
-- 检查这三个咨询师的排班情况
SELECT counselor_id, date_key, is_working, start_time, end_time
FROM psy_time_counselor_schedule 
WHERE counselor_id IN (4, 7, 8)
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
ORDER BY counselor_id, date_key;

-- ==================== 2. 删除旧排班记录（如果有） ====================
DELETE FROM psy_time_counselor_schedule 
WHERE counselor_id IN (4, 7, 8)
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01';

-- ==================== 3. 为咨询师4创建排班 ====================
INSERT INTO psy_time_counselor_schedule 
(counselor_id, date_key, day_of_week, is_working, start_time, end_time, del_flag, create_by, create_time)
VALUES 
-- 咨询师4的排班
(4, '2025-07-26', 6, 0, NULL, NULL, '0', 'system', NOW()),           -- 周六休息
(4, '2025-07-27', 0, 0, NULL, NULL, '0', 'system', NOW()),           -- 周日休息
(4, '2025-07-28', 1, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周一工作
(4, '2025-07-29', 2, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周二工作
(4, '2025-07-30', 3, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周三工作
(4, '2025-07-31', 4, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周四工作
(4, '2025-08-01', 5, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周五工作

-- 咨询师7的排班
(7, '2025-07-26', 6, 0, NULL, NULL, '0', 'system', NOW()),           -- 周六休息
(7, '2025-07-27', 0, 0, NULL, NULL, '0', 'system', NOW()),           -- 周日休息
(7, '2025-07-28', 1, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周一工作
(7, '2025-07-29', 2, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周二工作
(7, '2025-07-30', 3, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周三工作
(7, '2025-07-31', 4, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周四工作
(7, '2025-08-01', 5, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周五工作

-- 咨询师8的排班
(8, '2025-07-26', 6, 0, NULL, NULL, '0', 'system', NOW()),           -- 周六休息
(8, '2025-07-27', 0, 0, NULL, NULL, '0', 'system', NOW()),           -- 周日休息
(8, '2025-07-28', 1, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周一工作
(8, '2025-07-29', 2, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周二工作
(8, '2025-07-30', 3, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周三工作
(8, '2025-07-31', 4, 1, '09:00:00', '17:00:00', '0', 'system', NOW()), -- 周四工作
(8, '2025-08-01', 5, 1, '09:00:00', '17:00:00', '0', 'system', NOW()); -- 周五工作

-- ==================== 4. 验证排班创建成功 ====================
SELECT 
    counselor_id,
    date_key,
    CASE day_of_week 
        WHEN 0 THEN '周日'
        WHEN 1 THEN '周一'
        WHEN 2 THEN '周二'
        WHEN 3 THEN '周三'
        WHEN 4 THEN '周四'
        WHEN 5 THEN '周五'
        WHEN 6 THEN '周六'
    END as weekday,
    CASE is_working 
        WHEN 1 THEN '工作'
        WHEN 0 THEN '休息'
    END as work_status,
    start_time,
    end_time
FROM psy_time_counselor_schedule 
WHERE counselor_id IN (4, 7, 8)
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
ORDER BY counselor_id, date_key;

-- ==================== 5. 统计结果 ====================
SELECT 
    counselor_id,
    COUNT(*) as total_days,
    SUM(is_working) as working_days,
    SUM(CASE WHEN is_working = 0 THEN 1 ELSE 0 END) as rest_days
FROM psy_time_counselor_schedule 
WHERE counselor_id IN (4, 7, 8)
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
GROUP BY counselor_id
ORDER BY counselor_id;

-- ==================== 6. 完成提示 ====================
SELECT 
    '排班记录创建完成！' as message,
    '每个咨询师应该有7天排班记录（5个工作日 + 2个休息日）' as expected,
    '现在可以重新运行时间槽生成任务' as next_step;
