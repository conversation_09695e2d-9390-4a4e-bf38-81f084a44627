-- 批量修复咨询师排班和时间槽生成问题

-- ==================== 1. 诊断当前状态 ====================

-- 1.1 检查所有咨询师的基本信息
SELECT id, name, work_status, del_flag, create_time
FROM psy_consultants 
WHERE del_flag = '0'
ORDER BY id;

-- 1.2 检查现有排班记录情况
SELECT counselor_id, 
       COUNT(*) as total_schedules,
       COUNT(CASE WHEN date_key BETWEEN '2025-07-26' AND '2025-08-01' THEN 1 END) as target_week_schedules,
       MIN(date_key) as earliest_date,
       MAX(date_key) as latest_date
FROM psy_time_counselor_schedule 
WHERE del_flag = '0'
GROUP BY counselor_id
ORDER BY counselor_id;

-- 1.3 检查目标日期范围内的排班覆盖情况
SELECT date_key, COUNT(DISTINCT counselor_id) as counselor_count
FROM psy_time_counselor_schedule 
WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
GROUP BY date_key
ORDER BY date_key;

-- ==================== 2. 清理现有数据 ====================

-- 2.1 清理目标日期范围内的旧排班记录（如果需要重新生成）
-- DELETE FROM psy_time_counselor_schedule 
-- WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01';

-- 2.2 清理目标日期范围内的旧时间槽（软删除）
UPDATE psy_time_slot 
SET del_flag = '1', update_time = NOW()
WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0';

-- ==================== 3. 批量创建排班记录 ====================

-- 3.1 为所有活跃咨询师创建一周的标准排班
-- 获取所有活跃咨询师ID
SET @counselor_ids = (SELECT GROUP_CONCAT(id) FROM psy_consultants WHERE del_flag = '0' AND work_status = '0');

-- 为每个咨询师创建排班记录
INSERT INTO psy_time_counselor_schedule 
(counselor_id, date_key, day_of_week, is_working, start_time, end_time, del_flag, create_by, create_time)
SELECT 
    c.id as counselor_id,
    d.date_key,
    d.day_of_week,
    CASE 
        WHEN d.day_of_week IN (0, 6) THEN 0  -- 周日和周六休息
        ELSE 1  -- 周一到周五工作
    END as is_working,
    CASE 
        WHEN d.day_of_week IN (0, 6) THEN NULL
        ELSE '09:00:00'
    END as start_time,
    CASE 
        WHEN d.day_of_week IN (0, 6) THEN NULL
        ELSE '17:00:00'
    END as end_time,
    '0' as del_flag,
    'system' as create_by,
    NOW() as create_time
FROM 
    (SELECT id FROM psy_consultants WHERE del_flag = '0' AND work_status = '0') c
CROSS JOIN 
    (SELECT '2025-07-26' as date_key, 6 as day_of_week
     UNION ALL SELECT '2025-07-27', 0
     UNION ALL SELECT '2025-07-28', 1
     UNION ALL SELECT '2025-07-29', 2
     UNION ALL SELECT '2025-07-30', 3
     UNION ALL SELECT '2025-07-31', 4
     UNION ALL SELECT '2025-08-01', 5) d
ON DUPLICATE KEY UPDATE
    is_working = VALUES(is_working),
    start_time = VALUES(start_time),
    end_time = VALUES(end_time),
    update_time = NOW();

-- ==================== 4. 验证排班记录创建 ====================

-- 4.1 检查排班记录创建结果
SELECT counselor_id, date_key, day_of_week, is_working, start_time, end_time
FROM psy_time_counselor_schedule 
WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
ORDER BY counselor_id, date_key;

-- 4.2 统计每个咨询师的排班记录数
SELECT counselor_id, 
       COUNT(*) as schedule_count,
       SUM(is_working) as working_days,
       SUM(CASE WHEN is_working = 0 THEN 1 ELSE 0 END) as rest_days
FROM psy_time_counselor_schedule 
WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
GROUP BY counselor_id
ORDER BY counselor_id;

-- ==================== 5. 手动创建特定咨询师的排班（如果需要） ====================

-- 5.1 为咨询师4创建排班（如果上面的批量创建没有包含）
INSERT IGNORE INTO psy_time_counselor_schedule 
(counselor_id, date_key, day_of_week, is_working, start_time, end_time, del_flag, create_by, create_time)
VALUES 
(4, '2025-07-26', 6, 0, NULL, NULL, '0', 'system', NOW()),
(4, '2025-07-27', 0, 0, NULL, NULL, '0', 'system', NOW()),
(4, '2025-07-28', 1, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(4, '2025-07-29', 2, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(4, '2025-07-30', 3, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(4, '2025-07-31', 4, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(4, '2025-08-01', 5, 1, '09:00:00', '17:00:00', '0', 'system', NOW());

-- 5.2 为咨询师7创建排班
INSERT IGNORE INTO psy_time_counselor_schedule 
(counselor_id, date_key, day_of_week, is_working, start_time, end_time, del_flag, create_by, create_time)
VALUES 
(7, '2025-07-26', 6, 0, NULL, NULL, '0', 'system', NOW()),
(7, '2025-07-27', 0, 0, NULL, NULL, '0', 'system', NOW()),
(7, '2025-07-28', 1, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(7, '2025-07-29', 2, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(7, '2025-07-30', 3, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(7, '2025-07-31', 4, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(7, '2025-08-01', 5, 1, '09:00:00', '17:00:00', '0', 'system', NOW());

-- 5.3 为咨询师8创建排班
INSERT IGNORE INTO psy_time_counselor_schedule 
(counselor_id, date_key, day_of_week, is_working, start_time, end_time, del_flag, create_by, create_time)
VALUES 
(8, '2025-07-26', 6, 0, NULL, NULL, '0', 'system', NOW()),
(8, '2025-07-27', 0, 0, NULL, NULL, '0', 'system', NOW()),
(8, '2025-07-28', 1, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(8, '2025-07-29', 2, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(8, '2025-07-30', 3, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(8, '2025-07-31', 4, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
(8, '2025-08-01', 5, 1, '09:00:00', '17:00:00', '0', 'system', NOW());

-- ==================== 6. 最终验证 ====================

-- 6.1 验证所有咨询师都有完整的排班记录
SELECT 
    c.id as counselor_id,
    c.name as counselor_name,
    COUNT(s.id) as schedule_count,
    SUM(s.is_working) as working_days
FROM psy_consultants c
LEFT JOIN psy_time_counselor_schedule s ON c.id = s.counselor_id 
    AND s.date_key BETWEEN '2025-07-26' AND '2025-08-01'
    AND s.del_flag = '0'
WHERE c.del_flag = '0' AND c.work_status = '0'
GROUP BY c.id, c.name
ORDER BY c.id;

-- 6.2 检查是否有遗漏的日期
SELECT 
    date_key,
    COUNT(DISTINCT counselor_id) as counselor_count,
    SUM(is_working) as working_counselors
FROM psy_time_counselor_schedule 
WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
GROUP BY date_key
ORDER BY date_key;

-- ==================== 7. 提示信息 ====================
SELECT 
    '排班记录创建完成，现在可以重新运行时间槽生成任务' as message,
    COUNT(*) as total_schedules_created
FROM psy_time_counselor_schedule 
WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0';

-- ==================== 8. 检查时间槽生成前的准备工作 ====================

-- 8.1 确认没有活跃的时间槽（应该都被软删除了）
SELECT 
    counselor_id,
    date_key,
    COUNT(*) as active_slots
FROM psy_time_slot 
WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
GROUP BY counselor_id, date_key
ORDER BY counselor_id, date_key;

-- 8.2 如果上面的查询有结果，说明还有活跃的时间槽，需要清理
-- UPDATE psy_time_slot 
-- SET del_flag = '1', update_time = NOW()
-- WHERE date_key BETWEEN '2025-07-26' AND '2025-08-01'
--   AND del_flag = '0';

-- ==================== 执行完成提示 ====================
SELECT 
    '数据准备完成！' as status,
    '现在可以重新运行时间槽生成任务或手动调用生成方法' as next_step,
    '预期每个工作日每个咨询师生成约32-36个时间槽' as expected_result;
