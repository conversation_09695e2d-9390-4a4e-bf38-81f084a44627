# 心理测评系统分类关系修复方案

## 问题描述

系统出现以下错误：
```
Unknown column 's.category_id' in 'on clause'
```

**原因分析：**
- MyBatis 映射文件中的 SQL 查询尝试使用 `psy_t_scale.category_id` 字段
- 但实际数据库表中缺少该字段
- 存在两套不同的数据库设计方案

## 解决方案

### 方案一：添加 category_id 字段（推荐）

**优点：**
- 简单直接，适合大多数量表只有一个主分类的场景
- 与现有代码兼容性最好
- 查询性能更优（无需 JOIN）
- 实现成本最低

**实施步骤：**

1. **执行数据库修复脚本**
```bash
mysql -u your_username -p your_database < sql/fix_category_id_column.sql
```

2. **更新现有数据（如果有）**
```sql
-- 如果已有量表数据，可以根据业务需求设置默认分类
UPDATE psy_t_scale SET category_id = 100 WHERE category_id IS NULL;
```

3. **验证修复结果**
```sql
-- 检查字段是否添加成功
DESCRIBE psy_t_scale;

-- 测试查询是否正常
SELECT s.*, c.category_name 
FROM psy_t_scale s 
LEFT JOIN psy_category c ON s.category_id = c.category_id 
WHERE s.del_flag = 0 
LIMIT 5;
```

### 方案二：使用关联表（备选）

**优点：**
- 支持多分类关系
- 数据结构更灵活
- 符合规范化设计

**缺点：**
- 需要修改大量现有代码
- 查询复杂度增加
- 实施成本较高

**实施步骤：**

1. **修改 MyBatis 映射文件**
   - 移除所有 `s.category_id` 相关的 SQL
   - 使用关联表查询分类信息

2. **修改实体类**
   - 移除 `categoryId` 字段
   - 只保留 `categories` 列表

3. **修改业务逻辑**
   - 所有涉及分类的操作都通过关联表

## 推荐实施方案

**建议采用方案一**，理由如下：

1. **现有代码兼容性**：MyBatis 映射、实体类、初始化数据都是基于 `category_id` 字段设计的
2. **业务场景适配**：大多数心理测评量表都有一个主要分类，少数需要多分类的可以通过关联表补充
3. **实施成本**：只需要添加一个字段，无需修改代码
4. **性能考虑**：直接字段查询比 JOIN 查询更快

## 数据库设计说明

### 最终表结构

```sql
CREATE TABLE psy_t_scale (
  id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '量表ID',
  scale_name varchar(100) NOT NULL COMMENT '量表名称',
  scale_code varchar(50) NOT NULL COMMENT '量表编码',
  description text COMMENT '量表描述',
  instruction text COMMENT '测评说明',
  category_id bigint(20) DEFAULT NULL COMMENT '分类ID',  -- 主分类
  -- ... 其他字段
  PRIMARY KEY (id),
  KEY idx_category_id (category_id)
);

-- 可选：多分类关系表（用于需要多分类的特殊情况）
CREATE TABLE psy_t_scale_category_rel (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  scale_id bigint(20) NOT NULL,
  category_id bigint(20) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY uk_scale_category (scale_id, category_id)
);
```

### 使用建议

1. **单分类场景**：直接使用 `psy_t_scale.category_id` 字段
2. **多分类场景**：使用 `psy_t_scale_category_rel` 关联表
3. **查询优化**：优先使用主分类字段，必要时再关联多分类表

## 后续维护

1. **新增量表时**：设置合适的 `category_id` 值
2. **分类变更时**：同时更新主分类字段和关联表
3. **数据一致性**：定期检查主分类与关联表的数据一致性

## 测试验证

修复完成后，请测试以下功能：
- [ ] 量表列表查询
- [ ] 按分类筛选量表
- [ ] 量表详情查询
- [ ] 新增量表
- [ ] 编辑量表分类
