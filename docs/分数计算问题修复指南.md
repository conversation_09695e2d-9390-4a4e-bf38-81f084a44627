# 分数计算问题修复指南

## 🚨 问题现象

从数据库查询结果可以看到：
- 所有答题记录的 `answer_score` 都是 0.00
- 用户选择了正确的选项（"非常同意"、"同意"等）
- 但分数没有正确计算和保存

## 🔍 问题诊断

### 1. 立即诊断SQL

```sql
-- 检查选项分数配置
SELECT 
    o.id, o.content, o.option_value, o.sort,
    q.content as question_content
FROM psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
WHERE q.scale_id = 8 AND o.del_flag = 0
ORDER BY q.sort, o.sort
LIMIT 10;
```

### 2. 检查答题记录
```sql
-- 检查最近的答题记录
SELECT 
    a.id, a.option_id, a.answer_content, a.answer_score,
    o.content as option_content, o.option_value
FROM psy_t_answer_record a
LEFT JOIN psy_t_question_option o ON a.option_id = o.id
INNER JOIN psy_t_assessment_record r ON a.record_id = r.id
WHERE r.scale_id = 8 AND a.del_flag = 0
ORDER BY a.create_time DESC
LIMIT 10;
```

## 🔧 修复方案

### 方案1：代码修复（已完成）

#### 1.1 添加调试日志
```java
// 在 PsyTScoringRuleServiceImpl.calculateQuestionScore 方法中添加了详细日志
logger.info("计算分数 - questionId: {}, optionId: {}, answerContent: {}", questionId, optionId, answerContent);
logger.info("选项信息 - option: {}, optionValue: {}", 
    option != null ? option.getContent() : "null", 
    option != null ? option.getOptionValue() : "null");
```

#### 1.2 智能分数推断
```java
// 如果选项值为空，根据选项内容推断分数
private BigDecimal inferScoreFromContent(String content) {
    if (content.contains("非常同意")) return new BigDecimal("5");
    if (content.contains("同意")) return new BigDecimal("4");
    if (content.contains("中立")) return new BigDecimal("3");
    if (content.contains("不同意")) return new BigDecimal("2");
    if (content.contains("非常不同意")) return new BigDecimal("1");
    return BigDecimal.ZERO;
}
```

### 方案2：数据修复

#### 2.1 修复选项分数配置
```sql
-- 执行 docs/修复选项分数配置.sql 中的脚本
-- 注意：执行前请先备份数据！

-- 示例：修复"非常同意"选项
UPDATE psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
SET o.option_value = '5'
WHERE q.scale_id = 8 
  AND o.del_flag = 0 
  AND (o.option_value IS NULL OR o.option_value = '' OR o.option_value = '0')
  AND o.content LIKE '%非常同意%';
```

#### 2.2 重新计算现有答题分数
```sql
-- 更新答题记录分数
UPDATE psy_t_answer_record a
INNER JOIN psy_t_question_option o ON a.option_id = o.id
SET a.answer_score = CASE 
    WHEN o.option_value IS NOT NULL AND o.option_value != '' 
    THEN CAST(o.option_value AS DECIMAL(8,2))
    ELSE 0.00
END
WHERE a.del_flag = 0;
```

### 方案3：接口修复

#### 3.1 重新计算分数接口
```javascript
// 调用重新计算分数接口
fetch('/miniapp/user/assessment/recalculate-scores/24', {
    method: 'POST'
})
.then(response => response.json())
.then(data => {
    console.log('重新计算结果:', data);
});
```

## 🚀 立即修复步骤

### 步骤1：检查选项配置
```sql
-- 执行这个查询，查看选项值是否为空
SELECT COUNT(*) as total_options,
       COUNT(CASE WHEN option_value IS NULL OR option_value = '' THEN 1 END) as empty_values,
       COUNT(CASE WHEN option_value IS NOT NULL AND option_value != '' THEN 1 END) as valid_values
FROM psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
WHERE q.scale_id = 8 AND o.del_flag = 0;
```

### 步骤2：修复选项分数（如果需要）
```sql
-- 如果选项值为空，执行修复脚本
-- 复制并执行 docs/修复选项分数配置.sql 中的UPDATE语句
```

### 步骤3：重新计算答题分数
```bash
# 方法1：使用接口
curl -X POST http://localhost:8080/miniapp/user/assessment/recalculate-scores/24

# 方法2：直接SQL更新（如果接口不可用）
# 执行 docs/修复选项分数配置.sql 中的重新计算部分
```

### 步骤4：验证修复结果
```sql
-- 检查修复后的分数
SELECT 
    a.record_id,
    COUNT(*) as answer_count,
    SUM(a.answer_score) as total_score,
    AVG(a.answer_score) as avg_score,
    MIN(a.answer_score) as min_score,
    MAX(a.answer_score) as max_score
FROM psy_t_answer_record a
INNER JOIN psy_t_assessment_record r ON a.record_id = r.id
WHERE r.scale_id = 8 AND a.del_flag = 0
GROUP BY a.record_id
ORDER BY a.record_id;
```

## 📊 预期结果

修复后应该看到：
- 选项分数：1-5分（对应"非常不同意"到"非常同意"）
- 答题分数：每题1-5分
- 总分：24题 × 1-5分 = 24-120分

## ⚠️ 注意事项

### 1. 数据备份
```sql
-- 修复前备份相关表
CREATE TABLE psy_t_question_option_backup AS SELECT * FROM psy_t_question_option;
CREATE TABLE psy_t_answer_record_backup AS SELECT * FROM psy_t_answer_record;
```

### 2. 分步执行
- 先修复选项配置
- 再重新计算分数
- 最后验证结果

### 3. 日志监控
```bash
# 查看应用日志，确认分数计算过程
tail -f logs/application.log | grep "计算分数"
```

## 🧪 测试用例

### 1. 单个答题测试
```javascript
// 提交一个答案，查看分数计算
const answerData = {
    recordId: 24,
    questionId: 277,
    optionId: 760,  // "非常同意"选项
    responseTime: 30
};

fetch('/miniapp/user/assessment/answer', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(answerData)
});
```

### 2. 完整测评测试
```javascript
// 完成测评，查看总分计算
fetch('/miniapp/user/assessment/complete/24', {
    method: 'POST'
})
.then(response => response.json())
.then(data => {
    console.log('总分:', data.data.totalScore);
    console.log('维度分数:', data.data.dimensionScores);
});
```

## 📈 监控指标

### 1. 分数分布
```sql
-- 监控分数分布是否合理
SELECT 
    answer_score,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM psy_t_answer_record WHERE del_flag = 0), 2) as percentage
FROM psy_t_answer_record 
WHERE del_flag = 0 AND answer_score > 0
GROUP BY answer_score
ORDER BY answer_score;
```

### 2. 总分统计
```sql
-- 监控总分分布
SELECT 
    FLOOR(total_score/10)*10 as score_range,
    COUNT(*) as count
FROM psy_t_assessment_record 
WHERE scale_id = 8 AND del_flag = 0 AND total_score > 0
GROUP BY FLOOR(total_score/10)*10
ORDER BY score_range;
```

现在按照这个指南执行修复，分数计算问题应该能够解决！🎯
