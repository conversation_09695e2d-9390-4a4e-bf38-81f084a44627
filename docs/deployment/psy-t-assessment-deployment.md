# 心理测评系统部署指南

## 概述

本文档描述了新心理测评系统的部署流程，包括环境准备、数据库初始化、应用部署和配置说明。

## 环境要求

### 1. 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 100GB以上可用空间
- **网络**: 稳定的网络连接

### 2. 软件要求
- **操作系统**: Linux (CentOS 7+/Ubuntu 18+) 或 Windows Server 2016+
- **Java**: JDK 1.8 或 OpenJDK 8+
- **数据库**: MySQL 5.7+ 或 MySQL 8.0+
- **Redis**: 5.0+
- **Nginx**: 1.16+ (可选，用于反向代理)

## 部署步骤

### 1. 环境准备

#### 1.1 安装Java环境
```bash
# CentOS/RHEL
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# Ubuntu/Debian
apt-get update
apt-get install -y openjdk-8-jdk

# 验证安装
java -version
javac -version
```

#### 1.2 安装MySQL
```bash
# CentOS/RHEL 7
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
rpm -ivh mysql80-community-release-el7-3.noarch.rpm
yum install -y mysql-server

# Ubuntu/Debian
apt-get update
apt-get install -y mysql-server

# 启动MySQL服务
systemctl start mysqld
systemctl enable mysqld

# 获取临时密码（MySQL 8.0）
grep 'temporary password' /var/log/mysqld.log

# 安全配置
mysql_secure_installation
```

#### 1.3 安装Redis
```bash
# CentOS/RHEL
yum install -y epel-release
yum install -y redis

# Ubuntu/Debian
apt-get update
apt-get install -y redis-server

# 启动Redis服务
systemctl start redis
systemctl enable redis
```

### 2. 数据库初始化

#### 2.1 创建数据库
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE xihuan_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'xihuan'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON xihuan_db.* TO 'xihuan'@'%';
FLUSH PRIVILEGES;
```

#### 2.2 执行SQL脚本
```bash
# 执行数据库初始化脚本
mysql -u xihuan -p xihuan_db < sql/psy_t_assessment_schema.sql

# 执行基础数据脚本
mysql -u xihuan -p xihuan_db < sql/psy_t_assessment_data.sql
```

#### 2.3 验证数据库
```sql
-- 检查表是否创建成功
USE xihuan_db;
SHOW TABLES LIKE 'psy_t_%';

-- 检查基础数据
SELECT COUNT(*) FROM psy_t_scale;
```

### 3. 应用配置

#### 3.1 修改配置文件
编辑 `application-prod.yml`:

```yaml
# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      url: **************************************************************************************************************************************************
      username: xihuan
      password: your_password
      initial-size: 5
      min-idle: 5
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.xihuan: info
    root: info
  config: classpath:logback-spring.xml
```

#### 3.2 配置文件上传路径
```yaml
# 文件上传配置
xihuan:
  profile: /opt/xihuan/uploadPath
  addressEnabled: false
  captchaEnabled: true
  captchaType: math
```

### 4. 应用部署

#### 4.1 构建应用
```bash
# 进入项目根目录
cd /path/to/xihuan-project

# 使用Maven构建
mvn clean package -Dmaven.test.skip=true

# 或使用Gradle构建
./gradlew build -x test
```

#### 4.2 部署应用
```bash
# 创建部署目录
mkdir -p /opt/xihuan
cd /opt/xihuan

# 复制JAR文件
cp /path/to/xihuan-admin/target/xihuan-admin.jar ./

# 创建启动脚本
cat > start.sh << 'EOF'
#!/bin/bash
JAVA_OPTS="-Xms512m -Xmx2048m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
JAVA_OPTS="$JAVA_OPTS -Dspring.profiles.active=prod"
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Duser.timezone=Asia/Shanghai"

nohup java $JAVA_OPTS -jar xihuan-admin.jar > logs/application.log 2>&1 &
echo $! > application.pid
EOF

# 创建停止脚本
cat > stop.sh << 'EOF'
#!/bin/bash
if [ -f application.pid ]; then
    PID=$(cat application.pid)
    kill -9 $PID
    rm -f application.pid
    echo "Application stopped"
else
    echo "Application is not running"
fi
EOF

# 设置执行权限
chmod +x start.sh stop.sh

# 创建日志目录
mkdir -p logs

# 启动应用
./start.sh
```

#### 4.3 配置系统服务
```bash
# 创建systemd服务文件
cat > /etc/systemd/system/xihuan.service << 'EOF'
[Unit]
Description=Xihuan Assessment System
After=network.target

[Service]
Type=forking
User=root
WorkingDirectory=/opt/xihuan
ExecStart=/opt/xihuan/start.sh
ExecStop=/opt/xihuan/stop.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重载systemd配置
systemctl daemon-reload

# 启用服务
systemctl enable xihuan

# 启动服务
systemctl start xihuan

# 检查状态
systemctl status xihuan
```

### 5. Nginx配置（可选）

#### 5.1 安装Nginx
```bash
# CentOS/RHEL
yum install -y nginx

# Ubuntu/Debian
apt-get install -y nginx
```

#### 5.2 配置反向代理
```nginx
# /etc/nginx/conf.d/xihuan.conf
server {
    listen 80;
    server_name your-domain.com;

    # 静态资源
    location /static/ {
        alias /opt/xihuan/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # 文件上传大小限制
    client_max_body_size 100M;

    # 日志配置
    access_log /var/log/nginx/xihuan_access.log;
    error_log /var/log/nginx/xihuan_error.log;
}
```

#### 5.3 启动Nginx
```bash
# 测试配置
nginx -t

# 启动Nginx
systemctl start nginx
systemctl enable nginx
```

### 6. SSL配置（推荐）

#### 6.1 获取SSL证书
```bash
# 使用Let's Encrypt
yum install -y certbot python3-certbot-nginx
certbot --nginx -d your-domain.com
```

#### 6.2 配置HTTPS
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 其他配置同上...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 监控和维护

### 1. 日志监控
```bash
# 查看应用日志
tail -f /opt/xihuan/logs/application.log

# 查看错误日志
grep ERROR /opt/xihuan/logs/application.log

# 查看访问日志
tail -f /var/log/nginx/xihuan_access.log
```

### 2. 性能监控
```bash
# 检查Java进程
jps -l

# 查看内存使用
jstat -gc [pid]

# 查看线程状态
jstack [pid]

# 系统资源监控
top
htop
iostat
```

### 3. 数据库维护
```sql
-- 检查数据库大小
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'xihuan_db'
GROUP BY table_schema;

-- 优化表
OPTIMIZE TABLE psy_t_assessment_record;
OPTIMIZE TABLE psy_t_answer_record;

-- 清理过期数据
DELETE FROM psy_t_assessment_record 
WHERE status = 3 AND create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 4. 备份策略
```bash
# 数据库备份脚本
cat > /opt/xihuan/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/xihuan/backup"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="xihuan_db"
DB_USER="xihuan"
DB_PASS="your_password"

mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_DIR/db_backup_$DATE.sql.gz"
EOF

chmod +x /opt/xihuan/backup.sh

# 添加定时任务
echo "0 2 * * * /opt/xihuan/backup.sh" | crontab -
```

## 故障排除

### 1. 常见问题
- **应用启动失败**: 检查Java版本、配置文件、端口占用
- **数据库连接失败**: 检查数据库服务、用户权限、网络连接
- **Redis连接失败**: 检查Redis服务、配置参数
- **文件上传失败**: 检查目录权限、磁盘空间

### 2. 性能优化
- **JVM调优**: 根据服务器配置调整堆内存大小
- **数据库优化**: 添加索引、优化查询、配置连接池
- **缓存优化**: 合理使用Redis缓存热点数据
- **静态资源**: 使用CDN加速静态资源访问

### 3. 安全加固
- **防火墙配置**: 只开放必要端口
- **数据库安全**: 限制数据库用户权限
- **应用安全**: 定期更新依赖包、配置安全头
- **访问控制**: 配置IP白名单、限流策略
