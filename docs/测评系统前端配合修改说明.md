# 测评系统前端配合修改说明

## 概述

基于最新的测评系统后端接口，前端需要进行相应的调整以配合新的API结构和数据模型。

## 主要变更点

### 1. 接口路径变更

#### 原接口路径
```
/miniapp/user/assessment/scale/list
/miniapp/user/assessment/test/start
/miniapp/user/assessment/test/submit
```

#### 新接口路径
```
/miniapp/user/assessment/scales
/miniapp/user/assessment/start
/miniapp/user/assessment/answer
```

### 2. 数据结构变更

#### 量表数据结构
**原结构：**
```javascript
{
  scaleName: "焦虑自评量表",
  scaleCode: "SAS",
  isFree: 1,
  price: 0.00
}
```

**新结构：**
```javascript
{
  name: "焦虑自评量表",
  code: "SAS", 
  alias: "焦虑量表",
  payMode: 0,
  originalPrice: 0.00,
  currentPrice: 0.00,
  coverImage: "https://example.com/cover.jpg",
  tags: "焦虑,情绪,心理健康"
}
```

#### 测评记录数据结构
**原结构：**
```javascript
{
  sessionId: "session_001",
  scaleId: 1,
  status: 0
}
```

**新结构：**
```javascript
{
  id: 123,
  scaleId: 1,
  userId: 1001,
  sessionId: "uuid-session-id",
  status: 0,
  progress: 25.0,
  currentQuestionNo: 5,
  totalQuestions: 20
}
```

### 3. 测评流程调整

#### 原流程
1. 开始测评 → 返回sessionId
2. 获取题目 → 使用sessionId
3. 提交答案 → 使用sessionId
4. 完成测评 → 使用sessionId

#### 新流程
1. 检查是否可以测评 → 验证用户权限
2. 开始测评 → 返回recordId
3. 获取题目 → 使用recordId
4. 提交答案 → 使用recordId
5. 完成测评 → 使用recordId

## 前端修改建议

### 1. API调用层修改

#### 量表列表接口
```javascript
// 原代码
const getScaleList = async (params) => {
  return await request.get('/miniapp/user/assessment/scale/list', { params });
}

// 新代码
const getScaleList = async (params) => {
  return await request.get('/miniapp/user/assessment/scales', { params });
}
```

#### 开始测评接口
```javascript
// 原代码
const startAssessment = async (data) => {
  return await request.post('/miniapp/user/assessment/test/start', data);
}

// 新代码
const startAssessment = async (userId, scaleId) => {
  return await request.post('/miniapp/user/assessment/start', null, {
    params: { userId, scaleId }
  });
}
```

#### 提交答案接口
```javascript
// 原代码
const submitAnswer = async (data) => {
  return await request.post('/miniapp/user/assessment/test/submit', data);
}

// 新代码
const submitAnswer = async (recordId, questionId, optionId, answerContent, responseTime) => {
  return await request.post('/miniapp/user/assessment/answer', null, {
    params: { recordId, questionId, optionId, answerContent, responseTime }
  });
}
```

### 2. 数据处理层修改

#### 量表数据适配
```javascript
// 数据适配函数
const adaptScaleData = (scaleData) => {
  return {
    id: scaleData.id,
    name: scaleData.name,
    code: scaleData.code,
    description: scaleData.description,
    coverImage: scaleData.coverImage || '/static/default-cover.jpg',
    isFree: scaleData.payMode === 0,
    price: scaleData.currentPrice || 0,
    questionCount: scaleData.questionCount,
    timeLimit: scaleData.timeLimit,
    rating: scaleData.ratingAvg,
    testCount: scaleData.testCount,
    tags: scaleData.tags ? scaleData.tags.split(',') : []
  };
}
```

#### 测评记录数据适配
```javascript
// 测评记录适配函数
const adaptRecordData = (recordData) => {
  return {
    recordId: recordData.id,
    scaleId: recordData.scaleId,
    scaleName: recordData.scaleName,
    progress: recordData.progress || 0,
    currentQuestion: recordData.currentQuestionNo || 1,
    totalQuestions: recordData.totalQuestions || 0,
    status: recordData.status,
    startTime: recordData.startTime,
    endTime: recordData.endTime,
    totalScore: recordData.totalScore,
    resultLevel: recordData.resultLevel
  };
}
```

### 3. 页面组件修改

#### 量表列表页面
```vue
<template>
  <view class="scale-list">
    <view class="scale-item" v-for="scale in scaleList" :key="scale.id">
      <image :src="scale.coverImage" class="scale-cover"></image>
      <view class="scale-info">
        <text class="scale-name">{{ scale.name }}</text>
        <text class="scale-alias">{{ scale.alias }}</text>
        <view class="scale-tags">
          <text class="tag" v-for="tag in scale.tags" :key="tag">{{ tag }}</text>
        </view>
        <view class="scale-meta">
          <text class="question-count">{{ scale.questionCount }}题</text>
          <text class="time-limit">{{ Math.ceil(scale.timeLimit / 60) }}分钟</text>
          <text class="price" v-if="scale.payMode > 0">¥{{ scale.currentPrice }}</text>
          <text class="free" v-else>免费</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      scaleList: []
    }
  },
  async onLoad() {
    await this.loadScaleList();
  },
  methods: {
    async loadScaleList() {
      try {
        const response = await this.$api.getScaleList();
        this.scaleList = response.data.map(this.adaptScaleData);
      } catch (error) {
        console.error('加载量表列表失败:', error);
      }
    },
    adaptScaleData(scale) {
      return {
        ...scale,
        tags: scale.tags ? scale.tags.split(',') : [],
        coverImage: scale.coverImage || '/static/default-cover.jpg'
      };
    }
  }
}
</script>
```

#### 测评页面
```vue
<template>
  <view class="assessment-page">
    <view class="progress-bar">
      <view class="progress" :style="{ width: progressPercent + '%' }"></view>
    </view>
    <view class="question-info">
      <text>{{ currentQuestion }}/{{ totalQuestions }}</text>
    </view>
    <view class="question-content">
      <text class="question-text">{{ question.questionText }}</text>
      <view class="options">
        <view 
          class="option" 
          v-for="option in question.optionList" 
          :key="option.id"
          :class="{ active: selectedOptionId === option.id }"
          @click="selectOption(option.id)"
        >
          <text>{{ option.optionText }}</text>
        </view>
      </view>
    </view>
    <view class="actions">
      <button @click="previousQuestion" :disabled="!hasPrevious">上一题</button>
      <button @click="nextQuestion" :disabled="!selectedOptionId">下一题</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      recordId: null,
      question: {},
      selectedOptionId: null,
      currentQuestion: 1,
      totalQuestions: 20,
      startTime: null
    }
  },
  computed: {
    progressPercent() {
      return (this.currentQuestion / this.totalQuestions) * 100;
    },
    hasPrevious() {
      return this.currentQuestion > 1;
    }
  },
  async onLoad(options) {
    this.recordId = options.recordId;
    await this.loadQuestions();
  },
  methods: {
    async loadQuestions() {
      try {
        const response = await this.$api.getAssessmentQuestions(this.recordId);
        this.questions = response.data;
        this.totalQuestions = this.questions.length;
        this.loadCurrentQuestion();
      } catch (error) {
        console.error('加载题目失败:', error);
      }
    },
    loadCurrentQuestion() {
      this.question = this.questions[this.currentQuestion - 1] || {};
      this.selectedOptionId = null;
      this.startTime = Date.now();
    },
    selectOption(optionId) {
      this.selectedOptionId = optionId;
    },
    async nextQuestion() {
      if (!this.selectedOptionId) return;
      
      await this.submitAnswer();
      
      if (this.currentQuestion < this.totalQuestions) {
        this.currentQuestion++;
        this.loadCurrentQuestion();
      } else {
        await this.completeAssessment();
      }
    },
    async previousQuestion() {
      if (this.currentQuestion > 1) {
        this.currentQuestion--;
        this.loadCurrentQuestion();
      }
    },
    async submitAnswer() {
      const responseTime = Math.floor((Date.now() - this.startTime) / 1000);
      try {
        await this.$api.submitAnswer(
          this.recordId,
          this.question.id,
          this.selectedOptionId,
          null,
          responseTime
        );
      } catch (error) {
        console.error('提交答案失败:', error);
      }
    },
    async completeAssessment() {
      try {
        await this.$api.completeAssessment(this.recordId);
        uni.navigateTo({
          url: `/pages/assessment/result?recordId=${this.recordId}`
        });
      } catch (error) {
        console.error('完成测评失败:', error);
      }
    }
  }
}
</script>
```

## 注意事项

1. **接口兼容性**：建议在修改前端代码时保持向后兼容，可以通过配置开关来控制使用新旧接口
2. **错误处理**：新接口的错误响应格式可能有变化，需要更新错误处理逻辑
3. **数据缓存**：由于数据结构变化，需要清理旧的缓存数据
4. **测试验证**：修改完成后需要进行完整的功能测试，确保所有流程正常工作
5. **渐进式迁移**：建议分模块逐步迁移，降低风险

### 4. 订单相关页面

#### 订单列表页面
```vue
<template>
  <view class="order-list">
    <view class="order-item" v-for="order in orderList" :key="order.id">
      <view class="order-header">
        <text class="order-no">订单号：{{ order.orderNo }}</text>
        <text class="order-status" :class="getStatusClass(order.orderStatus)">
          {{ getStatusText(order.orderStatus) }}
        </text>
      </view>
      <view class="order-content">
        <text class="scale-name">{{ order.scaleName }}</text>
        <view class="price-info">
          <text class="original-price" v-if="order.discountAmount > 0">
            ¥{{ order.originalPrice }}
          </text>
          <text class="actual-price">¥{{ order.actualPrice }}</text>
        </view>
      </view>
      <view class="order-actions">
        <button v-if="order.orderStatus === 0" @click="payOrder(order)" class="pay-btn">
          立即支付
        </button>
        <button v-if="order.orderStatus === 0" @click="cancelOrder(order)" class="cancel-btn">
          取消订单
        </button>
        <button v-if="order.orderStatus === 1" @click="startAssessment(order)" class="start-btn">
          开始测评
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderList: []
    }
  },
  async onLoad() {
    await this.loadOrderList();
  },
  methods: {
    async loadOrderList() {
      try {
        const response = await this.$api.getOrderList();
        this.orderList = response.data;
      } catch (error) {
        console.error('加载订单列表失败:', error);
      }
    },
    getStatusText(status) {
      const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '已完成',
        3: '已取消',
        4: '已退款'
      };
      return statusMap[status] || '未知';
    },
    getStatusClass(status) {
      const classMap = {
        0: 'pending',
        1: 'paid',
        2: 'completed',
        3: 'cancelled',
        4: 'refunded'
      };
      return classMap[status] || '';
    },
    async payOrder(order) {
      try {
        const response = await this.$api.payOrder({
          orderNo: order.orderNo,
          paymentMethod: 'WECHAT'
        });
        uni.showToast({ title: '支付成功' });
        await this.loadOrderList();
      } catch (error) {
        uni.showToast({ title: '支付失败', icon: 'error' });
      }
    },
    async cancelOrder(order) {
      try {
        await this.$api.cancelOrder({
          orderNo: order.orderNo,
          cancelReason: '不想购买了'
        });
        uni.showToast({ title: '订单已取消' });
        await this.loadOrderList();
      } catch (error) {
        uni.showToast({ title: '取消失败', icon: 'error' });
      }
    }
  }
}
</script>
```

### 5. 评价相关页面

#### 评价列表页面
```vue
<template>
  <view class="review-list">
    <view class="review-item" v-for="review in reviewList" :key="review.id">
      <view class="review-header">
        <view class="user-info">
          <image :src="review.avatar" class="avatar"></image>
          <text class="nickname">{{ review.displayName }}</text>
        </view>
        <view class="rating">
          <text class="stars">{{ getStars(review.rating) }}</text>
          <text class="rating-text">{{ review.rating }}分</text>
        </view>
      </view>
      <view class="review-content">
        <text>{{ review.content }}</text>
      </view>
      <view class="review-footer">
        <text class="time">{{ formatTime(review.createTime) }}</text>
        <view class="actions">
          <button @click="likeReview(review)" :class="{ liked: review.liked }">
            👍 {{ review.likeCount }}
          </button>
          <button @click="viewReplies(review)">
            💬 {{ review.replyCount }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      reviewList: [],
      scaleId: null
    }
  },
  async onLoad(options) {
    this.scaleId = options.scaleId;
    await this.loadReviewList();
  },
  methods: {
    async loadReviewList() {
      try {
        const response = await this.$api.getScaleReviews(this.scaleId);
        this.reviewList = response.data;
      } catch (error) {
        console.error('加载评价列表失败:', error);
      }
    },
    getStars(rating) {
      return '★'.repeat(rating) + '☆'.repeat(5 - rating);
    },
    formatTime(time) {
      // 格式化时间显示
      return new Date(time).toLocaleDateString();
    },
    async likeReview(review) {
      try {
        if (review.liked) {
          await this.$api.unlikeReview(review.id);
          review.liked = false;
          review.likeCount--;
        } else {
          await this.$api.likeReview(review.id);
          review.liked = true;
          review.likeCount++;
        }
      } catch (error) {
        uni.showToast({ title: error.message, icon: 'error' });
      }
    }
  }
}
</script>
```

## 迁移检查清单

- [ ] 更新API接口路径
- [ ] 修改数据结构适配
- [ ] 更新页面组件
- [ ] 测试量表列表功能
- [ ] 测试测评流程
- [ ] 测试结果展示
- [ ] 测试订单功能
- [ ] 测试评价功能
- [ ] 验证错误处理
- [ ] 清理旧代码和缓存
