# 答题接口参数问题诊断和解决方案

## 🚨 问题描述

调用 `/miniapp/user/assessment/answer` 接口时出现错误：
```
Required request parameter 'recordId' for method parameter type Long is not present
```

## 📊 提供的参数

```javascript
{
    answerContent: "非常同意",
    optionId: 760,
    questionId: 277,
    recordId: "23",
    responseTime: 30
}
```

## 🔍 问题分析

### 可能的原因

1. **请求方式问题**：使用了错误的HTTP方法
2. **Content-Type问题**：参数传递格式不正确
3. **参数序列化问题**：前端参数序列化方式与后端期望不匹配
4. **字符串转换问题**：`recordId: "23"` 是字符串，需要转换为Long

## 🔧 解决方案

### 方案1：使用原始接口（推荐）

#### ✅ 正确的请求格式

**请求方式**：`POST`
**Content-Type**：`application/x-www-form-urlencoded` 或 `multipart/form-data`
**URL**：`http://localhost:8080/miniapp/user/assessment/answer`

**参数格式**：
```javascript
// 使用 FormData
const formData = new FormData();
formData.append('recordId', '23');
formData.append('questionId', '277');
formData.append('optionId', '760');
formData.append('answerContent', '非常同意');
formData.append('responseTime', '30');

fetch('/miniapp/user/assessment/answer', {
    method: 'POST',
    body: formData
});
```

**或者使用 URLSearchParams**：
```javascript
const params = new URLSearchParams();
params.append('recordId', '23');
params.append('questionId', '277');
params.append('optionId', '760');
params.append('answerContent', '非常同意');
params.append('responseTime', '30');

fetch('/miniapp/user/assessment/answer', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params
});
```

### 方案2：使用新增的JSON接口

#### ✅ JSON请求格式

**请求方式**：`POST`
**Content-Type**：`application/json`
**URL**：`http://localhost:8080/miniapp/user/assessment/answer/json`

**参数格式**：
```javascript
fetch('/miniapp/user/assessment/answer/json', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        recordId: 23,        // 注意：这里是数字，不是字符串
        questionId: 277,
        optionId: 760,
        answerContent: "非常同意",
        responseTime: 30
    })
});
```

### 方案3：使用调试接口诊断问题

#### 🔍 调试步骤

**URL**：`http://localhost:8080/miniapp/user/assessment/answer/debug`
**请求方式**：`POST`

```javascript
// 使用相同的参数调用调试接口
fetch('/miniapp/user/assessment/answer/debug', {
    method: 'POST',
    // 使用与原始请求相同的参数格式
    body: formData  // 或者你当前使用的参数格式
});
```

**调试接口返回信息**：
```json
{
    "code": 200,
    "data": {
        "requestParameters": {
            "recordId": ["23"],
            "questionId": ["277"],
            "optionId": ["760"],
            "answerContent": ["非常同意"],
            "responseTime": ["30"]
        },
        "contentType": "application/x-www-form-urlencoded",
        "method": "POST",
        "hasRecordId": true,
        "hasQuestionId": true
    }
}
```

## 🛠️ 前端代码示例

### jQuery 示例

```javascript
// 方案1：使用表单参数
$.ajax({
    url: '/miniapp/user/assessment/answer',
    type: 'POST',
    data: {
        recordId: 23,
        questionId: 277,
        optionId: 760,
        answerContent: '非常同意',
        responseTime: 30
    },
    success: function(response) {
        console.log('答案保存成功', response);
    },
    error: function(xhr, status, error) {
        console.error('答案保存失败', error);
    }
});

// 方案2：使用JSON格式
$.ajax({
    url: '/miniapp/user/assessment/answer/json',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({
        recordId: 23,
        questionId: 277,
        optionId: 760,
        answerContent: '非常同意',
        responseTime: 30
    }),
    success: function(response) {
        console.log('答案保存成功', response);
    },
    error: function(xhr, status, error) {
        console.error('答案保存失败', error);
    }
});
```

### Axios 示例

```javascript
// 方案1：使用表单参数
const formData = new FormData();
formData.append('recordId', '23');
formData.append('questionId', '277');
formData.append('optionId', '760');
formData.append('answerContent', '非常同意');
formData.append('responseTime', '30');

axios.post('/miniapp/user/assessment/answer', formData)
    .then(response => {
        console.log('答案保存成功', response.data);
    })
    .catch(error => {
        console.error('答案保存失败', error);
    });

// 方案2：使用JSON格式
axios.post('/miniapp/user/assessment/answer/json', {
    recordId: 23,
    questionId: 277,
    optionId: 760,
    answerContent: '非常同意',
    responseTime: 30
})
.then(response => {
    console.log('答案保存成功', response.data);
})
.catch(error => {
    console.error('答案保存失败', error);
});
```

### 原生 JavaScript 示例

```javascript
// 方案1：使用表单参数
const formData = new FormData();
formData.append('recordId', '23');
formData.append('questionId', '277');
formData.append('optionId', '760');
formData.append('answerContent', '非常同意');
formData.append('responseTime', '30');

fetch('/miniapp/user/assessment/answer', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('答案保存成功', data);
})
.catch(error => {
    console.error('答案保存失败', error);
});

// 方案2：使用JSON格式
fetch('/miniapp/user/assessment/answer/json', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        recordId: 23,
        questionId: 277,
        optionId: 760,
        answerContent: '非常同意',
        responseTime: 30
    })
})
.then(response => response.json())
.then(data => {
    console.log('答案保存成功', data);
})
.catch(error => {
    console.error('答案保存失败', error);
});
```

## 🔍 诊断步骤

### 1. 检查请求格式
使用浏览器开发者工具的Network面板检查：
- 请求方法是否为 `POST`
- Content-Type 是否正确
- 参数是否正确传递

### 2. 使用调试接口
```bash
curl -X POST http://localhost:8080/miniapp/user/assessment/answer/debug \
  -d "recordId=23&questionId=277&optionId=760&answerContent=非常同意&responseTime=30"
```

### 3. 检查后端日志
查看Spring Boot应用的日志，确认参数是否正确接收。

## ⚠️ 常见问题

### 1. Content-Type 不匹配
**问题**：使用 `application/json` 但参数按表单格式传递
**解决**：确保Content-Type与参数格式匹配

### 2. 参数名称不匹配
**问题**：前端参数名与后端 `@RequestParam` 不一致
**解决**：确保参数名完全匹配（区分大小写）

### 3. 数据类型转换问题
**问题**：字符串无法转换为Long类型
**解决**：确保数字参数传递为有效的数字格式

### 4. 请求方法错误
**问题**：使用GET请求调用POST接口
**解决**：确保使用正确的HTTP方法

## 📝 测试建议

1. **先使用调试接口**确认参数传递正确
2. **使用Postman或curl**测试接口
3. **检查浏览器Network面板**确认请求格式
4. **查看后端日志**确认参数接收情况

## 🚀 推荐方案

建议使用 **方案1（表单参数）**，因为：
- 兼容性更好
- 实现简单
- 符合RESTful API规范
- 与现有代码风格一致

如果前端已经使用JSON格式，可以使用 **方案2（JSON接口）**。
