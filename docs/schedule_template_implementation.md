# 排班模板系统实现文档

## 概述

排班模板系统允许咨询师创建和管理排班模板，支持按周设置工作时间，并可以基于模板生成实际排班。

## 已实现的功能

### 1. 核心实体类
- **PsyTimeScheduleTemplate** - 排班模板主表
- **PsyTimeTemplateItem** - 模板明细表（按星期设置时间段）

### 2. 数据访问层
- **PsyTimeScheduleTemplateMapper** - 模板数据访问接口
- **PsyTimeTemplateItemMapper** - 模板明细数据访问接口
- 对应的XML映射文件

### 3. 业务服务层
- **IPsyTimeScheduleTemplateService** - 模板服务接口
- **PsyTimeScheduleTemplateServiceImpl** - 模板服务实现
- **IPsyTimeTemplateItemService** - 模板明细服务接口
- **PsyTimeTemplateItemServiceImpl** - 模板明细服务实现

### 4. 控制器层
- **PsyTimeScheduleTemplateController** - 模板管理控制器
- **PsyTimeTemplateItemController** - 模板明细管理控制器

## API 接口列表

### 排班模板管理

#### 1. 查询模板列表
```
GET /system/schedule/template/list
```

#### 2. 获取模板详情
```
GET /system/schedule/template/{id}
```

#### 3. 根据咨询师ID查询模板
```
GET /system/schedule/template/counselor/{counselorId}
```

#### 4. 查询默认模板
```
GET /system/schedule/template/counselor/{counselorId}/default
```

#### 5. 查询有效模板
```
GET /system/schedule/template/counselor/{counselorId}/effective?date=2025-07-18
```

#### 6. 新增模板
```
POST /system/schedule/template
Content-Type: application/json

{
  "counselorId": 1,
  "name": "工作日排班",
  "isDefault": 1,
  "effectiveStart": "2025-07-01",
  "effectiveEnd": "2025-12-31",
  "templateItems": [
    {
      "dayOfWeek": 1,
      "startTime": "09:00",
      "endTime": "18:00",
      "centerId": 1
    },
    {
      "dayOfWeek": 2,
      "startTime": "09:00",
      "endTime": "18:00",
      "centerId": 1
    }
  ]
}
```

#### 7. 修改模板
```
PUT /system/schedule/template
```

#### 8. 设置默认模板
```
PUT /system/schedule/template/setDefault/{counselorId}/{templateId}
```

#### 9. 删除模板
```
DELETE /system/schedule/template/{ids}
```

#### 10. 创建默认模板
```
POST /system/schedule/template/createDefault/{counselorId}?centerId=1
```

#### 11. 复制模板
```
POST /system/schedule/template/copy/{templateId}?newName=新模板名称
```

#### 12. 校验模板名称唯一性
```
POST /system/schedule/template/checkTemplateNameUnique
```

### 模板明细管理

#### 1. 查询明细列表
```
GET /system/schedule/template/item/list
```

#### 2. 根据模板ID查询明细
```
GET /system/schedule/template/item/template/{templateId}
```

#### 3. 新增明细
```
POST /system/schedule/template/item
```

#### 4. 批量新增明细
```
POST /system/schedule/template/item/batch
```

#### 5. 修改明细
```
PUT /system/schedule/template/item
```

#### 6. 删除明细
```
DELETE /system/schedule/template/item/{ids}
```

## 核心功能说明

### 1. 模板结构
- **主表（psy_time_schedule_template）**：存储模板基本信息
- **明细表（psy_time_template_item）**：存储每个星期的具体时间安排

### 2. 默认模板机制
- 每个咨询师可以设置一个默认模板
- 设置新默认模板时，会自动取消原有默认模板

### 3. 有效期管理
- 支持设置模板的有效开始和结束日期
- 查询有效模板时会根据日期过滤

### 4. 模板复制功能
- 支持复制现有模板创建新模板
- 复制时会同时复制所有明细记录

### 5. 时间冲突检查
- 支持检查同一天内时间段是否冲突
- 防止创建重叠的时间段

## 数据库表结构

### psy_time_schedule_template（排班模板表）
```sql
CREATE TABLE psy_time_schedule_template (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    counselor_id BIGINT NOT NULL COMMENT '咨询师ID',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认模板',
    effective_start DATE COMMENT '生效开始日期',
    effective_end DATE COMMENT '生效结束日期',
    del_flag TINYINT(1) DEFAULT 0 COMMENT '删除标志',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注'
);
```

### psy_time_template_item（模板明细表）
```sql
CREATE TABLE psy_time_template_item (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_id BIGINT NOT NULL COMMENT '模板ID',
    day_of_week INT NOT NULL COMMENT '星期几(1-7)',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    center_id BIGINT NOT NULL COMMENT '咨询中心ID',
    del_flag TINYINT(1) DEFAULT 0 COMMENT '删除标志',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注'
);
```

## 使用示例

### 1. 创建工作日排班模板
```json
{
  "counselorId": 1,
  "name": "标准工作日排班",
  "isDefault": 1,
  "templateItems": [
    {"dayOfWeek": 1, "startTime": "09:00", "endTime": "12:00", "centerId": 1},
    {"dayOfWeek": 1, "startTime": "14:00", "endTime": "18:00", "centerId": 1},
    {"dayOfWeek": 2, "startTime": "09:00", "endTime": "12:00", "centerId": 1},
    {"dayOfWeek": 2, "startTime": "14:00", "endTime": "18:00", "centerId": 1},
    {"dayOfWeek": 3, "startTime": "09:00", "endTime": "12:00", "centerId": 1},
    {"dayOfWeek": 3, "startTime": "14:00", "endTime": "18:00", "centerId": 1},
    {"dayOfWeek": 4, "startTime": "09:00", "endTime": "12:00", "centerId": 1},
    {"dayOfWeek": 4, "startTime": "14:00", "endTime": "18:00", "centerId": 1},
    {"dayOfWeek": 5, "startTime": "09:00", "endTime": "12:00", "centerId": 1},
    {"dayOfWeek": 5, "startTime": "14:00", "endTime": "18:00", "centerId": 1}
  ]
}
```

### 2. 创建周末排班模板
```json
{
  "counselorId": 1,
  "name": "周末排班",
  "isDefault": 0,
  "templateItems": [
    {"dayOfWeek": 6, "startTime": "10:00", "endTime": "16:00", "centerId": 1},
    {"dayOfWeek": 7, "startTime": "10:00", "endTime": "16:00", "centerId": 1}
  ]
}
```

## 权限配置

需要在系统中配置以下权限：
- `system:template:list` - 查看模板列表
- `system:template:query` - 查看模板详情
- `system:template:add` - 新增模板
- `system:template:edit` - 修改模板
- `system:template:remove` - 删除模板
- `system:template:export` - 导出模板

## 注意事项

1. **删除模板时会同时删除所有相关明细**
2. **设置默认模板时会自动取消其他默认模板**
3. **模板名称在同一咨询师下必须唯一**
4. **星期几使用1-7表示，1为周一，7为周日**
5. **时间格式使用HH:mm，如09:00、18:30**
6. **支持软删除，删除的记录不会物理删除**
