# 心理测评系统订单功能说明

## 概述

心理测评系统的订单功能提供完整的付费测评流程，包括订单创建、支付、退款等功能。支持免费和付费两种模式的量表。

## 功能特性

### 1. 订单管理
- ✅ 订单创建和管理
- ✅ 订单状态跟踪
- ✅ 订单过期处理
- ✅ 订单统计分析

### 2. 支付功能
- ✅ 多种支付方式支持
- ✅ 支付状态管理
- ✅ 支付回调处理
- ✅ 支付安全验证

### 3. 退款功能
- ✅ 退款申请
- ✅ 退款审核
- ✅ 退款处理
- ✅ 退款记录

### 4. 优惠功能
- ✅ 优惠券支持
- ✅ 折扣计算
- ✅ 价格计算引擎

## 数据库设计

### 订单表 (psy_t_order)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 订单ID |
| order_no | varchar(50) | 订单编号 |
| user_id | bigint | 用户ID |
| scale_id | bigint | 量表ID |
| scale_name | varchar(100) | 量表名称 |
| original_price | decimal(10,2) | 原价 |
| actual_price | decimal(10,2) | 实付金额 |
| discount_amount | decimal(10,2) | 优惠金额 |
| coupon_id | bigint | 优惠券ID |
| payment_method | varchar(20) | 支付方式 |
| payment_status | tinyint | 支付状态 |
| order_status | tinyint | 订单状态 |
| pay_time | datetime | 支付时间 |
| expire_time | datetime | 过期时间 |
| refund_amount | decimal(10,2) | 退款金额 |
| refund_time | datetime | 退款时间 |
| refund_reason | varchar(200) | 退款原因 |

## 订单状态说明

### 支付状态 (payment_status)
- 0: 待支付
- 1: 已支付
- 2: 支付失败
- 3: 已退款

### 订单状态 (order_status)
- 0: 待支付
- 1: 已支付
- 2: 已完成
- 3: 已取消
- 4: 退款中
- 5: 已退款

## API 接口

### 后台管理端接口

#### 1. 订单管理
```
GET /system/assessment/order/list          # 查询订单列表
GET /system/assessment/order/{id}          # 查询订单详情
POST /system/assessment/order              # 新增订单
PUT /system/assessment/order               # 修改订单
DELETE /system/assessment/order/{ids}      # 删除订单
```

#### 2. 订单操作
```
PUT /system/assessment/order/complete/{orderNo}    # 完成订单
PUT /system/assessment/order/cancel/{orderNo}      # 取消订单
PUT /system/assessment/order/refund/{orderNo}      # 处理退款
POST /system/assessment/order/handleExpired        # 处理过期订单
```

#### 3. 统计分析
```
GET /system/assessment/order/stats         # 订单统计
GET /system/assessment/order/income        # 收入统计
GET /system/assessment/order/hotSales      # 热销量表
GET /system/assessment/order/trend         # 订单趋势
```

### 小程序用户端接口

#### 1. 订单管理
```
GET /miniapp/user/order/list               # 查询订单列表
GET /miniapp/user/order/{id}               # 查询订单详情
POST /miniapp/user/order/create            # 创建订单
```

#### 2. 订单操作
```
POST /miniapp/user/order/pay               # 支付订单
POST /miniapp/user/order/cancel/{orderNo}  # 取消订单
POST /miniapp/user/order/refund            # 申请退款
```

#### 3. 订单查询
```
GET /miniapp/user/order/pending            # 待支付订单
GET /miniapp/user/order/paid               # 已支付订单
GET /miniapp/user/order/completed          # 已完成订单
GET /miniapp/user/order/cancelled          # 已取消订单
```

#### 4. 其他功能
```
GET /miniapp/user/order/stats              # 用户订单统计
GET /miniapp/user/order/checkPurchased/{scaleId}  # 检查购买状态
```

## 业务流程

### 1. 订单创建流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant A as 小程序
    participant S as 服务器
    participant D as 数据库

    U->>A: 选择付费量表
    A->>S: 创建订单请求
    S->>D: 检查量表信息
    D-->>S: 返回量表数据
    S->>D: 计算订单金额
    S->>D: 创建订单记录
    D-->>S: 返回订单信息
    S-->>A: 返回订单详情
    A-->>U: 显示支付页面
```

### 2. 支付流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant A as 小程序
    participant S as 服务器
    participant P as 支付平台
    participant D as 数据库

    U->>A: 确认支付
    A->>S: 支付请求
    S->>P: 调用支付接口
    P-->>S: 返回支付结果
    S->>D: 更新订单状态
    D-->>S: 更新成功
    S-->>A: 返回支付结果
    A-->>U: 显示支付结果
```

### 3. 退款流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant A as 小程序
    participant S as 服务器
    participant M as 管理员
    participant P as 支付平台

    U->>A: 申请退款
    A->>S: 退款申请
    S->>S: 更新订单状态为退款中
    M->>S: 审核退款申请
    S->>P: 调用退款接口
    P-->>S: 返回退款结果
    S->>S: 更新订单状态为已退款
    S-->>A: 通知退款结果
    A-->>U: 显示退款结果
```

## 核心功能实现

### 1. 订单编号生成
```java
public String generateOrderNo() {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
    String timestamp = sdf.format(new Date());
    String random = String.valueOf((int) (Math.random() * 9000) + 1000);
    return "PSY" + timestamp + random;
}
```

### 2. 价格计算
```java
public Map<String, BigDecimal> calculateOrderAmount(Long scaleId, Long couponId) {
    // 获取量表原价
    PsyAssessmentScale scale = scaleService.selectScaleById(scaleId);
    BigDecimal originalPrice = scale.getPrice();
    
    // 计算优惠金额
    BigDecimal discountAmount = calculateDiscount(couponId, originalPrice);
    
    // 计算实付金额
    BigDecimal actualPrice = originalPrice.subtract(discountAmount);
    
    return Map.of(
        "originalPrice", originalPrice,
        "discountAmount", discountAmount,
        "actualPrice", actualPrice
    );
}
```

### 3. 购买状态检查
```java
public boolean checkUserPurchased(Long userId, Long scaleId) {
    PsyAssessmentOrder order = selectValidOrderByUserAndScale(userId, scaleId);
    return order != null && order.isPaid();
}
```

### 4. 过期订单处理
```java
@Scheduled(fixedRate = 300000) // 每5分钟执行一次
public void handleExpiredOrders() {
    List<PsyAssessmentOrder> expiredOrders = orderMapper.selectExpiredOrders();
    for (PsyAssessmentOrder order : expiredOrders) {
        cancelOrder(order.getOrderNo());
    }
}
```

## 安全考虑

### 1. 权限控制
- 用户只能操作自己的订单
- 管理员可以查看和管理所有订单
- 支付回调需要验证签名

### 2. 数据验证
- 订单金额验证
- 支付状态验证
- 重复支付检查

### 3. 防刷机制
- 订单创建频率限制
- 支付请求防重复
- 异常订单监控

## 扩展功能

### 1. 优惠券系统
- 优惠券创建和管理
- 优惠券使用规则
- 优惠券统计分析

### 2. 会员系统
- 会员等级管理
- 会员专享价格
- 会员积分系统

### 3. 分销系统
- 推广员管理
- 佣金计算
- 分销统计

## 监控和运维

### 1. 订单监控
- 订单量监控
- 支付成功率监控
- 退款率监控

### 2. 异常处理
- 支付异常处理
- 订单状态异常修复
- 数据一致性检查

### 3. 报表统计
- 日/周/月订单报表
- 收入统计报表
- 用户消费分析

## 总结

订单系统已经实现了完整的付费测评功能，包括：

1. **完整的订单生命周期管理**
2. **多种支付方式支持**
3. **完善的退款机制**
4. **详细的统计分析**
5. **安全的权限控制**

系统支持免费和付费两种模式，用户在进行付费量表测评前需要先购买，确保了商业模式的完整性。
