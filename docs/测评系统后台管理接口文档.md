# 测评系统后台管理接口文档

## 概述

本文档描述了测评系统后台管理端的所有API接口，供管理员使用。

## 基础信息

- **基础URL**: `/system`
- **认证方式**: JWT Token + 权限验证
- **数据格式**: JSON
- **字符编码**: UTF-8
- **版本**: v2.0
- **更新时间**: 2025-01-21

## 通用响应格式

```json
{
  "code": 200,
  "msg": "操作成功", 
  "data": {}
}
```

## 权限说明

所有后台接口都需要管理员权限，使用 `@PreAuthorize` 注解进行权限控制。

## 接口列表

### 1. 量表管理接口

#### 1.1 查询量表列表
```
GET /system/scale/list
```

**权限要求**: `system:scale:list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | String | 否 | 量表名称 |
| code | String | 否 | 量表编码 |
| status | Integer | 否 | 状态 |
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |

#### 1.2 获取量表详情
```
GET /system/scale/{id}
```

**权限要求**: `system:scale:query`

#### 1.3 新增量表
```
POST /system/scale
```

**权限要求**: `system:scale:add`

#### 1.4 修改量表
```
PUT /system/scale
```

**权限要求**: `system:scale:edit`

#### 1.5 删除量表
```
DELETE /system/scale/{ids}
```

**权限要求**: `system:scale:remove`

#### 1.6 发布量表
```
POST /system/scale/{id}/publish
```

**权限要求**: `system:scale:publish`

#### 1.7 下架量表
```
POST /system/scale/{id}/unpublish
```

**权限要求**: `system:scale:publish`

### 2. 订单管理接口

#### 2.1 查询订单列表
```
GET /system/assessment/order/list
```

**权限要求**: `system:assessmentOrder:list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 否 | 订单号 |
| userId | Long | 否 | 用户ID |
| scaleId | Long | 否 | 量表ID |
| paymentStatus | Integer | 否 | 支付状态 |
| orderStatus | Integer | 否 | 订单状态 |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |

#### 2.2 查询订单详情
```
GET /system/assessment/order/{id}
```

**权限要求**: `system:assessmentOrder:query`

#### 2.3 处理退款
```
POST /system/assessment/order/refund/{id}
```

**权限要求**: `system:assessmentOrder:refund`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| refundAmount | BigDecimal | 是 | 退款金额 |
| refundReason | String | 是 | 退款原因 |

#### 2.4 订单统计
```
GET /system/assessment/order/stats
```

**权限要求**: `system:assessmentOrder:list`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalOrders": 1500,
    "totalAmount": 15000.00,
    "todayOrders": 25,
    "todayAmount": 250.00,
    "pendingOrders": 5,
    "refundOrders": 12
  }
}
```

#### 2.5 导出订单数据
```
POST /system/assessment/order/export
```

**权限要求**: `system:assessmentOrder:export`

### 3. 评价管理接口

#### 3.1 查询评价列表
```
GET /system/assessment/review/list
```

**权限要求**: `system:assessmentReview:list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 否 | 量表ID |
| userId | Long | 否 | 用户ID |
| status | Integer | 否 | 审核状态 |
| rating | Integer | 否 | 评分 |
| isAnonymous | Integer | 否 | 是否匿名 |
| isTop | Integer | 否 | 是否置顶 |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |

#### 3.2 获取评价详情
```
GET /system/assessment/review/{id}
```

**权限要求**: `system:assessmentReview:query`

#### 3.3 获取评价详细信息（包含关联数据）
```
GET /system/assessment/review/details/{id}
```

**权限要求**: `system:assessmentReview:query`

#### 3.4 审核评价
```
PUT /system/assessment/review/audit/{id}
```

**权限要求**: `system:assessmentReview:audit`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | Integer | 是 | 审核状态（1=通过，2=拒绝） |
| auditRemark | String | 否 | 审核备注 |

#### 3.5 批量审核评价
```
PUT /system/assessment/review/audit/batch
```

**权限要求**: `system:assessmentReview:audit`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | Long[] | 是 | 评价ID数组 |
| status | Integer | 是 | 审核状态 |
| auditRemark | String | 否 | 审核备注 |

#### 3.6 置顶评价
```
PUT /system/assessment/review/top/{id}
```

**权限要求**: `system:assessmentReview:top`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isTop | Integer | 是 | 是否置顶（0=否，1=是） |

#### 3.7 删除评价
```
DELETE /system/assessment/review/{ids}
```

**权限要求**: `system:assessmentReview:remove`

#### 3.8 查询待审核评价
```
GET /system/assessment/review/pending
```

**权限要求**: `system:assessmentReview:list`

#### 3.9 查询置顶评价
```
GET /system/assessment/review/top/{scaleId}
```

**权限要求**: `system:assessmentReview:list`

#### 3.10 查询热门评价
```
GET /system/assessment/review/hot
```

**权限要求**: `system:assessmentReview:list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 否 | 量表ID |
| limit | Integer | 否 | 限制数量，默认10 |

#### 3.11 搜索评价
```
GET /system/assessment/review/search
```

**权限要求**: `system:assessmentReview:list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 否 | 搜索关键词 |
| scaleId | Long | 否 | 量表ID |
| userId | Long | 否 | 用户ID |
| status | Integer | 否 | 审核状态 |
| rating | Integer | 否 | 评分 |

#### 3.12 查询评价统计信息
```
GET /system/assessment/review/statistics
```

**权限要求**: `system:assessmentReview:list`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "scaleId": 1,
      "scaleName": "焦虑自评量表",
      "totalReviews": 150,
      "averageRating": 4.5,
      "pendingReviews": 5,
      "approvedReviews": 140,
      "rejectedReviews": 5
    }
  ]
}
```

#### 3.13 查询量表评价统计
```
GET /system/assessment/review/statistics/scale/{scaleId}
```

**权限要求**: `system:assessmentReview:list`

#### 3.14 查询用户评价统计
```
GET /system/assessment/review/statistics/user/{userId}
```

**权限要求**: `system:assessmentReview:list`

#### 3.15 查询评分分布
```
GET /system/assessment/review/distribution/{scaleId}
```

**权限要求**: `system:assessmentReview:list`

#### 3.16 查询评价排行榜
```
GET /system/assessment/review/ranking
```

**权限要求**: `system:assessmentReview:list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | String | 是 | 排行类型（like=点赞数，reply=回复数） |
| limit | Integer | 否 | 限制数量，默认10 |

#### 3.17 导出评价数据
```
POST /system/assessment/review/export
```

**权限要求**: `system:assessmentReview:export`

#### 3.18 导出评价统计报告
```
POST /system/assessment/review/export/statistics
```

**权限要求**: `system:assessmentReview:export`

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 权限列表

| 权限代码 | 权限名称 | 说明 |
|----------|----------|------|
| system:scale:list | 量表查询 | 查询量表列表 |
| system:scale:query | 量表详情 | 查看量表详情 |
| system:scale:add | 量表新增 | 新增量表 |
| system:scale:edit | 量表编辑 | 修改量表 |
| system:scale:remove | 量表删除 | 删除量表 |
| system:scale:publish | 量表发布 | 发布/下架量表 |
| system:assessmentOrder:list | 订单查询 | 查询订单列表 |
| system:assessmentOrder:query | 订单详情 | 查看订单详情 |
| system:assessmentOrder:refund | 订单退款 | 处理订单退款 |
| system:assessmentOrder:export | 订单导出 | 导出订单数据 |
| system:assessmentReview:list | 评价查询 | 查询评价列表 |
| system:assessmentReview:query | 评价详情 | 查看评价详情 |
| system:assessmentReview:audit | 评价审核 | 审核评价内容 |
| system:assessmentReview:top | 评价置顶 | 置顶评价 |
| system:assessmentReview:remove | 评价删除 | 删除评价 |
| system:assessmentReview:export | 评价导出 | 导出评价数据 |

## 注意事项

1. **权限验证**：所有接口都需要相应的权限才能访问
2. **数据安全**：敏感数据会进行脱敏处理
3. **操作日志**：重要操作会记录操作日志
4. **数据导出**：导出功能支持Excel格式
5. **批量操作**：支持批量审核、删除等操作
6. **统计分析**：提供丰富的统计分析功能
