# 正确的时间槽生成逻辑实现

## 🎯 问题分析

您说得对！之前的逻辑完全错误，应该很简单：

### **正确的逻辑应该是**：
1. **首先使用咨询师模板的时间点生成时间槽**
2. **如果模板过期或不存在，使用系统默认时间段（psy_time_range表）生成**

### **之前错误的逻辑**：
- 依赖排班记录（psy_time_counselor_schedule）
- 没有使用咨询师模板
- 没有使用系统时间段表（psy_time_range）

## ✅ 重新实现的正确逻辑

### **1. 主要生成方法**
```java
private int generateSlotsForCounselorOnDate(Long counselorId, LocalDate date) {
    // 检查是否已存在时间槽，避免重复生成
    if (hasExistingTimeSlots(counselorId, date)) {
        return 0;
    }

    // 检查是否为工作日（周一到周五）
    if (isWeekend(date)) {
        return 0;
    }

    // 1. 首先尝试使用咨询师模板生成时间槽
    int templateSlots = generateSlotsFromTemplate(counselorId, date);
    if (templateSlots > 0) {
        return templateSlots;
    }

    // 2. 模板不存在或过期，使用系统默认时间段生成
    int defaultSlots = generateSlotsFromSystemTimeRanges(counselorId, date);
    return defaultSlots;
}
```

### **2. 咨询师模板生成逻辑**
```java
private int generateSlotsFromTemplate(Long counselorId, LocalDate date) {
    // 查询咨询师的有效模板
    PsyTimeScheduleTemplate template = templateService.selectEffectiveTemplate(counselorId, date);
    
    if (template == null) {
        // 尝试获取默认模板
        template = templateService.selectDefaultTemplateByCounselorId(counselorId);
    }
    
    if (template == null) {
        return 0; // 没有模板
    }

    // 获取当前日期是星期几
    int dayOfWeek = date.getDayOfWeek().getValue();

    // 查找匹配当前星期的模板明细
    List<PsyTimeTemplateItem> dayItems = template.getTemplateItems()
        .stream()
        .filter(item -> item.getDayOfWeek().equals(dayOfWeek))
        .filter(item -> item.getDelFlag() == 0)
        .sorted(by startTime)
        .collect(toList());

    // 根据模板明细生成时间槽
    List<PsyTimeSlot> slots = new ArrayList<>();
    for (PsyTimeTemplateItem item : dayItems) {
        slots.addAll(generateSlotsFromTemplateItem(counselorId, date, item));
    }

    return batchInsertTimeSlots(slots);
}
```

### **3. 系统默认时间段生成逻辑**
```java
private int generateSlotsFromSystemTimeRanges(Long counselorId, LocalDate date) {
    // 获取所有有效的系统时间段
    List<PsyTimeRange> timeRanges = timeRangeService.selectAllActiveTimeRanges();
    
    if (timeRanges.isEmpty()) {
        logger.warn("系统中没有配置时间段，无法生成默认时间槽");
        return 0;
    }

    // 根据系统时间段生成时间槽
    List<PsyTimeSlot> slots = new ArrayList<>();
    for (PsyTimeRange timeRange : timeRanges) {
        slots.addAll(generateSlotsFromTimeRange(counselorId, date, timeRange));
    }

    return batchInsertTimeSlots(slots);
}
```

### **4. 时间槽生成细节**
```java
// 根据模板明细生成时间槽（每15分钟一个）
private List<PsyTimeSlot> generateSlotsFromTemplateItem(Long counselorId, LocalDate date, PsyTimeTemplateItem item) {
    LocalTime startTime = item.getStartTime();
    LocalTime endTime = item.getEndTime();
    
    List<PsyTimeSlot> slots = new ArrayList<>();
    LocalTime current = startTime;
    
    while (current.isBefore(endTime)) {
        LocalTime slotEnd = current.plusMinutes(15);
        if (slotEnd.isAfter(endTime)) break;
        
        PsyTimeSlot slot = createTimeSlot(counselorId, date, current, slotEnd, null);
        slots.add(slot);
        current = slotEnd;
    }
    
    return slots;
}

// 根据系统时间段生成时间槽
private List<PsyTimeSlot> generateSlotsFromTimeRange(Long counselorId, LocalDate date, PsyTimeRange timeRange) {
    LocalTime startTime = LocalTime.of(timeRange.getStartHour(), 0);
    LocalTime endTime = LocalTime.of(timeRange.getEndHour(), 0);
    
    // 同样每15分钟生成一个时间槽
    // ... 类似逻辑
}
```

## 📊 数据表依赖关系

### **1. 咨询师模板相关表**
- `psy_time_schedule_template` - 咨询师排班模板主表
- `psy_time_template_item` - 模板明细表（包含具体时间段）

### **2. 系统时间段表**
- `psy_time_range` - 系统默认时间段配置表

### **3. 生成的时间槽表**
- `psy_time_slot` - 最终生成的时间槽表

## 🔧 生成流程

### **流程图**：
```
开始生成时间槽
    ↓
检查是否已存在时间槽？
    ↓ 否
检查是否为工作日？
    ↓ 是
查询咨询师模板
    ↓
模板存在且有效？
    ↓ 是                    ↓ 否
使用模板生成时间槽    →    使用系统时间段生成
    ↓                        ↓
生成成功？              生成成功？
    ↓ 是                    ↓ 是
返回生成数量            返回生成数量
```

### **预期结果**：
- **有模板的咨询师**：按模板时间段生成（如：9:00-12:00, 14:00-18:00）
- **无模板的咨询师**：按系统默认时间段生成（如：9:00-21:00）
- **每个时间段**：每15分钟生成一个时间槽
- **工作日**：周一到周五生成
- **周末**：不生成

## 🎯 修复效果

### **修复前**：
- 咨询师4、7、8：都生成了0个时间槽 ❌
- 依赖错误的排班记录逻辑

### **修复后预期**：
- **有模板的咨询师**：按模板生成时间槽 ✅
- **无模板的咨询师**：按系统时间段生成时间槽 ✅
- **每个工作日**：生成相应数量的时间槽 ✅

### **日志输出预期**：
```
为咨询师 4 在日期 2025-07-28 使用模板生成了 32 个时间槽
为咨询师 7 在日期 2025-07-28 使用系统默认时间段生成了 48 个时间槽
为咨询师 8 在日期 2025-07-28 使用模板生成了 24 个时间槽
```

## ⚠️ 注意事项

### **1. 服务依赖**
需要确保以下服务正常工作：
- `IPsyTimeScheduleTemplateService` - 模板服务
- `IPsyTimeRangeService` - 时间段服务

### **2. 数据完整性**
- 确保 `psy_time_range` 表有数据
- 确保模板表结构正确
- 确保时间槽表能正常插入

### **3. 性能考虑**
- 批量插入时间槽
- 避免重复生成
- 合理的日志记录

## 🚀 测试验证

### **1. 重新编译部署**
```bash
mvn clean compile package
```

### **2. 检查依赖服务**
确保模板服务和时间段服务正常工作。

### **3. 重新运行生成任务**
现在应该能看到正确的时间槽生成逻辑。

### **4. 验证生成结果**
```sql
-- 检查时间槽生成情况
SELECT 
    counselor_id,
    date_key,
    COUNT(*) as slot_count,
    MIN(start_time) as earliest_time,
    MAX(end_time) as latest_time
FROM psy_time_slot 
WHERE counselor_id IN (4, 7, 8)
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
GROUP BY counselor_id, date_key
ORDER BY counselor_id, date_key;
```

现在的逻辑应该是正确的，简单明了：**优先模板，回退到系统默认时间段**！
