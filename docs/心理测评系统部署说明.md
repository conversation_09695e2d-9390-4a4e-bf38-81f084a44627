# 心理测评系统部署说明

## 系统概述

心理测评系统是基于若依框架开发的完整心理量表测评平台，支持三端分离架构：
- **后台管理端**：量表管理、数据统计、用户管理
- **小程序用户端**：心理测评、结果查看、评价反馈
- **小程序咨询师端**：测评数据分析、用户指导

## 技术架构

### 后端技术栈
- **框架**：Spring Boot 2.5.x
- **ORM**：MyBatis 3.5.x
- **数据库**：MySQL 8.0+
- **缓存**：Redis 6.0+
- **安全**：Spring Security + JWT
- **文档**：Swagger 3.0

### 前端技术栈
- **后台管理**：Vue 3 + Element Plus
- **小程序**：微信小程序原生开发

## 部署步骤

### 1. 环境准备

#### 1.1 服务器要求
- **操作系统**：CentOS 7+ / Ubuntu 18.04+
- **CPU**：2核心以上
- **内存**：4GB以上
- **存储**：50GB以上

#### 1.2 软件环境
```bash
# 安装 Java 8+
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 安装 MySQL 8.0
yum install -y mysql-server mysql

# 安装 Redis
yum install -y redis

# 安装 Nginx
yum install -y nginx
```

### 2. 数据库初始化

#### 2.1 创建数据库
```sql
-- 创建数据库
CREATE DATABASE xihuanxinli_com DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'xihuan'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON xihuanxinli_com.* TO 'xihuan'@'%';
FLUSH PRIVILEGES;
```

#### 2.2 导入表结构和数据
```bash
# 导入测评系统表结构
mysql -u xihuan -p xihuanxinli_com < sql/psy_assessment_tables.sql

# 导入初始化数据
mysql -u xihuan -p xihuanxinli_com < sql/psy_assessment_init_data.sql
```

### 3. 后端部署

#### 3.1 配置文件修改

修改 `xihuan-admin/src/main/resources/application.yml`：

```yaml
# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /

# 数据库配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      url: ********************************************************************************************************************************************************
      username: xihuan
      password: your_password

# Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
```

#### 3.2 编译打包
```bash
# 进入项目根目录
cd /path/to/xihuan

# 编译打包
mvn clean package -Dmaven.test.skip=true

# 生成的jar包位置
ls -la xihuan-admin/target/xihuan-admin.jar
```

#### 3.3 启动服务
```bash
# 创建启动脚本
cat > /opt/xihuan/start.sh << 'EOF'
#!/bin/bash
cd /opt/xihuan
nohup java -jar xihuan-admin.jar --spring.profiles.active=prod > logs/app.log 2>&1 &
echo $! > app.pid
EOF

chmod +x /opt/xihuan/start.sh

# 启动服务
./start.sh
```

#### 3.4 设置开机自启
```bash
# 创建systemd服务文件
cat > /etc/systemd/system/xihuan.service << 'EOF'
[Unit]
Description=XiHuan Assessment System
After=network.target

[Service]
Type=forking
User=root
WorkingDirectory=/opt/xihuan
ExecStart=/opt/xihuan/start.sh
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/opt/xihuan/app.pid
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
systemctl enable xihuan
systemctl start xihuan
systemctl status xihuan
```

### 4. 前端部署

#### 4.1 后台管理前端
```bash
# 进入前端项目目录
cd /path/to/xihuan-ui

# 安装依赖
npm install

# 修改配置文件
vim .env.production
# VUE_APP_BASE_API = 'http://your-domain.com'

# 构建生产版本
npm run build:prod

# 部署到nginx
cp -r dist/* /var/www/html/admin/
```

#### 4.2 Nginx配置
```nginx
# /etc/nginx/conf.d/xihuan.conf
server {
    listen 80;
    server_name your-domain.com;

    # 后台管理前端
    location /admin {
        alias /var/www/html/admin;
        index index.html;
        try_files $uri $uri/ /admin/index.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源
    location /profile {
        alias /opt/xihuan/uploadPath;
    }
}
```

### 5. 小程序配置

#### 5.1 微信小程序配置
```javascript
// config.js
const config = {
  // 开发环境
  development: {
    baseUrl: 'http://localhost:8080',
    appId: 'your_dev_appid'
  },
  // 生产环境
  production: {
    baseUrl: 'https://your-domain.com',
    appId: 'your_prod_appid'
  }
}

module.exports = config
```

#### 5.2 小程序发布
1. 使用微信开发者工具打开小程序项目
2. 修改 `app.js` 中的服务器域名配置
3. 点击"上传"按钮，填写版本号和项目备注
4. 登录微信公众平台，提交审核
5. 审核通过后发布上线

### 6. 系统配置

#### 6.1 初始化管理员账号
```sql
-- 插入管理员用户
INSERT INTO sys_user (user_id, user_name, nick_name, email, phonenumber, sex, password, status, create_by, create_time) 
VALUES (1, 'admin', '管理员', '<EMAIL>', '18888888888', '1', '$2a$10$7JB720yubVSOfvVMe6/YqOPKYvuFI5QHFVsQHKJw.8cxhkg/Y67q6', '0', 'admin', sysdate());

-- 插入角色
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, status, create_by, create_time) 
VALUES (1, '超级管理员', 'admin', 1, '0', 'admin', sysdate());

-- 关联用户角色
INSERT INTO sys_user_role (user_id, role_id) VALUES (1, 1);
```

#### 6.2 配置测评分类
```sql
-- 插入测评分类
INSERT INTO psy_category (category_id, parent_id, category_name, order_num, status, create_by, create_time) VALUES
(100, 0, '心理测评', 4, '0', 'admin', NOW()),
(101, 100, '情绪测评', 1, '0', 'admin', NOW()),
(102, 100, '人格测评', 2, '0', 'admin', NOW()),
(103, 100, '认知测评', 3, '0', 'admin', NOW()),
(104, 100, '行为测评', 4, '0', 'admin', NOW()),
(105, 100, '社交测评', 5, '0', 'admin', NOW());
```

### 7. 监控和维护

#### 7.1 日志配置
```yaml
# application.yml
logging:
  level:
    com.xihuan: debug
    root: info
  file:
    name: logs/xihuan.log
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
```

#### 7.2 健康检查
```bash
# 创建健康检查脚本
cat > /opt/xihuan/health_check.sh << 'EOF'
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/actuator/health)
if [ $response -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy, restarting..."
    systemctl restart xihuan
    exit 1
fi
EOF

chmod +x /opt/xihuan/health_check.sh

# 添加到crontab
echo "*/5 * * * * /opt/xihuan/health_check.sh" | crontab -
```

#### 7.3 数据备份
```bash
# 创建备份脚本
cat > /opt/xihuan/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backup"
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u xihuan -p'your_password' xihuanxinli_com > $BACKUP_DIR/db_backup_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz /opt/xihuan/uploadPath

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /opt/xihuan/backup.sh

# 每天凌晨2点执行备份
echo "0 2 * * * /opt/xihuan/backup.sh" | crontab -
```

## 使用说明

### 1. 后台管理使用

#### 1.1 登录系统
- 访问：`http://your-domain.com/admin`
- 用户名：`admin`
- 密码：`admin123`

#### 1.2 量表管理
1. 进入"心理测评" -> "量表管理"
2. 点击"新增"创建量表
3. 填写量表基本信息
4. 添加题目和选项
5. 设置结果解释规则
6. 发布量表

#### 1.3 数据统计
1. 进入"心理测评" -> "测评统计"
2. 查看测评数据报表
3. 导出统计数据

### 2. 小程序使用

#### 2.1 用户端功能
- 浏览量表列表
- 搜索心理测评
- 进行心理测评
- 查看测评结果
- 评价量表

#### 2.2 咨询师端功能
- 查看用户测评数据
- 分析测评结果
- 提供专业建议

## 常见问题

### 1. 数据库连接失败
- 检查数据库服务是否启动
- 确认数据库连接配置正确
- 检查防火墙设置

### 2. Redis连接失败
- 检查Redis服务状态
- 确认Redis配置正确
- 检查网络连接

### 3. 小程序无法访问接口
- 检查服务器域名配置
- 确认SSL证书有效
- 检查微信小程序域名白名单

### 4. 文件上传失败
- 检查上传目录权限
- 确认磁盘空间充足
- 检查文件大小限制

## 技术支持

如有问题，请联系技术支持：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx
- QQ群：xxxxxxxxx

## 版本更新

### v1.0.0 (2024-12-01)
- 初始版本发布
- 完整的测评系统功能
- 三端分离架构
- 基础数据统计功能
