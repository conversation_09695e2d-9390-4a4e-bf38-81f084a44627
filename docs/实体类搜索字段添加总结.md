# 实体类搜索字段添加总结

## 🔧 修复的编译错误

### 问题描述
搜索服务实现类中使用了 `getSearchKeywords()` 方法，但各个实体类缺少对应的字段，导致编译错误：
- `PsyConsultant.getSearchKeywords()` - 找不到符号
- `PsyCourse.getSearchKeywords()` - 找不到符号  
- `PsyMeditation.getSearchKeywords()` - 找不到符号
- `PsyAssessment.getSearchKeywords()` - 找不到符号

## ✅ 添加的字段

### 1. 咨询师实体类 (`PsyConsultant`)
**文件路径**: `xihuan-common/src/main/java/com/xihuan/common/core/domain/consultant/PsyConsultant.java`

**新增字段**:
```java
@Excel(name = "搜索关键词")
private String searchKeywords;

@Excel(name = "被搜索次数")
private Integer searchCount;

@Excel(name = "查看次数")
private Integer viewCount;
```

### 2. 课程实体类 (`PsyCourse`)
**文件路径**: `xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyCourse.java`

**新增字段**:
```java
/** 搜索关键词 */
@Excel(name = "搜索关键词")
private String searchKeywords;

/** 被搜索次数 */
@Excel(name = "被搜索次数")
private Integer searchCount;
```

**注意**: `viewCount` 字段已存在，无需添加

### 3. 冥想实体类 (`PsyMeditation`)
**文件路径**: `xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyMeditation.java`

**新增字段**:
```java
/** 搜索关键词 */
@Excel(name = "搜索关键词")
private String searchKeywords;

/** 被搜索次数 */
@Excel(name = "被搜索次数")
private Integer searchCount;
```

**注意**: `playCount` 字段已存在，相当于查看次数

### 4. 测评实体类 (`PsyAssessment`)
**文件路径**: `xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessment.java`

**新增字段**:
```java
/** 搜索关键词 */
@Excel(name = "搜索关键词")
private String searchKeywords;

/** 被搜索次数 */
@Excel(name = "被搜索次数")
private Integer searchCount;

/** 查看次数 */
@Excel(name = "查看次数")
private Integer viewCount;
```

## 🎯 字段用途说明

### 1. searchKeywords (搜索关键词)
- **类型**: `String`
- **用途**: 存储预处理的搜索关键词，包含实体的各种可搜索信息
- **内容示例**:
  - 咨询师: "张三,心理咨询师,焦虑症专家,北京,女咨询师,可预约"
  - 课程: "情绪管理课程,心理学,入门课程,免费课程,已发布"
  - 冥想: "睡眠冥想,放松,李老师,短时冥想,免费冥想"
  - 测评: "焦虑测评,心理测试,免费测评,简短测评,启用测评"

### 2. searchCount (被搜索次数)
- **类型**: `Integer`
- **用途**: 统计该实体被搜索的次数
- **默认值**: 0
- **用于**: 热度排序、推荐算法

### 3. viewCount (查看次数)
- **类型**: `Integer`
- **用途**: 统计该实体详情页被查看的次数
- **默认值**: 0
- **用于**: 热度排序、推荐算法

## 🔍 Lombok自动生成方法

由于所有实体类都使用了 `@Data` 注解，Lombok会自动生成以下方法：

### Getter方法
```java
public String getSearchKeywords()
public Integer getSearchCount()
public Integer getViewCount()
```

### Setter方法
```java
public void setSearchKeywords(String searchKeywords)
public void setSearchCount(Integer searchCount)
public void setViewCount(Integer viewCount)
```

## 📊 数据库对应关系

### 实体类字段 → 数据库字段映射

| 实体类 | Java字段 | 数据库字段 | 类型 |
|--------|----------|------------|------|
| PsyConsultant | searchKeywords | search_keywords | text |
| PsyConsultant | searchCount | search_count | int(11) |
| PsyConsultant | viewCount | view_count | int(11) |
| PsyCourse | searchKeywords | search_keywords | text |
| PsyCourse | searchCount | search_count | int(11) |
| PsyMeditation | searchKeywords | search_keywords | text |
| PsyMeditation | searchCount | search_count | int(11) |


## 🚀 搜索功能使用

### 关键词匹配逻辑
```java
// 咨询师关键词匹配
private boolean matchesConsultantKeyword(PsyConsultant consultant, String keyword) {
    String lowerKeyword = keyword.toLowerCase();
    return (StringUtils.isNotEmpty(consultant.getName()) && 
            consultant.getName().toLowerCase().contains(lowerKeyword)) ||
           (StringUtils.isNotEmpty(consultant.getPersonalIntro()) && 
            consultant.getPersonalIntro().toLowerCase().contains(lowerKeyword)) ||
           (StringUtils.isNotEmpty(consultant.getSearchKeywords()) && 
            consultant.getSearchKeywords().toLowerCase().contains(lowerKeyword));
}
```

### 搜索结果构建
```java
SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
item.setId(consultant.getId());
item.setType("consultant");
item.setTitle(consultant.getName());
item.setDescription(consultant.getPersonalIntro());
item.setRelevanceScore(calculateRelevanceScore(consultant.getName(), keyword));
```

## 🎉 修复完成

现在所有编译错误已修复：

- ✅ **PsyConsultant** - 添加了 searchKeywords, searchCount, viewCount 字段
- ✅ **PsyCourse** - 添加了 searchKeywords, searchCount 字段
- ✅ **PsyMeditation** - 添加了 searchKeywords, searchCount 字段
- ✅ **PsyAssessment** - 添加了 searchKeywords, searchCount, viewCount 字段
- ✅ **Lombok自动生成** - 所有getter/setter方法自动可用
- ✅ **搜索功能** - 可以正常使用 getSearchKeywords() 方法

搜索功能现在可以正常编译和运行！🚀

## 📝 下一步操作

1. **执行数据库脚本**: 运行 `sql/add_search_fields.sql` 为数据库表添加对应字段
2. **初始化搜索关键词**: 运行 `sql/init_search_keywords.sql` 为现有数据生成搜索关键词
3. **测试搜索功能**: 验证搜索接口是否正常工作
4. **优化搜索算法**: 根据实际使用情况调整相关度计算和关键词匹配逻辑
