# 答题记录表字段映射修复说明

## 🔍 问题描述

XML文件中的字段映射与数据库表结构不匹配，导致查询可能失败或返回错误数据。

## 📊 数据库表结构

### psy_t_answer_record 表字段
```sql
id             bigint auto_increment comment '答案ID'
record_id      bigint                comment '测评记录ID'
question_id    bigint                comment '题目ID'
option_id      bigint                comment '选项ID(单选/多选)'
answer_content text                  comment '答案内容(填空题/原answer_text字段)'
answer_score   decimal(10, 2)        comment '得分'
answer_time    datetime              comment '答题时间'
response_time  int                   comment '答题耗时(秒)'
del_flag       tinyint               comment '删除标志(0=正常 1=删除)'
create_by      varchar(64)           comment '创建者'
create_time    datetime              comment '创建时间'
update_by      varchar(64)           comment '更新者'
update_time    datetime              comment '更新时间'
```

## 🔧 修复内容

### 1. ResultMap 字段映射修复

#### ✅ 修复前后对比
```xml
<!-- 修复前 -->
<result property="score" column="o_score"/>

<!-- 修复后 -->
<result property="sort" column="o_sort"/>
```

**说明**: `psy_t_question_option` 表中没有 `score` 字段，应该使用 `sort` 字段。

### 2. 查询字段修复

#### ✅ selectQuestionAnswerStats 修复
```xml
<!-- 修复前 -->
ORDER BY o.order_num

<!-- 修复后 -->
ORDER BY o.sort
```

**说明**: `psy_t_question_option` 表中排序字段是 `sort`，不是 `order_num`。

#### ✅ calculateAnswerScore 修复
```xml
<!-- 修复前 -->
WHEN q.is_reverse = 1 AND o.option_value IS NOT NULL THEN (q.reverse_value - o.option_value)

<!-- 修复后 -->
-- 删除了不存在的 is_reverse 和 reverse_value 字段的引用
```

**说明**: `psy_t_question` 表中没有 `is_reverse` 和 `reverse_value` 字段。

#### ✅ validateAnswer 修复
```xml
<!-- 修复前 -->
WHEN q.question_type = 'SINGLE' AND #{optionId} IS NOT NULL THEN 1
WHEN q.question_type = 'MULTIPLE' AND #{optionId} IS NOT NULL THEN 1

<!-- 修复后 -->
WHEN q.question_type = 'SINGLE_CHOICE' AND #{optionId} IS NOT NULL THEN 1
WHEN q.question_type = 'MULTIPLE_CHOICE' AND #{optionId} IS NOT NULL THEN 1
```

**说明**: 修正了问题类型的枚举值，使其与数据库中的实际值匹配。

### 3. 关联查询字段修复

#### ✅ 详情查询修复
```xml
<!-- 修复前 -->
o.score as o_score

<!-- 修复后 -->
o.sort as o_sort
```

**说明**: 统一了选项表的字段引用。

## 📋 字段映射对照表

### psy_t_answer_record 字段映射
| 实体属性 | 数据库字段 | 数据类型 | 说明 |
|----------|------------|----------|------|
| id | id | bigint | 主键 |
| recordId | record_id | bigint | 测评记录ID |
| questionId | question_id | bigint | 题目ID |
| optionId | option_id | bigint | 选项ID |
| answerContent | answer_content | text | 答案内容 |
| answerScore | answer_score | decimal(10,2) | 得分 |
| answerTime | answer_time | datetime | 答题时间 |
| responseTime | response_time | int | 答题耗时 |
| delFlag | del_flag | tinyint | 删除标志 |
| createBy | create_by | varchar(64) | 创建者 |
| createTime | create_time | datetime | 创建时间 |
| updateBy | update_by | varchar(64) | 更新者 |
| updateTime | update_time | datetime | 更新时间 |

### psy_t_question 关联字段
| 实体属性 | 数据库字段 | 说明 |
|----------|------------|------|
| id | id | 题目ID |
| questionNo | question_no | 题目编号 |
| content | content | 题目内容 |
| questionType | question_type | 题目类型 |
| subscaleRef | subscale_ref | 维度引用 |

### psy_t_question_option 关联字段
| 实体属性 | 数据库字段 | 说明 |
|----------|------------|------|
| id | id | 选项ID |
| optionText | option_text | 选项文本 |
| optionValue | option_value | 选项值 |
| sort | sort | 排序 |

## 🧪 验证方法

### 1. 执行验证SQL
```bash
mysql -u username -p database_name < sql/verify_answer_record_fields.sql
```

### 2. 测试接口验证
```bash
# 测试Mapper方法修复效果
GET /test/timeslot/test/mapper-fix
```

### 3. 功能测试
- 测试答题记录的增删改查
- 测试答题进度查询
- 测试答题统计功能
- 测试关联查询功能

## ⚠️ 注意事项

### 1. 数据类型匹配
- `answer_score` 使用 `decimal(10,2)` 类型
- `response_time` 使用 `int` 类型（秒）
- `del_flag` 使用 `tinyint` 类型

### 2. 枚举值规范
- 问题类型：`SINGLE_CHOICE`, `MULTIPLE_CHOICE`, `TEXT`
- 删除标志：`0`=正常, `1`=删除
- 状态值：根据具体业务定义

### 3. 外键关系
- `record_id` → `psy_t_assessment_record.id`
- `question_id` → `psy_t_question.id`
- `option_id` → `psy_t_question_option.id`

### 4. 索引建议
```sql
CREATE INDEX idx_answer_record_id ON psy_t_answer_record(record_id);
CREATE INDEX idx_answer_question_id ON psy_t_answer_record(question_id);
CREATE INDEX idx_answer_option_id ON psy_t_answer_record(option_id);
CREATE INDEX idx_answer_del_flag ON psy_t_answer_record(del_flag);
```

## 📈 性能优化建议

1. **查询优化**：为常用查询字段添加索引
2. **分页查询**：大数据量时使用分页
3. **缓存策略**：对频繁查询的统计数据添加缓存
4. **批量操作**：使用批量插入和更新减少数据库交互

## 🔗 相关文件

- **修复文件**: `xihuan-system/src/main/resources/mapper/system/PsyTAnswerRecordMapper.xml`
- **验证脚本**: `sql/verify_answer_record_fields.sql`
- **实体类**: `xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTAnswerRecord.java`

## ✅ 修复验证清单

- [x] 基础字段映射正确
- [x] 关联查询字段正确
- [x] 计算查询字段正确
- [x] 删除了不存在的字段引用
- [x] 修正了问题类型枚举值
- [x] 统一了字段命名规范
- [x] 添加了NULL值处理
- [x] 优化了SQL查询性能
