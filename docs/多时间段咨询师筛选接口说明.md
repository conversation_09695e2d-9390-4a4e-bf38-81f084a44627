# 多时间段咨询师筛选接口说明

## 功能概述

为了满足前端选择多个时间段筛选咨询师的需求，我们提供了多种筛选接口，支持单个时间段、多个时间段以及灵活的混合筛选。

## 🎯 接口列表

### 1. **单个时间段筛选**（原有接口）
```bash
GET /miniapp/timeSlot/counselors?date=2025-07-10&startTime=14:00&endTime=15:00&centerId=1
```

### 2. **多个时间段筛选**（新增）
```bash
POST /miniapp/timeSlot/counselors/multiple
```

### 3. **灵活筛选接口**（推荐）
```bash
POST /miniapp/timeSlot/counselors/flexible
```

## 📋 详细接口说明

### 1. 单个时间段筛选

**接口**: `GET /miniapp/timeSlot/counselors`

**参数**:
- `date`: 预约日期（yyyy-MM-dd）
- `startTime`: 开始时间（HH:mm）
- `endTime`: 结束时间（HH:mm）
- `centerId`: 咨询中心ID（可选，默认1）

**使用场景**: 用户只选择一个时间段

**示例**:
```bash
GET /miniapp/timeSlot/counselors?date=2025-07-10&startTime=14:00&endTime=15:00&centerId=1
```

### 2. 多个时间段筛选（取交集）

**接口**: `POST /miniapp/timeSlot/counselors/multiple`

**请求体**:
```json
{
  "date": "2025-07-10",
  "centerId": 1,
  "timeSlots": [
    {
      "startTime": "09:00",
      "endTime": "10:00"
    },
    {
      "startTime": "14:00",
      "endTime": "15:00"
    },
    {
      "startTime": "16:00",
      "endTime": "17:00"
    }
  ]
}
```

**功能**: 筛选出在**所有时间段都可用**的咨询师（取交集）

**使用场景**: 用户需要预约多个不连续的时间段，要求同一个咨询师

### 3. 灵活筛选接口（推荐）

**接口**: `POST /miniapp/timeSlot/counselors/flexible`

**支持两种模式**:

#### 模式1：单个时间段
```json
{
  "date": "2025-07-10",
  "centerId": 1,
  "startTime": "14:00",
  "endTime": "15:00"
}
```

#### 模式2：多个时间段
```json
{
  "date": "2025-07-10",
  "centerId": 1,
  "timeSlots": [
    {
      "startTime": "09:00",
      "endTime": "10:00"
    },
    {
      "startTime": "14:00",
      "endTime": "15:00"
    }
  ]
}
```

## 📱 前端使用示例

### JavaScript调用示例

#### 1. 单个时间段筛选
```javascript
// 方式1：GET请求
async function getSingleTimeSlotCounselors(date, startTime, endTime, centerId = 1) {
  const response = await fetch(
    `/miniapp/timeSlot/counselors?date=${date}&startTime=${startTime}&endTime=${endTime}&centerId=${centerId}`
  );
  return await response.json();
}

// 方式2：POST请求（使用灵活接口）
async function getSingleTimeSlotCounselorsPost(date, startTime, endTime, centerId = 1) {
  const response = await fetch('/miniapp/timeSlot/counselors/flexible', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      date,
      startTime,
      endTime,
      centerId
    })
  });
  return await response.json();
}
```

#### 2. 多个时间段筛选
```javascript
async function getMultipleTimeSlotCounselors(date, timeSlots, centerId = 1) {
  const response = await fetch('/miniapp/timeSlot/counselors/multiple', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      date,
      centerId,
      timeSlots
    })
  });
  return await response.json();
}

// 使用示例
const timeSlots = [
  { startTime: "09:00", endTime: "10:00" },
  { startTime: "14:00", endTime: "15:00" },
  { startTime: "16:00", endTime: "17:00" }
];

const result = await getMultipleTimeSlotCounselors("2025-07-10", timeSlots);
```

#### 3. 灵活筛选（推荐）
```javascript
async function getFlexibleCounselors(params) {
  const response = await fetch('/miniapp/timeSlot/counselors/flexible', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params)
  });
  return await response.json();
}

// 单个时间段
const singleResult = await getFlexibleCounselors({
  date: "2025-07-10",
  startTime: "14:00",
  endTime: "15:00",
  centerId: 1
});

// 多个时间段
const multipleResult = await getFlexibleCounselors({
  date: "2025-07-10",
  centerId: 1,
  timeSlots: [
    { startTime: "09:00", endTime: "10:00" },
    { startTime: "14:00", endTime: "15:00" }
  ]
});
```

## 📊 响应格式

### 单个时间段响应
```json
{
  "code": 200,
  "data": [
    {
      "counselorId": 1,
      "availableSlots": 4,
      "timeRange": "14:00-15:00",
      "recommendationScore": 8.5,
      "scoreDetail": {
        "baseScore": 5.0,
        "timePreferenceScore": 7.0,
        "availabilityScore": 9.0,
        "totalScore": 8.5
      }
    }
  ]
}
```

### 多个时间段响应（灵活接口）
```json
{
  "code": 200,
  "data": {
    "type": "multiple",
    "timeRanges": ["09:00-10:00", "14:00-15:00"],
    "timeSlotCount": 2,
    "description": "筛选出在所有时间段都可用的咨询师",
    "count": 3,
    "counselors": [
      {
        "counselorId": 1,
        "availableSlots": 8,
        "timeRange": "09:00-10:00,14:00-15:00",
        "recommendationScore": 8.5
      }
    ]
  }
}
```

## 🎨 前端UI设计建议

### 1. 时间选择器
```html
<!-- 支持多选的时间段选择器 -->
<div class="time-slot-selector">
  <div class="time-slot" data-time="09:00-10:00">
    <span class="time">09:00-10:00</span>
    <span class="available">3人可约</span>
  </div>
  <div class="time-slot selected" data-time="14:00-15:00">
    <span class="time">14:00-15:00</span>
    <span class="available">5人可约</span>
  </div>
  <div class="time-slot selected" data-time="16:00-17:00">
    <span class="time">16:00-17:00</span>
    <span class="available">2人可约</span>
  </div>
</div>

<button onclick="searchCounselors()">搜索咨询师</button>
```

### 2. JavaScript逻辑
```javascript
let selectedTimeSlots = [];

// 时间段选择
document.querySelectorAll('.time-slot').forEach(slot => {
  slot.addEventListener('click', function() {
    const timeRange = this.dataset.time;
    const [startTime, endTime] = timeRange.split('-');
    
    if (this.classList.contains('selected')) {
      // 取消选择
      this.classList.remove('selected');
      selectedTimeSlots = selectedTimeSlots.filter(
        slot => !(slot.startTime === startTime && slot.endTime === endTime)
      );
    } else {
      // 添加选择
      this.classList.add('selected');
      selectedTimeSlots.push({ startTime, endTime });
    }
    
    updateSearchButton();
  });
});

// 搜索咨询师
async function searchCounselors() {
  if (selectedTimeSlots.length === 0) {
    alert('请选择至少一个时间段');
    return;
  }
  
  const params = {
    date: getCurrentDate(),
    centerId: 1
  };
  
  if (selectedTimeSlots.length === 1) {
    // 单个时间段
    params.startTime = selectedTimeSlots[0].startTime;
    params.endTime = selectedTimeSlots[0].endTime;
  } else {
    // 多个时间段
    params.timeSlots = selectedTimeSlots;
  }
  
  try {
    const result = await getFlexibleCounselors(params);
    displayCounselors(result.data);
  } catch (error) {
    console.error('搜索失败:', error);
  }
}
```

## ⚠️ 注意事项

### 1. 多时间段筛选逻辑
- **取交集**: 只返回在**所有选择时间段都可用**的咨询师
- **推荐度**: 基于所有时间段的综合可用性计算
- **时间冲突**: 自动处理重叠时间段

### 2. 性能考虑
- 建议单次查询的时间段数量不超过5个
- 大量时间段查询可能影响响应速度
- 前端可以添加loading状态

### 3. 用户体验
- 清楚显示选择了多少个时间段
- 提示用户多时间段筛选的含义（取交集）
- 当没有咨询师满足所有时间段时，给出友好提示

## 🚀 推荐使用方式

**最佳实践**: 使用 `POST /miniapp/timeSlot/counselors/flexible` 接口

**优势**:
- 一个接口支持单个和多个时间段
- 响应格式统一且信息丰富
- 自动识别筛选类型
- 便于前端统一处理

现在你的小程序支持用户选择多个时间段来筛选咨询师了！🎉
