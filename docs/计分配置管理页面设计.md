# 计分配置管理页面设计

## 🎯 新的配置化计分系统优势

### ✅ **解决的问题**：
1. **无需修改代码** - 新增量表只需在后台配置
2. **灵活配置** - 支持多种计分方法的组合
3. **可视化管理** - 后台界面直观配置
4. **向后兼容** - 保持现有硬编码计分的兼容性
5. **测试验证** - 配置前可以测试验证

### 🏗️ **系统架构**：

```
前端管理界面
    ↓
配置化计分控制器
    ↓
配置化计分服务
    ↓
数据库配置表
    ↓
动态计分执行
```

## 📋 前端管理页面功能

### 1. **量表计分配置列表页**
- 显示所有量表的计分配置状态
- 支持搜索、筛选、排序
- 显示计分方法、配置状态等信息

### 2. **计分配置编辑页**
- 选择计分方法（下拉选择）
- 配置反向计分题目
- 设置标准分转换系数
- 配置基础分数
- 设置维度数量

### 3. **配置测试页**
- 输入测试数据
- 预览计分结果
- 验证配置正确性

## 🔧 配置项说明

### **基础配置**
```json
{
  "scoring_method": "STANDARD_SCORE",           // 计分方法
  "has_reverse_items": 1,                       // 是否有反向题
  "has_standard_score": 1,                      // 是否需要标准分
  "standard_score_multiplier": 1.25,           // 标准分系数
  "dimension_count": 1,                         // 维度数量
  "raw_score_range": "20-80",                   // 原始分范围
  "standard_score_range": "25-100"              // 标准分范围
}
```

### **反向计分配置**
```json
{
  "reverse_questions": [5, 9, 13, 17, 19],     // 反向计分题目
  "reverse_max_value": 4                        // 反向计分最大值
}
```

### **公式计分配置**
```json
{
  "base_score": 18,                             // 基础分数
  "formula_config": {
    "type": "BASE_PLUS_SUM",                    // 公式类型
    "base_score_per_dimension": 18              // 每维度基础分
  }
}
```

## 🎨 前端页面结构

### **1. 计分配置管理主页**
```html
<!-- 搜索区域 -->
<el-form :model="queryParams" ref="queryForm" :inline="true">
  <el-form-item label="量表编码" prop="scaleCode">
    <el-input v-model="queryParams.scaleCode" placeholder="请输入量表编码" />
  </el-form-item>
  <el-form-item label="计分方法" prop="scoringMethod">
    <el-select v-model="queryParams.scoringMethod" placeholder="请选择计分方法">
      <el-option label="简单求和" value="SIMPLE_SUM" />
      <el-option label="反向计分" value="REVERSE_SCORING" />
      <el-option label="公式计分" value="FORMULA_WITH_BASE" />
      <el-option label="标准分转换" value="STANDARD_SCORE" />
    </el-select>
  </el-form-item>
  <el-form-item>
    <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
    <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
  </el-form-item>
</el-form>

<!-- 操作按钮 -->
<el-row :gutter="10" class="mb8">
  <el-col :span="1.5">
    <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增配置</el-button>
  </el-col>
  <el-col :span="1.5">
    <el-button type="success" icon="el-icon-edit" @click="handleBatchConfig">批量配置</el-button>
  </el-col>
</el-row>

<!-- 数据表格 -->
<el-table v-loading="loading" :data="configList">
  <el-table-column label="量表编码" prop="scaleCode" />
  <el-table-column label="量表名称" prop="scaleName" />
  <el-table-column label="计分方法" prop="scoringMethodDesc" />
  <el-table-column label="配置状态">
    <template slot-scope="scope">
      <el-tag v-if="scope.row.status === 1" type="success">已配置</el-tag>
      <el-tag v-else type="info">未配置</el-tag>
    </template>
  </el-table-column>
  <el-table-column label="操作" class-name="small-padding fixed-width">
    <template slot-scope="scope">
      <el-button size="mini" type="text" @click="handleUpdate(scope.row)">编辑</el-button>
      <el-button size="mini" type="text" @click="handleTest(scope.row)">测试</el-button>
      <el-button size="mini" type="text" @click="handleCopy(scope.row)">复制</el-button>
      <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
    </template>
  </el-table-column>
</el-table>
```

### **2. 计分配置编辑对话框**
```html
<el-dialog title="计分配置" :visible.sync="open" width="800px">
  <el-form ref="form" :model="form" :rules="rules" label-width="120px">
    <!-- 基础配置 -->
    <el-form-item label="计分方法" prop="scoringMethod">
      <el-select v-model="form.scoringMethod" @change="handleScoringMethodChange">
        <el-option v-for="(desc, method) in scoringMethods" 
                   :key="method" :label="desc" :value="method" />
      </el-select>
    </el-form-item>
    
    <!-- 反向计分配置 -->
    <el-form-item label="反向计分" v-if="showReverseConfig">
      <el-switch v-model="form.hasReverseItems" @change="handleReverseChange" />
    </el-form-item>
    
    <el-form-item label="反向计分题目" v-if="form.hasReverseItems">
      <el-input v-model="form.reverseQuestions" 
                placeholder="请输入题目编号，用逗号分隔，如：1,2,5,8" />
    </el-form-item>
    
    <el-form-item label="反向计分最大值" v-if="form.hasReverseItems">
      <el-input-number v-model="form.reverseMaxValue" :min="2" :max="10" />
    </el-form-item>
    
    <!-- 标准分配置 -->
    <el-form-item label="标准分转换" v-if="showStandardConfig">
      <el-switch v-model="form.hasStandardScore" @change="handleStandardChange" />
    </el-form-item>
    
    <el-form-item label="转换系数" v-if="form.hasStandardScore">
      <el-input-number v-model="form.standardScoreMultiplier" 
                       :precision="2" :step="0.01" :min="0.1" :max="10" />
    </el-form-item>
    
    <!-- 公式计分配置 -->
    <el-form-item label="基础分数" v-if="showFormulaConfig">
      <el-input-number v-model="form.baseScore" :min="0" :max="100" />
    </el-form-item>
    
    <!-- 其他配置 -->
    <el-form-item label="维度数量">
      <el-input-number v-model="form.dimensionCount" :min="1" :max="10" />
    </el-form-item>
    
    <el-form-item label="分数范围">
      <el-col :span="11">
        <el-input v-model="form.rawScoreRange" placeholder="原始分范围，如：20-80" />
      </el-col>
      <el-col :span="2" class="text-center">-</el-col>
      <el-col :span="11">
        <el-input v-model="form.standardScoreRange" placeholder="标准分范围，如：25-100" />
      </el-col>
    </el-form-item>
    
    <el-form-item label="配置说明">
      <el-input v-model="form.description" type="textarea" :rows="3" />
    </el-form-item>
  </el-form>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="cancel">取 消</el-button>
    <el-button type="primary" @click="handleTest">测试配置</el-button>
    <el-button type="primary" @click="submitForm">确 定</el-button>
  </div>
</el-dialog>
```

### **3. 配置测试对话框**
```html
<el-dialog title="配置测试" :visible.sync="testOpen" width="600px">
  <el-form ref="testForm" :model="testForm" label-width="120px">
    <el-form-item label="测试数据">
      <el-input v-model="testForm.testData" type="textarea" :rows="5"
                placeholder="请输入测试数据，JSON格式" />
    </el-form-item>
  </el-form>
  
  <el-divider content-position="left">测试结果</el-divider>
  
  <div v-if="testResult">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="计分方法">{{ testResult.scoringMethod }}</el-descriptions-item>
      <el-descriptions-item label="总分">{{ testResult.totalScore }}</el-descriptions-item>
      <el-descriptions-item label="原始分" v-if="testResult.rawScore">{{ testResult.rawScore }}</el-descriptions-item>
      <el-descriptions-item label="标准分" v-if="testResult.standardScore">{{ testResult.standardScore }}</el-descriptions-item>
    </el-descriptions>
    
    <div v-if="testResult.dimensionScores" class="mt-4">
      <h4>维度分数</h4>
      <el-table :data="dimensionScoreList" size="mini">
        <el-table-column prop="dimension" label="维度" />
        <el-table-column prop="score" label="分数" />
      </el-table>
    </div>
  </div>
  
  <div slot="footer" class="dialog-footer">
    <el-button @click="testOpen = false">关 闭</el-button>
    <el-button type="primary" @click="executeTest">执行测试</el-button>
  </div>
</el-dialog>
```

## 📊 API接口设计

### **配置管理接口**
```javascript
// 获取计分配置
GET /system/scoring-config/{scaleId}

// 保存计分配置
PUT /system/scoring-config/{scaleId}

// 获取支持的计分方法
GET /system/scoring-config/methods

// 验证配置
POST /system/scoring-config/validate

// 测试配置
POST /system/scoring-config/test/{scaleId}

// 生成规则说明
GET /system/scoring-config/description/{scaleId}

// 执行配置化计分
POST /system/scoring-config/execute/{recordId}
```

## 🚀 使用流程

### **管理员配置流程**：
1. 进入计分配置管理页面
2. 选择要配置的量表
3. 选择计分方法
4. 配置相关参数
5. 测试配置正确性
6. 保存配置

### **系统计分流程**：
1. 用户完成测评
2. 系统查找量表配置
3. 如果有配置，使用配置化计分
4. 如果无配置，使用默认计分
5. 生成测评报告

## 🎯 配置示例

### **SAS量表配置示例**：
```json
{
  "scoring_method": "STANDARD_SCORE",
  "has_reverse_items": 1,
  "reverse_questions": [5, 9, 13, 17, 19],
  "reverse_max_value": 4,
  "has_standard_score": 1,
  "standard_score_multiplier": 1.25,
  "dimension_count": 1,
  "raw_score_range": "20-80",
  "standard_score_range": "25-100",
  "description": "SAS焦虑自评量表：部分题目反向计分，然后进行标准分转换"
}
```

### **PRCA-24量表配置示例**：
```json
{
  "scoring_method": "FORMULA_WITH_BASE",
  "has_reverse_items": 0,
  "has_standard_score": 0,
  "base_score": 18,
  "dimension_count": 4,
  "raw_score_range": "24-120",
  "standard_score_range": "24-120",
  "description": "PRCA-24沟通焦虑量表：每个维度基础分18分加上题目分数"
}
```

这样的配置化系统让您可以：
- ✅ **无需修改代码**就能添加新量表
- ✅ **灵活配置**各种计分规则
- ✅ **可视化管理**所有量表配置
- ✅ **测试验证**确保配置正确
- ✅ **向后兼容**保持现有功能
