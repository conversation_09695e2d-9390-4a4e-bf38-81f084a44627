-- 检查咨询师排班和时间槽生成问题

-- ==================== 1. 检查咨询师4的基本信息 ====================
SELECT id, name, work_status, del_flag, create_time, update_time 
FROM psy_consultants 
WHERE id = 4;

-- ==================== 2. 检查咨询师4的排班记录 ====================
SELECT * FROM psy_time_counselor_schedule 
WHERE counselor_id = 4 
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
ORDER BY date_key;

-- ==================== 3. 检查咨询师4的所有排班记录 ====================
SELECT counselor_id, date_key, is_working, start_time, end_time, del_flag
FROM psy_time_counselor_schedule 
WHERE counselor_id = 4 
  AND del_flag = '0'
ORDER BY date_key DESC
LIMIT 10;

-- ==================== 4. 检查是否有任何咨询师的排班记录 ====================
SELECT counselor_id, COUNT(*) as schedule_count
FROM psy_time_counselor_schedule 
WHERE del_flag = '0'
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
GROUP BY counselor_id
ORDER BY counselor_id;

-- ==================== 5. 检查咨询师4的时间槽记录（已删除的） ====================
SELECT date_key, COUNT(*) as slot_count, 
       SUM(CASE WHEN del_flag = '0' THEN 1 ELSE 0 END) as active_slots,
       SUM(CASE WHEN del_flag = '1' THEN 1 ELSE 0 END) as deleted_slots
FROM psy_time_slot 
WHERE counselor_id = 4 
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
GROUP BY date_key
ORDER BY date_key;

-- ==================== 6. 检查系统中是否有默认排班模板 ====================
SELECT * FROM psy_time_counselor_schedule 
WHERE counselor_id = 4 
  AND del_flag = '0'
ORDER BY date_key DESC
LIMIT 5;

-- ==================== 7. 为咨询师4创建排班记录（如果没有的话） ====================
-- 先检查是否需要创建排班记录
SELECT 
    '需要为咨询师4创建排班记录' as message,
    COUNT(*) as existing_schedules
FROM psy_time_counselor_schedule 
WHERE counselor_id = 4 
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0';

-- 如果上面的查询结果为0，则执行以下插入语句：

-- 为咨询师4创建一周的排班记录（工作日）
INSERT INTO psy_time_counselor_schedule 
(counselor_id, date_key, day_of_week, is_working, start_time, end_time, del_flag, create_by, create_time)
VALUES 
-- 2025-07-26 (周六) - 休息
(4, '2025-07-26', 6, 0, NULL, NULL, '0', 'system', NOW()),
-- 2025-07-27 (周日) - 休息  
(4, '2025-07-27', 0, 0, NULL, NULL, '0', 'system', NOW()),
-- 2025-07-28 (周一) - 工作
(4, '2025-07-28', 1, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
-- 2025-07-29 (周二) - 工作
(4, '2025-07-29', 2, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
-- 2025-07-30 (周三) - 工作
(4, '2025-07-30', 3, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
-- 2025-07-31 (周四) - 工作
(4, '2025-07-31', 4, 1, '09:00:00', '17:00:00', '0', 'system', NOW()),
-- 2025-08-01 (周五) - 工作
(4, '2025-08-01', 5, 1, '09:00:00', '17:00:00', '0', 'system', NOW());

-- ==================== 8. 验证排班记录创建成功 ====================
SELECT counselor_id, date_key, day_of_week, is_working, start_time, end_time
FROM psy_time_counselor_schedule 
WHERE counselor_id = 4 
  AND date_key BETWEEN '2025-07-26' AND '2025-08-01'
  AND del_flag = '0'
ORDER BY date_key;

-- ==================== 9. 检查其他咨询师的排班情况作为参考 ====================
SELECT counselor_id, COUNT(*) as schedule_count,
       MIN(date_key) as earliest_date,
       MAX(date_key) as latest_date
FROM psy_time_counselor_schedule 
WHERE del_flag = '0'
GROUP BY counselor_id
ORDER BY counselor_id
LIMIT 5;
