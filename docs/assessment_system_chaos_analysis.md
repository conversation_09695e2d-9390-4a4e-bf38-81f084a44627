# 测评系统混乱问题分析报告

## 🚨 核心问题：两套测评体系并存

系统中存在两套完全不同的心理测评体系，导致严重的混乱和冲突。

## 📋 问题详细分析

### 1. 两套实体类体系

#### 第一套：PsyAssessment系列（旧版本）
```
PsyAssessmentScale      → 映射到 psy_t_scale 表
PsyAssessmentQuestion   → 映射到 psy_t_question 表  
PsyAssessmentAnswer     → 映射到 psy_t_answer_record 表
PsyAssessmentRecord     → 映射到 psy_t_assessment_record 表
PsyAssessmentOrder      → 映射到 psy_t_assessment_order 表
```

#### 第二套：PsyT系列（新版本）
```
PsyTScale              → 映射到 psy_t_scale 表
PsyTQuestion           → 映射到 psy_t_question 表
PsyTAnswerRecord       → 映射到 psy_t_answer_record 表
PsyTAssessmentRecord   → 映射到 psy_t_assessment_record 表
PsyTEnterprise         → 映射到 psy_t_enterprise 表
```

### 2. 两套XML映射文件

#### 第一套：PsyAssessment系列
```
PsyAssessmentScaleMapper.xml
PsyAssessmentQuestionMapper.xml
PsyAssessmentAnswerMapper.xml
PsyAssessmentRecordMapper.xml
PsyAssessmentOrderMapper.xml
```

#### 第二套：PsyT系列
```
PsyTScaleMapper.xml
PsyTQuestionMapper.xml
PsyTAnswerRecordMapper.xml
PsyTAssessmentRecordMapper.xml
```

### 3. 字段映射冲突

#### PsyAssessmentScale vs PsyTScale
| 字段功能 | PsyAssessmentScale | PsyTScale | 数据库实际字段 | 冲突状态 |
|----------|-------------------|-----------|----------------|----------|
| 量表名称 | name | name | name | ✅ 一致 |
| 量表编码 | code | code | code | ✅ 一致 |
| 分类ID | categoryId (Integer) | - | category_id | ❌ 类型不一致 |
| 量表描述 | description | description | description | ✅ 一致 |
| 量表介绍 | introduction | introduction | introduction | ✅ 一致 |
| 作者 | - | author | - | ❌ 数据库无此字段 |
| 版本 | - | version | - | ❌ 数据库无此字段 |
| 题目数量 | questionCount | - | question_count | ❌ 映射不一致 |
| 时间限制 | timeLimit | timeLimit | duration | ❌ 字段名不匹配 |
| 价格 | price | originalPrice/currentPrice | price | ❌ 字段设计不一致 |

#### PsyAssessmentAnswer vs PsyTAnswerRecord
| 字段功能 | PsyAssessmentAnswer | PsyTAnswerRecord | 数据库实际字段 | 冲突状态 |
|----------|-------------------|------------------|----------------|----------|
| 答案内容 | answerText | answerContent | answer_content | ❌ 属性名不一致 |
| 得分 | score | answerScore | answer_score | ❌ 属性名不一致 |
| 答题时间 | answerTime | answerTime | answer_time | ✅ 一致 |
| 耗时 | timeSpent | responseTime | response_time | ❌ 属性名不一致 |

### 4. 数据库表结构与实体类不匹配

#### psy_t_scale 表的实际结构
```sql
-- 数据库实际字段
id, name, code, category_id, description, introduction,
test_notice, test_purpose, test_object, test_preparation,
test_processing, test_attention, test_theory, test_application,
reference_literature, question_count, scoring_type, duration,
norm_mean, norm_sd, applicable_age, image_url, price, pay_mode,
pay_phase, free_vip_level, free_report_level, paid_report_level,
enterprise_id, status, sort, search_keywords, search_count,
view_count, del_flag, create_by, create_time, update_by, update_time, remark
```

#### PsyAssessmentScale 缺失的字段
```
❌ test_notice, test_purpose, test_object, test_preparation
❌ test_processing, test_attention, test_theory, test_application  
❌ reference_literature, scoring_type, norm_mean, norm_sd
❌ pay_mode, pay_phase, free_vip_level, free_report_level
❌ paid_report_level, enterprise_id, sort
```

#### PsyTScale 多余的字段
```
❌ author, version (数据库中不存在)
❌ alias, instruction (与introduction重复)
❌ originalPrice, currentPrice (数据库只有price)
```

### 5. XML映射错误

#### PsyAssessmentAnswerMapper.xml 错误
```xml
<!-- ❌ 错误的字段映射 -->
<result property="answerText" column="answer_text"/>    <!-- 应该是 answer_content -->
<result property="score" column="score"/>              <!-- 应该是 answer_score -->
<result property="timeSpent" column="time_spent"/>      <!-- 应该是 response_time -->
```

#### PsyTAnswerRecordMapper.xml 错误
```xml
<!-- ❌ 错误的表名 -->
FROM psy_t_answer_record_record a    <!-- 应该是 psy_t_answer_record -->
```

### 6. 服务层混乱

#### 同一个表有两套服务
```
PsyAssessmentScaleService     → 操作 psy_t_scale 表
PsyTScaleService             → 操作 psy_t_scale 表
```

#### 字段映射不一致
```java
// PsyAssessmentScaleServiceImpl
dto.setScaleName(scale.getName());           // ✅ 正确
dto.setCategoryId(scale.getCategoryId());    // ❌ 类型不匹配

// 缺少新字段的映射
// ❌ 没有映射 test_notice, test_purpose 等字段
```

## 🎯 解决方案建议

### 方案一：统一到 PsyT 系列（推荐）
1. **删除 PsyAssessment 系列**的所有文件
2. **完善 PsyT 系列**的字段映射
3. **修复所有 XML 映射错误**
4. **统一服务层接口**

### 方案二：统一到 PsyAssessment 系列
1. **删除 PsyT 系列**的所有文件
2. **完善 PsyAssessment 系列**的字段映射
3. **添加缺失的字段**
4. **修复所有映射错误**

## 🚨 当前系统风险

1. **数据不一致**：两套系统可能产生不同的数据
2. **功能冲突**：同一功能有两套实现
3. **维护困难**：修改需要同时维护两套代码
4. **性能问题**：重复的映射和查询
5. **业务混乱**：开发人员不知道使用哪套API

## 📋 紧急修复清单

### 立即需要修复的问题
1. **PsyAssessmentAnswerMapper.xml** 字段映射错误
2. **PsyTAnswerRecordMapper.xml** 表名错误
3. **PsyAssessmentScale** 缺失字段映射
4. **PsyTScale** 多余字段清理
5. **服务层字段映射统一**

### 中期整合计划
1. **确定主要体系**（建议使用 PsyT 系列）
2. **迁移现有数据**
3. **删除冗余文件**
4. **统一API接口**
5. **更新前端调用**

## 🎉 整合后的预期效果

1. **单一数据源**：只有一套实体类和映射
2. **字段完整**：所有数据库字段都有对应映射
3. **类型正确**：字段类型与数据库完全匹配
4. **性能提升**：减少冗余查询和映射
5. **维护简单**：只需维护一套代码

## ⚠️ 风险提示

在整合过程中需要注意：
1. **备份现有数据**
2. **逐步迁移，避免中断服务**
3. **充分测试所有功能**
4. **更新相关文档**
5. **通知前端开发人员API变更**
