# 咨询师到店时间过滤功能完整指南

## 🎯 功能概述

实现了基于咨询师到店时间的智能预约过滤系统，每个咨询师可以独立控制自己的到店时间设置，系统会自动过滤掉咨询师来不及到店的时间段。

### 核心特性
- ✅ **个人控制**：每个咨询师独立设置到店时间和过滤开关
- ✅ **智能过滤**：自动过滤来不及到店的时间段
- ✅ **线上线下区分**：线上咨询不受到店时间限制
- ✅ **实时生效**：设置修改后立即生效
- ✅ **多中心支持**：不同咨询中心可以有不同配置

## 🏗️ 系统架构

### 数据库设计
```sql
-- 咨询师配置表
CREATE TABLE psy_consultant_center_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    consultant_id BIGINT NOT NULL COMMENT '咨询师ID',
    center_id BIGINT NOT NULL COMMENT '咨询中心ID',
    arrival_time_hours DECIMAL(3,1) DEFAULT 2.0 COMMENT '到店时间(小时)',
    enable_arrival_filter TINYINT(1) DEFAULT 1 COMMENT '是否启用过滤',
    status TINYINT(1) DEFAULT 1 COMMENT '状态',
    UNIQUE KEY uk_consultant_center (consultant_id, center_id)
);

-- 系统配置
INSERT INTO sys_config VALUES 
('咨询师默认到店时间', 'psy.consultant.default.arrival.hours', '2.0', 'Y'),
('新咨询师默认启用过滤', 'psy.consultant.default.arrival.filter.enabled', 'true', 'Y');
```

### 核心服务类
```java
// 时间槽过滤服务
IPsyTimeSlotFilterService
├── filterTimeSlotsByArrivalTime() // 过滤时间槽
├── getConsultantArrivalTime()     // 获取到店时间
├── isArrivalFilterEnabled()       // 检查是否启用过滤
└── getEarliestOfflineAppointmentTime() // 计算最早预约时间

// 咨询师配置服务
IPsyConsultantCenterConfigService
├── setArrivalTime()              // 设置到店时间
├── setArrivalFilterEnabled()     // 设置过滤开关
├── getArrivalTime()              // 获取到店时间
└── isArrivalFilterEnabled()      // 检查过滤状态
```

## 📱 API接口文档

### 1. 咨询师个人设置接口

#### 获取个人配置
```http
GET /miniapp/consultant/settings/{consultantId}/center/{centerId}

Response:
{
  "code": 200,
  "data": {
    "config": {
      "arrivalTimeHours": 2.0,
      "enableArrivalFilter": 1
    },
    "isEnabled": true,
    "arrivalTimeHours": 2.0
  }
}
```

#### 更新到店时间
```http
PUT /miniapp/consultant/settings/{consultantId}/center/{centerId}/arrivalTime
Content-Type: application/x-www-form-urlencoded

hours=1.5

Response:
{
  "code": 200,
  "msg": "到店时间已更新为 1.5 小时"
}
```

#### 切换过滤开关
```http
PUT /miniapp/consultant/settings/{consultantId}/center/{centerId}/toggleFilter
Content-Type: application/x-www-form-urlencoded

enabled=false

Response:
{
  "code": 200,
  "msg": "已禁用到店时间过滤功能，客户可以预约任何时间段的线下咨询",
  "data": {
    "enabled": false,
    "arrivalTimeHours": 2.0
  }
}
```

#### 重置为默认设置
```http
POST /miniapp/consultant/settings/{consultantId}/center/{centerId}/reset

Response:
{
  "code": 200,
  "msg": "已重置为默认设置",
  "data": {
    "arrivalTimeHours": 2.0,
    "enableArrivalFilter": 1
  }
}
```

### 2. 客户端查询接口

#### 获取过滤后的时间槽
```http
GET /miniapp/timeSlot/filtered?startDate=2025-07-18&endDate=2025-07-25&consultantId=1&consultationType=2

Response:
{
  "code": 200,
  "data": {
    "timeSlots": [...],
    "consultationType": 2,
    "consultantId": 1,
    "filteredCount": 15,
    "earliestOfflineTime": "2025-07-18T12:00:00",
    "arrivalTimeHours": 2.0
  }
}
```

#### 获取可用咨询师（支持过滤）
```http
GET /miniapp/timeSlot/counselors?date=2025-07-18&startTime=11:00&endTime=12:00&consultationType=2

Response:
{
  "code": 200,
  "data": {
    "counselors": [
      {
        "id": 1,
        "name": "张医生",
        "arrivalTimeHours": 2.0,
        "earliestOfflineTime": "2025-07-18T12:00:00"
      }
    ],
    "consultationType": 2,
    "totalCount": 1
  }
}
```

### 3. 管理后台接口

#### 查看咨询师配置
```http
GET /system/consultant/config/list

Response:
{
  "code": 200,
  "rows": [
    {
      "consultantId": 1,
      "centerId": 1,
      "arrivalTimeHours": 2.0,
      "enableArrivalFilter": 1
    }
  ]
}
```

#### 批量创建默认配置
```http
POST /system/consultant/config/batchCreateDefault/1
Content-Type: application/json

[1, 2, 3, 4, 5]

Response:
{
  "code": 200,
  "msg": "成功为 5 个咨询师创建默认配置"
}
```

## 🎮 使用场景示例

### 场景1：住得远的咨询师，不想限制客户
```javascript
// 咨询师设置：3小时到店，但禁用过滤
PUT /miniapp/consultant/settings/1/center/1/arrivalTime?hours=3.0
PUT /miniapp/consultant/settings/1/center/1/toggleFilter?enabled=false

// 客户查询（当前时间10:00，预约11:00）
GET /miniapp/timeSlot/counselors?consultantId=1&consultationType=2&startTime=11:00

// 结果：显示该咨询师（虽然来不及，但咨询师选择不限制）
{
  "counselors": [
    {
      "id": 1,
      "name": "张医生",
      "arrivalTimeHours": 3.0,
      "filterEnabled": false,
      "note": "该咨询师已禁用到店时间过滤"
    }
  ]
}
```

### 场景2：住得近的咨询师，启用过滤
```javascript
// 咨询师设置：0.5小时到店，启用过滤
PUT /miniapp/consultant/settings/2/center/1/arrivalTime?hours=0.5
PUT /miniapp/consultant/settings/2/center/1/toggleFilter?enabled=true

// 客户查询（当前时间10:00，预约10:30）
GET /miniapp/timeSlot/counselors?consultantId=2&consultationType=2&startTime=10:30

// 结果：显示该咨询师（0.5小时能到店）
{
  "counselors": [
    {
      "id": 2,
      "name": "李医生",
      "arrivalTimeHours": 0.5,
      "filterEnabled": true,
      "earliestOfflineTime": "2025-07-18T10:30:00"
    }
  ]
}
```

### 场景3：线上咨询不受限制
```javascript
// 任何咨询师的线上咨询都不受到店时间限制
GET /miniapp/timeSlot/counselors?consultantId=1&consultationType=1&startTime=10:30

// 结果：显示所有在线咨询师
{
  "counselors": [
    {
      "id": 1,
      "name": "张医生",
      "consultationType": "线上咨询",
      "note": "线上咨询不受到店时间限制"
    }
  ]
}
```

## 🔧 核心算法

### 过滤逻辑
```java
// 1. 检查是否启用过滤
if (!isArrivalFilterEnabled(consultantId, centerId)) {
    return timeSlots; // 不过滤，返回所有时间段
}

// 2. 获取到店时间
Double arrivalTimeHours = getConsultantArrivalTime(consultantId, centerId);

// 3. 计算最早可预约时间
LocalDateTime earliestTime = currentTime.plusMinutes((long)(arrivalTimeHours * 60));

// 4. 过滤时间槽
return timeSlots.stream()
    .filter(slot -> {
        LocalDate slotDate = LocalDate.parse(slot.getDateKey());
        LocalDateTime slotDateTime = LocalDateTime.of(slotDate, slot.getStartTime());
        return slotDateTime.isAfter(earliestTime) || slotDateTime.isEqual(earliestTime);
    })
    .collect(Collectors.toList());
```

## 🎨 前端界面设计

### 咨询师设置页面
```html
<div class="consultant-settings">
  <div class="setting-card">
    <h3>到店时间设置</h3>
    
    <div class="form-group">
      <label>到店所需时间</label>
      <div class="input-group">
        <input type="number" v-model="arrivalHours" step="0.5" min="0" max="24">
        <span class="unit">小时</span>
      </div>
      <div class="help-text">设置您从当前位置到咨询中心所需的时间</div>
    </div>
    
    <div class="form-group">
      <label>启用到店时间过滤</label>
      <el-switch v-model="filterEnabled" @change="toggleFilter"></el-switch>
      <div class="help-text">
        启用后，客户只能预约您能及时到店的时间段；
        禁用后，客户可以预约任何时间段
      </div>
    </div>
    
    <div class="preview-section" v-if="filterEnabled">
      <h4>预览效果</h4>
      <p>当前时间：{{ currentTime }}</p>
      <p>最早可预约线下咨询：{{ earliestTime }}</p>
      <p class="warning">{{ arrivalHours }}小时内的时间段将被过滤</p>
    </div>
    
    <div class="action-buttons">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
      <el-button @click="resetDefault">重置默认</el-button>
    </div>
  </div>
</div>
```

### 客户端显示
```html
<div class="counselor-list">
  <div class="counselor-item" v-for="counselor in counselors" :key="counselor.id">
    <div class="counselor-info">
      <h4>{{ counselor.name }}</h4>
      <div class="availability-info">
        <span v-if="!counselor.filterEnabled" class="tag no-limit">不限时间</span>
        <span v-else class="tag limited">{{ counselor.arrivalTimeHours }}h到店</span>
        
        <div class="time-info">
          <span v-if="!counselor.filterEnabled">该咨询师接受任何时间段的预约</span>
          <span v-else>最早可预约：{{ counselor.earliestOfflineTime }}</span>
        </div>
      </div>
    </div>
  </div>
</div>
```

## 📊 数据统计

### 配置分布统计
```sql
-- 启用/禁用过滤的咨询师分布
SELECT 
    CASE enable_arrival_filter 
        WHEN 1 THEN '已启用' 
        WHEN 0 THEN '已禁用' 
    END as filter_status,
    COUNT(*) as count,
    ROUND(AVG(arrival_time_hours), 1) as avg_arrival_time
FROM psy_consultant_center_config 
WHERE status = 1 
GROUP BY enable_arrival_filter;

-- 到店时间分布
SELECT 
    CASE 
        WHEN arrival_time_hours <= 1 THEN '1小时内'
        WHEN arrival_time_hours <= 2 THEN '1-2小时'
        WHEN arrival_time_hours <= 3 THEN '2-3小时'
        ELSE '3小时以上'
    END as time_range,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM psy_consultant_center_config WHERE status = 1), 1) as percentage
FROM psy_consultant_center_config 
WHERE status = 1 
GROUP BY time_range
ORDER BY MIN(arrival_time_hours);
```

## 🚀 部署指南

### 1. 数据库更新
```bash
# 执行数据库脚本
mysql -u username -p database_name < sql/add_counselor_arrival_time.sql
```

### 2. 应用重启
```bash
# 重启Spring Boot应用
./restart.sh
```

### 3. 功能验证
```bash
# 测试咨询师设置接口
curl -X PUT "http://localhost:8080/miniapp/consultant/settings/1/center/1/toggleFilter?enabled=false"

# 测试客户查询接口
curl "http://localhost:8080/miniapp/timeSlot/counselors?consultantId=1&consultationType=2&date=2025-07-18&startTime=11:00"

# 测试管理后台接口
curl "http://localhost:8080/system/consultant/config/list"
```

## ✅ 测试用例

### 功能测试
- [x] 咨询师可以设置到店时间
- [x] 咨询师可以启用/禁用过滤
- [x] 线下咨询正确应用过滤
- [x] 线上咨询不受过滤影响
- [x] 设置修改后立即生效

### 边界测试
- [x] 到店时间为0（立即可用）
- [x] 到店时间很长（如8小时）
- [x] 跨日期预约处理
- [x] 无配置时的默认行为

### 性能测试
- [x] 大量时间槽过滤性能
- [x] 多咨询师并发查询
- [x] 数据库查询优化

## 🎯 优势总结

1. **个人自主权**：每个咨询师完全控制自己的设置
2. **业务合理性**：既保护咨询师时间，又给予灵活选择
3. **用户体验好**：客户看到的都是可行的预约时间
4. **技术可靠性**：实时生效，数据一致性强
5. **扩展性强**：支持多中心、多场景配置

这个功能完美解决了咨询师到店时间管理的痛点，实现了个性化、智能化的预约过滤系统！
