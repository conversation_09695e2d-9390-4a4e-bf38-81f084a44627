-- 为SAS和STAI测评量表补充详细的搜索关键字

-- ==================== 1. SAS焦虑自评量表关键字更新 ====================
UPDATE `psy_t_scale` SET 
    `search_keywords` = 'SAS,焦虑自评量表,Self-Rating Anxiety Scale,Zung焦虑量表,焦虑测试,焦虑评估,焦虑筛查,焦虑自测,心理测评,心理测试,情绪测评,免费测评,可测评,已发布,心理量表,焦虑症状,焦虑程度,焦虑水平,焦虑状态,焦虑病人,主观感受,疗效评估,治疗评估,心理咨询,心理医生,精神科,<PERSON><PERSON>量表,<PERSON>,1971年,20题,4级评分,LIKERT量表,5-10分钟,快速测试,简短测评,标准分转换,粗分计算,1.25系数,焦虑维度,情绪症状,认知症状,躯体症状,运动性反应,紧张不安,易怒恐惧,注意力不集中,过度担忧,心悸出汗,呼吸急促,胃肠不适,坐立不安,生物心理社会模式,BPS模型,神经递质,遗传易感性,人格特质,神经质,应对方式,工作压力,人际冲突,生活事件,焦虑筛查工具,情绪状态,心理咨询师,心理医生,精神科大夫,心理测量工具,成人测评,专业量表,临床评估,焦虑诊断,焦虑治疗,焦虑监测'
WHERE `id` = 1 AND `code` = 'SAS';

-- ==================== 2. STAI状态特质焦虑问卷关键字更新 ====================
UPDATE `psy_t_scale` SET 
    `search_keywords` = 'STAI,状态特质焦虑问卷,State-Trait Anxiety Inventory,Spielberger焦虑量表,状态焦虑,特质焦虑,S-AI,T-AI,焦虑测试,焦虑评估,焦虑筛查,心理测评,心理测试,情绪测评,免费测评,可测评,已发布,心理量表,焦虑区分,焦虑类型,短暂焦虑,持久焦虑,情绪状态,人格特质,焦虑倾向,焦虑体验,临床评估,行为评估,内科评估,Spielberger,Cattell,1966-1979,1983年,40题,LIKERT量表,10-20分钟,标准测评,状态焦虑量表,特质焦虑量表,不愉快情绪,紧张恐惧,忧虑神经质,植物神经,功能亢进,短暂性焦虑,稳定性焦虑,人格差异,个体差异,焦虑倾向,临床学家,行为学家,内科学家,研究工具,临床实践,初中文化,成人测评,专业量表,心理咨询,心理医生,精神科,焦虑诊断,焦虑治疗,焦虑研究,情绪体验,心理状态,焦虑水平,焦虑程度,焦虑监测,治疗评估,疗效评估'
WHERE `id` = 2 AND `code` = 'STAI';

-- ==================== 3. 为所有测评量表添加通用关键字（如果还没有的话） ====================
UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`search_keywords`, ''),
        IFNULL(`name`, ''),
        IFNULL(`code`, ''),
        IFNULL(`description`, ''),
        CASE 
            WHEN `pay_mode` = 0 THEN '免费测评,免费测试'
            WHEN `pay_mode` = 1 THEN '付费测评,专业测评'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '可测评,已发布'
            WHEN `status` = 0 THEN '未发布'
            ELSE ''
        END,
        '心理测评,心理测试,性格测试,情绪测评,心理量表',
        CASE 
            WHEN `question_count` <= 20 THEN '简短测评,快速测试'
            WHEN `question_count` BETWEEN 21 AND 50 THEN '标准测评'
            WHEN `question_count` > 50 THEN '详细测评,专业测评'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '' OR LENGTH(`search_keywords`) < 50;

-- ==================== 4. 添加更多专业测评量表的关键字（基于常见量表） ====================

-- 为可能存在的其他量表添加关键字
UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',抑郁测试,抑郁自评,SDS量表,抑郁症状,情绪低落,抑郁筛查,抑郁评估')
WHERE (`code` LIKE '%SDS%' OR `name` LIKE '%抑郁%') 
  AND `search_keywords` NOT LIKE '%抑郁测试%';

UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',MBTI,人格测试,性格分析,16型人格,性格类型,人格类型,职业性格,心理类型')
WHERE (`code` LIKE '%MBTI%' OR `name` LIKE '%MBTI%' OR `name` LIKE '%人格%') 
  AND `search_keywords` NOT LIKE '%MBTI%';

UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',SCL-90,症状自评,心理症状,精神症状,症状清单,90项症状,心理健康,精神健康')
WHERE (`code` LIKE '%SCL%' OR `name` LIKE '%症状%') 
  AND `search_keywords` NOT LIKE '%SCL-90%';

UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',PDQ-4,人格障碍,人格测评,人格诊断,人格筛查,人格问题')
WHERE (`code` LIKE '%PDQ%' OR `name` LIKE '%人格障碍%') 
  AND `search_keywords` NOT LIKE '%PDQ-4%';

UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',BAI,贝克焦虑,Beck焦虑,焦虑量表,焦虑症状,焦虑评估')
WHERE (`code` LIKE '%BAI%' OR `name` LIKE '%贝克%' OR `name` LIKE '%Beck%') 
  AND `search_keywords` NOT LIKE '%BAI%';

UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',FNE,负面评价,社交恐惧,社交焦虑,害怕评价,社交回避')
WHERE (`code` LIKE '%FNE%' OR `name` LIKE '%负面评价%' OR `name` LIKE '%社交%') 
  AND `search_keywords` NOT LIKE '%FNE%';

-- ==================== 5. 添加测评相关的功能性关键字 ====================
UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',在线测评,网上测试,心理自测,心理筛查,心理诊断,心理评估,情绪评估,性格评估,人格评估,心理健康,精神健康,心理状态,情绪状态,心理问题,情绪问题,心理咨询,心理治疗,心理医生,心理专家,心理科,精神科,临床心理,健康心理,发展心理,社会心理')
WHERE LENGTH(IFNULL(`search_keywords`, '')) < 200;

-- ==================== 6. 添加适用人群和使用场景关键字 ====================
UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',成人测评,青少年测评,学生测评,职场测评,员工测评,求职测评,婚前测评,产前测评,老年测评,康复测评,治疗前测评,治疗后测评,疗效评估,预后评估,风险评估,健康体检,心理体检,入职体检,升学体检,兵检心理,公务员心理,教师心理,医护心理,企业心理,学校心理,医院心理,社区心理')
WHERE LENGTH(IFNULL(`search_keywords`, '')) < 300;

-- ==================== 7. 清理多余的逗号和重复内容 ====================
UPDATE `psy_t_scale` SET 
    `search_keywords` = TRIM(BOTH ',' FROM REPLACE(REPLACE(`search_keywords`, ',,', ','), ',,', ','))
WHERE `search_keywords` IS NOT NULL;

-- 移除开头和结尾的逗号
UPDATE `psy_t_scale` SET 
    `search_keywords` = TRIM(BOTH ',' FROM `search_keywords`)
WHERE `search_keywords` LIKE ',%' OR `search_keywords` LIKE '%,';

-- ==================== 8. 验证更新结果 ====================
-- 查看SAS和STAI的关键字更新结果
SELECT id, name, code, LEFT(search_keywords, 200) as keywords_preview 
FROM `psy_t_scale` 
WHERE `code` IN ('SAS', 'STAI') 
ORDER BY id;
