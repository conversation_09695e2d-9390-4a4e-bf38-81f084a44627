# 咨询师到店时间过滤功能实现文档

## 功能概述

实现了基于咨询师到店时间的智能时间槽过滤功能，解决了线下咨询预约中"咨询师来不及到店"的问题。

### 业务场景
- **问题**：咨询师排期10:00-15:00，但现在已经10:00了，咨询师需要2小时到店
- **解决方案**：10:00-12:00时间段自动过滤掉，只显示12:00之后的可选时间
- **线上咨询**：不受到店时间限制，所有时间段都可选

## 实现方案

### 1. 数据库设计

#### 方案A：咨询师主表添加字段
```sql
ALTER TABLE psy_consultants 
ADD COLUMN arrival_time_hours DECIMAL(3,1) DEFAULT 2.0 COMMENT '到店所需时间(小时)';
```

#### 方案B：独立配置表（推荐）
```sql
CREATE TABLE psy_consultant_center_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    consultant_id BIGINT NOT NULL COMMENT '咨询师ID',
    center_id BIGINT NOT NULL COMMENT '咨询中心ID',
    arrival_time_hours DECIMAL(3,1) DEFAULT 2.0 COMMENT '到该中心所需时间(小时)',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认配置',
    status TINYINT(1) DEFAULT 1 COMMENT '状态',
    UNIQUE KEY uk_consultant_center (consultant_id, center_id)
);
```

#### 系统配置
```sql
INSERT INTO sys_config VALUES 
('咨询师默认到店时间', 'psy.consultant.default.arrival.hours', '2.0', 'Y'),
('启用到店时间过滤', 'psy.consultant.arrival.filter.enabled', 'true', 'Y');
```

### 2. 核心服务类

#### IPsyTimeSlotFilterService - 时间槽过滤服务
```java
public interface IPsyTimeSlotFilterService {
    // 根据到店时间过滤时间槽
    List<PsyTimeSlot> filterTimeSlotsByArrivalTime(
        List<PsyTimeSlot> timeSlots, 
        Long consultantId, 
        Long centerId, 
        Integer consultationType,
        LocalDateTime currentTime
    );
    
    // 获取咨询师到店时间
    Double getConsultantArrivalTime(Long consultantId, Long centerId);
    
    // 计算最早可预约时间
    LocalDateTime getEarliestOfflineAppointmentTime(
        Long consultantId, Long centerId, LocalDateTime currentTime
    );
}
```

### 3. 过滤逻辑

#### 核心算法
```java
// 1. 获取咨询师到店时间（小时）
Double arrivalTimeHours = getConsultantArrivalTime(consultantId, centerId);

// 2. 计算最早可预约时间
LocalDateTime earliestTime = currentTime.plusMinutes((long)(arrivalTimeHours * 60));

// 3. 过滤时间槽
return timeSlots.stream()
    .filter(slot -> {
        LocalDateTime slotDateTime = LocalDateTime.of(slot.getSlotDate(), slot.getStartTime());
        return slotDateTime.isAfter(earliestTime) || slotDateTime.isEqual(earliestTime);
    })
    .collect(Collectors.toList());
```

## API接口更新

### 1. 获取过滤后的时间槽
```
GET /miniapp/timeSlot/filtered
参数：
- startDate: 开始日期
- endDate: 结束日期  
- centerId: 咨询中心ID
- consultantId: 咨询师ID（可选）
- consultationType: 咨询类型（1=线上 2=线下，默认2）

响应：
{
  "code": 200,
  "data": {
    "timeSlots": [...],
    "consultationType": 2,
    "consultantId": 1,
    "filteredCount": 15,
    "earliestOfflineTime": "2025-07-18T12:00:00",
    "arrivalTimeHours": 2.0
  }
}
```

### 2. 获取可用咨询师（支持到店时间过滤）
```
GET /miniapp/timeSlot/counselors
参数：
- date: 预约日期
- startTime: 开始时间
- endTime: 结束时间
- centerId: 咨询中心ID
- consultationType: 咨询类型（1=线上 2=线下，默认2）

响应：
{
  "code": 200,
  "data": {
    "counselors": [
      {
        "id": 1,
        "name": "张医生",
        "arrivalTimeHours": 2.0,
        "earliestOfflineTime": "2025-07-18T12:00:00"
      }
    ],
    "consultationType": 2,
    "totalCount": 3,
    "timeRange": "10:00-11:00"
  }
}
```

## 使用示例

### 场景1：线下咨询预约
```javascript
// 当前时间：2025-07-18 10:00
// 咨询师需要2小时到店
// 预约时间：2025-07-18 11:00

// 请求
GET /miniapp/timeSlot/counselors?date=2025-07-18&startTime=11:00&endTime=12:00&consultationType=2

// 响应：该时间段没有可用咨询师（因为11:00咨询师还没到店）
{
  "counselors": [],
  "totalCount": 0,
  "message": "该时间段咨询师来不及到店"
}

// 请求12:00之后的时间段
GET /miniapp/timeSlot/counselors?date=2025-07-18&startTime=12:00&endTime=13:00&consultationType=2

// 响应：有可用咨询师
{
  "counselors": [
    {
      "id": 1,
      "name": "张医生",
      "arrivalTimeHours": 2.0,
      "earliestOfflineTime": "2025-07-18T12:00:00"
    }
  ],
  "totalCount": 1
}
```

### 场景2：线上咨询预约
```javascript
// 线上咨询不受到店时间限制
GET /miniapp/timeSlot/counselors?date=2025-07-18&startTime=10:30&endTime=11:30&consultationType=1

// 响应：所有在线咨询师都可用
{
  "counselors": [
    {"id": 1, "name": "张医生"},
    {"id": 2, "name": "李医生"}
  ],
  "totalCount": 2
}
```

## 配置管理

### 1. 全局配置
```sql
-- 设置默认到店时间为1.5小时
UPDATE sys_config SET config_value = '1.5' 
WHERE config_key = 'psy.consultant.default.arrival.hours';

-- 禁用到店时间过滤
UPDATE sys_config SET config_value = 'false' 
WHERE config_key = 'psy.consultant.arrival.filter.enabled';
```

### 2. 咨询师个性化配置
```sql
-- 设置咨询师1到中心1需要3小时
INSERT INTO psy_consultant_center_config 
(consultant_id, center_id, arrival_time_hours) 
VALUES (1, 1, 3.0)
ON DUPLICATE KEY UPDATE arrival_time_hours = 3.0;
```

## 前端集成建议

### 1. 时间选择组件
```javascript
// 根据咨询类型动态过滤时间槽
function loadTimeSlots(consultationType) {
    const url = consultationType === 1 
        ? '/miniapp/timeSlot/available'  // 线上：显示所有可用时间
        : '/miniapp/timeSlot/filtered';  // 线下：过滤到店时间
        
    fetch(url + '?consultationType=' + consultationType)
        .then(response => response.json())
        .then(data => {
            renderTimeSlots(data.timeSlots);
            if (consultationType === 2 && data.earliestOfflineTime) {
                showArrivalTimeNotice(data.earliestOfflineTime);
            }
        });
}
```

### 2. 用户提示
```javascript
// 显示到店时间提示
function showArrivalTimeNotice(earliestTime) {
    const notice = `咨询师需要时间到店，最早可预约线下咨询时间为：${earliestTime}`;
    showToast(notice);
}
```

## 测试用例

### 1. 基础功能测试
- ✅ 线上咨询不受到店时间限制
- ✅ 线下咨询正确过滤来不及的时间段
- ✅ 默认配置生效
- ✅ 个性化配置覆盖默认配置

### 2. 边界条件测试
- ✅ 咨询师到店时间为0（立即可用）
- ✅ 咨询师到店时间很长（如8小时）
- ✅ 跨日期预约
- ✅ 配置不存在时的降级处理

### 3. 性能测试
- ✅ 批量过滤多个咨询师
- ✅ 大量时间槽的过滤性能
- ✅ 缓存机制（如需要）

## 部署步骤

1. **执行数据库脚本**
   ```bash
   mysql -u username -p database_name < sql/add_counselor_arrival_time.sql
   ```

2. **重启应用服务**
   ```bash
   # 重启Spring Boot应用以加载新的服务类
   ```

3. **验证功能**
   ```bash
   # 测试API接口
   curl "http://localhost:8080/miniapp/timeSlot/filtered?consultationType=2&consultantId=1"
   ```

## 注意事项

1. **兼容性**：新功能向后兼容，不影响现有接口
2. **性能**：过滤逻辑在内存中执行，性能影响很小
3. **配置**：支持全局和个性化配置，灵活性强
4. **扩展性**：可以轻松扩展支持更复杂的到店时间计算（如交通状况）

这个实现完美解决了您提到的"延后逻辑不对"的问题，现在系统会智能地根据咨询师到店时间过滤掉来不及的时间段！
