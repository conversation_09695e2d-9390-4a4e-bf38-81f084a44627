# 心理测评系统接口文档

## 概述

心理测评系统提供完整的心理量表测评功能，包括量表管理、测评流程、结果分析等。系统分为三个端：
- **后台管理端**：管理员使用，负责量表管理、数据统计等
- **小程序用户端**：普通用户使用，进行心理测评
- **小程序咨询师端**：咨询师使用，查看测评数据和分析

## 基础信息

- **基础URL**: `/api`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8
- **版本**: v2.0
- **更新时间**: 2025-01-21

## 通用响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

## 数据库表结构

### 主要表说明

| 表名 | 说明 | 备注 |
|------|------|------|
| psy_t_scale | 测评量表主表 | 存储量表基本信息 |
| psy_t_question | 测评题目表 | 存储量表题目 |
| psy_t_question_option | 题目选项表 | 存储题目选项 |
| psy_t_assessment_record | 测评记录表 | 存储用户测评记录 |
| psy_t_answer_record | 答题记录表 | 存储用户答题记录 |
| psy_t_subscale | 分量表表 | 存储量表的分量表信息 |
| psy_t_scoring_rule | 计分规则表 | 存储量表的计分规则 |
| psy_t_interpretation | 结果解释表 | 存储测评结果解释规则 |
| psy_t_assessment_order | 测评订单表 | 存储付费测评订单 |
| psy_t_enterprise | 企业信息表 | 存储企业客户信息 |

## 小程序用户端接口

### 1. 量表浏览接口

#### 1.1 查询启用的量表列表
```
GET /miniapp/user/assessment/scales
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页数量，默认10 |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "焦虑自评量表",
      "code": "SAS",
      "alias": "焦虑量表",
      "description": "用于评定焦虑状态的轻重程度",
      "instruction": "请根据您最近一周的实际感受进行选择",
      "author": "Zung",
      "version": "1.0",
      "questionCount": 20,
      "timeLimit": 1800,
      "coverImage": "https://example.com/cover.jpg",
      "tags": "焦虑,情绪,心理健康",
      "viewCount": 150,
      "testCount": 89,
      "ratingAvg": 4.5,
      "ratingCount": 25,
      "payMode": 0,
      "originalPrice": 0.00,
      "currentPrice": 0.00,
      "status": 1,
      "createTime": "2024-12-01 10:00:00"
    }
  ]
}
```

#### 1.2 查询热门量表
```
GET /miniapp/user/assessment/scales/hot
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | Integer | 否 | 限制数量，默认10 |

#### 1.3 查询最新量表
```
GET /miniapp/user/assessment/scales/latest
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | Integer | 否 | 限制数量，默认10 |

#### 1.4 根据分类查询量表
```
GET /miniapp/user/assessment/scales/category/{categoryId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | Integer | 是 | 分类ID |

#### 1.5 搜索量表
```
GET /miniapp/user/assessment/scales/search
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 否 | 搜索关键词 |
| categoryId | Integer | 否 | 分类ID |

#### 1.6 获取量表详情
```
GET /miniapp/user/assessment/scales/{id}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 量表ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "name": "焦虑自评量表",
    "code": "SAS",
    "alias": "焦虑量表",
    "description": "焦虑自评量表详细描述...",
    "instruction": "请根据您最近一周的实际感受进行选择...",
    "author": "Zung",
    "version": "1.0",
    "questionCount": 20,
    "timeLimit": 1800,
    "coverImage": "https://example.com/cover.jpg",
    "tags": "焦虑,情绪,心理健康",
    "searchKeywords": "焦虑 紧张 担心",
    "viewCount": 151,
    "testCount": 89,
    "ratingAvg": 4.5,
    "ratingCount": 25,
    "payMode": 0,
    "originalPrice": 0.00,
    "currentPrice": 0.00,
    "freeReportLevel": 1,
    "paidReportLevel": 3,
    "status": 1,
    "createTime": "2024-12-01 10:00:00",
    "updateTime": "2024-12-01 15:30:00"
  }
}
```

#### 1.7 查询用户收藏的量表
```
GET /miniapp/user/assessment/favorites/{userId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

#### 1.8 查询相似量表推荐
```
GET /miniapp/user/assessment/recommendations/{scaleId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 是 | 量表ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | Integer | 否 | 限制数量，默认5 |

### 2. 测评流程接口

#### 2.1 检查用户是否可以开始测评
```
GET /miniapp/user/assessment/check-can-start
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 是 | 量表ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "canAssess": true,
    "errors": null,
    "remainingTests": 5,
    "lastTestTime": "2024-12-01 10:00:00",
    "cooldownTime": 0
  }
}
```

#### 2.2 开始测评
```
POST /miniapp/user/assessment/start
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 是 | 量表ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": 123
}
```
> 返回测评记录ID

#### 2.3 获取测评题目
```
GET /miniapp/user/assessment/questions/{recordId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "scaleId": 1,
      "questionNo": 1,
      "questionText": "我觉得比平常容易紧张和着急",
      "questionType": "SINGLE",
      "isRequired": 1,
      "isReverse": 0,
      "reverseValue": null,
      "subscaleRef": "anxiety",
      "subscaleId": 1,
      "sort": 1,
      "orderNum": 1,
      "optionList": [
        {
          "id": 1,
          "questionId": 1,
          "optionText": "没有或很少时间",
          "optionValue": "A",
          "score": 1,
          "orderNum": 1
        },
        {
          "id": 2,
          "questionId": 1,
          "optionText": "小部分时间",
          "optionValue": "B",
          "score": 2,
          "orderNum": 2
        }
      ]
    }
  ]
}
```

#### 2.4 获取下一题
```
GET /miniapp/user/assessment/next-question/{recordId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "questionId": 2,
    "questionNo": 2,
    "questionText": "我无缘无故地感到害怕",
    "questionType": "SINGLE",
    "hasNext": true,
    "hasPrevious": true,
    "options": [...]
  }
}
```

#### 2.5 获取上一题
```
GET /miniapp/user/assessment/previous-question/{recordId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

#### 2.6 保存答题记录
```
POST /miniapp/user/assessment/answer
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |
| questionId | Long | 是 | 题目ID |
| optionId | Long | 否 | 选项ID（单选/多选题必填） |
| answerContent | String | 否 | 答案内容（文本题必填） |
| responseTime | Integer | 否 | 答题耗时（秒） |

**响应示例：**
```json
{
  "code": 200,
  "msg": "答案已保存",
  "data": null
}
```

#### 2.7 查询答题进度
```
GET /miniapp/user/assessment/progress/{recordId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "currentQuestionNo": 5,
    "totalQuestions": 20,
    "answeredQuestions": 4,
    "progress": 20.0,
    "timeElapsed": 300,
    "isCompleted": false
  }
}
```

#### 2.8 暂停测评
```
POST /miniapp/user/assessment/pause/{recordId}
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

#### 2.9 恢复测评
```
POST /miniapp/user/assessment/resume/{recordId}
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

#### 2.10 完成测评
```
POST /miniapp/user/assessment/complete/{recordId}
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "测评已完成",
  "data": null
}
```

#### 2.11 取消测评
```
POST /miniapp/user/assessment/cancel/{recordId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

### 3. 测评结果接口

#### 3.1 查询测评结果
```
GET /miniapp/user/assessment/result/{recordId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "recordId": 123,
    "scaleId": 1,
    "scaleName": "焦虑自评量表",
    "userId": 1001,
    "totalScore": 35.0,
    "maxScore": 80.0,
    "scorePercentage": 43.75,
    "resultLevel": "正常",
    "resultDescription": "您的焦虑水平在正常范围内",
    "suggestions": "继续保持良好的心理状态",
    "dimensionScores": {
      "焦虑": 12.0,
      "躯体": 15.0,
      "睡眠": 8.0
    },
    "duration": 900,
    "startTime": "2024-12-01 10:00:00",
    "endTime": "2024-12-01 10:15:00",
    "status": 2
  }
}
```

#### 3.2 生成测评报告
```
GET /miniapp/user/assessment/report/{recordId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "reportId": "RPT_20241201_001",
    "reportTitle": "焦虑自评量表测评报告",
    "reportContent": "详细的测评报告内容...",
    "reportLevel": 1,
    "generateTime": "2024-12-01 10:16:00"
  }
}
```

### 4. 测评记录管理接口

#### 4.1 查询用户的测评记录
```
GET /miniapp/user/assessment/records
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 123,
      "scaleId": 1,
      "scaleName": "焦虑自评量表",
      "scaleCode": "SAS",
      "userId": 1001,
      "startTime": "2024-12-01 10:00:00",
      "endTime": "2024-12-01 10:15:00",
      "totalScore": 35.0,
      "resultLevel": "正常",
      "status": 2,
      "duration": 900,
      "progress": 100.0,
      "createTime": "2024-12-01 10:00:00"
    }
  ]
}
```

#### 4.2 查询用户最近的测评记录
```
GET /miniapp/user/assessment/records/recent
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | Integer | 否 | 限制数量，默认10 |

#### 4.3 查询用户未完成的测评记录
```
GET /miniapp/user/assessment/records/incomplete
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

#### 4.4 查询用户已完成的测评记录
```
GET /miniapp/user/assessment/records/completed
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

#### 4.5 查询用户测评统计
```
GET /miniapp/user/assessment/stats
```

**请求头：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer token |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalTests": 15,
    "completedTests": 12,
    "incompleteTests": 3,
    "totalDuration": 10800,
    "avgScore": 72.5,
    "bestScore": 95.0,
    "recentTestTime": "2024-12-01 10:00:00",
    "favoriteScales": 5
  }
}
```

## 数据模型定义

### 1. 量表模型 (PsyTScale)

```json
{
  "id": 1,
  "code": "SAS_001",
  "name": "焦虑自评量表",
  "alias": "焦虑量表",
  "description": "用于评定焦虑状态的轻重程度及其在治疗中的变化",
  "instruction": "请根据您最近一周的实际感受进行选择",
  "author": "Zung",
  "version": "1.0",
  "scoringType": "sum",
  "payMode": 0,
  "payPhase": 0,
  "originalPrice": 0.00,
  "currentPrice": 0.00,
  "freeReportLevel": 1,
  "paidReportLevel": 3,
  "timeLimit": 1800,
  "questionCount": 20,
  "coverImage": "https://example.com/cover.jpg",
  "tags": "焦虑,情绪,心理健康",
  "searchKeywords": "焦虑 紧张 担心 害怕",
  "searchCount": 100,
  "viewCount": 500,
  "testCount": 200,
  "ratingAvg": 4.5,
  "ratingCount": 50,
  "sort": 1,
  "status": 1,
  "delFlag": "0",
  "createTime": "2024-12-01 10:00:00",
  "updateTime": "2024-12-01 15:30:00"
}
```

### 2. 测评记录模型 (PsyTAssessmentRecord)

```json
{
  "id": 123,
  "scaleId": 1,
  "userId": 1001,
  "enterpriseId": null,
  "sessionId": "uuid-session-id",
  "startTime": "2024-12-01 10:00:00",
  "endTime": "2024-12-01 10:15:00",
  "totalScore": 35.0,
  "maxScore": 80.0,
  "scorePercentage": 43.75,
  "resultLevel": "正常",
  "resultDescription": "您的焦虑水平在正常范围内",
  "suggestions": "继续保持良好的心理状态",
  "currentQuestionNo": 20,
  "answeredQuestions": 20,
  "totalQuestions": 20,
  "progress": 100.0,
  "duration": 900,
  "status": 2,
  "source": "personal",
  "reportLevel": 1,
  "delFlag": "0",
  "createTime": "2024-12-01 10:00:00",
  "updateTime": "2024-12-01 10:15:00"
}
```

### 3. 题目模型 (PsyTQuestion)

```json
{
  "id": 1,
  "scaleId": 1,
  "questionNo": 1,
  "questionText": "我觉得比平常容易紧张和着急",
  "questionType": "SINGLE",
  "isReverse": 0,
  "reverseValue": null,
  "options": "选项配置JSON",
  "subscaleRef": "anxiety",
  "subscaleId": 1,
  "isRequired": 1,
  "sort": 1,
  "orderNum": 1,
  "status": 1,
  "delFlag": "0"
}
```

### 4. 答题记录模型 (PsyTAnswerRecord)

```json
{
  "id": 1,
  "recordId": 123,
  "questionId": 1,
  "optionId": 2,
  "answerContent": "小部分时间",
  "answerScore": 2.0,
  "answerTime": "2024-12-01 10:02:00",
  "responseTime": 30,
  "delFlag": 0
}
```

### 5. 测评订单模型 (PsyTAssessmentOrder)

```json
{
  "id": 1,
  "orderNo": "ORD20241201001",
  "userId": 1001,
  "scaleId": 1,
  "scaleName": "焦虑自评量表",
  "originalPrice": 10.00,
  "actualPrice": 8.00,
  "discountAmount": 2.00,
  "couponId": null,
  "paymentMethod": "WECHAT",
  "paymentStatus": 1,
  "orderStatus": 1,
  "payTime": "2024-12-01 10:05:00",
  "expireTime": "2024-12-01 10:35:00",
  "refundAmount": null,
  "refundTime": null,
  "refundReason": null,
  "delFlag": 0,
  "createTime": "2024-12-01 10:00:00",
  "updateTime": "2024-12-01 10:05:00"
}
```

### 6. 测评评价模型 (PsyTAssessmentReview)

```json
{
  "id": 1,
  "scaleId": 1,
  "userId": 1001,
  "recordId": 123,
  "orderId": 1,
  "rating": 5,
  "content": "这个测评很准确，帮助我了解了自己的焦虑状况",
  "isAnonymous": 0,
  "status": 1,
  "auditRemark": null,
  "auditTime": "2024-12-01 11:00:00",
  "auditBy": "admin",
  "likeCount": 15,
  "replyCount": 3,
  "isTop": 0,
  "topTime": null,
  "delFlag": "0",
  "createTime": "2024-12-01 10:30:00",
  "updateTime": "2024-12-01 11:00:00"
}
```

### 5. 测评订单接口

#### 5.1 查询用户订单列表
```
GET /miniapp/user/order/list
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "orderNo": "ORD20241201001",
      "scaleId": 1,
      "scaleName": "焦虑自评量表",
      "originalPrice": 10.00,
      "actualPrice": 8.00,
      "discountAmount": 2.00,
      "paymentMethod": "WECHAT",
      "paymentStatus": 1,
      "orderStatus": 1,
      "payTime": "2024-12-01 10:05:00",
      "expireTime": "2024-12-01 10:35:00",
      "createTime": "2024-12-01 10:00:00"
    }
  ]
}
```

#### 5.2 查询订单详情
```
GET /miniapp/user/order/{id}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 订单ID |

#### 5.3 创建订单
```
POST /miniapp/user/order/create
```

**请求体：**
```json
{
  "scaleId": 1
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "orderNo": "ORD20241201001",
    "scaleId": 1,
    "scaleName": "焦虑自评量表",
    "originalPrice": 10.00,
    "actualPrice": 10.00,
    "discountAmount": 0.00,
    "paymentStatus": 0,
    "orderStatus": 0,
    "expireTime": "2024-12-01 10:35:00"
  }
}
```

#### 5.4 支付订单
```
POST /miniapp/user/order/pay
```

**请求体：**
```json
{
  "orderNo": "ORD20241201001",
  "paymentMethod": "WECHAT"
}
```

#### 5.5 取消订单
```
POST /miniapp/user/order/cancel
```

**请求体：**
```json
{
  "orderNo": "ORD20241201001",
  "cancelReason": "不想购买了"
}
```

#### 5.6 申请退款
```
POST /miniapp/user/order/refund
```

**请求体：**
```json
{
  "orderNo": "ORD20241201001",
  "refundReason": "测评结果不准确"
}
```

#### 5.7 查询待支付订单
```
GET /miniapp/user/order/pending
```

#### 5.8 查询已支付订单
```
GET /miniapp/user/order/paid
```

### 6. 测评评价接口

#### 6.1 提交测评评价
```
POST /miniapp/user/assessment/review
```

**请求体：**
```json
{
  "scaleId": 1,
  "recordId": 123,
  "orderId": 1,
  "rating": 5,
  "content": "这个测评很准确，帮助我了解了自己的焦虑状况",
  "isAnonymous": 0
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "评价提交成功，等待审核",
  "data": null
}
```

#### 6.2 获取量表评价列表
```
GET /miniapp/user/assessment/reviews/{scaleId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 是 | 量表ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "scaleId": 1,
      "userId": 1001,
      "recordId": 123,
      "rating": 5,
      "content": "这个测评很准确，帮助我了解了自己的焦虑状况",
      "isAnonymous": 0,
      "status": 1,
      "likeCount": 15,
      "replyCount": 3,
      "nickName": "用户A",
      "avatar": "https://example.com/avatar.jpg",
      "createTime": "2024-12-01 10:30:00"
    }
  ]
}
```

#### 6.3 获取用户评价列表
```
GET /miniapp/user/assessment/reviews/user
```

#### 6.4 获取评价详情
```
GET /miniapp/user/assessment/review/{id}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 评价ID |

#### 6.5 根据测评记录获取评价
```
GET /miniapp/user/assessment/review/record/{recordId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| recordId | Long | 是 | 测评记录ID |

#### 6.6 检查用户是否可以评价
```
GET /miniapp/user/assessment/review/check
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 是 | 量表ID |
| recordId | Long | 是 | 测评记录ID |

#### 6.7 获取量表评价统计
```
GET /miniapp/user/assessment/review/stats/{scaleId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 是 | 量表ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalReviews": 150,
    "averageRating": 4.5,
    "ratingDistribution": {
      "5": 80,
      "4": 45,
      "3": 20,
      "2": 3,
      "1": 2
    }
  }
}
```

#### 6.8 获取用户评价统计
```
GET /miniapp/user/assessment/review/stats/user
```

#### 6.9 获取评价摘要
```
GET /miniapp/user/assessment/review/summary/{scaleId}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 是 | 量表ID |

#### 6.10 获取热门评价
```
GET /miniapp/user/assessment/review/hot
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | Integer | 否 | 限制数量，默认10 |

#### 6.11 搜索评价
```
GET /miniapp/user/assessment/review/search
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 否 | 搜索关键词 |
| scaleId | Long | 否 | 量表ID |
| rating | Integer | 否 | 评分筛选 |

## 后台管理端接口

### 1. 测评评价管理接口

#### 1.1 查询评价列表
```
GET /system/assessment/review/list
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleId | Long | 否 | 量表ID |
| userId | Long | 否 | 用户ID |
| status | Integer | 否 | 审核状态 |
| rating | Integer | 否 | 评分 |
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |

#### 1.2 获取评价详情
```
GET /system/assessment/review/{id}
```

#### 1.3 审核评价
```
PUT /system/assessment/review/audit/{id}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 评价ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | Integer | 是 | 审核状态（1=通过，2=拒绝） |
| auditRemark | String | 否 | 审核备注 |

#### 1.4 批量审核评价
```
PUT /system/assessment/review/audit/batch
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | Long[] | 是 | 评价ID数组 |
| status | Integer | 是 | 审核状态 |
| auditRemark | String | 否 | 审核备注 |

#### 1.5 置顶评价
```
PUT /system/assessment/review/top/{id}
```

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 评价ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isTop | Integer | 是 | 是否置顶（0=否，1=是） |

#### 1.6 删除评价
```
DELETE /system/assessment/review/{ids}
```

#### 1.7 查询待审核评价
```
GET /system/assessment/review/pending
```

#### 1.8 查询评价统计
```
GET /system/assessment/review/statistics
```

#### 1.9 搜索评价
```
GET /system/assessment/review/search
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 否 | 搜索关键词 |
| scaleId | Long | 否 | 量表ID |
| userId | Long | 否 | 用户ID |
| status | Integer | 否 | 审核状态 |
| rating | Integer | 否 | 评分 |

#### 1.10 导出评价数据
```
POST /system/assessment/review/export
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 测评状态说明

### 测评记录状态 (status)

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 进行中 | 测评正在进行中 |
| 1 | 已暂停 | 测评已暂停，可恢复 |
| 2 | 已完成 | 测评已完成 |
| 3 | 已取消 | 测评已取消 |
| 4 | 已超时 | 测评已超时 |

### 量表状态 (status)

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 草稿 | 量表草稿状态 |
| 1 | 已发布 | 量表已发布，可用于测评 |
| 2 | 已下架 | 量表已下架，不可用于测评 |

### 付费模式 (payMode)

| 模式值 | 模式名称 | 说明 |
|--------|----------|------|
| 0 | 免费 | 完全免费 |
| 1 | 付费 | 需要付费才能测评 |
| 2 | 部分付费 | 基础功能免费，高级功能付费 |

### 订单状态 (orderStatus)

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 待支付 | 订单已创建，等待支付 |
| 1 | 已支付 | 订单已支付，可以使用 |
| 2 | 已完成 | 订单已完成，已使用 |
| 3 | 已取消 | 订单已取消 |
| 4 | 已退款 | 订单已退款 |

### 支付状态 (paymentStatus)

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 待支付 | 等待支付 |
| 1 | 已支付 | 支付成功 |
| 2 | 已退款 | 已退款 |

### 评价审核状态 (status)

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 待审核 | 评价已提交，等待审核 |
| 1 | 审核通过 | 评价审核通过，可以显示 |
| 2 | 审核不通过 | 评价审核不通过，不显示 |

### 支付方式 (paymentMethod)

| 方式值 | 方式名称 | 说明 |
|--------|----------|------|
| WECHAT | 微信支付 | 微信小程序支付 |
| ALIPAY | 支付宝 | 支付宝支付 |

## 测评流程说明

### 1. 完整测评流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant A as 小程序
    participant S as 服务器
    participant D as 数据库

    U->>A: 选择量表
    A->>S: 获取量表详情
    S->>D: 查询量表信息
    D-->>S: 返回量表数据
    S-->>A: 返回量表详情
    A-->>U: 显示量表信息

    U->>A: 开始测评
    A->>S: 检查是否可以测评
    S->>D: 检查用户权限和限制
    D-->>S: 返回检查结果
    S-->>A: 返回检查结果

    A->>S: 开始测评请求
    S->>D: 创建测评记录
    D-->>S: 返回记录ID
    S-->>A: 返回测评记录ID
    A-->>U: 开始答题

    loop 答题过程
        U->>A: 提交答案
        A->>S: 保存答案
        S->>D: 保存答题记录
        D-->>S: 保存成功
        S-->>A: 返回结果
        A-->>U: 显示下一题或进度
    end

    U->>A: 完成测评
    A->>S: 完成测评请求
    S->>D: 计算得分和结果
    D-->>S: 返回测评结果
    S-->>A: 返回测评结果
    A-->>U: 显示测评报告
```

### 2. 测评结果计算

1. **答案分数计算**：根据选项分值或自定义计分规则计算每题得分
2. **总分计算**：所有题目得分之和，支持反向计分
3. **维度得分**：按分量表分组计算得分
4. **得分率**：(总分/满分) × 100%
5. **结果等级**：根据得分范围匹配解释规则
6. **建议生成**：根据结果等级提供相应建议

### 3. 权限控制

- **后台管理端**：需要管理员权限，使用 `@PreAuthorize` 注解控制
- **小程序用户端**：需要用户登录，只能访问自己的数据
- **小程序咨询师端**：需要咨询师身份验证

### 4. 数据安全

- 所有接口都需要身份验证
- 用户只能访问自己的测评数据
- 匿名测评不显示用户信息
- 敏感数据加密存储

## 注意事项

1. **测评记录管理**：每次测评都会生成唯一的记录ID，用于标识测评过程
2. **时间限制**：支持设置测评时间限制，超时自动提交
3. **断点续答**：支持测评过程中断后继续答题
4. **数据统计**：实时更新量表的查看次数、测试次数等统计数据
5. **评分机制**：支持多种计分方式，包括选项分值、反向计分和自定义计分
6. **结果解释**：支持总分解释和维度解释，提供个性化建议
7. **答案验证**：提交答案前会进行有效性验证
8. **自动计分**：答案提交时自动计算分数
9. **进度保存**：支持实时保存答题进度
10. **状态管理**：完善的测评状态管理（进行中、暂停、完成、取消等）

## 错误处理

### 常见错误及处理

1. **测评记录不存在**：返回404错误，提示"测评记录不存在"
2. **答案验证失败**：返回400错误，提示具体的验证错误信息
3. **测评已完成**：返回400错误，提示"测评已完成，无法继续答题"
4. **题目不存在**：返回404错误，提示"题目不存在"
5. **用户权限不足**：返回403错误，提示"权限不足"
6. **测评超时**：返回400错误，提示"测评已超时"

### 错误响应格式

```json
{
  "code": 400,
  "msg": "答案无效：选择题必须选择一个选项",
  "data": null
}
```

## 更新日志

### v2.0 (2025-01-21)
- 重构测评流程，优化用户体验
- 新增答案验证和自动计分功能
- 完善测评状态管理
- 优化数据模型结构
- 增强错误处理机制
- 新增测评进度保存和恢复功能

### v1.0 (2024-12-01)
- 初始版本发布
- 基础测评功能实现
- 量表管理功能
- 测评记录管理
