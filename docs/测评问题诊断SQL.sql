-- 测评问题诊断SQL查询

-- 1. 检查测评记录24的基本信息
SELECT id, scale_id, user_id, session_id, status, total_score, 
       start_time, completion_time, result_description, suggestions
FROM psy_t_assessment_record 
WHERE id = 24;

-- 2. 检查量表8的选项分数配置
SELECT q.id as question_id, q.content as question_content, q.subscale_ref as dimension,
       o.id as option_id, o.content as option_content, o.option_value, o.sort
FROM psy_t_question q
LEFT JOIN psy_t_question_option o ON q.id = o.question_id
WHERE q.scale_id = 8 AND q.del_flag = 0 AND (o.del_flag = 0 OR o.del_flag IS NULL)
ORDER BY q.id, o.sort;

-- 3. 检查测评记录24的答题分数
SELECT ar.id, ar.question_id, ar.option_id, ar.answer_content, ar.answer_score,
       q.content as question_content, q.subscale_ref as dimension,
       o.content as option_content, o.option_value
FROM psy_t_answer_record ar
LEFT JOIN psy_t_question q ON ar.question_id = q.id
LEFT JOIN psy_t_question_option o ON ar.option_id = o.id
WHERE ar.record_id = 24 AND ar.del_flag = 0
ORDER BY ar.question_id;

-- 4. 统计答题分数情况
SELECT 
    COUNT(*) as total_answers,
    COUNT(CASE WHEN answer_score IS NOT NULL AND answer_score > 0 THEN 1 END) as scored_answers,
    COUNT(CASE WHEN answer_score IS NULL OR answer_score = 0 THEN 1 END) as zero_score_answers,
    SUM(COALESCE(answer_score, 0)) as total_score,
    AVG(COALESCE(answer_score, 0)) as avg_score
FROM psy_t_answer_record 
WHERE record_id = 24 AND del_flag = 0;

-- 5. 按维度统计分数
SELECT q.subscale_ref as dimension,
       COUNT(*) as question_count,
       SUM(COALESCE(ar.answer_score, 0)) as dimension_score,
       AVG(COALESCE(ar.answer_score, 0)) as avg_score
FROM psy_t_answer_record ar
LEFT JOIN psy_t_question q ON ar.question_id = q.id
WHERE ar.record_id = 24 AND ar.del_flag = 0
GROUP BY q.subscale_ref
ORDER BY q.subscale_ref;

-- 6. 检查量表8的解释配置
SELECT dimension, min_score, max_score, level_name, interpretation_text, level_color
FROM psy_t_interpretation 
WHERE scale_id = 8 AND del_flag = 0
ORDER BY dimension, min_score;

-- 7. 检查选项值的分布情况
SELECT DISTINCT option_value, COUNT(*) as count
FROM psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
WHERE q.scale_id = 8 AND o.del_flag = 0
GROUP BY option_value
ORDER BY option_value;

-- 8. 检查是否有空的选项值
SELECT q.id as question_id, q.content as question_content,
       o.id as option_id, o.content as option_content, o.option_value
FROM psy_t_question q
INNER JOIN psy_t_question_option o ON q.id = o.question_id
WHERE q.scale_id = 8 AND q.del_flag = 0 AND o.del_flag = 0
  AND (o.option_value IS NULL OR o.option_value = '' OR o.option_value = '0')
ORDER BY q.id, o.sort;

-- 9. 检查维度配置是否完整
SELECT DISTINCT q.subscale_ref as dimension
FROM psy_t_question q
WHERE q.scale_id = 8 AND q.del_flag = 0
ORDER BY q.subscale_ref;

-- 10. 检查是否所有维度都有解释配置
SELECT q.subscale_ref as dimension,
       COUNT(DISTINCT q.id) as question_count,
       COUNT(DISTINCT i.id) as interpretation_count
FROM psy_t_question q
LEFT JOIN psy_t_interpretation i ON q.scale_id = i.scale_id AND q.subscale_ref = i.dimension
WHERE q.scale_id = 8 AND q.del_flag = 0
GROUP BY q.subscale_ref
ORDER BY q.subscale_ref;
