# 开始测评接口内部类重构说明

## 🎯 重构目标

将 `startAssessment` 方法改为使用内部类接收参数，并使用 `@Data` 注解简化代码。

## 🔧 重构内容

### 1. 创建内部类

#### ✅ StartAssessmentRequest 内部类
```java
/**
 * 开始测评请求参数内部类
 */
@Data
public static class StartAssessmentRequest {
    private Long scaleId;
}
```

#### ✅ AnswerRequest 内部类（优化）
```java
/**
 * 答题请求参数内部类
 */
@Data
public static class AnswerRequest {
    private Long recordId;
    private Long questionId;
    private Long optionId;
    private String answerContent;
    private Integer responseTime;
}
```

### 2. 方法重构

#### ❌ 重构前
```java
@PostMapping("/start")
public AjaxResult startAssessment(@RequestParam Long scaleId, HttpServletRequest request) {
    // 获取当前登录用户
    LoginUser loginUser = tokenService.getLoginUser(request);
    if (loginUser == null) {
        return error("用户未登录");
    }

    Long userId = loginUser.getUserId();

    // 检查用户是否可以测评
    Map<String, Object> checkResult = assessmentRecordService.checkUserCanAssess(userId, scaleId);
    if (!(Boolean) checkResult.get("canAssess")) {
        return error("无法开始测评：" + checkResult.get("errors"));
    }

    Long recordId = assessmentRecordService.startAssessment(userId, scaleId, null);
    return success(recordId);
}
```

#### ✅ 重构后
```java
@PostMapping("/start")
public AjaxResult startAssessment(@RequestBody StartAssessmentRequest request, HttpServletRequest httpRequest) {
    
    logger.info("收到开始测评请求: {}", request);
    
    // 参数验证
    if (request.getScaleId() == null) {
        return error("量表ID不能为空");
    }
    
    // 获取当前登录用户
    LoginUser loginUser = tokenService.getLoginUser(httpRequest);
    if (loginUser == null) {
        return error("用户未登录");
    }

    Long userId = loginUser.getUserId();

    // 检查用户是否可以测评
    Map<String, Object> checkResult = assessmentRecordService.checkUserCanAssess(userId, request.getScaleId());
    if (!(Boolean) checkResult.get("canAssess")) {
        return error("无法开始测评：" + checkResult.get("errors"));
    }

    Long recordId = assessmentRecordService.startAssessment(userId, request.getScaleId(), null);
    return success(recordId);
}
```

### 3. 兼容性接口

#### ✅ 表单参数兼容接口
```java
@PostMapping("/start/form")
public AjaxResult startAssessmentForm(@RequestParam(value = "scaleId", required = true) Long scaleId, HttpServletRequest request) {
    
    // 创建StartAssessmentRequest对象
    StartAssessmentRequest startRequest = new StartAssessmentRequest();
    startRequest.setScaleId(scaleId);
    
    // 调用主要的开始测评方法
    return startAssessment(startRequest, request);
}
```

## 📊 使用方式

### 1. 主接口（JSON格式）

**URL**: `/miniapp/user/assessment/start`
**方法**: `POST`
**Content-Type**: `application/json`

#### ✅ 请求示例

```javascript
// 使用 fetch
fetch('/miniapp/user/assessment/start', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        scaleId: 1
    })
});

// 使用 axios
axios.post('/miniapp/user/assessment/start', {
    scaleId: 1
});

// 使用 jQuery
$.ajax({
    url: '/miniapp/user/assessment/start',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({
        scaleId: 1
    }),
    success: function(response) {
        console.log('开始测评成功', response);
    }
});
```

### 2. 兼容接口（表单格式）

**URL**: `/miniapp/user/assessment/start/form`
**方法**: `POST`
**Content-Type**: `application/x-www-form-urlencoded`

#### ✅ 请求示例

```javascript
// 使用 FormData
const formData = new FormData();
formData.append('scaleId', '1');

fetch('/miniapp/user/assessment/start/form', {
    method: 'POST',
    body: formData
});

// 使用 URLSearchParams
const params = new URLSearchParams();
params.append('scaleId', '1');

fetch('/miniapp/user/assessment/start/form', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params
});

// 使用 jQuery
$.ajax({
    url: '/miniapp/user/assessment/start/form',
    type: 'POST',
    data: {
        scaleId: 1
    },
    success: function(response) {
        console.log('开始测评成功', response);
    }
});
```

## 🧪 测试方法

### 1. 使用 curl 测试主接口
```bash
curl -X POST http://localhost:8080/miniapp/user/assessment/start \
  -H "Content-Type: application/json" \
  -d '{"scaleId": 1}'
```

### 2. 使用 curl 测试兼容接口
```bash
curl -X POST http://localhost:8080/miniapp/user/assessment/start/form \
  -d "scaleId=1"
```

### 3. 使用 Postman 测试

#### 主接口测试
- **Method**: POST
- **URL**: `http://localhost:8080/miniapp/user/assessment/start`
- **Headers**: `Content-Type: application/json`
- **Body**: 
```json
{
    "scaleId": 1
}
```

#### 兼容接口测试
- **Method**: POST
- **URL**: `http://localhost:8080/miniapp/user/assessment/start/form`
- **Headers**: `Content-Type: application/x-www-form-urlencoded`
- **Body**: 
```
scaleId=1
```

## 📋 参数说明

### StartAssessmentRequest 参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| scaleId | Long | ✅ | 量表ID | 1 |

## 📊 响应格式

### 成功响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": 23  // recordId
}
```

### 错误响应
```json
{
    "code": 500,
    "msg": "量表ID不能为空",
    "data": null
}
```

## ⚠️ 注意事项

### 1. @Data 注解的优势
- **自动生成**: getter、setter、toString、equals、hashCode 方法
- **代码简洁**: 减少样板代码
- **易于维护**: 字段变更时自动更新相关方法

### 2. 参数验证
- 添加了空值检查
- 提供了详细的错误信息
- 记录了请求日志便于调试

### 3. 向后兼容
- 保留了表单参数接口
- 不影响现有的前端代码
- 提供了平滑的迁移路径

### 4. 类型安全
- 编译时检查参数类型
- IDE 提供更好的代码提示
- 减少运行时错误

## 📈 优势对比

### 重构前
```java
// 参数分散，不易管理
@RequestParam Long scaleId

// 需要手动验证每个参数
if (scaleId == null) { ... }

// 参数传递复杂
method(scaleId, param2, param3, ...)
```

### 重构后
```java
// 参数集中管理
@RequestBody StartAssessmentRequest request

// 统一验证
if (request.getScaleId() == null) { ... }

// 参数传递简洁
method(request)

// 自动生成方法（@Data）
// getter、setter、toString、equals、hashCode
```

## 🚀 推荐使用方式

1. **新开发**: 使用主接口（JSON格式），类型安全，易于维护
2. **现有项目**: 可以继续使用兼容接口，逐步迁移到主接口
3. **调试**: 利用自动生成的 toString 方法查看请求参数

## ✅ 重构验证清单

- [x] 创建了 StartAssessmentRequest 内部类
- [x] 使用了 @Data 注解简化代码
- [x] 重构了主要的 startAssessment 方法
- [x] 添加了兼容性接口
- [x] 保持了向后兼容性
- [x] 添加了参数验证和日志
- [x] 提供了详细的使用文档

现在开始测评接口使用内部类和 @Data 注解，代码更加简洁和类型安全！🎉
