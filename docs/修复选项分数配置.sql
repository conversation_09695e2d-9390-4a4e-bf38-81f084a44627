-- 修复选项分数配置SQL脚本

-- 1. 检查当前选项分数配置情况
SELECT 
    q.scale_id,
    q.id as question_id,
    q.content as question_content,
    o.id as option_id,
    o.content as option_content,
    o.option_value,
    o.sort
FROM psy_t_question q
LEFT JOIN psy_t_question_option o ON q.id = o.question_id
WHERE q.scale_id = 8 AND q.del_flag = 0 AND o.del_flag = 0
ORDER BY q.sort, o.sort;

-- 2. 检查选项值为空或0的情况
SELECT 
    q.id as question_id,
    q.content as question_content,
    o.id as option_id,
    o.content as option_content,
    o.option_value,
    o.sort
FROM psy_t_question q
INNER JOIN psy_t_question_option o ON q.id = o.question_id
WHERE q.scale_id = 8 AND q.del_flag = 0 AND o.del_flag = 0
  AND (o.option_value IS NULL OR o.option_value = '' OR o.option_value = '0')
ORDER BY q.sort, o.sort;

-- 3. 统计选项值分布
SELECT 
    option_value,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT content SEPARATOR '; ') as sample_contents
FROM psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
WHERE q.scale_id = 8 AND o.del_flag = 0
GROUP BY option_value
ORDER BY option_value;

-- 4. 修复PRCA-24量表的选项分数（如果需要）
-- 注意：执行前请先备份数据！

-- 4.1 修复"非常同意"类选项
UPDATE psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
SET o.option_value = '5'
WHERE q.scale_id = 8 
  AND o.del_flag = 0 
  AND (o.option_value IS NULL OR o.option_value = '' OR o.option_value = '0')
  AND (o.content LIKE '%非常同意%' OR o.content LIKE '%完全同意%');

-- 4.2 修复"同意"类选项
UPDATE psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
SET o.option_value = '4'
WHERE q.scale_id = 8 
  AND o.del_flag = 0 
  AND (o.option_value IS NULL OR o.option_value = '' OR o.option_value = '0')
  AND (o.content LIKE '%同意%' AND o.content NOT LIKE '%非常%' AND o.content NOT LIKE '%不%');

-- 4.3 修复"中立"类选项
UPDATE psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
SET o.option_value = '3'
WHERE q.scale_id = 8 
  AND o.del_flag = 0 
  AND (o.option_value IS NULL OR o.option_value = '' OR o.option_value = '0')
  AND (o.content LIKE '%中立%' OR o.content LIKE '%一般%' OR o.content LIKE '%不确定%');

-- 4.4 修复"不同意"类选项
UPDATE psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
SET o.option_value = '2'
WHERE q.scale_id = 8 
  AND o.del_flag = 0 
  AND (o.option_value IS NULL OR o.option_value = '' OR o.option_value = '0')
  AND (o.content LIKE '%不同意%' AND o.content NOT LIKE '%非常%');

-- 4.5 修复"非常不同意"类选项
UPDATE psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
SET o.option_value = '1'
WHERE q.scale_id = 8 
  AND o.del_flag = 0 
  AND (o.option_value IS NULL OR o.option_value = '' OR o.option_value = '0')
  AND (o.content LIKE '%非常不同意%' OR o.content LIKE '%完全不同意%');

-- 5. 验证修复结果
SELECT 
    o.option_value,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT o.content SEPARATOR '; ') as sample_contents
FROM psy_t_question_option o
INNER JOIN psy_t_question q ON o.question_id = q.id
WHERE q.scale_id = 8 AND o.del_flag = 0
GROUP BY o.option_value
ORDER BY CAST(o.option_value AS UNSIGNED);

-- 6. 重新计算已有答题记录的分数
-- 注意：这会更新现有的答题记录分数

-- 6.1 创建临时表存储新分数
CREATE TEMPORARY TABLE temp_answer_scores AS
SELECT 
    a.id as answer_id,
    a.option_id,
    a.answer_content,
    o.option_value as new_score
FROM psy_t_answer_record a
INNER JOIN psy_t_assessment_record r ON a.record_id = r.id
LEFT JOIN psy_t_question_option o ON a.option_id = o.id
WHERE r.scale_id = 8 AND a.del_flag = 0 AND r.del_flag = 0;

-- 6.2 更新答题记录分数
UPDATE psy_t_answer_record a
INNER JOIN temp_answer_scores t ON a.id = t.answer_id
SET a.answer_score = CASE 
    WHEN t.new_score IS NOT NULL AND t.new_score != '' THEN CAST(t.new_score AS DECIMAL(8,2))
    ELSE 0.00
END
WHERE a.del_flag = 0;

-- 6.3 重新计算测评记录总分
UPDATE psy_t_assessment_record r
SET r.total_score = (
    SELECT COALESCE(SUM(a.answer_score), 0)
    FROM psy_t_answer_record a
    WHERE a.record_id = r.id AND a.del_flag = 0
)
WHERE r.scale_id = 8 AND r.del_flag = 0;

-- 7. 验证修复后的分数
SELECT 
    r.id as record_id,
    r.user_id,
    r.total_score,
    COUNT(a.id) as answer_count,
    SUM(a.answer_score) as calculated_total,
    AVG(a.answer_score) as avg_score
FROM psy_t_assessment_record r
LEFT JOIN psy_t_answer_record a ON r.id = a.record_id AND a.del_flag = 0
WHERE r.scale_id = 8 AND r.del_flag = 0
GROUP BY r.id, r.user_id, r.total_score
ORDER BY r.id;

-- 8. 检查具体的答题分数
SELECT 
    a.id,
    a.record_id,
    a.question_id,
    a.option_id,
    a.answer_content,
    a.answer_score,
    o.content as option_content,
    o.option_value,
    q.content as question_content
FROM psy_t_answer_record a
INNER JOIN psy_t_assessment_record r ON a.record_id = r.id
LEFT JOIN psy_t_question_option o ON a.option_id = o.id
LEFT JOIN psy_t_question q ON a.question_id = q.id
WHERE r.scale_id = 8 AND a.del_flag = 0 AND r.del_flag = 0
ORDER BY a.record_id, q.sort
LIMIT 20;
