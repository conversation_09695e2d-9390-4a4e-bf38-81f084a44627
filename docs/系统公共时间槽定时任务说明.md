# 系统公共时间槽定时任务说明

## 功能概述

为系统公共时间槽添加了专门的定时任务来管理过期状态，确保小程序中显示的时间槽状态始终准确。

## 新增定时任务

### 🕐 updateSystemSlotExpiredStatus
- **任务名称**: `updateSystemSlotExpiredStatus`
- **调用目标**: `com.xihuan.system.service.task.PsyTimeSlotTaskService.updateSystemSlotExpiredStatus`
- **cron表达式**: `0 */15 * * * ?`
- **执行频率**: 每15分钟
- **功能描述**: 更新系统公共时间槽的过期状态，支持延后配置

## 任务执行逻辑

### 1. 状态判断
```java
// 获取延后配置
boolean isDelayEnabled = isDelayExpirationEnabled();
int delayHours = isDelayEnabled ? getDelayExpirationHours() : 0;

// 更新过期状态
int count = systemTimeSlotService.updateExpiredSlotStatusWithDelay(centerId);
```

### 2. SQL执行
```sql
UPDATE psy_system_time_slot 
SET status = 2, update_time = NOW()
WHERE center_id = #{centerId}
  AND status = 0
  AND del_flag = 0
  AND CONCAT(date_key, ' ', end_time) < 
  CASE 
      WHEN #{delayHours} > 0 THEN DATE_SUB(NOW(), INTERVAL #{delayHours} HOUR)
      ELSE NOW()
  END
```

### 3. 延后过期支持
- **未启用延后**: 时间槽结束时间 < 当前时间 → 过期
- **启用延后**: 时间槽结束时间 < (当前时间 - 延后小时数) → 过期

## 完整定时任务列表

| 序号 | 任务名称 | 执行频率 | 功能 |
|------|----------|----------|------|
| 1 | generateFutureTimeSlots | 每天2:00 | 生成咨询师时间槽 |
| 2 | generateSystemTimeSlots | 每天2:05 | 生成系统公共时间槽 |
| 3 | cleanExpiredTimeSlots | 每天3:00 | 清理过期咨询师时间槽 |
| 4 | cleanExpiredSystemSlots | 每天3:10 | 清理过期系统时间槽 |
| 5 | updateExpiredSlotStatusWithDelay | 每小时 | 更新咨询师时间槽过期状态 |
| 6 | updateSystemSlotAvailability | 每30分钟 | 更新系统时间槽可用性统计 |
| 7 | **updateSystemSlotExpiredStatus** | **每15分钟** | **更新系统公共时间槽过期状态** |

## 执行时间安排

### 高频任务
- **每15分钟**: 更新系统公共时间槽过期状态
- **每30分钟**: 更新系统时间槽可用性统计
- **每小时**: 更新咨询师时间槽过期状态

### 日常维护任务
- **凌晨2:00**: 生成咨询师时间槽
- **凌晨2:05**: 生成系统公共时间槽
- **凌晨3:00**: 清理过期咨询师时间槽
- **凌晨3:10**: 清理过期系统时间槽

## 配置方式

### 1. 数据库配置
```sql
-- 添加系统公共时间槽过期状态更新任务
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark) 
VALUES (6, 'updateSystemSlotExpiredStatus', 'SYSTEM', 'psyTimeSlotTaskService.updateSystemSlotExpiredStatus()', '0 */15 * * * ?', '1', '1', '0', 'admin', now(), '更新系统公共时间槽的过期状态，支持延后配置');
```

### 2. 管理后台配置
- 访问：`/monitor/job`
- 添加新任务或修改现有任务
- 启动/停止/暂停任务

## 监控和日志

### 执行日志
```
2025-07-10 10:15:00 INFO - 开始执行定时任务：更新系统公共时间槽过期状态
2025-07-10 10:15:01 INFO - 定时任务完成：成功更新 12 个系统公共时间槽的过期状态
```

### 监控指标
- **执行频率**: 每15分钟
- **执行耗时**: 通常 < 1秒
- **更新数量**: 根据过期时间槽数量变化
- **错误率**: 应该为0

## 与其他任务的关系

### 数据依赖
```
咨询师时间槽 → 系统公共时间槽 → 过期状态更新
```

### 执行顺序
1. **生成阶段**: 先生成咨询师时间槽，再生成系统时间槽
2. **更新阶段**: 并行更新各种状态和统计
3. **清理阶段**: 先清理咨询师时间槽，再清理系统时间槽

## 性能考虑

### 优化措施
- **索引优化**: 在 `status`, `date_key`, `center_id` 上建立索引
- **批量操作**: 使用批量更新减少数据库连接
- **条件过滤**: 只更新状态为可用(0)的时间槽

### 性能指标
- **查询时间**: < 100ms
- **更新时间**: < 500ms
- **内存使用**: < 10MB
- **CPU使用**: < 5%

## 故障处理

### 常见问题
1. **任务不执行**: 检查Quartz配置和任务状态
2. **更新数量为0**: 检查时间槽数据和延后配置
3. **执行时间过长**: 检查数据库索引和数据量

### 排查步骤
1. 查看任务执行日志
2. 检查数据库连接状态
3. 验证延后过期配置
4. 手动执行任务测试

## 测试验证

### 手动测试
```bash
# 手动更新过期状态
PUT /system/systemTimeSlot/updateExpiredStatus?centerId=1

# 查看更新结果
GET /system/systemTimeSlot/list
```

### 自动化测试
```java
// 测试定时任务方法
@Test
public void testUpdateSystemSlotExpiredStatus() {
    taskService.updateSystemSlotExpiredStatus();
    // 验证状态更新结果
}
```

## 注意事项

### ⚠️ 重要提醒
1. **频率设置**: 15分钟频率确保状态及时更新，但不会过度消耗资源
2. **延后配置**: 延后过期功能会影响状态判断，需要合理配置
3. **数据一致性**: 确保系统时间槽状态与实际时间保持一致
4. **监控告警**: 建议设置任务执行失败的告警机制

### 🔧 最佳实践
1. **定期检查**: 定期检查任务执行状况和数据准确性
2. **性能监控**: 监控任务执行时间和资源使用情况
3. **配置备份**: 重要配置修改前先备份
4. **测试验证**: 配置修改后进行充分测试

现在系统公共时间槽有了专门的定时任务来管理过期状态，确保用户在小程序中看到的时间槽状态始终准确！🎯
