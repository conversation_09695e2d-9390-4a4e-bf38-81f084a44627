# 量表引用文献字段实现总结

## 🎯 需求描述

为 `psy_t_scale` 表添加引用文献字段，用于存储量表的参考文献信息，提升量表的学术性和专业性。

## ✅ 实现内容

### 1. 数据库表结构更新

添加了 `reference_literature` 字段到 `psy_t_scale` 表：

```sql
ALTER TABLE psy_t_scale 
ADD COLUMN reference_literature TEXT COMMENT '引用文献' AFTER test_application;
```

**字段特性：**
- **字段名**：`reference_literature`
- **数据类型**：`TEXT`
- **位置**：在 `test_application` 字段之后
- **注释**：引用文献
- **可空性**：允许为空

### 2. 实体类更新

在 `PsyAssessmentScale` 实体类中添加了对应字段：

```java
/** 引用文献 */
@Excel(name = "引用文献")
private String referenceLiterature;
```

### 3. DTO类更新

在 `ScaleDTO` 中添加了对应字段：

```java
/** 引用文献 */
private String referenceLiterature;
```

### 4. Mapper XML更新

更新了以下部分：

#### ResultMap 字段映射
```xml
<result property="referenceLiterature" column="reference_literature"/>
```

#### 查询语句
```xml
SELECT s.reference_literature, ...
FROM psy_t_scale s
```

#### 插入语句
```xml
INSERT INTO psy_t_scale (..., reference_literature, ...)
VALUES (..., #{referenceLiterature}, ...)
```

#### 更新语句
```xml
<if test="referenceLiterature != null">reference_literature = #{referenceLiterature},</if>
```

### 5. 服务层更新

在 `PsyAssessmentScaleServiceImpl.getScaleDTO()` 方法中添加了字段映射：

```java
dto.setReferenceLiterature(scale.getReferenceLiterature());
```

## 📋 完整字段清单

现在量表表包含以下完整的测评详细信息字段：

| 序号 | 字段名 | 数据库字段 | 实体类字段 | DTO字段 | 说明 |
|------|--------|------------|------------|---------|------|
| 1 | 测评须知 | test_notice | testNotice | testNotice | 测评前的注意事项 |
| 2 | 测评目的 | test_purpose | testPurpose | testPurpose | 测评的目标和意义 |
| 3 | 测评对象 | test_object | testObject | testObject | 适用的人群范围 |
| 4 | 测评准备 | test_preparation | testPreparation | testPreparation | 测评前的准备工作 |
| 5 | 测评后处理 | test_processing | testProcessing | testProcessing | 测评完成后的处理 |
| 6 | 注意事项 | test_attention | testAttention | testAttention | 测评过程中的注意事项 |
| 7 | 基础理论 | test_theory | testTheory | testTheory | 量表的理论基础 |
| 8 | 测评应用 | test_application | testApplication | testApplication | 量表的应用场景 |
| 9 | **引用文献** | **reference_literature** | **referenceLiterature** | **referenceLiterature** | **量表的参考文献** |
| 10 | 适用年龄 | applicable_age | applicableAge | applicableAge | 适用的年龄范围 |

## 📚 引用文献格式示例

### 1. 标准学术格式
```
Spielberger, C. D., Gorsuch, R. L., Lushene, R., Vagg, P. R., & Jacobs, G. A. (1983). Manual for the State-Trait Anxiety Inventory. Palo Alto, CA: Consulting Psychologists Press.

Spielberger, C. D. (1989). State-Trait Anxiety Inventory: Bibliography (2nd ed.). Palo Alto, CA: Consulting Psychologists Press.

李文利, 钱铭怡. (1995). 状态-特质焦虑量表中国大学生常模修订. 北京大学学报(自然科学版), 31(1), 108-114.
```

### 2. 多个文献格式
```
Beck, A. T., Steer, R. A., & Brown, G. K. (1996). Manual for the Beck Depression Inventory-II. San Antonio, TX: Psychological Corporation.

Beck, A. T., Ward, C. H., Mendelson, M., Mock, J., & Erbaugh, J. (1961). An inventory for measuring depression. Archives of General Psychiatry, 4(6), 561-571.

王征宇. (1984). Beck抑郁量表的信度研究. 中国神经精神疾病杂志, 10(3), 164-168.
```

## 🚀 部署步骤

### 1. 执行数据库更新
```bash
# 添加引用文献字段
mysql -u username -p database_name < sql/add_reference_field_to_scale.sql

# 插入包含引用文献的测试数据
mysql -u username -p database_name < sql/insert_scale_with_reference_data.sql

# 验证字段完整性
mysql -u username -p database_name < sql/verify_reference_field_complete.sql
```

### 2. 重启应用
```bash
# 重新编译并重启应用
mvn clean compile
./restart.sh
```

### 3. 验证功能
```bash
# 测试量表列表接口
curl "http://localhost:8080/miniapp/user/assessment/scale/list?pageNum=1&pageSize=5"

# 测试量表详情接口
curl "http://localhost:8080/miniapp/user/assessment/scale/1"
```

## 📊 API返回示例

### 小程序接口返回
```json
{
  "code": 200,
  "data": {
    "rows": [
      {
        "id": 1,
        "scaleName": "STAI状态-特质焦虑量表",
        "scaleCode": "STAI",
        "description": "STAI是目前国际上使用最广泛的焦虑评估工具之一",
        "testNotice": "受试者一般需具有初中文化水平。",
        "testPurpose": "区别评定短暂的焦虑情绪状态和人格特质性焦虑倾向",
        "testTheory": "Cattell (1961-1966)和Spielberger (1966-1979)提出状态焦虑...",
        "testApplication": "广泛应用于临床心理学、健康心理学、教育心理学等领域",
        "referenceLiterature": "Spielberger, C. D., Gorsuch, R. L., Lushene, R., Vagg, P. R., & Jacobs, G. A. (1983). Manual for the State-Trait Anxiety Inventory. Palo Alto, CA: Consulting Psychologists Press.\n\nSpielberger, C. D. (1989). State-Trait Anxiety Inventory: Bibliography (2nd ed.). Palo Alto, CA: Consulting Psychologists Press.",
        "applicableAge": "16岁以上成人",
        "questionCount": 40,
        "duration": "15-20分钟"
      }
    ]
  }
}
```

## 🎯 业务价值

### 1. 学术专业性
- **权威性**：提供量表的原始文献来源
- **可追溯性**：用户可以查阅原始研究
- **学术规范**：符合心理测量学的学术标准

### 2. 用户信任度
- **透明度**：公开量表的理论依据
- **可信度**：展示量表的科学基础
- **专业形象**：提升平台的专业水准

### 3. 法律合规
- **版权说明**：明确量表的版权信息
- **使用授权**：说明量表的使用许可
- **免责声明**：提供必要的法律保护

## 🔧 技术特点

### 1. 数据存储
- **TEXT类型**：支持长文本存储
- **UTF-8编码**：支持中英文混合
- **灵活格式**：支持多种引用格式

### 2. 前端展示
- **格式保持**：保持原有的换行和格式
- **链接支持**：可以包含DOI链接
- **搜索功能**：支持按文献搜索量表

### 3. 数据管理
- **批量导入**：支持批量更新引用文献
- **版本控制**：支持文献信息的版本管理
- **质量检查**：可以验证文献格式的规范性

## ✅ 验证清单

- [x] 数据库字段已添加
- [x] 实体类字段已添加
- [x] DTO类字段已添加
- [x] Mapper XML已更新
- [x] 服务层映射已添加
- [x] 测试数据已准备
- [x] API接口返回完整数据
- [x] 验证脚本已准备
- [x] 文档已完善

## 🎉 完成效果

现在量表系统包含了完整的引用文献信息：

1. **完整的学术信息**：每个量表都有详细的参考文献
2. **标准化格式**：统一的文献引用格式
3. **多语言支持**：支持中英文文献混合
4. **版本追踪**：可以记录文献的更新历史
5. **用户友好**：前端可以美观地展示文献信息

引用文献字段的添加大大提升了量表系统的学术性和专业性！
