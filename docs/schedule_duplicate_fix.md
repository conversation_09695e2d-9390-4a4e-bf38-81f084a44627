# 咨询师排班重复记录问题修复

## 问题描述

在调用 `/system/counselor/schedule/generateAll` 接口时，出现以下错误：

```
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1-2025-07-18' for key 'psy_time_counselor_schedule.uniq_counselor_date'
```

## 根本原因

1. 数据库表 `psy_time_counselor_schedule` 存在唯一约束 `uniq_counselor_date`，防止同一咨询师在同一日期有重复排班
2. 批量插入时如果存在重复记录，整个批次都会失败
3. 可能存在并发访问或数据不一致导致的重复插入

## 解决方案

### 1. 改进重复检查逻辑

**文件**: `PsyTimeCounselorScheduleServiceImpl.java`

- 优化 `generateDefaultSchedule` 方法，使用批量查询替代逐个检查
- 使用 `Set<LocalDate>` 提高查询效率
- 添加参数验证和错误处理

### 2. 增强批量插入容错性

**文件**: `PsyTimeCounselorScheduleServiceImpl.java`

- 改进 `batchInsertSchedules` 方法，添加异常处理
- 当遇到重复键异常时，自动降级到安全模式
- 提供三层容错机制：
  1. 正常批量插入
  2. INSERT IGNORE 批量插入
  3. 逐个插入并跳过重复记录

### 3. 数据库层面的重复处理

**文件**: `PsyTimeCounselorScheduleMapper.xml`

- 添加 `batchInsertSchedulesIgnoreDuplicate` 方法
- 使用 `INSERT IGNORE` 语句自动跳过重复记录

### 4. 重复记录清理功能

**文件**: `PsyTimeCounselorScheduleMapper.xml`

- 添加 `cleanupDuplicateSchedules` 方法
- 清理已存在的重复记录，保留ID最小的记录

### 5. 控制器层面的支持

**文件**: `PsyTimeCounselorScheduleController.java`

- 添加 `/cleanup` 接口用于清理重复记录
- 改进错误处理和日志记录

## 修改的文件列表

1. `xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyTimeCounselorScheduleServiceImpl.java`
2. `xihuan-system/src/main/java/com/xihuan/system/service/IPsyTimeCounselorScheduleService.java`
3. `xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTimeCounselorScheduleMapper.java`
4. `xihuan-system/src/main/resources/mapper/system/PsyTimeCounselorScheduleMapper.xml`
5. `xihuan-admin/src/main/java/com/xihuan/web/controller/system/PsyTimeCounselorScheduleController.java`

## 新增功能

### 1. 容错批量插入
```java
// 自动处理重复记录的批量插入
int batchInsertSchedules(List<PsyTimeCounselorSchedule> schedules);
```

### 2. 忽略重复的批量插入
```java
// 使用 INSERT IGNORE 的批量插入
int batchInsertSchedulesIgnoreDuplicate(List<PsyTimeCounselorSchedule> schedules);
```

### 3. 重复记录清理
```java
// 清理重复的排班记录
int cleanupDuplicateSchedules();
```

### 4. 清理接口
```
POST /system/counselor/schedule/cleanup
```

## 使用建议

### 1. 预防性措施
- 在生成排班前，可以先调用清理接口清除已有重复记录
- 使用改进后的生成接口，自动处理重复情况

### 2. 故障恢复
- 如果遇到重复键异常，系统会自动降级处理
- 可以手动调用清理接口清除重复记录后重试

### 3. 监控建议
- 关注日志中的重复记录警告
- 定期检查是否有重复记录产生

## 测试建议

1. 测试正常的排班生成流程
2. 测试重复记录的处理机制
3. 测试清理功能的有效性
4. 测试并发访问的安全性

## 注意事项

1. 清理重复记录时会保留ID最小的记录
2. 批量插入失败时会自动降级，可能影响性能
3. 建议在低峰期执行清理操作
