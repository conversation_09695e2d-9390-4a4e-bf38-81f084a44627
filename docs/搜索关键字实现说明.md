# 搜索关键字实现说明

## 🎯 问题解决

您遇到的问题是：热门搜索数据虽然真实了，但是咨询师、测评、冥想和课程的数据本身没有关键字字段，导致搜索不出来结果。

## 🔧 解决方案

### **1. 添加搜索字段**
为四个核心表添加搜索相关字段：

```sql
-- 咨询师表
ALTER TABLE `psy_consultants` 
ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词',
ADD COLUMN `search_count` INT DEFAULT 0 COMMENT '被搜索次数',
ADD COLUMN `view_count` INT DEFAULT 0 COMMENT '查看次数';

-- 课程表
ALTER TABLE `psy_course` 
ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词',
ADD COLUMN `search_count` INT DEFAULT 0 COMMENT '被搜索次数';

-- 冥想表
ALTER TABLE `psy_meditation` 
ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词',
ADD COLUMN `search_count` INT DEFAULT 0 COMMENT '被搜索次数';

-- 测评量表
ALTER TABLE `psy_t_scale` 
ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词',
ADD COLUMN `search_count` INT DEFAULT 0 COMMENT '被搜索次数';
```

### **2. 生成搜索关键词**
为现有数据自动生成丰富的搜索关键词：

## 📊 关键词生成规则

### **咨询师关键词包含**
- **基础信息**: 姓名、个人介绍、职称
- **地理信息**: 省份、城市、区域
- **特征标签**: 
  - 性别：男咨询师/女咨询师
  - 能力：可讲课/可出差
  - 状态：可预约/休息中
  - 价格：免费咨询/经济实惠/高端咨询
- **通用标签**: 心理咨询师、心理医生、心理专家
- **专业领域**: 婚姻咨询、青少年心理、职场心理

### **课程关键词包含**
- **基础信息**: 课程标题、简介、讲师、标签
- **难度标签**: 入门课程/进阶课程/高级课程
- **价格标签**: 免费课程/付费课程
- **状态标签**: 已发布/可学习
- **时长标签**: 短课程/中等时长/长课程
- **通用标签**: 心理学课程、在线课程、心理健康课程
- **主题标签**: 情绪管理、压力管理、人际关系

### **冥想关键词包含**
- **基础信息**: 标题、描述、引导师、标签
- **难度标签**: 入门冥想/进阶冥想/高级冥想
- **价格标签**: 免费冥想/付费冥想
- **状态标签**: 已发布/可播放
- **时长标签**: 短时冥想/中时冥想/长时冥想
- **通用标签**: 冥想、引导冥想、正念冥想
- **功能标签**: 睡眠冥想、减压冥想、专注力冥想

### **测评关键词包含**
- **基础信息**: 量表名称、编码、描述、介绍
- **价格标签**: 免费测评/付费测评
- **状态标签**: 可测评/已发布
- **题目标签**: 简短测评/标准测评/详细测评
- **通用标签**: 心理测评、心理测试、性格测试
- **专业标签**: 
  - SAS → 焦虑测试、焦虑自评
  - SDS → 抑郁测试、抑郁自评
  - MBTI → 人格测试、性格分析
  - SCL-90 → 症状自评、心理症状

## 🎯 关键词示例

### **咨询师关键词示例**
```
张医生,心理咨询师,女咨询师,女性咨询师,可预约,在线咨询师,心理医生,心理专家,咨询师,中等价位,婚姻咨询,情感问题,夫妻关系,北京,朝阳区
```

### **课程关键词示例**
```
情绪管理课程,心理健康,入门课程,基础课程,免费课程,公开课,已发布,可学习,心理学课程,心理课程,短课程,快速学习,情绪管理,情绪调节,情绪控制
```

### **冥想关键词示例**
```
睡眠冥想,助眠音频,入门冥想,基础冥想,免费冥想,公开冥想,已发布,可播放,冥想,冥想音频,引导冥想,短时冥想,快速冥想,睡眠冥想,助眠,改善睡眠
```

### **测评关键词示例**
```
SAS焦虑自评量表,SAS,焦虑测试,免费测评,免费测试,可测评,已发布,心理测评,心理测试,简短测评,快速测试,焦虑测试,焦虑自评,SAS量表
```

## 🔍 搜索功能增强

### **1. 模糊匹配**
```sql
-- 在搜索关键词中查找
WHERE search_keywords LIKE CONCAT('%', #{keyword}, '%')
```

### **2. 多关键词搜索**
```sql
-- 支持空格分隔的多关键词
WHERE (search_keywords LIKE '%关键词1%' AND search_keywords LIKE '%关键词2%')
```

### **3. 权重排序**
```sql
-- 按搜索次数和相关度排序
ORDER BY 
  CASE WHEN title LIKE '%关键词%' THEN 3
       WHEN search_keywords LIKE '%关键词%' THEN 2
       ELSE 1 END DESC,
  search_count DESC,
  view_count DESC
```

## 📈 搜索统计

### **搜索记录更新**
每次搜索时更新：
- `psy_search_record` 表记录用户搜索行为
- `psy_hot_search` 表更新热门搜索统计
- 对应内容表的 `search_count` 字段增加

### **热门内容推荐**
基于搜索次数推荐：
- 热门咨询师（按 `search_count` 排序）
- 热门课程（按 `search_count` 排序）
- 热门冥想（按 `search_count` 排序）
- 热门测评（按 `search_count` 排序）

## 🚀 使用方法

### **1. 执行SQL脚本**
```bash
# 在数据库中执行
source 添加搜索关键字字段和数据.sql;
```

### **2. 验证搜索功能**
```sql
-- 测试咨询师搜索
SELECT name, search_keywords FROM psy_consultants 
WHERE search_keywords LIKE '%心理咨询师%' LIMIT 5;

-- 测试课程搜索
SELECT title, search_keywords FROM psy_course 
WHERE search_keywords LIKE '%情绪管理%' LIMIT 5;

-- 测试冥想搜索
SELECT title, search_keywords FROM psy_meditation 
WHERE search_keywords LIKE '%睡眠%' LIMIT 5;

-- 测试测评搜索
SELECT name, search_keywords FROM psy_t_scale 
WHERE search_keywords LIKE '%焦虑%' LIMIT 5;
```

### **3. 前端搜索测试**
现在可以搜索以下内容：
- **咨询师**: "女咨询师"、"婚姻咨询"、"心理医生"
- **课程**: "情绪管理"、"免费课程"、"心理学"
- **冥想**: "睡眠冥想"、"减压"、"正念"
- **测评**: "焦虑测试"、"MBTI"、"免费测评"

## ✅ 预期效果

执行脚本后：

1. **搜索功能正常** - 所有热门搜索词都能找到对应内容
2. **搜索结果丰富** - 每个关键词都有多个相关结果
3. **搜索精准度高** - 关键词匹配准确，结果相关性强
4. **用户体验提升** - 搜索建议实用，能快速找到需要的服务

现在您可以执行SQL脚本，搜索功能就能正常工作了！用户搜索"心理咨询师"、"焦虑测试"、"睡眠冥想"等热门词汇都能找到对应的真实内容。
