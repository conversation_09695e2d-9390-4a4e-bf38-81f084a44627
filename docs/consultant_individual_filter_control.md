# 咨询师个人控制到店时间过滤功能

## 功能概述

每个咨询师可以独立控制自己的到店时间过滤设置，包括：
- ✅ **个人控制**：每个咨询师独立设置，不受其他咨询师影响
- ✅ **灵活配置**：可以设置到店时间和是否启用过滤
- ✅ **多中心支持**：不同咨询中心可以有不同的设置
- ✅ **实时生效**：设置后立即生效，影响客户的预约选择

## 核心设计理念

### 个人自主权
```
咨询师A：启用过滤，2小时到店 → 客户看到过滤后的时间段
咨询师B：禁用过滤，3小时到店 → 客户看到所有时间段
咨询师C：启用过滤，0.5小时到店 → 客户几乎看到所有时间段
```

### 业务场景
1. **住得近的咨询师**：设置0.5小时，启用过滤
2. **住得远的咨询师**：设置3小时，但可以选择禁用过滤（不想限制客户选择）
3. **灵活咨询师**：根据情况随时调整设置

## 数据库设计

### 核心字段
```sql
CREATE TABLE psy_consultant_center_config (
    consultant_id BIGINT NOT NULL,
    center_id BIGINT NOT NULL,
    arrival_time_hours DECIMAL(3,1) DEFAULT 2.0,
    enable_arrival_filter TINYINT(1) DEFAULT 1,  -- 关键字段：个人控制开关
    status TINYINT(1) DEFAULT 1
);
```

### 配置示例
```sql
-- 咨询师1：启用过滤，1.5小时到店
INSERT INTO psy_consultant_center_config VALUES (1, 1, 1.5, 1, 1);

-- 咨询师2：禁用过滤，3小时到店（住得远但不想限制客户）
INSERT INTO psy_consultant_center_config VALUES (2, 1, 3.0, 0, 1);

-- 咨询师3：启用过滤，0.5小时到店（住得很近）
INSERT INTO psy_consultant_center_config VALUES (3, 1, 0.5, 1, 1);
```

## API接口设计

### 1. 咨询师个人设置接口

#### 获取个人设置
```
GET /miniapp/consultant/settings/{consultantId}/center/{centerId}

响应：
{
  "code": 200,
  "data": {
    "config": {
      "arrivalTimeHours": 2.0,
      "enableArrivalFilter": 1
    },
    "isEnabled": true,
    "arrivalTimeHours": 2.0
  }
}
```

#### 更新到店时间
```
PUT /miniapp/consultant/settings/{consultantId}/center/{centerId}/arrivalTime
参数：hours=1.5

响应：
{
  "code": 200,
  "msg": "到店时间已更新为 1.5 小时"
}
```

#### 切换过滤开关
```
PUT /miniapp/consultant/settings/{consultantId}/center/{centerId}/toggleFilter
参数：enabled=false

响应：
{
  "code": 200,
  "msg": "已禁用到店时间过滤功能，客户可以预约任何时间段的线下咨询",
  "data": {
    "enabled": false,
    "arrivalTimeHours": 2.0
  }
}
```

### 2. 客户端时间槽查询接口（已更新）

#### 过滤逻辑
```java
// 检查该咨询师是否启用过滤
if (!isArrivalFilterEnabled(consultantId, centerId)) {
    return timeSlots; // 不过滤，返回所有时间段
}

// 启用过滤，应用到店时间逻辑
Double arrivalTimeHours = getConsultantArrivalTime(consultantId, centerId);
LocalDateTime earliestTime = currentTime.plusMinutes((long)(arrivalTimeHours * 60));
return filterTimeSlots(timeSlots, earliestTime);
```

## 使用场景示例

### 场景1：咨询师住得很远，但不想限制客户
```javascript
// 咨询师设置：3小时到店，但禁用过滤
PUT /miniapp/consultant/settings/1/center/1/toggleFilter?enabled=false

// 客户查询时间槽（当前时间10:00）
GET /miniapp/timeSlot/counselors?consultantId=1&consultationType=2&startTime=11:00

// 结果：显示11:00时间段（虽然咨询师来不及，但咨询师选择不限制）
{
  "counselors": [
    {
      "id": 1,
      "name": "张医生",
      "arrivalTimeHours": 3.0,
      "filterEnabled": false,
      "note": "该咨询师已禁用到店时间过滤"
    }
  ]
}
```

### 场景2：咨询师住得近，启用过滤
```javascript
// 咨询师设置：0.5小时到店，启用过滤
PUT /miniapp/consultant/settings/2/center/1/arrivalTime?hours=0.5
PUT /miniapp/consultant/settings/2/center/1/toggleFilter?enabled=true

// 客户查询时间槽（当前时间10:00）
GET /miniapp/timeSlot/counselors?consultantId=2&consultationType=2&startTime=10:30

// 结果：显示10:30时间段（咨询师10:30能到店）
{
  "counselors": [
    {
      "id": 2,
      "name": "李医生",
      "arrivalTimeHours": 0.5,
      "filterEnabled": true,
      "earliestOfflineTime": "2025-07-18T10:30:00"
    }
  ]
}
```

### 场景3：咨询师临时调整设置
```javascript
// 咨询师今天住得远，临时调整
PUT /miniapp/consultant/settings/3/center/1/arrivalTime?hours=4.0

// 或者临时禁用过滤
PUT /miniapp/consultant/settings/3/center/1/toggleFilter?enabled=false

// 设置立即生效，影响后续的客户预约查询
```

## 前端界面设计建议

### 咨询师设置页面
```html
<div class="consultant-settings">
  <h3>到店时间设置</h3>
  
  <div class="setting-item">
    <label>到店所需时间：</label>
    <input type="number" v-model="arrivalHours" step="0.5" min="0" max="24">
    <span>小时</span>
  </div>
  
  <div class="setting-item">
    <label>启用到店时间过滤：</label>
    <switch v-model="filterEnabled" @change="toggleFilter">
    <div class="help-text">
      启用后，客户只能预约您能及时到店的时间段
    </div>
  </div>
  
  <div class="preview" v-if="filterEnabled">
    <p>当前时间：{{ currentTime }}</p>
    <p>最早可预约线下咨询：{{ earliestTime }}</p>
  </div>
  
  <div class="actions">
    <button @click="saveSettings">保存设置</button>
    <button @click="resetDefault">重置默认</button>
  </div>
</div>
```

### 客户端显示
```html
<div class="counselor-item">
  <h4>{{ counselor.name }}</h4>
  <div v-if="!counselor.filterEnabled" class="notice">
    <span class="tag">不限时间</span>
    该咨询师接受任何时间段的预约
  </div>
  <div v-else class="notice">
    <span class="tag">{{ counselor.arrivalTimeHours }}h到店</span>
    最早可预约：{{ counselor.earliestOfflineTime }}
  </div>
</div>
```

## 管理后台功能

### 批量管理
```javascript
// 管理员可以查看所有咨询师的设置
GET /system/consultant/config/list

// 为新咨询师批量创建默认配置
POST /system/consultant/config/batchCreateDefault/1
Body: [1, 2, 3, 4, 5] // 咨询师ID列表
```

### 统计分析
```sql
-- 查看启用/禁用过滤的咨询师分布
SELECT 
    enable_arrival_filter,
    COUNT(*) as count,
    AVG(arrival_time_hours) as avg_arrival_time
FROM psy_consultant_center_config 
WHERE status = 1 
GROUP BY enable_arrival_filter;

-- 查看不同到店时间的分布
SELECT 
    CASE 
        WHEN arrival_time_hours <= 1 THEN '1小时内'
        WHEN arrival_time_hours <= 2 THEN '1-2小时'
        WHEN arrival_time_hours <= 3 THEN '2-3小时'
        ELSE '3小时以上'
    END as time_range,
    COUNT(*) as count
FROM psy_consultant_center_config 
WHERE status = 1 AND enable_arrival_filter = 1
GROUP BY time_range;
```

## 部署和测试

### 1. 数据库更新
```bash
mysql -u username -p database_name < sql/add_counselor_arrival_time.sql
```

### 2. 功能测试
```bash
# 测试咨询师设置接口
curl -X PUT "http://localhost:8080/miniapp/consultant/settings/1/center/1/toggleFilter?enabled=false"

# 测试客户查询接口
curl "http://localhost:8080/miniapp/timeSlot/counselors?consultantId=1&consultationType=2"
```

### 3. 验证逻辑
- ✅ 启用过滤的咨询师：客户看到过滤后的时间段
- ✅ 禁用过滤的咨询师：客户看到所有时间段
- ✅ 线上咨询：不受任何过滤影响
- ✅ 设置实时生效：修改后立即影响客户查询

## 优势总结

1. **个人自主权**：每个咨询师完全控制自己的设置
2. **灵活性强**：可以随时调整，适应不同情况
3. **用户友好**：清晰的界面和提示信息
4. **业务合理**：既保护咨询师，又给予选择权
5. **技术可靠**：实时生效，数据一致性好

这个设计完美解决了您提出的需求：**每个咨询师都能控制自己是否开启这个容错功能**！
