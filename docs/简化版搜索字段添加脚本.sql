-- 简化版搜索字段添加脚本（兼容性更好）

-- ==================== 1. 直接添加搜索字段（忽略已存在错误） ====================

-- 1.1 咨询师表添加搜索字段
ALTER TABLE `psy_consultants` ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词';
ALTER TABLE `psy_consultants` ADD COLUMN `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数';
ALTER TABLE `psy_consultants` ADD COLUMN `view_count` INT(11) DEFAULT 0 COMMENT '查看次数';

-- 1.2 课程表添加搜索字段
ALTER TABLE `psy_course` ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词';
ALTER TABLE `psy_course` ADD COLUMN `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数';

-- 1.3 冥想表添加搜索字段
ALTER TABLE `psy_meditation` ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词';
ALTER TABLE `psy_meditation` ADD COLUMN `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数';

-- 1.4 测评量表添加搜索字段
ALTER TABLE `psy_t_scale` ADD COLUMN `search_keywords` TEXT COMMENT '搜索关键词';
ALTER TABLE `psy_t_scale` ADD COLUMN `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数';

-- ==================== 2. 初始化咨询师搜索关键词 ====================
UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`name`, ''),
        IFNULL(`personal_intro`, ''),
        IFNULL(`personal_title`, ''),
        IFNULL(`location`, ''),
        IFNULL(`province`, ''),
        IFNULL(`city`, ''),
        IFNULL(`district`, ''),
        CASE 
            WHEN `gender` = '0' THEN '男咨询师,男性咨询师'
            WHEN `gender` = '1' THEN '女咨询师,女性咨询师'
            ELSE ''
        END,
        '心理咨询师,心理医生,心理专家,咨询师',
        CASE 
            WHEN `price` = 0 THEN '免费咨询'
            WHEN `price` > 0 AND `price` <= 200 THEN '经济实惠,低价咨询'
            WHEN `price` > 200 AND `price` <= 500 THEN '中等价位'
            WHEN `price` > 500 THEN '高端咨询,资深专家'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- ==================== 3. 初始化课程搜索关键词 ====================
UPDATE `psy_course` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`title`, ''),
        IFNULL(`summary`, ''),
        IFNULL(`tags`, ''),
        CASE 
            WHEN `is_free` = 1 THEN '免费课程,公开课'
            WHEN `is_free` = 0 THEN '付费课程,精品课程'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '已发布,可学习'
            WHEN `status` = 0 THEN '未发布'
            WHEN `status` = 2 THEN '已下架'
            ELSE ''
        END,
        '心理学课程,心理课程,在线课程,心理健康课程'
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- ==================== 4. 初始化冥想搜索关键词 ====================
UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`title`, ''),
        IFNULL(`description`, ''),
        IFNULL(`narrator`, ''),
        IFNULL(`tags`, ''),
        CASE 
            WHEN `is_free` = 1 THEN '免费冥想,公开冥想'
            WHEN `is_free` = 0 THEN '付费冥想,精品冥想'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '已发布,可播放'
            WHEN `status` = 0 THEN '未发布'
            WHEN `status` = 2 THEN '已下架'
            ELSE ''
        END,
        '冥想,冥想音频,引导冥想,正念冥想',
        CASE 
            WHEN `duration` < 300 THEN '短时冥想,快速冥想'
            WHEN `duration` BETWEEN 300 AND 1200 THEN '中时冥想,标准冥想'
            WHEN `duration` > 1200 THEN '长时冥想,深度冥想'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- ==================== 5. 初始化测评量表搜索关键词 ====================
UPDATE `psy_t_scale` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`name`, ''),
        IFNULL(`code`, ''),
        IFNULL(`description`, ''),
        IFNULL(`introduction`, ''),
        CASE 
            WHEN `pay_mode` = 0 THEN '免费测评,免费测试'
            WHEN `pay_mode` = 1 THEN '付费测评,专业测评'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '可测评,已发布'
            WHEN `status` = 0 THEN '未发布'
            ELSE ''
        END,
        '心理测评,心理测试,性格测试,情绪测评,心理量表',
        CASE 
            WHEN `code` LIKE '%SAS%' THEN '焦虑测试,焦虑自评,SAS量表'
            WHEN `code` LIKE '%SDS%' THEN '抑郁测试,抑郁自评,SDS量表'
            WHEN `code` LIKE '%MBTI%' THEN 'MBTI,人格测试,性格分析'
            WHEN `code` LIKE '%SCL%' THEN 'SCL-90,症状自评,心理症状'
            WHEN `code` LIKE '%PDQ%' THEN 'PDQ-4,人格障碍,人格测评'
            WHEN `code` LIKE '%STAI%' THEN 'STAI,状态焦虑,特质焦虑'
            WHEN `code` LIKE '%BAI%' THEN 'BAI,贝克焦虑,焦虑量表'
            WHEN `code` LIKE '%FNE%' THEN 'FNE,负面评价,社交恐惧'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- ==================== 6. 添加搜索索引 ====================
ALTER TABLE `psy_consultants` ADD INDEX `idx_search_keywords` (`search_keywords`(500));
ALTER TABLE `psy_course` ADD INDEX `idx_search_keywords` (`search_keywords`(500));
ALTER TABLE `psy_meditation` ADD INDEX `idx_search_keywords` (`search_keywords`(500));
ALTER TABLE `psy_t_scale` ADD INDEX `idx_search_keywords` (`search_keywords`(500));

-- 为搜索次数创建索引
ALTER TABLE `psy_consultants` ADD INDEX `idx_consultant_search_count` (`search_count`);
ALTER TABLE `psy_course` ADD INDEX `idx_course_search_count` (`search_count`);
ALTER TABLE `psy_meditation` ADD INDEX `idx_meditation_search_count` (`search_count`);
ALTER TABLE `psy_t_scale` ADD INDEX `idx_scale_search_count` (`search_count`);

-- ==================== 7. 添加专业领域关键词 ====================

-- 为咨询师添加专业领域关键词
UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',婚姻咨询,情感问题,夫妻关系')
WHERE (`name` LIKE '%婚姻%' OR `personal_intro` LIKE '%婚姻%' OR `personal_intro` LIKE '%夫妻%')
  AND `search_keywords` NOT LIKE '%婚姻咨询%';

UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',青少年心理,儿童心理,亲子关系')
WHERE (`personal_intro` LIKE '%青少年%' OR `personal_intro` LIKE '%儿童%' OR `personal_intro` LIKE '%亲子%')
  AND `search_keywords` NOT LIKE '%青少年心理%';

UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',职场心理,工作压力,职业规划')
WHERE (`personal_intro` LIKE '%职场%' OR `personal_intro` LIKE '%工作%' OR `personal_intro` LIKE '%职业%')
  AND `search_keywords` NOT LIKE '%职场心理%';

-- 为课程添加主题关键词
UPDATE `psy_course` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',情绪管理,情绪调节,情绪控制')
WHERE (`title` LIKE '%情绪%' OR `summary` LIKE '%情绪%')
  AND `search_keywords` NOT LIKE '%情绪管理%';

UPDATE `psy_course` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',压力管理,减压,放松')
WHERE (`title` LIKE '%压力%' OR `summary` LIKE '%压力%' OR `summary` LIKE '%减压%')
  AND `search_keywords` NOT LIKE '%压力管理%';

UPDATE `psy_course` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',人际关系,社交技巧,沟通技巧')
WHERE (`title` LIKE '%人际%' OR `summary` LIKE '%人际%' OR `summary` LIKE '%沟通%')
  AND `search_keywords` NOT LIKE '%人际关系%';

-- 为冥想添加功能关键词
UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',睡眠冥想,助眠,改善睡眠')
WHERE (`title` LIKE '%睡眠%' OR `description` LIKE '%睡眠%' OR `description` LIKE '%助眠%')
  AND `search_keywords` NOT LIKE '%睡眠冥想%';

UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',减压冥想,放松冥想,缓解压力')
WHERE (`title` LIKE '%减压%' OR `description` LIKE '%减压%' OR `description` LIKE '%放松%')
  AND `search_keywords` NOT LIKE '%减压冥想%';

UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT(IFNULL(`search_keywords`, ''), ',专注力,注意力,集中精神')
WHERE (`title` LIKE '%专注%' OR `description` LIKE '%专注%' OR `description` LIKE '%注意力%')
  AND `search_keywords` NOT LIKE '%专注力%';

-- ==================== 8. 清理多余的逗号 ====================
UPDATE `psy_consultants` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(`search_keywords`, ',,', ',')) WHERE `search_keywords` IS NOT NULL;
UPDATE `psy_course` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(`search_keywords`, ',,', ',')) WHERE `search_keywords` IS NOT NULL;
UPDATE `psy_meditation` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(`search_keywords`, ',,', ',')) WHERE `search_keywords` IS NOT NULL;
UPDATE `psy_t_scale` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(`search_keywords`, ',,', ',')) WHERE `search_keywords` IS NOT NULL;
