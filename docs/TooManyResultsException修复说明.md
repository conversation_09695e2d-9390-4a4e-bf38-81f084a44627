# TooManyResultsException 修复说明

## 🚨 问题描述

在调用 `/miniapp/user/assessment/complete/23` 接口时出现以下错误：

```
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 4
```

## 🔍 问题分析

### 错误原因
MyBatis 的 `TooManyResultsException` 异常表明：
- 某个方法期望返回单个结果（使用 `selectOne()`）
- 但实际查询返回了多条记录（4条）

### 问题定位
通过分析代码发现问题出现在：

**文件**: `PsyTAnswerRecordMapper.java`
**方法**: `selectAnswerStats`

#### ❌ 问题代码
```java
// Mapper接口定义 - 返回单个Map
Map<String, Object> selectAnswerStats(Long recordId);
```

```xml
<!-- XML查询 - 实际返回多条记录 -->
<select id="selectAnswerStats" parameterType="Long" resultType="java.util.Map">
    SELECT 
        q.subscale_ref as dimension,
        COUNT(a.id) as answer_count,
        SUM(a.answer_score) as total_score,
        AVG(a.answer_score) as avg_score,
        AVG(a.response_time) as avg_response_time
    FROM psy_t_answer_record a
    JOIN psy_t_question q ON a.question_id = q.id
    WHERE a.record_id = #{recordId} AND a.del_flag = 0
    GROUP BY q.subscale_ref  <!-- 这里会产生多条记录 -->
</select>
```

### 矛盾分析
- **接口定义**: 返回 `Map<String, Object>`（单个对象）
- **XML查询**: 使用 `GROUP BY q.subscale_ref`，会返回多条记录（每个维度一条）
- **MyBatis行为**: 因为接口定义返回单个对象，所以使用 `selectOne()`
- **实际结果**: 查询返回4条记录（4个不同的维度）
- **异常**: `TooManyResultsException`

## 🔧 修复方案

### 1. 修复 Mapper 接口定义

#### ✅ 修复后
```java
/**
 * 查询答题统计信息
 * 
 * @param recordId 测评记录ID
 * @return 统计信息列表（每个维度一条记录）
 */
List<Map<String, Object>> selectAnswerStats(Long recordId);
```

### 2. 修复 Service 层代码

#### ❌ 修复前
```java
@Override
public Map<String, BigDecimal> calculateDimensionScores(Long recordId) {
    Map<String, BigDecimal> dimensionScores = new HashMap<>();

    // 获取答题统计信息，其中包含维度分数
    Object statsResult = answerRecordMapper.selectAnswerStats(recordId);
    List<Map<String, Object>> stats = null;

    if (statsResult instanceof List) {
        stats = (List<Map<String, Object>>) statsResult;
    } else if (statsResult instanceof Map) {
        stats = new ArrayList<>();
        stats.add((Map<String, Object>) statsResult);
    }
    // ...
}
```

#### ✅ 修复后
```java
@Override
public Map<String, BigDecimal> calculateDimensionScores(Long recordId) {
    Map<String, BigDecimal> dimensionScores = new HashMap<>();

    // 获取答题统计信息，其中包含维度分数
    List<Map<String, Object>> stats = answerRecordMapper.selectAnswerStats(recordId);
    // ...
}
```

## 📊 修复效果

### 修复前的执行流程
1. 调用 `answerRecordMapper.selectAnswerStats(recordId)`
2. MyBatis 看到返回类型是 `Map<String, Object>`，使用 `selectOne()`
3. SQL 查询返回4条记录（4个维度）
4. MyBatis 抛出 `TooManyResultsException`

### 修复后的执行流程
1. 调用 `answerRecordMapper.selectAnswerStats(recordId)`
2. MyBatis 看到返回类型是 `List<Map<String, Object>>`，使用 `selectList()`
3. SQL 查询返回4条记录（4个维度）
4. MyBatis 正常返回包含4个Map的List

## 🧪 验证方法

### 1. 单元测试
```java
@Test
public void testSelectAnswerStats() {
    Long recordId = 23L;
    List<Map<String, Object>> stats = answerRecordMapper.selectAnswerStats(recordId);
    
    // 验证返回结果
    assertNotNull(stats);
    assertTrue(stats.size() > 0);
    
    // 验证每条记录的结构
    for (Map<String, Object> stat : stats) {
        assertNotNull(stat.get("dimension"));
        assertNotNull(stat.get("answer_count"));
        assertNotNull(stat.get("total_score"));
        assertNotNull(stat.get("avg_score"));
    }
}
```

### 2. 集成测试
```java
@Test
public void testCalculateDimensionScores() {
    Long recordId = 23L;
    Map<String, BigDecimal> dimensionScores = answerRecordService.calculateDimensionScores(recordId);
    
    // 验证维度分数计算
    assertNotNull(dimensionScores);
    assertTrue(dimensionScores.size() > 0);
    
    // 验证每个维度都有分数
    for (Map.Entry<String, BigDecimal> entry : dimensionScores.entrySet()) {
        assertNotNull(entry.getKey());
        assertNotNull(entry.getValue());
        assertTrue(entry.getValue().compareTo(BigDecimal.ZERO) >= 0);
    }
}
```

### 3. 接口测试
```bash
# 测试完成测评接口
curl -X POST http://localhost:8080/miniapp/user/assessment/complete/23
```

## 📋 相关数据结构

### selectAnswerStats 返回的数据结构
```json
[
    {
        "dimension": "焦虑",
        "answer_count": 6,
        "total_score": 18.00,
        "avg_score": 3.00,
        "avg_response_time": 25.5
    },
    {
        "dimension": "抑郁", 
        "answer_count": 8,
        "total_score": 24.00,
        "avg_score": 3.00,
        "avg_response_time": 30.2
    },
    {
        "dimension": "压力",
        "answer_count": 5,
        "total_score": 15.00,
        "avg_score": 3.00,
        "avg_response_time": 28.8
    },
    {
        "dimension": "睡眠",
        "answer_count": 5,
        "total_score": 12.00,
        "avg_score": 2.40,
        "avg_response_time": 22.1
    }
]
```

### calculateDimensionScores 返回的数据结构
```json
{
    "焦虑": 18.00,
    "抑郁": 24.00,
    "压力": 15.00,
    "睡眠": 12.00
}
```

## ⚠️ 注意事项

### 1. MyBatis 返回类型规则
- `Map<String, Object>` → 使用 `selectOne()`，期望单条记录
- `List<Map<String, Object>>` → 使用 `selectList()`，可以返回多条记录

### 2. SQL 查询设计
- 使用 `GROUP BY` 时通常会返回多条记录
- 接口返回类型必须与实际查询结果匹配

### 3. 错误处理
- 添加适当的NULL检查
- 处理空结果集的情况

## 🔍 类似问题排查方法

### 1. 检查接口定义与XML查询的一致性
```java
// 检查点1：返回类型
Map<String, Object> method1();     // 期望单条记录
List<Map<String, Object>> method2(); // 期望多条记录

// 检查点2：XML查询
SELECT ... GROUP BY ...  // 通常返回多条记录
SELECT ... WHERE id = ... // 通常返回单条记录
```

### 2. 查看SQL执行结果
```sql
-- 直接在数据库中执行SQL，查看返回记录数
SELECT 
    q.subscale_ref as dimension,
    COUNT(a.id) as answer_count,
    SUM(a.answer_score) as total_score,
    AVG(a.answer_score) as avg_score,
    AVG(a.response_time) as avg_response_time
FROM psy_t_answer_record a
JOIN psy_t_question q ON a.question_id = q.id
WHERE a.record_id = 23 AND a.del_flag = 0
GROUP BY q.subscale_ref;
```

### 3. 启用MyBatis日志
```yaml
# application.yml
logging:
  level:
    com.xihuan.system.mapper: DEBUG
```

## ✅ 修复验证清单

- [x] 修复了Mapper接口返回类型
- [x] 简化了Service层的类型处理逻辑
- [x] 消除了类型转换的复杂性
- [x] 保持了业务逻辑的正确性
- [x] 添加了详细的文档说明
- [x] 提供了测试验证方法

## 🚀 后续优化建议

### 1. 代码审查
- 检查其他类似的Mapper方法
- 确保接口定义与XML查询一致

### 2. 单元测试
- 为所有Mapper方法添加单元测试
- 验证返回结果的数量和结构

### 3. 监控告警
- 添加异常监控
- 及时发现类似问题

现在 `/miniapp/user/assessment/complete/23` 接口应该可以正常工作了！🎉
