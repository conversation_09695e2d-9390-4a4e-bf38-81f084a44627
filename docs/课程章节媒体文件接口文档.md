# 课程章节媒体文件接口文档

## 概述

为了支持课程章节的视频、音频、文档播放功能，我们为 `psy_course_chapter` 表添加了媒体文件相关字段。

## 数据库字段变更

### 新增字段

| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| media_url | varchar | 500 | YES | NULL | 媒体文件URL（视频/音频/文档地址） |
| media_file_name | varchar | 255 | YES | NULL | 媒体文件名称 |
| media_file_size | bigint | - | YES | NULL | 媒体文件大小（字节） |

### 字段说明

- **media_url**: 存储媒体文件的完整访问URL，支持阿里云OSS、本地文件等
- **media_file_name**: 原始文件名，用于下载时显示
- **media_file_size**: 文件大小，用于前端显示和下载进度

## 内容类型支持

根据 `content_type` 字段区分不同类型的媒体文件：

- **0: 视频** - 支持 mp4, avi, mov, wmv 等格式
- **1: 音频** - 支持 mp3, wav, aac, flac 等格式  
- **2: 文档** - 支持 pdf, doc, docx, ppt, pptx 等格式

## API 接口变更

### 1. 章节创建接口

**接口地址**: `POST /system/course/chapter`

**请求参数**:
```json
{
    "courseId": 1,
    "chapterTitle": "第一章：心理学基础",
    "chapterContent": "本章介绍心理学的基本概念...",
    "contentType": 0,
    "duration": 1800,
    "mediaUrl": "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/videos/20241219/chapter1.mp4",
    "mediaFileName": "心理学基础-第一章.mp4",
    "mediaFileSize": 52428800,
    "isTrial": 1,
    "chapterOrder": 1
}
```

### 2. 章节更新接口

**接口地址**: `PUT /system/course/chapter/{id}`

**请求参数**:
```json
{
    "id": 1,
    "chapterTitle": "第一章：心理学基础（更新版）",
    "mediaUrl": "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/videos/20241219/chapter1_v2.mp4",
    "mediaFileName": "心理学基础-第一章-更新版.mp4",
    "mediaFileSize": 62428800
}
```

### 3. 章节详情查询

**接口地址**: `GET /system/course/chapter/{id}`

**响应示例**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "courseId": 1,
        "chapterTitle": "第一章：心理学基础",
        "chapterContent": "本章介绍心理学的基本概念...",
        "contentType": 0,
        "duration": 1800,
        "mediaUrl": "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/videos/20241219/chapter1.mp4",
        "mediaFileName": "心理学基础-第一章.mp4",
        "mediaFileSize": 52428800,
        "isTrial": 1,
        "chapterOrder": 1,
        "level": 1,
        "parentId": 0
    }
}
```

## 文件上传流程

### 1. 上传媒体文件

**接口地址**: `POST /common/upload`

**请求方式**: multipart/form-data

**响应示例**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "url": "https://xihuanxinli.oss-cn-beijing.aliyuncs.com/videos/20241219/uuid_video.mp4",
    "fileName": "uuid_video.mp4",
    "originalFilename": "心理学基础.mp4"
}
```

### 2. 创建章节时关联文件

将上传接口返回的 `url` 作为 `mediaUrl`，`originalFilename` 作为 `mediaFileName`。

## 前端播放器集成

### 视频播放器

```javascript
// 视频播放器配置
const videoConfig = {
    url: chapter.mediaUrl,
    title: chapter.chapterTitle,
    duration: chapter.duration,
    isTrial: chapter.isTrial === 1
};
```

### 音频播放器

```javascript
// 音频播放器配置
const audioConfig = {
    url: chapter.mediaUrl,
    title: chapter.chapterTitle,
    duration: chapter.duration
};
```

### 文档预览

```javascript
// 文档预览配置
const docConfig = {
    url: chapter.mediaUrl,
    fileName: chapter.mediaFileName,
    fileSize: chapter.mediaFileSize
};
```

## 注意事项

1. **文件大小限制**: 单个文件最大支持 100MB
2. **格式支持**: 确保上传的文件格式与 `content_type` 匹配
3. **试听权限**: `isTrial=1` 的章节无需购买即可播放
4. **URL有效期**: 阿里云OSS URL默认有效期为1小时，长期存储建议使用永久链接
5. **安全考虑**: 敏感内容建议使用带签名的临时URL

## 使用示例

### 后端Controller示例

```java
@RestController
@RequestMapping("/system/chapter")
public class PsyCourseChapterController {

    @Autowired
    private IPsyCourseChapterService chapterService;

    /**
     * 新增章节（包含媒体文件）
     */
    @PostMapping
    public AjaxResult add(@RequestBody PsyCourseChapter chapter) {
        // 验证媒体文件信息
        if (chapter.getContentType() != null && chapter.getContentType() != 2) {
            if (StringUtils.isEmpty(chapter.getMediaUrl())) {
                return error("媒体文件URL不能为空");
            }
        }

        return toAjax(chapterService.insertChapter(chapter));
    }

    /**
     * 获取章节播放信息
     */
    @GetMapping("/play/{id}")
    public AjaxResult getPlayInfo(@PathVariable Long id) {
        PsyCourseChapter chapter = chapterService.selectChapterById(id);
        if (chapter == null) {
            return error("章节不存在");
        }

        // 构建播放信息
        Map<String, Object> playInfo = new HashMap<>();
        playInfo.put("id", chapter.getId());
        playInfo.put("title", chapter.getChapterTitle());
        playInfo.put("contentType", chapter.getContentType());
        playInfo.put("mediaUrl", chapter.getMediaUrl());
        playInfo.put("mediaFileName", chapter.getMediaFileName());
        playInfo.put("duration", chapter.getDuration());
        playInfo.put("isTrial", chapter.getIsTrial());

        return success(playInfo);
    }
}
```

### 前端Vue组件示例

```vue
<template>
  <div class="chapter-player">
    <!-- 视频播放器 -->
    <video v-if="chapter.contentType === 0"
           :src="chapter.mediaUrl"
           controls
           :poster="chapter.coverImage">
      您的浏览器不支持视频播放
    </video>

    <!-- 音频播放器 -->
    <audio v-else-if="chapter.contentType === 1"
           :src="chapter.mediaUrl"
           controls>
      您的浏览器不支持音频播放
    </audio>

    <!-- 文档预览 -->
    <iframe v-else-if="chapter.contentType === 2"
            :src="getDocumentPreviewUrl(chapter.mediaUrl)"
            width="100%"
            height="600px">
    </iframe>

    <!-- 章节信息 -->
    <div class="chapter-info">
      <h3>{{ chapter.chapterTitle }}</h3>
      <p>文件大小: {{ formatFileSize(chapter.mediaFileSize) }}</p>
      <p>时长: {{ formatDuration(chapter.duration) }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChapterPlayer',
  props: {
    chapterId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      chapter: {}
    }
  },
  mounted() {
    this.loadChapter();
  },
  methods: {
    async loadChapter() {
      try {
        const response = await this.$http.get(`/system/chapter/play/${this.chapterId}`);
        this.chapter = response.data;
      } catch (error) {
        this.$message.error('加载章节失败');
      }
    },

    getDocumentPreviewUrl(url) {
      // 使用在线文档预览服务
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`;
    },

    formatFileSize(bytes) {
      if (!bytes) return '未知';
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    },

    formatDuration(seconds) {
      if (!seconds) return '00:00';
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  }
}
</script>
```

## 数据迁移

执行以下SQL脚本添加新字段：

```sql
-- 添加媒体文件相关字段
ALTER TABLE `psy_course_chapter`
ADD COLUMN `media_url` varchar(500) NULL COMMENT '媒体文件URL（视频/音频/文档地址）' AFTER `is_trial`,
ADD COLUMN `media_file_name` varchar(255) NULL COMMENT '媒体文件名称' AFTER `media_url`,
ADD COLUMN `media_file_size` bigint NULL COMMENT '媒体文件大小（字节）' AFTER `media_file_name`;

-- 添加索引
CREATE INDEX `idx_media_url` ON `psy_course_chapter` (`media_url`);
```

## 总结

通过添加 `media_url`、`media_file_name`、`media_file_size` 三个字段，课程章节表现在可以完整支持：

1. **视频播放** - 存储视频文件URL，支持在线播放
2. **音频播放** - 存储音频文件URL，支持在线播放
3. **文档预览** - 存储文档文件URL，支持在线预览
4. **文件管理** - 记录文件名称和大小，便于管理和显示

所有现有的API接口都会自动支持这些新字段，无需额外修改服务层代码。
