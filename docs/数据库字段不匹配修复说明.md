# 数据库字段不匹配修复说明

## 🚨 问题描述

在调用 `selectOptionById` 方法时出现SQL语法错误：
```
java.sql.SQLSyntaxErrorException: Unknown column 'o.order_num' in 'field list'
```

## 🔍 问题原因

1. **数据库表结构不一致**：不同的SQL脚本中对 `psy_t_question_option` 表的定义不同
2. **XML映射字段过多**：Mapper XML中包含了数据库中不存在的字段
3. **版本差异**：可能使用了不同版本的数据库表结构

## 📊 实际数据库表结构

根据代码检索结果，实际的 `psy_t_question_option` 表结构应该是：

```sql
CREATE TABLE psy_t_question_option (
    id           bigint auto_increment PRIMARY KEY,
    question_id  bigint NOT NULL,
    option_text  varchar(500) NOT NULL,
    option_value varchar(50) DEFAULT NULL,  -- 或者是 INT 类型
    sort         int DEFAULT 0,
    del_flag     tinyint DEFAULT 0,
    create_by    varchar(64) DEFAULT '',
    create_time  datetime DEFAULT CURRENT_TIMESTAMP,
    update_by    varchar(64) DEFAULT '',
    update_time  datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🔧 修复内容

### 1. 移除不存在的字段

#### ❌ 修复前（包含不存在的字段）
```xml
<sql id="selectOptionVo">
    SELECT o.id, o.question_id, o.option_text, o.option_value, o.sort, o.order_num,
           o.status, o.del_flag, o.create_by, o.create_time, o.update_by, o.update_time, o.remark
    FROM psy_t_question_option o
</sql>
```

#### ✅ 修复后（只包含存在的字段）
```xml
<sql id="selectOptionVo">
    SELECT o.id, o.question_id, o.option_text, o.option_value, o.sort,
           o.del_flag, o.create_by, o.create_time, o.update_by, o.update_time
    FROM psy_t_question_option o
</sql>
```

### 2. 修复ResultMap映射

#### ❌ 修复前
```xml
<resultMap id="OptionResultMap" type="PsyTQuestionOption">
    <result property="orderNum" column="order_num"/>    <!-- 字段不存在 -->
    <result property="status" column="status"/>         <!-- 字段不存在 -->
    <result property="remark" column="remark"/>         <!-- 字段不存在 -->
</resultMap>
```

#### ✅ 修复后
```xml
<resultMap id="OptionResultMap" type="PsyTQuestionOption">
    <!-- 注意：以下字段在某些数据库版本中可能不存在，已注释
    <result property="orderNum" column="order_num"/>
    <result property="status" column="status"/>
    <result property="remark" column="remark"/>
    -->
</resultMap>
```

### 3. 修复INSERT语句

#### ❌ 修复前
```xml
<if test="orderNum != null">order_num,</if>
<if test="status != null">status,</if>
<if test="remark != null">remark,</if>
```

#### ✅ 修复后
```xml
<!-- 注意：以下字段在某些数据库版本中可能不存在，已注释
<if test="orderNum != null">order_num,</if>
<if test="status != null">status,</if>
<if test="remark != null">remark,</if>
-->
```

### 4. 修复UPDATE语句

#### ❌ 修复前
```xml
<if test="orderNum != null">order_num = #{orderNum},</if>
<if test="status != null">status = #{status},</if>
<if test="remark != null">remark = #{remark},</if>
```

#### ✅ 修复后
```xml
<!-- 注意：以下字段在某些数据库版本中可能不存在，已注释
<if test="orderNum != null">order_num = #{orderNum},</if>
<if test="status != null">status = #{status},</if>
<if test="remark != null">remark = #{remark},</if>
-->
```

### 5. 修复ORDER BY语句

#### ❌ 修复前
```xml
ORDER BY o.question_id, o.sort, o.order_num
ORDER BY o.sort, o.order_num
```

#### ✅ 修复后
```xml
ORDER BY o.question_id, o.sort
ORDER BY o.sort
```

## 📋 字段对照表

| 实体类属性 | 数据库字段 | 状态 | 说明 |
|------------|------------|------|------|
| id | id | ✅ | 选项ID |
| questionId | question_id | ✅ | 题目ID |
| optionText | option_text | ✅ | 选项文本 |
| optionValue | option_value | ✅ | 选项分值 |
| sort | sort | ✅ | 排序 |
| delFlag | del_flag | ✅ | 删除标志 |
| createBy | create_by | ✅ | 创建者 |
| createTime | create_time | ✅ | 创建时间 |
| updateBy | update_by | ✅ | 更新者 |
| updateTime | update_time | ✅ | 更新时间 |
| orderNum | order_num | ❌ | 不存在（已注释） |
| status | status | ❌ | 不存在（已注释） |
| remark | remark | ❌ | 不存在（已注释） |

## 🧪 验证方法

### 1. 检查数据库表结构
```sql
-- 查看实际的表结构
DESCRIBE psy_t_question_option;

-- 或者使用
SHOW CREATE TABLE psy_t_question_option;
```

### 2. 测试查询方法
```bash
# 重启应用后测试
curl -X POST http://localhost:8080/miniapp/user/assessment/answer \
  -H "Content-Type: application/json" \
  -d '{"recordId": 24, "questionId": 277, "optionId": 762, "responseTime": 30}'
```

### 3. 查看日志输出
应该看到正常的分数计算日志：
```
INFO - 计算分数 - questionId: 277, optionId: 762, answerContent: 不确定
INFO - 选项信息 - optionText: 不确定, optionValue: 3
INFO - 从选项获得分数: 3
INFO - 最终计算分数: questionId: 277, optionId: 762, score: 3
```

## ⚠️ 注意事项

### 1. 数据库版本差异
- 不同环境的数据库表结构可能不同
- 开发、测试、生产环境需要保持一致

### 2. 实体类兼容性
```java
// 实体类中保留这些属性，但数据库中可能不存在
private Integer orderNum;  // 可能为null
private Integer status;    // 可能为null
private String remark;     // 可能为null
```

### 3. 向后兼容
- XML中使用注释而不是删除，便于将来扩展
- 保持实体类属性完整，避免编译错误

### 4. 数据库同步
```sql
-- 如果需要添加缺失的字段（可选）
ALTER TABLE psy_t_question_option ADD COLUMN order_num INT DEFAULT 0 COMMENT '显示顺序';
ALTER TABLE psy_t_question_option ADD COLUMN status TINYINT DEFAULT 1 COMMENT '状态';
ALTER TABLE psy_t_question_option ADD COLUMN remark VARCHAR(500) DEFAULT NULL COMMENT '备注';
```

## 📊 数据类型说明

### option_value 字段类型
根据不同的表定义，`option_value` 可能是：
- `VARCHAR(50)` - 字符串类型
- `INT` - 整数类型

当前实体类定义为 `Integer optionValue`，与数据库类型匹配。

## 🚀 预期效果

修复后应该能够：
1. **正常查询选项**：`selectOptionById` 不再报SQL语法错误
2. **正确计算分数**：分数计算流程正常工作
3. **保存答题记录**：答题分数正确保存到数据库
4. **完整日志输出**：看到详细的分数计算过程

## 📈 后续优化建议

### 1. 统一表结构
- 在所有环境中使用相同的表结构
- 创建标准的DDL脚本

### 2. 版本管理
- 使用数据库版本管理工具（如Flyway）
- 记录表结构变更历史

### 3. 自动检测
```java
// 可以添加启动时的表结构检测
@PostConstruct
public void checkTableStructure() {
    // 检查必要字段是否存在
}
```

现在数据库字段映射问题已经修复，重启应用后分数计算应该能正常工作了！🎉
