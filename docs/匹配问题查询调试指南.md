# 匹配问题查询调试指南

## 🔍 问题描述
数据库中有7条 `psy_match_question` 记录，但 `questionMapper.selectQuestionList(title)` 只查出4条。

## 🔧 调试步骤

### 1. 直接数据库查询检查

#### 查看所有问题记录
```sql
SELECT 
    id,
    title,
    del_flag,
    status,
    sort,
    type,
    parent_id,
    is_required,
    create_time
FROM psy_match_question 
ORDER BY id;
```

#### 查看符合查询条件的记录
```sql
SELECT 
    id,
    title,
    del_flag,
    status,
    sort,
    type,
    parent_id,
    is_required,
    create_time
FROM psy_match_question 
WHERE del_flag = '0'
ORDER BY sort ASC;
```

#### 检查删除标志分布
```sql
SELECT 
    del_flag,
    COUNT(*) as count
FROM psy_match_question 
GROUP BY del_flag;
```

### 2. 使用调试接口

#### 调试接口地址
```bash
GET /test/timeslot/debug/match-questions
GET /test/timeslot/debug/match-questions?title=搜索关键词
```

#### 返回结果分析
调试接口会返回：
- `allQuestionsInDB`: 数据库中所有问题记录
- `filteredQuestions`: 符合 `del_flag='0'` 条件的记录
- `serviceResultCount`: Service方法返回的记录数
- `analysis`: 差异分析

### 3. 可能的原因分析

#### 原因1：删除标志问题
**检查方法**：
```sql
SELECT del_flag, COUNT(*) FROM psy_match_question GROUP BY del_flag;
```

**可能情况**：
- `del_flag = '1'` 或 `del_flag = '2'` 的记录被过滤
- `del_flag` 字段为 `NULL` 的记录

**解决方案**：
```sql
-- 修复删除标志
UPDATE psy_match_question SET del_flag = '0' WHERE del_flag IS NULL OR del_flag != '0';
```

#### 原因2：JOIN条件影响
**检查方法**：
```sql
-- 检查是否因为LEFT JOIN导致记录重复或丢失
SELECT 
    q.id,
    q.title,
    COUNT(o.id) as option_count,
    COUNT(co.consultant_id) as consultant_count
FROM psy_match_question q
LEFT JOIN psy_match_question_option o ON q.id = o.question_id
LEFT JOIN psy_match_consultant_option co ON o.id = co.option_id
WHERE q.del_flag = '0'
GROUP BY q.id, q.title
ORDER BY q.sort ASC;
```

#### 原因3：数据类型问题
**检查方法**：
```sql
-- 检查del_flag字段的数据类型和值
SELECT 
    id,
    title,
    del_flag,
    CHAR_LENGTH(del_flag) as flag_length,
    ASCII(del_flag) as flag_ascii
FROM psy_match_question;
```

### 4. 修复建议

#### 方案1：标准化删除标志
```sql
-- 确保所有记录的del_flag都是标准值
UPDATE psy_match_question 
SET del_flag = '0' 
WHERE del_flag IS NULL OR del_flag NOT IN ('0', '1', '2');
```

#### 方案2：检查并修复数据完整性
```sql
-- 检查是否有孤立的选项记录
SELECT 
    o.id as option_id,
    o.question_id,
    q.id as question_exists
FROM psy_match_question_option o
LEFT JOIN psy_match_question q ON o.question_id = q.id
WHERE q.id IS NULL;

-- 清理孤立的选项记录
DELETE FROM psy_match_question_option 
WHERE question_id NOT IN (SELECT id FROM psy_match_question WHERE del_flag = '0');
```

#### 方案3：优化查询逻辑
如果确认数据正确，可以考虑修改查询逻辑：

```xml
<!-- 简化查询，避免复杂JOIN -->
<select id="selectQuestionListSimple" resultMap="QuestionResult">
    SELECT q.*
    FROM psy_match_question q
    WHERE q.del_flag = '0'
    <if test="title != null and title != ''">
        AND q.title LIKE CONCAT('%', #{title}, '%')
    </if>
    ORDER BY q.sort ASC
</select>
```

### 5. 验证步骤

#### 步骤1：执行调试接口
```bash
curl -X GET "http://localhost:8080/test/timeslot/debug/match-questions"
```

#### 步骤2：分析返回结果
检查：
- `totalInDB` 是否为7
- `filteredCount` 是否为4
- `serviceResultCount` 是否为4

#### 步骤3：根据分析结果采取行动
- 如果 `totalInDB = 7` 但 `filteredCount = 4`，说明有3条记录的 `del_flag` 不为 '0'
- 如果 `filteredCount = 7` 但 `serviceResultCount = 4`，说明Service层有额外的过滤逻辑

### 6. 常见解决方案

#### 解决方案1：修复删除标志
```sql
UPDATE psy_match_question SET del_flag = '0' WHERE id IN (缺失的记录ID);
```

#### 解决方案2：检查状态字段
```sql
-- 如果有status字段影响
SELECT id, title, status, del_flag FROM psy_match_question WHERE del_flag = '0';
```

#### 解决方案3：重建索引
```sql
-- 如果是索引问题
ANALYZE TABLE psy_match_question;
```

## 📝 调试日志

### 正常情况日志
```
调试结果 - 数据库总数: 7, 过滤后: 7, Service返回: 7
```

### 异常情况日志
```
调试结果 - 数据库总数: 7, 过滤后: 4, Service返回: 4
```

## 🔗 相关文件

- `PsyMatchQuestionMapper.xml` - SQL映射文件
- `PsyMatchQuestionServiceImpl.java` - Service实现
- `TimeSlotTestController.java` - 调试控制器

## ⚠️ 注意事项

1. **备份数据**：在执行任何UPDATE操作前，请先备份相关表
2. **测试环境**：建议先在测试环境验证修复方案
3. **影响评估**：修改 `del_flag` 可能影响其他功能，需要全面测试
