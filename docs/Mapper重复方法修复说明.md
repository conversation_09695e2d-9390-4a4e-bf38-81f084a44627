# Mapper重复方法修复说明

## 🚨 问题描述

启动应用时出现以下错误：
```
Mapped Statements collection already contains value for com.xihuan.system.mapper.PsyTAnswerRecordMapper.calculateTotalScore. 
please check file [D:\code\XiHuan\xihuan-system\target\classes\mapper\system\PsyTAnswerRecordMapper.xml]
```

这个错误表明在 `PsyTAnswerRecordMapper.xml` 文件中有重复的方法定义。

## 🔍 问题分析

MyBatis不允许同一个方法ID在同一个Mapper中出现多次。通过检查发现以下重复定义：

1. **calculateTotalScore** - 在第182行和第345行重复定义
2. **calculateDimensionScore** - 在第184行和第365行重复定义  
3. **selectAnswerTimeStats** - 与 `selectAnswerDurationStats` 功能重复

## 🔧 修复内容

### 1. 删除重复的 calculateTotalScore 定义

**删除位置**: 第181-184行
```xml
<!-- 删除前 -->
<select id="calculateTotalScore" parameterType="Long" resultType="java.math.BigDecimal">
    SELECT IFNULL(SUM(answer_score), 0) FROM psy_t_answer_record WHERE record_id = #{recordId} AND del_flag = 0
</select>

<!-- 删除后 -->
<!-- 计算测评记录的总分（已移动到文件末尾，避免重复） -->
```

**保留位置**: 第345-349行（更完整的实现）
```xml
<select id="calculateTotalScore" parameterType="Long" resultType="java.math.BigDecimal">
    SELECT COALESCE(SUM(a.answer_score), 0) as total_score
    FROM psy_t_answer_record a
    WHERE a.record_id = #{recordId} AND a.del_flag = 0
</select>
```

### 2. 删除重复的 calculateDimensionScore 定义

**删除位置**: 第183-189行
```xml
<!-- 删除前 -->
<select id="calculateDimensionScore" resultType="java.math.BigDecimal">
    SELECT IFNULL(SUM(a.answer_score), 0)
    FROM psy_t_answer_record a
    JOIN psy_t_question q ON a.question_id = q.id
    WHERE a.record_id = #{recordId} AND q.subscale_ref = #{dimension} AND a.del_flag = 0
</select>

<!-- 删除后 -->
<!-- 计算维度得分（已移动到文件末尾，避免重复） -->
```

**保留位置**: 第365-371行（更完整的实现）
```xml
<select id="calculateDimensionScore" resultType="java.math.BigDecimal">
    SELECT COALESCE(SUM(a.answer_score), 0) as dimension_score
    FROM psy_t_answer_record a
    INNER JOIN psy_t_question q ON a.question_id = q.id
    WHERE a.record_id = #{recordId} AND a.del_flag = 0
    AND q.subscale_ref = #{dimension}
</select>
```

### 3. 删除功能重复的 selectAnswerTimeStats

**删除位置**: 第391-401行
```xml
<!-- 删除前 -->
<select id="selectAnswerTimeStats" parameterType="Long" resultType="java.util.Map">
    SELECT 
        COUNT(a.id) as total_answers,
        SUM(a.response_time) as total_time,
        AVG(a.response_time) as avg_time,
        MIN(a.response_time) as min_time,
        MAX(a.response_time) as max_time,
        ROUND(STDDEV(a.response_time), 2) as std_time
    FROM psy_t_answer_record a
    WHERE a.record_id = #{recordId} AND a.del_flag = 0 AND a.response_time IS NOT NULL
</select>

<!-- 删除后 -->
<!-- 查询答题时长统计（与selectAnswerDurationStats重复，已删除） -->
```

**保留方法**: `selectAnswerDurationStats`（功能相同，保留更早定义的版本）

## ✅ 修复验证

### 修复前的错误
```
Mapped Statements collection already contains value for com.xihuan.system.mapper.PsyTAnswerRecordMapper.calculateTotalScore
```

### 修复后的状态
- ✅ 删除了重复的 `calculateTotalScore` 定义
- ✅ 删除了重复的 `calculateDimensionScore` 定义  
- ✅ 删除了功能重复的 `selectAnswerTimeStats` 方法
- ✅ 保留了更完整和规范的方法实现

## 📊 修复统计

| 方法名 | 重复次数 | 修复方式 | 保留位置 |
|--------|----------|----------|----------|
| calculateTotalScore | 2 | 删除第一个定义 | 第345行 |
| calculateDimensionScore | 2 | 删除第一个定义 | 第365行 |
| selectAnswerTimeStats | 1 | 删除（功能重复） | 使用selectAnswerDurationStats |

## 🚀 测试建议

修复后请执行以下测试：

1. **重启应用**：确保没有 `Mapped Statements collection already contains value` 错误
2. **功能测试**：测试相关的答题记录功能
3. **接口测试**：使用测试接口验证Mapper方法正常工作

### 测试接口
```bash
# 测试Mapper方法修复效果
GET /test/timeslot/test/mapper-fix
```

## ⚠️ 注意事项

1. **方法命名**: 确保Mapper接口中的方法名与XML中的id完全一致
2. **参数映射**: 检查方法参数与XML中的参数类型匹配
3. **返回类型**: 确保返回类型与resultType或resultMap匹配
4. **SQL语法**: 验证SQL语句在目标数据库中能正确执行

## 🔗 相关文件

- **修复文件**: `xihuan-system/src/main/resources/mapper/system/PsyTAnswerRecordMapper.xml`
- **接口文件**: `xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTAnswerRecordMapper.java`
- **测试文件**: `xihuan-admin/src/main/java/com/xihuan/web/controller/test/TimeSlotTestController.java`

## 📈 后续预防措施

1. **代码审查**: 在添加新方法时检查是否已存在
2. **命名规范**: 使用清晰的方法命名避免功能重复
3. **文档维护**: 及时更新Mapper方法文档
4. **自动化测试**: 添加启动测试确保没有重复定义
