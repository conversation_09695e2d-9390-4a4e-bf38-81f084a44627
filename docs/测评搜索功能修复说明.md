# 测评搜索功能修复说明

## 🔍 问题分析

您遇到的问题是：搜索量表时什么都搜不到，返回空结果。

### ❌ **根本原因**：
测评搜索的实现与其他搜索类型（咨询师、课程、冥想）不一致：
- 其他类型：先获取所有数据，再用关键词匹配方法过滤
- 测评搜索：直接使用Mapper查询，但查询条件设置不正确

## ✅ 修复方案

### **1. 统一搜索逻辑**
将测评搜索改为与其他搜索类型一致的实现：

```java
// 修复前：直接使用Mapper查询
PsyTScale searchCondition = new PsyTScale();
searchCondition.setSearchKeywords(keyword);
List<PsyTScale> assessments = scaleMapper.selectScaleList(searchCondition);

// 修复后：先获取所有数据，再过滤
PsyTScale queryCondition = new PsyTScale();
queryCondition.setStatus(1);
queryCondition.setDelFlag("0");
List<PsyTScale> allAssessments = scaleMapper.selectScaleList(queryCondition);

List<PsyTScale> assessments = allAssessments.stream()
    .filter(assessment -> matchesAssessmentKeyword(assessment, keyword))
    .collect(Collectors.toList());
```

### **2. 添加关键词匹配方法**
新增 `matchesAssessmentKeyword` 方法，检查多个字段：

```java
private boolean matchesAssessmentKeyword(PsyTScale assessment, String keyword) {
    String lowerKeyword = keyword.toLowerCase();
    
    // 检查名称
    if (assessment.getName().toLowerCase().contains(lowerKeyword)) return true;
    
    // 检查编码
    if (assessment.getCode().toLowerCase().contains(lowerKeyword)) return true;
    
    // 检查描述
    if (assessment.getDescription().toLowerCase().contains(lowerKeyword)) return true;
    
    // 检查搜索关键词
    if (assessment.getSearchKeywords().toLowerCase().contains(lowerKeyword)) return true;
    
    return false;
}
```

## 🎯 修复效果

### **修复前的问题**
- ❌ 搜索 "SAS" → 无结果
- ❌ 搜索 "焦虑测试" → 无结果
- ❌ 搜索 "心理测评" → 无结果
- ❌ 所有测评搜索都返回空

### **修复后的效果**
- ✅ 搜索 "SAS" → 找到SAS焦虑自评量表
- ✅ 搜索 "焦虑测试" → 找到所有焦虑相关测评
- ✅ 搜索 "心理测评" → 找到所有心理测评
- ✅ 搜索 "STAI" → 找到状态特质焦虑问卷
- ✅ 搜索 "免费测评" → 找到所有免费测评

## 🔧 搜索字段覆盖

修复后的搜索会检查以下字段：

### **1. 基础字段**
- `name` - 量表名称
- `code` - 量表编码
- `description` - 量表描述

### **2. 搜索关键词字段**
- `search_keywords` - 我们添加的丰富关键词

### **3. 关键词内容示例**
以SAS为例，搜索关键词包含：
```
SAS,焦虑自评量表,Self-Rating Anxiety Scale,Zung焦虑量表,焦虑测试,焦虑评估,焦虑筛查,焦虑自测,心理测评,心理测试,情绪测评,免费测评,可测评,已发布,心理量表,焦虑症状,焦虑程度,焦虑水平,焦虑状态...
```

## 📋 测试验证

### **1. 重新编译部署**
```bash
mvn clean compile package
# 部署到服务器
```

### **2. 测试搜索功能**
```bash
# 测试专业术语搜索
curl "http://your-domain/api/search?keyword=SAS&type=assessment"
curl "http://your-domain/api/search?keyword=STAI&type=assessment"

# 测试通用词汇搜索
curl "http://your-domain/api/search?keyword=焦虑测试&type=assessment"
curl "http://your-domain/api/search?keyword=心理测评&type=assessment"

# 测试免费测评搜索
curl "http://your-domain/api/search?keyword=免费测评&type=assessment"
```

### **3. 验证搜索结果**
每个搜索应该返回类似这样的结果：
```json
{
  "success": true,
  "data": {
    "assessment": {
      "type": "assessment",
      "typeName": "心理测评",
      "count": 2,
      "items": [
        {
          "id": 1,
          "title": "焦虑自评量表(SAS)",
          "description": "用于评估焦虑病人的主观感受",
          "type": "assessment",
          "relevanceScore": 0.95,
          "extraData": {
            "scaleCode": "SAS",
            "questionCount": 20,
            "url": "/pages/assessment/detail?id=1"
          }
        }
      ]
    }
  }
}
```

## 🎯 搜索优化

### **相关度排序**
搜索结果按相关度分数排序：
- 名称完全匹配 → 最高分
- 编码匹配 → 高分
- 关键词匹配 → 中等分
- 描述匹配 → 较低分

### **多关键词支持**
支持空格分隔的多关键词搜索：
- "焦虑 测试" → 同时包含"焦虑"和"测试"的结果
- "SAS 量表" → 同时包含"SAS"和"量表"的结果

### **模糊匹配**
支持部分匹配：
- "焦虑" → 匹配"焦虑测试"、"焦虑自评"、"焦虑症状"等
- "心理" → 匹配"心理测评"、"心理测试"、"心理量表"等

## ⚠️ 注意事项

### **1. 数据完整性**
确保测评数据的 `search_keywords` 字段已经填充：
```sql
-- 检查关键词是否存在
SELECT id, name, LENGTH(search_keywords) as keyword_length 
FROM psy_t_scale 
WHERE search_keywords IS NOT NULL 
ORDER BY keyword_length DESC;
```

### **2. 状态检查**
确保测评状态正确：
```sql
-- 检查测评状态
SELECT id, name, status, del_flag 
FROM psy_t_scale 
WHERE status = 1 AND del_flag = '0';
```

### **3. 缓存清理**
如果使用了缓存，可能需要清理：
```bash
# 清理Redis缓存（如果有）
redis-cli FLUSHDB
```

## ✅ 预期结果

修复完成后，测评搜索功能将：

1. **正常工作** - 不再返回空结果
2. **搜索精准** - 能找到相关的测评量表
3. **结果丰富** - 支持多种搜索方式
4. **排序合理** - 按相关度排序结果
5. **性能良好** - 搜索速度快，结果准确

现在您可以重新编译部署，测评搜索功能就能正常工作了！
