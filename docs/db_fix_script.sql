-- 检查psy_t_question表是否存在question_type字段
SELECT COUNT(*) 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_question' 
  AND COLUMN_NAME = 'question_type';

-- 如果不存在，添加question_type字段
ALTER TABLE psy_t_question 
ADD COLUMN question_type INT DEFAULT 1 COMMENT '题目类型(1=单选 2=多选 3=填空 4=量表)' AFTER question_text;

-- 更新已有数据，设置默认题目类型为单选
UPDATE psy_t_question SET question_type = 1 WHERE question_type IS NULL;

-- 检查是否有其他缺失的字段
SELECT COLUMN_NAME 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_question';

-- 确保其他必要字段也存在
ALTER TABLE psy_t_question 
ADD COLUMN IF NOT EXISTS is_required INT DEFAULT 1 COMMENT '是否必答(0=否 1=是)' AFTER question_type,
ADD COLUMN IF NOT EXISTS score_type INT DEFAULT 1 COMMENT '计分类型(1=正向 2=反向)' AFTER is_required,
ADD COLUMN IF NOT EXISTS dimension VARCHAR(50) DEFAULT NULL COMMENT '所属维度' AFTER score_type;

-- 检查psy_t_scale表结构是否完整
SELECT COLUMN_NAME 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale';

-- 确保scale表的必要字段存在
ALTER TABLE psy_t_scale 
ADD COLUMN IF NOT EXISTS rating_avg DECIMAL(3,1) DEFAULT 0.0 COMMENT '平均评分' AFTER test_count,
ADD COLUMN IF NOT EXISTS rating_count INT DEFAULT 0 COMMENT '评分人数' AFTER rating_avg;

-- 检查psy_t_question_option表是否存在
SELECT COUNT(*) 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_question_option';

-- 如果不存在，创建选项表
CREATE TABLE IF NOT EXISTS psy_t_question_option (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '选项ID',
  question_id BIGINT NOT NULL COMMENT '题目ID',
  option_text VARCHAR(500) NOT NULL COMMENT '选项文本',
  option_value VARCHAR(50) DEFAULT NULL COMMENT '选项值',
  option_score INT DEFAULT 0 COMMENT '选项分数',
  option_order INT DEFAULT 0 COMMENT '选项顺序',
  is_correct INT DEFAULT 0 COMMENT '是否正确答案(0=否 1=是)',
  del_flag INT DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  INDEX idx_question_id (question_id)
) COMMENT='题目选项表';

-- 检查关联表是否存在
SELECT COUNT(*) 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale_category';

-- 如果不存在，创建量表分类关联表
CREATE TABLE IF NOT EXISTS psy_t_scale_category (
  scale_id BIGINT NOT NULL COMMENT '量表ID',
  category_id BIGINT NOT NULL COMMENT '分类ID',
  PRIMARY KEY (scale_id, category_id)
) COMMENT='量表分类关联表';

-- 检查解释表是否存在
SELECT COUNT(*) 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_interpretation';

-- 如果不存在，创建解释表
CREATE TABLE IF NOT EXISTS psy_t_interpretation (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT '解释ID',
  scale_id BIGINT NOT NULL COMMENT '量表ID',
  min_score INT DEFAULT 0 COMMENT '最小分数',
  max_score INT DEFAULT 100 COMMENT '最大分数',
  interpretation_text TEXT COMMENT '解释文本',
  level_name VARCHAR(50) DEFAULT NULL COMMENT '等级名称',
  level_color VARCHAR(20) DEFAULT NULL COMMENT '等级颜色',
  dimension VARCHAR(50) DEFAULT NULL COMMENT '所属维度',
  del_flag INT DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  INDEX idx_scale_id (scale_id)
) COMMENT='量表解释表';
