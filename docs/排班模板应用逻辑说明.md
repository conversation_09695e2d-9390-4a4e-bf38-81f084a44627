# 排班模板应用逻辑说明

## 概述

系统现在支持完整的排班模板应用逻辑，不再使用硬编码的默认时间，而是根据咨询师的个性化排班模板来生成排班记录。

## 核心逻辑流程

### 1. 排班生成优先级

```
1️⃣ 指定日期有效的排班模板
   ├── 查询在指定日期范围内有效的模板
   └── 根据模板的生效开始和结束日期判断

2️⃣ 咨询师的默认排班模板
   ├── 查询咨询师设置的默认模板（is_default = 1）
   └── 使用默认模板的时间设置

3️⃣ 系统默认设置
   ├── 当没有任何模板时的兜底方案
   └── 默认时间：9:00-21:00
```

### 2. 模板应用逻辑

#### A. 获取有效模板
```java
// 1. 优先获取指定日期有效的模板
PsyTimeScheduleTemplate effectiveTemplate = templateService.selectEffectiveTemplate(counselorId, date);

// 2. 如果没有，获取默认模板
if (effectiveTemplate == null) {
    effectiveTemplate = templateService.selectDefaultTemplateByCounselorId(counselorId);
}

// 3. 如果都没有，使用系统默认设置
if (effectiveTemplate == null) {
    applyDefaultScheduleSettings(schedule);
}
```

#### B. 模板时间计算
```java
// 根据当前日期是星期几，查找对应的模板明细
int dayOfWeek = date.getDayOfWeek().getValue(); // 1=周一, 7=周日

// 筛选匹配当前星期的模板明细
List<PsyTimeTemplateItem> dayItems = template.getTemplateItems().stream()
    .filter(item -> item.getDayOfWeek().equals(dayOfWeek))
    .filter(item -> item.getDelFlag() == 0)
    .sorted(by startTime)
    .collect(toList());

// 计算工作时间范围
LocalTime earliestStart = dayItems.stream().map(getStartTime).min();
LocalTime latestEnd = dayItems.stream().map(getEndTime).max();
```

## 数据结构说明

### 1. 排班模板表 (psy_time_schedule_template)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 模板ID |
| counselor_id | bigint | 咨询师ID |
| name | varchar | 模板名称 |
| is_default | tinyint | 是否默认模板(0否 1是) |
| effective_start | date | 生效开始日期 |
| effective_end | date | 生效结束日期 |

### 2. 模板明细表 (psy_time_template_item)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 明细ID |
| template_id | bigint | 模板ID |
| day_of_week | int | 星期几(1-7) |
| start_time | time | 开始时间 |
| end_time | time | 结束时间 |
| center_id | bigint | 咨询中心ID |

## 应用场景示例

### 场景1：咨询师有完整的周排班模板

```
模板名称：标准工作模板
生效期间：2024-01-01 到 2024-12-31
模板明细：
- 周一: 9:00-18:00
- 周二: 9:00-18:00  
- 周三: 9:00-18:00
- 周四: 9:00-18:00
- 周五: 9:00-18:00
- 周六: 14:00-21:00 (只做下午和晚上)
- 周日: 休息 (无明细记录)

生成结果：
- 周一到周五：9:00-18:00 排班
- 周六：14:00-21:00 排班
- 周日：使用系统默认 9:00-21:00 排班
```

### 场景2：咨询师只做晚上咨询

```
模板名称：晚间咨询模板
模板明细：
- 周一到周五: 18:00-21:00
- 周六周日: 19:00-22:00

生成结果：
- 工作日：18:00-21:00 排班
- 周末：19:00-22:00 排班
```

### 场景3：咨询师有特殊时间安排

```
模板名称：灵活时间模板
模板明细：
- 周一: 10:00-12:00, 14:00-16:00 (多个时间段)
- 周三: 9:00-21:00 (全天)
- 周五: 19:00-21:00 (只做晚上)

生成结果：
- 周一：10:00-16:00 排班 (取最早开始和最晚结束)
- 周三：9:00-21:00 排班
- 周五：19:00-21:00 排班
- 其他天：使用系统默认 9:00-21:00 排班
```

### 场景4：咨询师无任何模板

```
情况：新注册的咨询师，还没有设置排班模板

生成结果：
- 所有天：9:00-21:00 排班 (系统默认设置)
```

## 系统默认设置

### 1. 可配置的默认时间

```java
// 支持从系统配置读取，也可以硬编码
private LocalTime getSystemDefaultStartTime() {
    // 可以从配置表读取: schedule.default.start.time
    return LocalTime.of(9, 0); // 默认9:00
}

private LocalTime getSystemDefaultEndTime() {
    // 可以从配置表读取: schedule.default.end.time  
    return LocalTime.of(21, 0); // 默认21:00
}
```

### 2. 兜底机制

- 模板查询失败时使用硬编码默认值
- 模板应用失败时回退到系统默认设置
- 确保系统始终能生成有效的排班记录

## 测试接口

### 1. 模板信息查询
```bash
# 查看咨询师的模板信息
GET /test/scheduleTemplate/templateInfo/{counselorId}
```

### 2. 模板应用测试
```bash
# 测试模板应用逻辑
GET /test/scheduleTemplate/testTemplateApplication/{counselorId}

# 为咨询师生成排班（应用模板）
POST /test/scheduleTemplate/ensureWithTemplate/{counselorId}?days=7
```

### 3. 时间段匹配测试
```bash
# 测试时间段匹配逻辑
GET /test/scheduleTemplate/testTimeRangeMatching
```

## 优势特点

### 1. 个性化支持
- 完全支持咨询师的个性化工作时间安排
- 支持不同星期的不同工作时间
- 支持临时性的特殊排班安排

### 2. 灵活性
- 支持多个时间段的合并计算
- 支持模板的生效期间设置
- 支持默认模板和临时模板

### 3. 稳定性
- 完善的异常处理机制
- 多层级的兜底方案
- 确保系统始终能正常运行

### 4. 可维护性
- 清晰的逻辑层次
- 详细的日志记录
- 便于调试和问题排查

## 注意事项

1. **模板优先级**：有效期模板 > 默认模板 > 系统默认
2. **时间计算**：多个时间段取最早开始和最晚结束时间
3. **星期匹配**：严格按照模板中的星期设置进行匹配
4. **异常处理**：任何环节出错都会回退到系统默认设置
5. **性能考虑**：模板查询结果会包含完整的明细信息
