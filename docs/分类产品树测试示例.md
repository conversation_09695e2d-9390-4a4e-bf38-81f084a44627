# 分类产品树测试示例

## 测试接口

### 接口地址
```bash
GET /system/category/treeWithProducts
```

### 预期响应结构
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "categories": [
      {
        "categoryId": 11,
        "categoryName": "团单",
        "parentId": 0,
        "orderNum": 1,
        "status": "0",
        "products": [],
        "children": []
      },
      {
        "categoryId": 12,
        "categoryName": "咨询师",
        "parentId": 0,
        "orderNum": 2,
        "status": "0",
        "products": [],
        "children": []
      },
      {
        "categoryId": 13,
        "categoryName": "课程",
        "parentId": 0,
        "orderNum": 3,
        "status": "0",
        "products": [],
        "children": []
      },
      {
        "categoryId": 14,
        "categoryName": "冥想",
        "parentId": 0,
        "orderNum": 4,
        "status": "0",
        "products": [],
        "children": []
      },
      {
        "categoryId": 16,
        "categoryName": "测评",
        "parentId": 0,
        "orderNum": 5,
        "status": "0",
        "products": [
          {
            "id": 1,
            "title": "心理健康测评",
            "description": "专业的心理健康评估工具",
            "imageUrl": "/images/assessment1.jpg",
            "price": 29.90,
            "questionCount": 50,
            "payMode": 1,
            "status": 1
          }
        ],
        "children": [
          {
            "categoryId": 17,
            "categoryName": "儿童类",
            "parentId": 16,
            "orderNum": 1,
            "status": "0",
            "products": [
              {
                "id": 2,
                "title": "儿童注意力测评",
                "description": "专业的儿童注意力评估",
                "imageUrl": "/images/child-attention.jpg",
                "price": 19.90,
                "questionCount": 30,
                "payMode": 1,
                "status": 1
              }
            ]
          },
          {
            "categoryId": 18,
            "categoryName": "健康类",
            "parentId": 16,
            "orderNum": 2,
            "status": "0",
            "products": []
          }
        ]
      }
    ],
    "totalCount": 5
  }
}
```

## 前端测试代码

### JavaScript测试
```javascript
// 测试分类产品树接口
async function testCategoryTreeWithProducts() {
  try {
    console.log('开始测试分类产品树接口...');
    
    const response = await fetch('/system/category/treeWithProducts', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // 添加认证头如果需要
        'Authorization': 'Bearer your-token'
      }
    });
    
    const result = await response.json();
    
    console.log('接口响应:', result);
    
    if (result.code === 200) {
      console.log('✅ 接口调用成功');
      
      const categories = result.data.categories;
      console.log(`📊 总分类数: ${result.data.totalCount}`);
      
      // 遍历每个分类
      categories.forEach((category, index) => {
        console.log(`\n${index + 1}. 分类: ${category.categoryName} (ID: ${category.categoryId})`);
        console.log(`   产品数量: ${category.products.length}`);
        
        // 显示产品信息
        if (category.products.length > 0) {
          console.log('   产品列表:');
          category.products.forEach((product, pIndex) => {
            console.log(`     ${pIndex + 1}. ${product.title || product.name || '未知产品'}`);
          });
        }
        
        // 显示子分类
        if (category.children && category.children.length > 0) {
          console.log(`   子分类数量: ${category.children.length}`);
          category.children.forEach((child, cIndex) => {
            console.log(`     ${cIndex + 1}. ${child.categoryName} (产品: ${child.products.length}个)`);
          });
        }
      });
      
    } else {
      console.error('❌ 接口调用失败:', result.msg);
    }
    
  } catch (error) {
    console.error('❌ 请求异常:', error);
  }
}

// 执行测试
testCategoryTreeWithProducts();
```

### Vue.js测试组件
```vue
<template>
  <div class="category-tree-test">
    <h2>分类产品树测试</h2>
    
    <button @click="loadCategoryTree" :loading="loading">
      {{ loading ? '加载中...' : '加载分类树' }}
    </button>
    
    <div v-if="categoryTree.length > 0" class="tree-container">
      <div v-for="category in categoryTree" :key="category.categoryId" class="category-item">
        <h3>{{ category.categoryName }} ({{ category.products.length }}个产品)</h3>
        
        <!-- 产品列表 -->
        <div v-if="category.products.length > 0" class="products">
          <h4>产品:</h4>
          <ul>
            <li v-for="product in category.products" :key="product.id">
              {{ product.title || product.name }}
              <span v-if="product.price"> - ¥{{ product.price }}</span>
            </li>
          </ul>
        </div>
        
        <!-- 子分类 -->
        <div v-if="category.children && category.children.length > 0" class="children">
          <h4>子分类:</h4>
          <div v-for="child in category.children" :key="child.categoryId" class="child-category">
            <h5>{{ child.categoryName }} ({{ child.products.length }}个产品)</h5>
            <ul v-if="child.products.length > 0">
              <li v-for="product in child.products" :key="product.id">
                {{ product.title || product.name }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="error" class="error">
      错误: {{ error }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategoryTreeTest',
  data() {
    return {
      categoryTree: [],
      loading: false,
      error: null
    }
  },
  
  mounted() {
    this.loadCategoryTree();
  },
  
  methods: {
    async loadCategoryTree() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await this.$http.get('/system/category/treeWithProducts');
        
        if (response.data.code === 200) {
          this.categoryTree = response.data.data.categories;
          console.log('分类树加载成功:', this.categoryTree);
        } else {
          this.error = response.data.msg || '加载失败';
        }
        
      } catch (error) {
        this.error = '网络请求失败: ' + error.message;
        console.error('加载分类树失败:', error);
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style scoped>
.category-tree-test {
  padding: 20px;
}

.category-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.products, .children {
  margin-top: 10px;
}

.child-category {
  margin-left: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 3px;
  margin-bottom: 10px;
}

.error {
  color: red;
  padding: 10px;
  background-color: #ffe6e6;
  border-radius: 5px;
}
</style>
```

## 小程序测试代码

### 小程序页面测试
```javascript
// pages/test/category-tree.js
Page({
  data: {
    categoryTree: [],
    loading: false,
    error: ''
  },

  onLoad() {
    this.loadCategoryTree();
  },

  async loadCategoryTree() {
    this.setData({ loading: true, error: '' });

    try {
      const result = await this.request('/system/category/treeWithProducts');
      
      if (result.code === 200) {
        this.setData({
          categoryTree: result.data.categories
        });
        
        console.log('分类树数据:', result.data);
        
        // 显示统计信息
        wx.showToast({
          title: `加载了${result.data.totalCount}个分类`,
          icon: 'success'
        });
        
      } else {
        this.setData({ error: result.msg || '加载失败' });
      }
      
    } catch (error) {
      console.error('加载分类树失败:', error);
      this.setData({ error: '网络请求失败' });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 网络请求封装
  request(url, options = {}) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `https://your-domain.com${url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          // 添加认证头
          'Authorization': wx.getStorageSync('token')
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        },
        fail: reject
      });
    });
  },

  // 点击分类
  onCategoryTap(e) {
    const { categoryId, categoryName } = e.currentTarget.dataset;
    console.log('点击分类:', categoryName, categoryId);
    
    // 跳转到分类详情页
    wx.navigateTo({
      url: `/pages/category/detail?id=${categoryId}&name=${categoryName}`
    });
  },

  // 重新加载
  onRefresh() {
    this.loadCategoryTree();
  }
});
```

## 测试检查点

### 1. 基础功能测试
- ✅ 接口能正常响应
- ✅ 返回正确的JSON格式
- ✅ 包含所有顶级分类
- ✅ 测评分类包含子分类

### 2. 数据完整性测试
- ✅ 每个分类都有基本信息（ID、名称、状态等）
- ✅ 产品数据格式正确
- ✅ 子分类数据完整
- ✅ 排序正确

### 3. 产品类型测试
- ✅ 团单(11) - 返回产品列表
- ✅ 咨询师(12) - 返回咨询师列表
- ✅ 课程(13) - 返回课程列表
- ✅ 冥想(14) - 返回冥想列表
- ✅ 测评(16) - 返回测评列表及子分类

### 4. 异常处理测试
- ✅ 服务不可用时返回空列表
- ✅ 数据查询失败时不影响其他分类
- ✅ 网络异常时有合适的错误提示

## 预期结果

1. **成功响应**: 返回包含所有分类及其产品的树形结构
2. **测评分类**: 包含6个子分类，每个子分类都有对应的测评产品
3. **其他分类**: 根据实际数据返回对应的产品列表
4. **性能**: 响应时间在合理范围内（< 2秒）

现在可以使用这些测试代码来验证分类产品树接口的功能！🧪
