# 测评系统完整接口清单

## 概述

本文档列出了测评系统的所有API接口，包括小程序用户端、后台管理端的完整接口清单。

## 小程序用户端接口

### 1. 量表浏览接口 (8个)

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 查询启用的量表列表 | GET | `/miniapp/user/assessment/scales` | 获取所有启用的量表 |
| 2 | 查询热门量表 | GET | `/miniapp/user/assessment/scales/hot` | 获取热门量表列表 |
| 3 | 查询最新量表 | GET | `/miniapp/user/assessment/scales/latest` | 获取最新发布的量表 |
| 4 | 根据分类查询量表 | GET | `/miniapp/user/assessment/scales/category/{categoryId}` | 按分类筛选量表 |
| 5 | 搜索量表 | GET | `/miniapp/user/assessment/scales/search` | 关键词搜索量表 |
| 6 | 获取量表详情 | GET | `/miniapp/user/assessment/scales/{id}` | 获取量表详细信息 |
| 7 | 查询用户收藏的量表 | GET | `/miniapp/user/assessment/favorites/{userId}` | 获取用户收藏列表 |
| 8 | 查询相似量表推荐 | GET | `/miniapp/user/assessment/recommendations/{scaleId}` | 获取推荐量表 |

### 2. 测评流程接口 (11个)

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 检查用户是否可以开始测评 | GET | `/miniapp/user/assessment/check-can-start` | 验证测评权限 |
| 2 | 开始测评 | POST | `/miniapp/user/assessment/start` | 创建测评记录 |
| 3 | 获取测评题目 | GET | `/miniapp/user/assessment/questions/{recordId}` | 获取量表所有题目 |
| 4 | 获取下一题 | GET | `/miniapp/user/assessment/next-question/{recordId}` | 获取下一道题目 |
| 5 | 获取上一题 | GET | `/miniapp/user/assessment/previous-question/{recordId}` | 获取上一道题目 |
| 6 | 保存答题记录 | POST | `/miniapp/user/assessment/answer` | 提交单题答案 |
| 7 | 查询答题进度 | GET | `/miniapp/user/assessment/progress/{recordId}` | 获取答题进度 |
| 8 | 暂停测评 | POST | `/miniapp/user/assessment/pause/{recordId}` | 暂停当前测评 |
| 9 | 恢复测评 | POST | `/miniapp/user/assessment/resume/{recordId}` | 恢复暂停的测评 |
| 10 | 完成测评 | POST | `/miniapp/user/assessment/complete/{recordId}` | 提交完成测评 |
| 11 | 取消测评 | POST | `/miniapp/user/assessment/cancel/{recordId}` | 取消当前测评 |

### 3. 测评结果接口 (2个)

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 查询测评结果 | GET | `/miniapp/user/assessment/result/{recordId}` | 获取测评结果详情 |
| 2 | 生成测评报告 | GET | `/miniapp/user/assessment/report/{recordId}` | 生成测评报告 |

### 4. 测评记录管理接口 (5个)

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 查询用户的测评记录 | GET | `/miniapp/user/assessment/records/{userId}` | 获取用户所有测评记录 |
| 2 | 查询用户最近的测评记录 | GET | `/miniapp/user/assessment/records/{userId}/recent` | 获取最近测评记录 |
| 3 | 查询用户未完成的测评记录 | GET | `/miniapp/user/assessment/records/{userId}/incomplete` | 获取未完成的测评 |
| 4 | 查询用户已完成的测评记录 | GET | `/miniapp/user/assessment/records/{userId}/completed` | 获取已完成的测评 |
| 5 | 查询用户测评统计 | GET | `/miniapp/user/assessment/stats/{userId}` | 获取用户测评统计信息 |

### 5. 测评订单接口 (8个)

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 查询用户订单列表 | GET | `/miniapp/user/order/list` | 获取用户所有订单 |
| 2 | 查询订单详情 | GET | `/miniapp/user/order/{id}` | 获取订单详细信息 |
| 3 | 创建订单 | POST | `/miniapp/user/order/create` | 创建测评订单 |
| 4 | 支付订单 | POST | `/miniapp/user/order/pay` | 支付订单 |
| 5 | 取消订单 | POST | `/miniapp/user/order/cancel` | 取消未支付订单 |
| 6 | 申请退款 | POST | `/miniapp/user/order/refund` | 申请订单退款 |
| 7 | 查询待支付订单 | GET | `/miniapp/user/order/pending` | 获取待支付订单列表 |
| 8 | 查询已支付订单 | GET | `/miniapp/user/order/paid` | 获取已支付订单列表 |

### 6. 测评评价接口 (11个)

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 提交测评评价 | POST | `/miniapp/user/assessment/review` | 提交测评评价 |
| 2 | 获取量表评价列表 | GET | `/miniapp/user/assessment/reviews/{scaleId}` | 获取量表的所有评价 |
| 3 | 获取用户评价列表 | GET | `/miniapp/user/assessment/reviews/user` | 获取用户的所有评价 |
| 4 | 获取评价详情 | GET | `/miniapp/user/assessment/review/{id}` | 获取评价详细信息 |
| 5 | 根据记录获取评价 | GET | `/miniapp/user/assessment/review/record/{recordId}` | 根据测评记录获取评价 |
| 6 | 检查评价权限 | GET | `/miniapp/user/assessment/review/check` | 验证评价权限 |
| 7 | 获取量表评价统计 | GET | `/miniapp/user/assessment/review/stats/{scaleId}` | 获取量表评价统计 |
| 8 | 获取用户评价统计 | GET | `/miniapp/user/assessment/review/stats/user` | 获取用户评价统计 |
| 9 | 获取评价摘要 | GET | `/miniapp/user/assessment/review/summary/{scaleId}` | 获取量表评价摘要 |
| 10 | 获取热门评价 | GET | `/miniapp/user/assessment/review/hot` | 获取热门评价列表 |
| 11 | 搜索评价 | GET | `/miniapp/user/assessment/review/search` | 搜索评价内容 |

## 小程序用户端接口统计

| 模块 | 接口数量 | 说明 |
|------|----------|------|
| 量表浏览 | 8个 | 量表查询、搜索、推荐等 |
| 测评流程 | 11个 | 完整的测评流程管理 |
| 测评结果 | 2个 | 结果查询和报告生成 |
| 测评记录 | 5个 | 测评记录管理和统计 |
| 测评订单 | 8个 | 订单管理和支付流程 |
| 测评评价 | 11个 | 评价管理和统计分析 |
| **总计** | **45个** | 完整的小程序端功能 |

## 后台管理端接口

### 1. 量表管理接口

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 查询量表列表 | GET | `/system/scale/list` | 后台量表列表管理 |
| 2 | 获取量表详情 | GET | `/system/scale/{id}` | 后台量表详情查看 |
| 3 | 新增量表 | POST | `/system/scale` | 创建新量表 |
| 4 | 修改量表 | PUT | `/system/scale` | 编辑量表信息 |
| 5 | 删除量表 | DELETE | `/system/scale/{ids}` | 删除量表 |
| 6 | 发布量表 | POST | `/system/scale/{id}/publish` | 发布量表 |
| 7 | 下架量表 | POST | `/system/scale/{id}/unpublish` | 下架量表 |

### 2. 订单管理接口

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 查询订单列表 | GET | `/system/assessment/order/list` | 后台订单列表 |
| 2 | 查询订单详情 | GET | `/system/assessment/order/{id}` | 后台订单详情 |
| 3 | 处理退款 | POST | `/system/assessment/order/refund/{id}` | 处理退款申请 |
| 4 | 订单统计 | GET | `/system/assessment/order/stats` | 订单统计信息 |

### 3. 评价管理接口

| 序号 | 接口名称 | 请求方式 | 接口路径 | 功能描述 |
|------|----------|----------|----------|----------|
| 1 | 查询评价列表 | GET | `/system/assessment/review/list` | 后台评价列表 |
| 2 | 获取评价详情 | GET | `/system/assessment/review/{id}` | 后台评价详情 |
| 3 | 审核评价 | PUT | `/system/assessment/review/audit/{id}` | 审核评价内容 |
| 4 | 批量审核评价 | PUT | `/system/assessment/review/audit/batch` | 批量审核评价 |
| 5 | 置顶评价 | PUT | `/system/assessment/review/top/{id}` | 置顶评价 |
| 6 | 删除评价 | DELETE | `/system/assessment/review/{ids}` | 删除评价 |
| 7 | 查询待审核评价 | GET | `/system/assessment/review/pending` | 待审核评价列表 |
| 8 | 评价统计 | GET | `/system/assessment/review/statistics` | 评价统计信息 |
| 9 | 搜索评价 | GET | `/system/assessment/review/search` | 搜索评价 |
| 10 | 导出评价数据 | POST | `/system/assessment/review/export` | 导出评价数据 |

## 接口开发状态

### ✅ 已完成接口

- [x] 量表浏览相关接口 (8/8)
- [x] 测评流程相关接口 (11/11)
- [x] 测评结果相关接口 (2/2)
- [x] 测评记录管理接口 (5/5)
- [x] 测评订单相关接口 (8/8)
- [x] 测评评价相关接口 (11/11)

### 📝 接口文档状态

- [x] 完整接口文档已生成
- [x] 数据模型定义完整
- [x] 状态码说明完整
- [x] 前端配合修改说明已提供
- [x] API变更对比表已更新

## 总结

测评系统现已包含完整的45个小程序用户端接口，覆盖了：

1. **量表管理**：浏览、搜索、推荐、收藏
2. **测评流程**：权限检查、开始、答题、暂停、完成
3. **结果管理**：结果查询、报告生成
4. **记录管理**：历史记录、统计分析
5. **订单管理**：创建、支付、退款、查询
6. **评价管理**：提交、点赞、搜索、统计

所有接口都已实现并提供了完整的文档，前端可以根据文档进行对接开发。
