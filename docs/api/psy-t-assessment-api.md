# 心理测评系统API文档

## 概述

本文档描述了新心理测评系统（psy_t_*表）的API接口，包括后台管理接口和小程序接口。

## 基础信息

- 基础URL: `/api`
- 认证方式: JWT Token
- 数据格式: JSON
- 字符编码: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

## 1. 量表管理API

### 1.1 后台管理接口

#### 查询量表列表
- **接口**: `GET /system/scale/list`
- **权限**: `system:scale:list`
- **参数**:
  - `name`: 量表名称（可选）
  - `code`: 量表编码（可选）
  - `scoringType`: 计分类型（可选）
  - `payMode`: 付费模式（可选）
  - `status`: 状态（可选）
- **响应**: 分页的量表列表

#### 获取量表详情
- **接口**: `GET /system/scale/{id}`
- **权限**: `system:scale:query`
- **参数**: 
  - `id`: 量表ID
- **响应**: 量表详细信息（包含题目、分量表、计分规则）

#### 新增量表
- **接口**: `POST /system/scale`
- **权限**: `system:scale:add`
- **请求体**: 量表信息对象
- **响应**: 操作结果

#### 修改量表
- **接口**: `PUT /system/scale`
- **权限**: `system:scale:edit`
- **请求体**: 量表信息对象
- **响应**: 操作结果

#### 删除量表
- **接口**: `DELETE /system/scale/{ids}`
- **权限**: `system:scale:remove`
- **参数**: 
  - `ids`: 量表ID数组
- **响应**: 操作结果

#### 发布量表
- **接口**: `POST /system/scale/{id}/publish`
- **权限**: `system:scale:edit`
- **参数**: 
  - `id`: 量表ID
- **响应**: 操作结果

#### 下架量表
- **接口**: `POST /system/scale/{id}/unpublish`
- **权限**: `system:scale:edit`
- **参数**: 
  - `id`: 量表ID
- **响应**: 操作结果

#### 复制量表
- **接口**: `POST /system/scale/{id}/copy`
- **权限**: `system:scale:add`
- **参数**: 
  - `id`: 源量表ID
- **请求体**: 目标量表信息
- **响应**: 操作结果

#### 验证量表完整性
- **接口**: `GET /system/scale/{id}/validate`
- **权限**: `system:scale:query`
- **参数**: 
  - `id`: 量表ID
- **响应**: 验证结果

### 1.2 小程序接口

#### 查询量表列表
- **接口**: `GET /miniapp/user/psy-assessment/scale/list`
- **参数**:
  - `pageNum`: 页码
  - `pageSize`: 页大小
- **响应**: 分页的量表列表（仅已发布）

#### 获取量表详情
- **接口**: `GET /miniapp/user/psy-assessment/scale/{id}`
- **参数**: 
  - `id`: 量表ID
- **响应**: 量表详细信息

#### 搜索量表
- **接口**: `GET /miniapp/user/psy-assessment/scale/search`
- **参数**:
  - `keyword`: 搜索关键词（可选）
  - `scoringType`: 计分类型（可选）
  - `payMode`: 付费模式（可选）
- **响应**: 分页的量表列表

#### 查询热门量表
- **接口**: `GET /miniapp/user/psy-assessment/scale/hot`
- **参数**:
  - `limit`: 限制数量（默认10）
- **响应**: 热门量表列表

#### 查询免费量表
- **接口**: `GET /miniapp/user/psy-assessment/scale/free`
- **响应**: 免费量表列表

#### 查询推荐量表
- **接口**: `GET /miniapp/user/psy-assessment/scale/recommend`
- **参数**:
  - `limit`: 限制数量（默认10）
- **响应**: 推荐量表列表

#### 查询最新量表
- **接口**: `GET /miniapp/user/psy-assessment/scale/latest`
- **参数**:
  - `limit`: 限制数量（默认10）
- **响应**: 最新量表列表

#### 查询收藏量表
- **接口**: `GET /miniapp/user/psy-assessment/scale/favorite`
- **响应**: 分页的收藏量表列表

## 2. 测评流程API

### 2.1 开始测评
- **接口**: `POST /miniapp/user/psy-assessment/start`
- **参数**:
  - `scaleId`: 量表ID
- **响应**: 测评会话信息
```json
{
  "sessionId": "uuid",
  "recordId": 123,
  "scaleInfo": {},
  "firstQuestion": {}
}
```

### 2.2 获取题目
- **接口**: `GET /miniapp/user/psy-assessment/{sessionId}/question/{questionNo}`
- **参数**:
  - `sessionId`: 会话ID
  - `questionNo`: 题目序号
- **响应**: 题目信息
```json
{
  "questionId": 123,
  "questionNo": 1,
  "content": "题目内容",
  "type": "single_choice",
  "options": [],
  "isRequired": true,
  "hasNext": true,
  "hasPrevious": false
}
```

### 2.3 提交答案
- **接口**: `POST /miniapp/user/psy-assessment/{sessionId}/answer`
- **参数**:
  - `sessionId`: 会话ID
  - `questionId`: 题目ID
  - `answerContent`: 答案内容
  - `responseTime`: 答题耗时（可选）
- **响应**: 提交结果

### 2.4 批量提交答案
- **接口**: `POST /miniapp/user/psy-assessment/{sessionId}/batch-answer`
- **参数**:
  - `sessionId`: 会话ID
- **请求体**: 答案列表
- **响应**: 提交结果

### 2.5 获取下一题
- **接口**: `GET /miniapp/user/psy-assessment/{sessionId}/next`
- **参数**:
  - `sessionId`: 会话ID
- **响应**: 下一题信息

### 2.6 获取上一题
- **接口**: `GET /miniapp/user/psy-assessment/{sessionId}/previous`
- **参数**:
  - `sessionId`: 会话ID
- **响应**: 上一题信息

### 2.7 跳转到指定题目
- **接口**: `GET /miniapp/user/psy-assessment/{sessionId}/jump/{questionNo}`
- **参数**:
  - `sessionId`: 会话ID
  - `questionNo`: 题目序号
- **响应**: 题目信息

### 2.8 保存进度
- **接口**: `POST /miniapp/user/psy-assessment/{sessionId}/save`
- **参数**:
  - `sessionId`: 会话ID
- **响应**: 保存结果

### 2.9 恢复测评
- **接口**: `POST /miniapp/user/psy-assessment/{sessionId}/resume`
- **参数**:
  - `sessionId`: 会话ID
- **响应**: 测评状态

### 2.10 暂停测评
- **接口**: `POST /miniapp/user/psy-assessment/{sessionId}/pause`
- **参数**:
  - `sessionId`: 会话ID
- **响应**: 暂停结果

### 2.11 完成测评
- **接口**: `POST /miniapp/user/psy-assessment/{sessionId}/complete`
- **参数**:
  - `sessionId`: 会话ID
- **响应**: 测评结果
```json
{
  "recordId": 123,
  "totalScore": 85,
  "resultLevel": "中等",
  "resultDescription": "结果描述",
  "subscaleScores": [],
  "reportUrl": "报告链接"
}
```

### 2.12 放弃测评
- **接口**: `POST /miniapp/user/psy-assessment/{sessionId}/abandon`
- **参数**:
  - `sessionId`: 会话ID
  - `reason`: 放弃原因（可选）
- **响应**: 放弃结果

### 2.13 获取测评进度
- **接口**: `GET /miniapp/user/psy-assessment/{sessionId}/progress`
- **参数**:
  - `sessionId`: 会话ID
- **响应**: 进度信息
```json
{
  "currentQuestionNo": 5,
  "totalQuestions": 20,
  "answeredCount": 4,
  "progress": 20.0,
  "timeElapsed": 300
}
```

### 2.14 获取测评状态
- **接口**: `GET /miniapp/user/psy-assessment/{sessionId}/status`
- **参数**:
  - `sessionId`: 会话ID
- **响应**: 状态信息

## 3. 测评记录API

### 3.1 查询测评记录列表
- **接口**: `GET /miniapp/user/psy-assessment/record/list`
- **响应**: 分页的测评记录列表

### 3.2 查询量表测评记录
- **接口**: `GET /miniapp/user/psy-assessment/record/scale/{scaleId}`
- **参数**:
  - `scaleId`: 量表ID
- **响应**: 分页的测评记录列表

### 3.3 查询进行中的测评
- **接口**: `GET /miniapp/user/psy-assessment/record/in-progress`
- **响应**: 进行中的测评记录列表

### 3.4 查询已完成的测评
- **接口**: `GET /miniapp/user/psy-assessment/record/completed`
- **响应**: 分页的已完成测评记录列表

### 3.5 获取测评记录详情
- **接口**: `GET /miniapp/user/psy-assessment/record/{recordId}`
- **参数**:
  - `recordId`: 测评记录ID
- **响应**: 测评记录详细信息

### 3.6 生成测评报告
- **接口**: `POST /miniapp/user/psy-assessment/record/{recordId}/report`
- **参数**:
  - `recordId`: 测评记录ID
  - `reportLevel`: 报告层级（默认1）
- **响应**: 测评报告内容

### 3.7 获取测评建议
- **接口**: `GET /miniapp/user/psy-assessment/record/{recordId}/suggestions`
- **参数**:
  - `recordId`: 测评记录ID
- **响应**: 测评建议内容

## 4. 企业测评API

### 4.1 查询企业可用量表
- **接口**: `GET /miniapp/enterprise/assessment/scale/available`
- **参数**:
  - `enterpriseId`: 企业ID
- **响应**: 分页的可用量表列表

### 4.2 查询企业信息
- **接口**: `GET /miniapp/enterprise/assessment/enterprise/{enterpriseId}`
- **参数**:
  - `enterpriseId`: 企业ID
- **响应**: 企业详细信息

### 4.3 查询测评计划列表
- **接口**: `GET /miniapp/enterprise/assessment/plan/list`
- **参数**:
  - `enterpriseId`: 企业ID
- **响应**: 分页的测评计划列表

### 4.4 创建测评计划
- **接口**: `POST /miniapp/enterprise/assessment/plan/create`
- **请求体**: 测评计划信息
- **响应**: 操作结果

### 4.5 启动测评计划
- **接口**: `POST /miniapp/enterprise/assessment/plan/{planId}/start`
- **参数**:
  - `planId`: 计划ID
- **响应**: 操作结果

### 4.6 查询计划参与者
- **接口**: `GET /miniapp/enterprise/assessment/plan/{planId}/participants`
- **参数**:
  - `planId`: 计划ID
- **响应**: 分页的参与者列表

### 4.7 添加参与者
- **接口**: `POST /miniapp/enterprise/assessment/plan/{planId}/participants/add`
- **参数**:
  - `planId`: 计划ID
- **请求体**: 参与者列表
- **响应**: 操作结果

### 4.8 查询计划统计
- **接口**: `GET /miniapp/enterprise/assessment/plan/{planId}/stats`
- **参数**:
  - `planId`: 计划ID
- **响应**: 计划统计信息

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 6. 数据模型

### 6.1 量表模型 (PsyTScale)
```json
{
  "id": 1,
  "code": "SCALE001",
  "name": "焦虑自评量表",
  "alias": "SAS",
  "description": "量表描述",
  "instruction": "测评说明",
  "author": "作者",
  "version": "1.0",
  "scoringType": "sum",
  "payMode": 0,
  "payPhase": 0,
  "originalPrice": 10.00,
  "currentPrice": 8.00,
  "freeReportLevel": 1,
  "paidReportLevel": 3,
  "timeLimit": 1800,
  "questionCount": 20,
  "coverImage": "封面图片URL",
  "tags": "标签",
  "searchKeywords": "搜索关键词",
  "searchCount": 100,
  "viewCount": 500,
  "testCount": 200,
  "ratingAvg": 4.5,
  "ratingCount": 50,
  "sort": 1,
  "status": 1,
  "createTime": "2023-01-01 00:00:00",
  "updateTime": "2023-01-01 00:00:00"
}
```

### 6.2 测评记录模型 (PsyTAssessmentRecord)
```json
{
  "id": 1,
  "scaleId": 1,
  "userId": 1,
  "sessionId": "uuid",
  "startTime": "2023-01-01 10:00:00",
  "completionTime": "2023-01-01 10:30:00",
  "totalScore": 85,
  "resultLevel": "中等",
  "resultDescription": "结果描述",
  "currentQuestionNo": 20,
  "answeredCount": 20,
  "totalQuestions": 20,
  "progress": 100.0,
  "duration": 1800,
  "status": 2,
  "source": "personal",
  "reportLevel": 1,
  "createTime": "2023-01-01 10:00:00",
  "updateTime": "2023-01-01 10:30:00"
}
```

## 7. 统计分析API

### 7.1 用户测评统计
- **接口**: `GET /miniapp/user/psy-assessment/stats`
- **响应**: 用户测评统计信息

### 7.2 量表使用统计
- **接口**: `GET /system/scale/{id}/test-stats`
- **权限**: `system:scale:list`
- **参数**:
  - `id`: 量表ID
- **响应**: 量表测评统计信息

### 7.3 企业使用统计
- **接口**: `GET /miniapp/enterprise/assessment/enterprise/{enterpriseId}/usage-stats`
- **参数**:
  - `enterpriseId`: 企业ID
- **响应**: 企业使用统计信息

### 7.4 测评趋势统计
- **接口**: `GET /system/assessment/record/trend-stats`
- **权限**: `system:assessment:list`
- **参数**:
  - `days`: 天数（默认30）
- **响应**: 测评趋势统计

## 8. 注意事项

1. 所有接口都需要进行身份认证
2. 分页查询默认页大小为10，最大不超过100
3. 时间格式统一使用 `yyyy-MM-dd HH:mm:ss`
4. 金额字段使用BigDecimal类型，保留2位小数
5. 测评会话有效期为24小时，超时自动清理
6. 企业测评需要验证企业权限和额度
7. 所有删除操作都是逻辑删除
8. 敏感操作需要记录操作日志
9. 量表查看和搜索会自动更新统计计数
10. 企业测评会消耗企业测评额度
11. 测评结果支持多级报告（免费/付费）
12. 支持测评进度保存和恢复功能
