# 心理测评题目接口完善方案

## 问题分析

通过代码分析发现以下问题：

1. **后台管理量表详情查询**：虽然配置了题目关联，但可能没有正确返回题目信息
2. **小程序端题目接口**：缺少专门的题目获取接口
3. **测评流程接口**：题目相关的方法都是空实现

## 解决方案

### 1. 后台管理端

#### 1.1 量表详情查询（已完善）

**接口路径**：`GET /system/assessment/scale/{id}`

**功能**：查询量表详情时自动包含题目信息

**实现**：
- 使用 `ScaleDetailMap` 结果映射
- 自动关联查询题目列表
- 包含题目选项信息

#### 1.2 题目管理接口（新增）

**控制器**：`PsyAssessmentQuestionController`

**主要接口**：
- `GET /system/assessment/question/list` - 查询题目列表
- `GET /system/assessment/question/scale/{scaleId}` - 根据量表ID查询题目
- `GET /system/assessment/question/{id}` - 查询题目详情
- `POST /system/assessment/question` - 新增题目
- `PUT /system/assessment/question` - 修改题目
- `DELETE /system/assessment/question/{ids}` - 删除题目

### 2. 小程序端

#### 2.1 量表题目预览接口（新增）

**接口路径**：`GET /miniapp/user/assessment/scale/{scaleId}/questions`

**功能**：获取量表的所有题目（用于预览）

**返回数据**：
```json
{
  "code": 200,
  "data": {
    "scaleInfo": {
      "id": 1,
      "scaleName": "焦虑自评量表",
      "description": "量表描述...",
      "questionCount": 20
    },
    "questions": [
      {
        "id": 1,
        "questionNo": 1,
        "questionText": "题目内容",
        "questionType": 1,
        "options": [
          {
            "id": 1,
            "optionText": "选项A",
            "optionValue": "1",
            "score": 1
          }
        ]
      }
    ],
    "totalQuestions": 20
  }
}
```

#### 2.2 测评流程接口（已完善）

**控制器**：`MiniAppPsyTAssessmentController`

**主要接口**：
- `POST /miniapp/user/psy-assessment/start` - 开始测评
- `GET /miniapp/user/psy-assessment/{sessionId}/question/{questionNo}` - 获取指定题目
- `POST /miniapp/user/psy-assessment/{sessionId}/answer` - 提交答案
- `GET /miniapp/user/psy-assessment/{sessionId}/next` - 获取下一题
- `GET /miniapp/user/psy-assessment/scale/{scaleId}/preview` - 预览测评

### 3. 服务层实现

#### 3.1 题目服务（新增）

**接口**：`IPsyAssessmentQuestionService`
**实现**：`PsyAssessmentQuestionServiceImpl`

**主要方法**：
- `selectQuestionsByScaleId()` - 根据量表ID查询题目
- `selectQuestionsWithOptionsByScaleId()` - 查询题目（含选项）
- `selectQuestionByNo()` - 根据题目序号查询
- `insertQuestion()` - 新增题目
- `updateQuestion()` - 修改题目
- `deleteQuestionByIds()` - 删除题目

#### 3.2 测评流程服务（已完善）

**接口**：`IPsyTAssessmentService`
**实现**：`PsyTAssessmentServiceImpl`

**主要方法**：
- `startAssessment()` - 开始测评
- `getAssessmentQuestion()` - 获取题目
- `submitAnswer()` - 提交答案
- `previewAssessment()` - 预览测评

## 数据库表结构

### 题目表 (psy_t_question)

```sql
CREATE TABLE psy_t_question (
  id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '题目ID',
  scale_id bigint(20) NOT NULL COMMENT '量表ID',
  question_no int(11) NOT NULL COMMENT '题目序号',
  question_text text NOT NULL COMMENT '题目内容',
  question_type tinyint(4) DEFAULT 1 COMMENT '题目类型(1=单选 2=多选 3=填空 4=量表)',
  is_required tinyint(4) DEFAULT 1 COMMENT '是否必答(0=否 1=是)',
  score_type tinyint(4) DEFAULT 1 COMMENT '计分方式(1=选项分值 2=自定义)',
  dimension varchar(50) DEFAULT NULL COMMENT '维度',
  order_num int(11) DEFAULT NULL COMMENT '显示顺序',
  del_flag tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  create_by varchar(64) DEFAULT '' COMMENT '创建者',
  create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) DEFAULT '' COMMENT '更新者',
  update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_scale_id (scale_id),
  KEY idx_question_no (question_no)
);
```

### 选项表 (psy_t_question_option)

```sql
CREATE TABLE psy_t_question_option (
  id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '选项ID',
  question_id bigint(20) NOT NULL COMMENT '题目ID',
  option_text varchar(500) NOT NULL COMMENT '选项内容',
  option_value varchar(50) NOT NULL COMMENT '选项值',
  score decimal(5,2) DEFAULT 0.00 COMMENT '选项分数',
  order_num int(11) DEFAULT NULL COMMENT '显示顺序',
  del_flag tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_question_id (question_id)
);
```

## 使用示例

### 1. 后台管理 - 查看量表详情（含题目）

```javascript
// 请求
GET /system/assessment/scale/1

// 响应
{
  "code": 200,
  "data": {
    "id": 1,
    "scaleName": "焦虑自评量表",
    "scaleCode": "SAS",
    "description": "量表描述...",
    "questions": [
      {
        "id": 1,
        "questionNo": 1,
        "questionText": "我觉得比平常容易紧张和着急",
        "questionType": 1,
        "options": [
          {"optionText": "没有或很少时间", "score": 1},
          {"optionText": "小部分时间", "score": 2},
          {"optionText": "相当多时间", "score": 3},
          {"optionText": "绝大部分或全部时间", "score": 4}
        ]
      }
    ]
  }
}
```

### 2. 小程序端 - 预览量表题目

```javascript
// 请求
GET /miniapp/user/assessment/scale/1/questions

// 响应
{
  "code": 200,
  "data": {
    "scaleInfo": {
      "id": 1,
      "scaleName": "焦虑自评量表",
      "questionCount": 20
    },
    "questions": [...],
    "totalQuestions": 20
  }
}
```

### 3. 小程序端 - 测评流程

```javascript
// 1. 开始测评
POST /miniapp/user/psy-assessment/start
{
  "scaleId": 1
}

// 2. 获取题目
GET /miniapp/user/psy-assessment/{sessionId}/question/1

// 3. 提交答案
POST /miniapp/user/psy-assessment/{sessionId}/answer
{
  "questionId": 1,
  "answerContent": "2",
  "responseTime": 15
}
```

## 注意事项

1. **权限控制**：确保用户只能访问已发布的量表
2. **数据验证**：验证题目序号、答案格式等
3. **性能优化**：大量题目时考虑分页加载
4. **缓存策略**：量表题目可以考虑缓存
5. **错误处理**：完善异常处理和错误提示
