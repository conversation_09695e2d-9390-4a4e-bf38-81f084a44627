# 咨询师订单价格和日期筛选接口文档

## 接口概述

该接口用于咨询师根据价格范围和日期范围筛选自己的咨询订单，支持分页和排序功能。

## 接口信息

- **接口路径**: `/miniapp/consultant/orderManage/filterByPriceAndDate`
- **请求方法**: `GET`
- **权限要求**: 需要咨询师身份登录

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| minPrice | BigDecimal | 否 | - | 最小价格（元），筛选实际支付金额大于等于此值的订单 |
| maxPrice | BigDecimal | 否 | - | 最大价格（元），筛选实际支付金额小于等于此值的订单 |
| startDate | String | 否 | - | 开始日期，格式：yyyy-MM-dd，筛选创建时间大于等于此日期的订单 |
| endDate | String | 否 | - | 结束日期，格式：yyyy-MM-dd，筛选创建时间小于等于此日期的订单 |
| status | String | 否 | - | 订单状态，如：待支付、已支付、待咨询、咨询中、已完成、已取消 |
| pageNum | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 10 | 每页数量 |
| orderBy | String | 否 | createTime | 排序字段，可选值：createTime、paymentAmount、scheduledTime、paymentTime、updateTime |
| sortOrder | String | 否 | desc | 排序方式，可选值：asc（升序）、desc（降序） |

## 请求示例

```http
GET /miniapp/consultant/orderManage/filterByPriceAndDate?minPrice=100&maxPrice=500&startDate=2024-01-01&endDate=2024-12-31&status=已完成&pageNum=1&pageSize=10&orderBy=paymentAmount&sortOrder=desc
```

## 响应格式

### 成功响应

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "orders": [
      {
        "id": 1,
        "orderNo": "CO20240101001",
        "userId": 100,
        "consultantId": 200,
        "consultType": "心理咨询",
        "scheduledTime": "2024-01-15 14:00:00",
        "duration": 60,
        "unitPrice": 3.00,
        "originalAmount": 180.00,
        "discountAmount": 0.00,
        "paymentAmount": 180.00,
        "paymentMethod": "微信支付",
        "paymentTime": "2024-01-10 10:30:00",
        "status": "已完成",
        "createTime": "2024-01-10 10:00:00",
        "updateTime": "2024-01-15 15:00:00"
      }
    ],
    "totalCount": 25,
    "totalPages": 3,
    "currentPage": 1,
    "pageSize": 10,
    "hasNext": true,
    "hasPrevious": false,
    "filterSummary": {
      "minPrice": 100,
      "maxPrice": 500,
      "startDate": "2024-01-01 00:00:00",
      "endDate": "2024-12-31 23:59:59",
      "status": "已完成"
    }
  }
}
```

### 错误响应

```json
{
  "code": 500,
  "msg": "最小价格不能大于最大价格",
  "data": null
}
```

## 参数验证规则

1. **价格验证**：
   - minPrice 和 maxPrice 不能小于 0
   - minPrice 不能大于 maxPrice

2. **日期验证**：
   - 日期格式必须为 yyyy-MM-dd
   - startDate 不能晚于 endDate

3. **分页验证**：
   - pageNum 最小值为 1
   - pageSize 最小值为 1

## 排序字段说明

- `createTime`: 按订单创建时间排序
- `paymentAmount`: 按实际支付金额排序
- `scheduledTime`: 按预约咨询时间排序
- `paymentTime`: 按支付时间排序
- `updateTime`: 按更新时间排序

## 注意事项

1. 只能查询当前登录咨询师的订单
2. 所有筛选条件都是可选的，不传参数则返回所有订单
3. 日期筛选基于订单创建时间（create_time）
4. 价格筛选基于实际支付金额（payment_amount）
5. 支持多条件组合筛选
6. 返回结果包含完整的分页信息和筛选条件摘要

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 401 | 未登录或登录已过期 |
| 403 | 非咨询师身份，无权限访问 |
| 400 | 参数验证失败 |
| 500 | 服务器内部错误 |
