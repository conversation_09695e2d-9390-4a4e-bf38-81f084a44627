# 题目接口测试指南

## 测试前准备

1. **确保数据库已修复**：
   ```bash
   mysql -u your_username -p your_database < sql/fix_category_id_column.sql
   ```

2. **确保有测试数据**：
   ```bash
   mysql -u your_username -p your_database < sql/psy_assessment_init_data.sql
   ```

## 接口测试

### 1. 后台管理端测试

#### 1.1 查询量表详情（应包含题目）

```bash
# 测试量表详情查询
curl -X GET "http://localhost:8080/system/assessment/scale/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**期望结果**：
- 返回量表基本信息
- 包含 `questions` 数组
- 每个题目包含选项信息

#### 1.2 查询量表题目列表

```bash
# 根据量表ID查询题目
curl -X GET "http://localhost:8080/system/assessment/question/scale/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 小程序端测试

#### 2.1 预览量表题目

```bash
# 获取量表题目（用于预览）
curl -X GET "http://localhost:8080/miniapp/user/assessment/scale/1/questions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**期望结果**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "scaleInfo": {
      "id": 1,
      "scaleName": "焦虑自评量表",
      "scaleCode": "SAS",
      "questionCount": 20
    },
    "questions": [
      {
        "id": 1,
        "questionNo": 1,
        "questionText": "我觉得比平常容易紧张和着急",
        "questionType": 1,
        "isRequired": 1,
        "options": [
          {
            "id": 1,
            "optionText": "没有或很少时间",
            "optionValue": "1",
            "score": 1.00
          },
          {
            "id": 2,
            "optionText": "小部分时间",
            "optionValue": "2", 
            "score": 2.00
          }
        ]
      }
    ],
    "totalQuestions": 20
  }
}
```

#### 2.2 测评流程测试

```bash
# 1. 开始测评
curl -X POST "http://localhost:8080/miniapp/user/psy-assessment/start" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"scaleId": 1}'

# 2. 获取第一题
curl -X GET "http://localhost:8080/miniapp/user/psy-assessment/{sessionId}/question/1" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. 提交答案
curl -X POST "http://localhost:8080/miniapp/user/psy-assessment/{sessionId}/answer" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "questionId": 1,
    "answerContent": "2",
    "responseTime": 15
  }'
```

## 常见问题排查

### 1. 量表详情不包含题目

**可能原因**：
- 数据库中没有题目数据
- MyBatis映射配置错误
- 题目表名不匹配

**排查步骤**：
```sql
-- 检查题目表是否存在
SHOW TABLES LIKE 'psy_t_question';

-- 检查是否有题目数据
SELECT COUNT(*) FROM psy_t_question WHERE scale_id = 1;

-- 检查题目数据
SELECT * FROM psy_t_question WHERE scale_id = 1 LIMIT 5;
```

### 2. 小程序端接口404

**可能原因**：
- 路由配置错误
- 控制器注解错误
- 服务依赖注入失败

**排查步骤**：
1. 检查控制器类是否有 `@RestController` 注解
2. 检查方法是否有正确的 `@GetMapping` 注解
3. 检查服务是否正确注入

### 3. 题目选项为空

**可能原因**：
- 选项表数据缺失
- MyBatis关联查询配置错误

**排查步骤**：
```sql
-- 检查选项表
SELECT COUNT(*) FROM psy_t_question_option WHERE question_id IN (
  SELECT id FROM psy_t_question WHERE scale_id = 1
);

-- 检查选项数据
SELECT q.id, q.question_text, o.option_text, o.score 
FROM psy_t_question q 
LEFT JOIN psy_t_question_option o ON q.id = o.question_id 
WHERE q.scale_id = 1 
LIMIT 10;
```

## 数据验证

### 1. 验证量表数据

```sql
-- 查看量表基本信息
SELECT id, scale_name, scale_code, question_count, status 
FROM psy_t_scale 
WHERE del_flag = 0;
```

### 2. 验证题目数据

```sql
-- 查看题目统计
SELECT 
  s.scale_name,
  COUNT(q.id) as question_count,
  COUNT(o.id) as option_count
FROM psy_t_scale s
LEFT JOIN psy_t_question q ON s.id = q.scale_id AND q.del_flag = 0
LEFT JOIN psy_t_question_option o ON q.id = o.question_id AND o.del_flag = 0
WHERE s.del_flag = 0
GROUP BY s.id, s.scale_name;
```

### 3. 验证完整性

```sql
-- 检查没有选项的题目
SELECT q.id, q.question_text 
FROM psy_t_question q 
LEFT JOIN psy_t_question_option o ON q.id = o.question_id 
WHERE q.del_flag = 0 
  AND q.question_type IN (1, 2) -- 单选、多选题
  AND o.id IS NULL;
```

## 性能测试

### 1. 大量题目加载测试

```bash
# 测试包含大量题目的量表
curl -X GET "http://localhost:8080/miniapp/user/assessment/scale/{scaleId}/questions" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -w "Time: %{time_total}s\n"
```

### 2. 并发测试

```bash
# 使用ab工具进行并发测试
ab -n 100 -c 10 -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8080/miniapp/user/assessment/scale/1/questions"
```

## 日志检查

### 1. 应用日志

```bash
# 查看应用启动日志
tail -f logs/xihuan.log | grep -i "question\|assessment"

# 查看SQL执行日志
tail -f logs/xihuan.log | grep -i "select.*question"
```

### 2. 数据库日志

```bash
# MySQL查询日志
tail -f /var/log/mysql/mysql.log | grep -i "psy_t_question"
```

## 成功标准

### 1. 后台管理端

- ✅ 量表详情查询返回题目列表
- ✅ 题目管理CRUD操作正常
- ✅ 题目排序功能正常

### 2. 小程序端

- ✅ 量表题目预览接口正常
- ✅ 测评流程题目获取正常
- ✅ 答案提交功能正常

### 3. 数据完整性

- ✅ 所有量表都有对应题目
- ✅ 选择题都有选项
- ✅ 题目序号连续无重复
