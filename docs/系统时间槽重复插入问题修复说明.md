# 时间槽重复插入问题修复说明

## 问题描述

### 系统时间槽错误信息
```
org.springframework.dao.DuplicateKeyException:
Duplicate entry '1-2025-07-26-11:45:00' for key 'psy_system_time_slot.uniq_system_slot'
```

### 咨询师时间槽错误信息
```
org.springframework.dao.DuplicateKeyException:
Duplicate entry '2025-07-26-09:00:00-1-12' for key 'psy_time_slot.uniq_slot_key'
```

### 问题原因
1. **唯一约束冲突**：两个表都存在唯一约束
   - `psy_system_time_slot` 表：`uniq_system_slot (center_id, date_key, start_time)`
   - `psy_time_slot` 表：`uniq_slot_key (date_key, start_time, center_id, counselor_id)`
2. **重复执行**：定时任务重复执行时，尝试插入已存在的时间槽记录
3. **缺少检查**：生成时间槽时没有检查是否已存在相同记录

### 唯一约束定义
```sql
-- 系统时间槽表
UNIQUE KEY `uniq_system_slot` (`center_id`, `date_key`, `start_time`)

-- 咨询师时间槽表
UNIQUE KEY `uniq_slot_key` (`date_key`, `start_time`, `center_id`, `counselor_id`)
```

## 修复方案

### 1. 修改定时任务逻辑

#### 修改前
```java
// 第三步：生成系统公共时间槽
int systemSlotCount = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, 1L);
```

#### 修改后
```java
// 第三步：重新生成系统公共时间槽（先清理再生成，避免重复）
int systemSlotCount = systemTimeSlotService.regenerateSystemTimeSlots(startDate, endDate, 1L);
```

### 2. 增强生成逻辑

#### A. 添加重复检查
在 `PsySystemTimeSlotServiceImpl.generateSystemTimeSlots()` 方法中：

```java
// 过滤掉已存在的时间槽，避免重复插入
List<PsySystemTimeSlot> newSlots = filterExistingSlots(systemSlots);
if (!newSlots.isEmpty()) {
    int result = batchInsertSystemTimeSlots(newSlots);
    logger.info("成功生成 {} 个系统时间槽（过滤掉 {} 个已存在的）", 
        result, systemSlots.size() - newSlots.size());
    return result;
}
```

#### B. 实现过滤方法
```java
private List<PsySystemTimeSlot> filterExistingSlots(List<PsySystemTimeSlot> systemSlots) {
    List<PsySystemTimeSlot> newSlots = new ArrayList<>();
    
    for (PsySystemTimeSlot slot : systemSlots) {
        // 检查是否已存在相同的时间槽
        boolean exists = systemTimeSlotMapper.checkSystemSlotExists(
            slot.getCenterId(), slot.getDateKey(), slot.getStartTime()) > 0;
        
        if (!exists) {
            newSlots.add(slot);
        }
    }
    
    return newSlots;
}
```

### 3. 修改批量插入SQL

#### 修改前
```xml
<insert id="batchInsertSystemTimeSlots">
    insert into psy_system_time_slot (...)
    values (...)
</insert>
```

#### 修改后
```xml
<insert id="batchInsertSystemTimeSlots">
    insert ignore into psy_system_time_slot (...)
    values (...)
</insert>
```

使用 `INSERT IGNORE` 可以在遇到重复键时忽略插入，而不是抛出错误。

### 4. 利用现有的重新生成方法

系统已经提供了 `regenerateSystemTimeSlots` 方法：

```java
@Override
@Transactional
public int regenerateSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
    // 先删除指定日期范围的系统时间槽
    systemTimeSlotMapper.deleteSlotsByDateRange(startDate.toString(), endDate.toString(), centerId);
    
    // 重新生成
    return generateSystemTimeSlots(startDate, endDate, centerId);
}
```

## 修复效果

### 1. 避免重复插入错误
- 定时任务不再因为重复键约束而失败
- 系统可以正常运行，不会中断时间槽生成流程

### 2. 智能处理已存在记录
- 自动过滤掉已存在的时间槽
- 只插入新的时间槽记录
- 提供详细的日志信息

### 3. 多种解决方案
- **方案1**：使用 `regenerateSystemTimeSlots`（推荐用于定时任务）
- **方案2**：使用增强的 `generateSystemTimeSlots`（推荐用于手动生成）
- **方案3**：使用 `INSERT IGNORE`（数据库层面的保护）

## 测试验证

### 测试接口
创建了专门的测试控制器 `SystemTimeSlotTestController`：

```bash
# 测试重新生成方法
POST /test/systemTimeSlot/testGeneration?days=7

# 测试普通生成方法（带重复检查）
POST /test/systemTimeSlot/testNormalGeneration?days=7

# 触发完整定时任务
POST /test/systemTimeSlot/triggerFullTask

# 查看修复信息
GET /test/systemTimeSlot/fixInfo
```

### 验证步骤
1. **重复执行测试**：多次调用生成接口，确认不会出现重复插入错误
2. **定时任务测试**：手动触发定时任务，确认正常执行
3. **数据一致性检查**：验证生成的时间槽数据正确性

## 相关文件修改

### 1. 服务层
- `PsySystemTimeSlotServiceImpl.java` - 增强生成逻辑，添加重复检查

### 2. 定时任务
- `PsyTimeSlotTaskService.java` - 修改为使用重新生成方法

### 3. 数据访问层
- `PsySystemTimeSlotMapper.xml` - 修改批量插入SQL使用 INSERT IGNORE

### 4. 测试支持
- `SystemTimeSlotTestController.java` - 新增测试控制器

## 注意事项

### 1. 性能考虑
- 重复检查会增加一些数据库查询，但可以避免插入失败
- `regenerateSystemTimeSlots` 方法会先删除再插入，适合定时任务使用

### 2. 数据一致性
- 使用事务确保操作的原子性
- 删除和插入操作在同一个事务中完成

### 3. 日志监控
- 增加了详细的日志记录，便于问题排查
- 可以监控过滤掉的重复记录数量

### 4. 向后兼容
- 修改不影响现有功能
- 保持了原有接口的兼容性

## 总结

通过以上修复方案，彻底解决了系统时间槽重复插入的问题：

1. **根本解决**：修改定时任务使用重新生成方法
2. **智能防护**：添加重复检查逻辑
3. **数据库保护**：使用 INSERT IGNORE 避免错误
4. **完善测试**：提供测试接口验证修复效果

现在系统可以正常运行定时任务，不再出现重复键约束错误。
