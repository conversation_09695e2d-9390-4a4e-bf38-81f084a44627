# 答题接口内部类参数使用说明

## 🎯 解决方案概述

已创建内部类 `AnswerRequest` 来接收答题参数，提供了更好的类型安全性和参数验证。

## 📊 内部类定义

```java
public static class AnswerRequest {
    private Long recordId;      // 测评记录ID（必填）
    private Long questionId;    // 题目ID（必填）
    private Long optionId;      // 选项ID（选填，单选/多选题需要）
    private String answerContent; // 答案内容（选填，填空题需要）
    private Integer responseTime; // 答题耗时（选填，单位：秒）
    
    // getter/setter方法...
}
```

## 🔧 接口说明

### 1. 主接口（推荐使用）

**URL**: `/miniapp/user/assessment/answer`
**方法**: `POST`
**Content-Type**: `application/json`
**参数**: 使用 `@RequestBody AnswerRequest`

#### ✅ 请求示例

```javascript
// 使用 fetch
fetch('/miniapp/user/assessment/answer', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        recordId: 23,
        questionId: 277,
        optionId: 760,
        answerContent: "非常同意",
        responseTime: 30
    })
});

// 使用 axios
axios.post('/miniapp/user/assessment/answer', {
    recordId: 23,
    questionId: 277,
    optionId: 760,
    answerContent: "非常同意",
    responseTime: 30
});

// 使用 jQuery
$.ajax({
    url: '/miniapp/user/assessment/answer',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({
        recordId: 23,
        questionId: 277,
        optionId: 760,
        answerContent: "非常同意",
        responseTime: 30
    }),
    success: function(response) {
        console.log('保存成功', response);
    }
});
```

### 2. 兼容接口（表单参数）

**URL**: `/miniapp/user/assessment/answer/form`
**方法**: `POST`
**Content-Type**: `application/x-www-form-urlencoded`
**参数**: 使用 `@RequestParam`

#### ✅ 请求示例

```javascript
// 使用 FormData
const formData = new FormData();
formData.append('recordId', '23');
formData.append('questionId', '277');
formData.append('optionId', '760');
formData.append('answerContent', '非常同意');
formData.append('responseTime', '30');

fetch('/miniapp/user/assessment/answer/form', {
    method: 'POST',
    body: formData
});

// 使用 URLSearchParams
const params = new URLSearchParams();
params.append('recordId', '23');
params.append('questionId', '277');
params.append('optionId', '760');
params.append('answerContent', '非常同意');
params.append('responseTime', '30');

fetch('/miniapp/user/assessment/answer/form', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params
});

// 使用 jQuery
$.ajax({
    url: '/miniapp/user/assessment/answer/form',
    type: 'POST',
    data: {
        recordId: 23,
        questionId: 277,
        optionId: 760,
        answerContent: '非常同意',
        responseTime: 30
    },
    success: function(response) {
        console.log('保存成功', response);
    }
});
```

## 📋 参数说明

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| recordId | Long | ✅ | 测评记录ID | 23 |
| questionId | Long | ✅ | 题目ID | 277 |
| optionId | Long | ❌ | 选项ID（单选/多选题必填） | 760 |
| answerContent | String | ❌ | 答案内容（填空题必填） | "非常同意" |
| responseTime | Integer | ❌ | 答题耗时（秒） | 30 |

## 🔍 参数验证规则

### 必填参数验证
- `recordId`: 不能为空
- `questionId`: 不能为空

### 业务逻辑验证
- 测评记录必须存在且属于当前用户
- 答案格式必须符合题目类型要求
- 选择题必须提供 `optionId`
- 填空题必须提供 `answerContent`

## 📊 响应格式

### 成功响应
```json
{
    "code": 200,
    "msg": "答案已保存",
    "data": null
}
```

### 错误响应
```json
{
    "code": 500,
    "msg": "测评记录ID不能为空",
    "data": null
}
```

## 🧪 测试方法

### 1. 使用 curl 测试主接口
```bash
curl -X POST http://localhost:8080/miniapp/user/assessment/answer \
  -H "Content-Type: application/json" \
  -d '{
    "recordId": 23,
    "questionId": 277,
    "optionId": 760,
    "answerContent": "非常同意",
    "responseTime": 30
  }'
```

### 2. 使用 curl 测试兼容接口
```bash
curl -X POST http://localhost:8080/miniapp/user/assessment/answer/form \
  -d "recordId=23&questionId=277&optionId=760&answerContent=非常同意&responseTime=30"
```

### 3. 使用 Postman 测试

#### 主接口测试
- **Method**: POST
- **URL**: `http://localhost:8080/miniapp/user/assessment/answer`
- **Headers**: `Content-Type: application/json`
- **Body**: 
```json
{
    "recordId": 23,
    "questionId": 277,
    "optionId": 760,
    "answerContent": "非常同意",
    "responseTime": 30
}
```

#### 兼容接口测试
- **Method**: POST
- **URL**: `http://localhost:8080/miniapp/user/assessment/answer/form`
- **Headers**: `Content-Type: application/x-www-form-urlencoded`
- **Body**: 
```
recordId=23
questionId=277
optionId=760
answerContent=非常同意
responseTime=30
```

## ⚠️ 注意事项

### 1. Content-Type 要求
- **主接口**: 必须使用 `application/json`
- **兼容接口**: 使用 `application/x-www-form-urlencoded` 或 `multipart/form-data`

### 2. 参数类型
- 数字类型参数（recordId, questionId, optionId, responseTime）在JSON中应为数字，不要用引号包围
- 字符串类型参数（answerContent）需要用引号包围

### 3. 错误处理
- 接口会返回详细的错误信息
- 前端应该根据返回的 `code` 和 `msg` 进行相应处理

### 4. 日志记录
- 接口会记录详细的请求参数日志
- 便于调试和问题排查

## 🚀 推荐使用方式

1. **新项目**: 推荐使用主接口（JSON格式），类型安全，易于维护
2. **现有项目**: 如果已经使用表单参数，可以使用兼容接口
3. **调试阶段**: 可以同时测试两个接口，确保兼容性

## 📈 优势

### 使用内部类的优势
1. **类型安全**: 编译时检查参数类型
2. **代码清晰**: 参数结构一目了然
3. **易于维护**: 参数变更只需修改一个地方
4. **自动验证**: 可以添加注解进行参数验证
5. **文档友好**: IDE可以提供更好的代码提示

### 向后兼容
- 保留了原有的表单参数接口
- 不影响现有的前端代码
- 提供了平滑的迁移路径

现在你可以使用更加类型安全和清晰的方式来处理答题参数了！🎉
