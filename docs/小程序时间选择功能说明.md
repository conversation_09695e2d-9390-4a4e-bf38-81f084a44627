# 小程序时间选择功能说明

## 功能概述

为了满足你的需求，我已经实现了一个完整的**系统公共时间槽**功能，让用户在小程序中可以：

1. **先选择时间** - 查看系统级别的可用时间段
2. **再筛选咨询师** - 根据选择的时间筛选可用的咨询师
3. **智能推荐** - 基于可用性和推荐度排序

## 核心功能

### 🕐 系统公共时间槽
- **15分钟间隔**：精确到15分钟的时间槽
- **可用性统计**：显示每个时间段有多少咨询师可用
- **时间段分组**：按上午、下午、晚上分组展示
- **实时更新**：每30分钟自动更新可用性统计

### 👨‍⚕️ 咨询师筛选
- **时间匹配**：根据选择的时间段筛选可用咨询师
- **连续时段**：支持预约连续的多个15分钟时段
- **推荐排序**：按推荐度和可用性排序
- **详细信息**：显示咨询师的可用时间段详情

## 数据库设计

### 新增表：psy_system_time_slot
```sql
-- 系统公共时间槽表
CREATE TABLE `psy_system_time_slot` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '系统时间槽ID',
    `center_id` bigint NOT NULL COMMENT '咨询中心ID',
    `date_key` varchar(10) NOT NULL COMMENT '日期KEY（YYYY-MM-DD）',
    `week_day` varchar(5) DEFAULT NULL COMMENT '星期几',
    `range_id` bigint NOT NULL COMMENT '所属时间段ID',
    `start_time` time NOT NULL COMMENT '开始时间',
    `end_time` time NOT NULL COMMENT '结束时间',
    `available_counselors` int DEFAULT '0' COMMENT '可用咨询师数量',
    `total_counselors` int DEFAULT '0' COMMENT '总咨询师数量',
    `has_available` tinyint(1) DEFAULT '0' COMMENT '是否有可用咨询师',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_system_slot` (`center_id`, `date_key`, `start_time`)
) ENGINE=InnoDB COMMENT='系统公共时间槽表';
```

## API接口

### 小程序专用接口 (`/miniapp/timeSlot`)

#### 1. 获取公共时间槽
```bash
GET /miniapp/timeSlot/public?startDate=2025-07-10&endDate=2025-07-16&centerId=1
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "dates": [
      {
        "date": "2025-07-10",
        "weekDay": "周四",
        "isToday": true,
        "timeRanges": [
          {
            "rangeName": "上午",
            "iconUrl": "https://example.com/icons/morning.png",
            "startHour": 9,
            "endHour": 12,
            "slots": [
              {
                "slotId": 1,
                "startTime": "09:00",
                "endTime": "09:15",
                "timeDisplay": "09:00-09:15",
                "availableCounselors": 3,
                "totalCounselors": 5,
                "hasAvailable": true,
                "availabilityText": "3/5可用"
              }
            ]
          }
        ]
      }
    ],
    "totalDays": 7,
    "totalSlots": 280,
    "availableSlots": 156
  }
}
```

#### 2. 根据时间筛选咨询师
```bash
GET /miniapp/timeSlot/counselors?date=2025-07-10&startTime=09:00&endTime=10:00&centerId=1
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "counselorId": 1,
      "availableSlots": 4,
      "timeRange": "09:00-10:00",
      "recommendationScore": 8.5,
      "scoreDetail": {
        "baseScore": 5.0,
        "timePreferenceScore": 8.0,
        "availabilityScore": 9.0,
        "totalScore": 8.5
      }
    }
  ]
}
```

#### 3. 根据时长筛选咨询师
```bash
GET /miniapp/timeSlot/counselors/duration?date=2025-07-10&startTime=09:00&duration=60&centerId=1
```

#### 4. 获取推荐时间段
```bash
GET /miniapp/timeSlot/recommendations?startDate=2025-07-10&endDate=2025-07-16&centerId=1&limit=5
```

### 管理后台接口 (`/system/systemTimeSlot`)

#### 1. 生成系统时间槽
```bash
POST /system/systemTimeSlot/generate?startDate=2025-07-10&endDate=2025-07-16&centerId=1
```

#### 2. 更新可用性统计
```bash
PUT /system/systemTimeSlot/updateStats?date=2025-07-10&centerId=1
```

#### 3. 重新生成时间槽
```bash
POST /system/systemTimeSlot/regenerate?startDate=2025-07-10&endDate=2025-07-16&centerId=1
```

## 使用流程

### 1. 系统初始化
```bash
# 1. 执行数据库脚本创建新表
# 2. 初始化时间段定义
POST /system/timeRange/init

# 3. 生成咨询师时间槽
POST /system/timeTask/generate?days=7

# 4. 生成系统公共时间槽
POST /system/systemTimeSlot/generate?startDate=2025-07-10&endDate=2025-07-16&centerId=1
```

### 2. 小程序使用流程

#### 步骤1：用户选择日期
```javascript
// 获取一周的公共时间槽
const response = await fetch('/miniapp/timeSlot/public?startDate=2025-07-10&endDate=2025-07-16&centerId=1');
const timeSlots = await response.json();

// 展示日期选择器和时间段
```

#### 步骤2：用户选择具体时间
```javascript
// 用户点击某个时间槽后，获取可用咨询师
const counselors = await fetch('/miniapp/timeSlot/counselors?date=2025-07-10&startTime=09:00&endTime=09:15&centerId=1');

// 展示可用咨询师列表
```

#### 步骤3：用户选择咨询师并预约
```javascript
// 创建预约（使用原有的预约接口）
const appointment = {
  userId: 123,
  counselorId: 1,
  centerId: 1,
  startSlotId: 1001,
  slotIds: [1001], // 对应的咨询师时间槽ID
  duration: 15
};
```

## 定时任务

系统会自动执行以下定时任务：

1. **每天凌晨2点**：生成未来7天的咨询师时间槽和系统时间槽
2. **每天凌晨3点**：清理7天前的过期时间槽
3. **每30分钟**：更新系统时间槽的可用性统计
4. **每小时**：更新过期时间槽状态

## 优势特点

### 🚀 性能优化
- **预聚合数据**：系统时间槽预先计算可用性，查询速度快
- **批量操作**：支持批量生成和更新
- **索引优化**：合理的数据库索引设计

### 📱 用户体验
- **直观展示**：用户先看到时间可用性，再选择咨询师
- **智能推荐**：根据可用性和推荐度排序
- **实时更新**：可用性信息实时同步

### 🔧 易维护
- **自动化**：定时任务自动维护数据
- **灵活配置**：支持多咨询中心
- **完整API**：提供完整的管理接口

## 扩展建议

1. **缓存优化**：对热门时间段添加Redis缓存
2. **个性化推荐**：基于用户历史偏好推荐时间和咨询师
3. **实时通知**：时间槽状态变化时推送通知
4. **数据分析**：统计热门时间段和咨询师使用率

## 总结

现在你的系统支持：
- ✅ 用户先选择时间，再筛选咨询师
- ✅ 系统级别的时间槽展示
- ✅ 实时的可用性统计
- ✅ 智能的咨询师推荐
- ✅ 完整的API接口
- ✅ 自动化的数据维护

这个设计完美解决了你的需求，让小程序用户可以更直观地选择预约时间！
