# SQL错误修复方案总结

## 问题描述

在访问 `/system/assessment/scale/1` 接口时，系统抛出了以下SQL异常：

```
Error attempting to get column 'question_type' from result set.  Cause: java.sql.SQLException: Error
```

错误发生在 `PsyAssessmentScaleServiceImpl.selectScaleWithDetails` 方法中，该方法通过 MyBatis 查询量表详情，包括关联的题目信息。

## 根本原因分析

1. **数据库表结构与映射不匹配**：
   - `psy_t_question` 表中不存在 `question_type` 字段
   - 但 MyBatis 映射文件中的 `QuestionResultMap` 引用了这个不存在的字段
   - 当执行 `selectScaleWithDetails` 查询时，尝试获取不存在的字段导致SQL异常

2. **问题定位**：
   - 错误发生在 `PsyAssessmentScaleMapper.xml` 中的 `selectScaleWithDetails` 查询
   - 该查询使用了 `ScaleDetailMap` 结果映射
   - `ScaleDetailMap` 包含了题目集合，使用 `selectQuestionsByScaleId` 查询
   - `selectQuestionsByScaleId` 使用了 `SELECT *`，尝试获取所有字段包括不存在的 `question_type`

## 修复方案

### 1. 修改结果映射

修改 `PsyAssessmentQuestionMapper.xml` 中的 `QuestionResultMap`，移除不存在的字段映射：

```xml
<resultMap id="QuestionResultMap" type="PsyAssessmentQuestion">
    <id property="id" column="id"/>
    <result property="scaleId" column="scale_id"/>
    <result property="questionNo" column="question_no"/>
    <result property="questionText" column="question_text"/>
    <result property="delFlag" column="del_flag"/>
    <result property="createBy" column="create_by"/>
    <result property="createTime" column="create_time"/>
    <result property="updateBy" column="update_by"/>
    <result property="updateTime" column="update_time"/>
    <result property="remark" column="remark"/>
</resultMap>
```

### 2. 修改查询语句

修改所有使用 `SELECT *` 的查询，明确指定要查询的字段：

#### 2.1 修改 `selectQuestionList`

```xml
<select id="selectQuestionList" parameterType="PsyAssessmentQuestion" resultMap="QuestionResultMap">
    SELECT id, scale_id, question_no, question_text, del_flag, 
           create_by, create_time, update_by, update_time, remark
    FROM psy_t_question
    WHERE del_flag = 0
    <if test="scaleId != null">
        AND scale_id = #{scaleId}
    </if>
    <if test="questionText != null and questionText != ''">
        AND question_text LIKE CONCAT('%', #{questionText}, '%')
    </if>
    ORDER BY scale_id, question_no
</select>
```

#### 2.2 修改 `selectQuestionById`

```xml
<select id="selectQuestionById" parameterType="Long" resultMap="QuestionResultMap">
    SELECT id, scale_id, question_no, question_text, del_flag, 
           create_by, create_time, update_by, update_time, remark
    FROM psy_t_question WHERE id = #{id} AND del_flag = 0
</select>
```

#### 2.3 修改 `selectQuestionWithOptions`

```xml
<select id="selectQuestionWithOptions" parameterType="Long" resultMap="QuestionWithOptionsMap">
    SELECT id, scale_id, question_no, question_text, del_flag, 
           create_by, create_time, update_by, update_time, remark
    FROM psy_t_question WHERE id = #{id} AND del_flag = 0
</select>
```

#### 2.4 修改 `selectQuestionsByScaleId`

```xml
<select id="selectQuestionsByScaleId" parameterType="Long" resultMap="QuestionResultMap">
    SELECT id, scale_id, question_no, question_text, del_flag, 
           create_by, create_time, update_by, update_time, remark
    FROM psy_t_question 
    WHERE scale_id = #{scaleId} AND del_flag = 0
    ORDER BY question_no
</select>
```

#### 2.5 修改 `selectQuestionsWithOptionsByScaleId`

```xml
<select id="selectQuestionsWithOptionsByScaleId" parameterType="Long" resultMap="QuestionWithOptionsMap">
    SELECT id, scale_id, question_no, question_text, del_flag, 
           create_by, create_time, update_by, update_time, remark
    FROM psy_t_question 
    WHERE scale_id = #{scaleId} AND del_flag = 0
    ORDER BY question_no
</select>
```

### 3. 修改插入和更新语句

#### 3.1 修改 `insertQuestion`

```xml
<insert id="insertQuestion" parameterType="PsyAssessmentQuestion" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO psy_t_question (
        scale_id, question_no, question_text, del_flag, create_by, create_time, update_by, update_time, remark
    ) VALUES (
        #{scaleId}, #{questionNo}, #{questionText}, #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate(), #{remark}
    )
</insert>
```

#### 3.2 修改 `updateQuestion`

```xml
<update id="updateQuestion" parameterType="PsyAssessmentQuestion">
    UPDATE psy_t_question
    <set>
        <if test="scaleId != null">scale_id = #{scaleId},</if>
        <if test="questionNo != null">question_no = #{questionNo},</if>
        <if test="questionText != null">question_text = #{questionText},</if>
        <if test="delFlag != null">del_flag = #{delFlag},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        <if test="remark != null">remark = #{remark},</if>
        update_time = sysdate()
    </set>
    WHERE id = #{id}
</update>
```

## 修复效果

1. **解决SQL异常**：修复后不再尝试获取不存在的 `question_type` 字段
2. **保持功能完整性**：所有必要的字段仍然被正确映射和处理
3. **向后兼容**：实体类中的 `questionType` 字段仍然存在，但不再与数据库字段映射

## 长期解决方案

为了彻底解决问题并保持代码与数据库的一致性，建议采取以下措施：

### 方案一：修改数据库表结构

添加缺失的字段到 `psy_t_question` 表：

```sql
ALTER TABLE psy_t_question 
ADD COLUMN question_type INT DEFAULT 1 COMMENT '题目类型(1=单选 2=多选 3=填空 4=量表)' AFTER question_text,
ADD COLUMN is_required INT DEFAULT 1 COMMENT '是否必答(0=否 1=是)' AFTER question_type,
ADD COLUMN score_type INT DEFAULT 1 COMMENT '计分类型(1=正向 2=反向)' AFTER is_required,
ADD COLUMN dimension VARCHAR(50) DEFAULT NULL COMMENT '所属维度' AFTER score_type;
```

### 方案二：修改实体类

从 `PsyAssessmentQuestion` 实体类中移除不存在的字段：

```java
// 移除这些字段
private Integer questionType;
private Integer isRequired;
private Integer scoreType;
private String dimension;
```

## 建议

1. **避免使用 `SELECT *`**：
   - 明确指定需要查询的字段，避免因表结构变化导致的问题
   - 提高查询性能，只获取需要的数据

2. **保持实体类与数据库表结构一致**：
   - 确保实体类字段与数据库表字段一一对应
   - 使用数据库版本控制工具管理表结构变更

3. **使用数据库迁移工具**：
   - 使用 Flyway 或 Liquibase 等工具管理数据库变更
   - 确保开发、测试和生产环境的数据库结构一致

4. **完善错误处理**：
   - 添加更详细的错误日志，便于问题定位
   - 实现友好的错误提示，提高用户体验

## 总结

本次修复采用了修改 MyBatis 映射文件的方案，通过明确指定查询字段，避免了尝试获取不存在字段导致的SQL异常。这是一个快速有效的解决方案，但长期来看，应该考虑使数据库表结构与实体类保持一致，以避免类似问题再次发生。
