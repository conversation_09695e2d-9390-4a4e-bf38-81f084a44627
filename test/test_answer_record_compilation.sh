#!/bin/bash

# 答题记录实体类编译测试脚本
# 验证类型修复是否成功

echo "=========================================="
echo "答题记录实体类编译测试"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="D:/code/XiHuan"

echo -e "\n${YELLOW}1. 检查实体类文件是否存在...${NC}"
ENTITY_FILE="$PROJECT_ROOT/xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTAnswerRecord.java"

if [ -f "$ENTITY_FILE" ]; then
    echo -e "${GREEN}✓ 实体类文件存在${NC}"
else
    echo -e "${RED}✗ 实体类文件不存在: $ENTITY_FILE${NC}"
    exit 1
fi

echo -e "\n${YELLOW}2. 检查字段类型定义...${NC}"

# 检查 answerScore 字段类型
if grep -q "private BigDecimal answerScore;" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ answerScore 字段类型正确 (BigDecimal)${NC}"
else
    echo -e "${RED}✗ answerScore 字段类型错误${NC}"
fi

# 检查 reverseValue 字段类型
if grep -q "private BigDecimal reverseValue;" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ reverseValue 字段类型正确 (BigDecimal)${NC}"
else
    echo -e "${RED}✗ reverseValue 字段类型错误${NC}"
fi

# 检查 originalScore 字段类型
if grep -q "private BigDecimal originalScore;" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ originalScore 字段类型正确 (BigDecimal)${NC}"
else
    echo -e "${RED}✗ originalScore 字段类型错误${NC}"
fi

# 检查 finalScore 字段类型
if grep -q "private BigDecimal finalScore;" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ finalScore 字段类型正确 (BigDecimal)${NC}"
else
    echo -e "${RED}✗ finalScore 字段类型错误${NC}"
fi

echo -e "\n${YELLOW}3. 检查方法实现...${NC}"

# 检查 calculateFinalScore 方法返回类型
if grep -q "public BigDecimal calculateFinalScore()" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ calculateFinalScore 方法返回类型正确${NC}"
else
    echo -e "${RED}✗ calculateFinalScore 方法返回类型错误${NC}"
fi

# 检查 BigDecimal.ZERO 的使用
if grep -q "return BigDecimal.ZERO;" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ 正确使用 BigDecimal.ZERO${NC}"
else
    echo -e "${RED}✗ 未正确使用 BigDecimal.ZERO${NC}"
fi

# 检查 subtract 方法的使用
if grep -q "reverseValue.subtract(answerScore)" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ 正确使用 BigDecimal.subtract 方法${NC}"
else
    echo -e "${RED}✗ 未正确使用 BigDecimal.subtract 方法${NC}"
fi

echo -e "\n${YELLOW}4. 尝试编译项目...${NC}"

cd "$PROJECT_ROOT"

# 编译 common 模块
echo -e "\n${YELLOW}编译 xihuan-common 模块...${NC}"
if mvn -f xihuan-common/pom.xml clean compile -q; then
    echo -e "${GREEN}✓ xihuan-common 模块编译成功${NC}"
else
    echo -e "${RED}✗ xihuan-common 模块编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误信息：${NC}"
    mvn -f xihuan-common/pom.xml compile
    exit 1
fi

# 编译 system 模块
echo -e "\n${YELLOW}编译 xihuan-system 模块...${NC}"
if mvn -f xihuan-system/pom.xml clean compile -q; then
    echo -e "${GREEN}✓ xihuan-system 模块编译成功${NC}"
else
    echo -e "${RED}✗ xihuan-system 模块编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误信息：${NC}"
    mvn -f xihuan-system/pom.xml compile
    exit 1
fi

echo -e "\n${YELLOW}5. 检查导入语句...${NC}"

# 检查必要的导入
if grep -q "import java.math.BigDecimal;" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ BigDecimal 导入正确${NC}"
else
    echo -e "${RED}✗ 缺少 BigDecimal 导入${NC}"
fi

if grep -q "import java.util.Date;" "$ENTITY_FILE"; then
    echo -e "${GREEN}✓ Date 导入正确${NC}"
else
    echo -e "${RED}✗ 缺少 Date 导入${NC}"
fi

echo -e "\n${YELLOW}6. 代码质量检查...${NC}"

# 检查是否有类型转换问题
if grep -q "Integer.*=.*BigDecimal" "$ENTITY_FILE"; then
    echo -e "${RED}✗ 发现 Integer 和 BigDecimal 类型转换问题${NC}"
else
    echo -e "${GREEN}✓ 无类型转换问题${NC}"
fi

# 检查是否有算术运算问题
if grep -q "BigDecimal.*-.*Integer\|Integer.*-.*BigDecimal" "$ENTITY_FILE"; then
    echo -e "${RED}✗ 发现 BigDecimal 和 Integer 混合运算问题${NC}"
else
    echo -e "${GREEN}✓ 无混合运算问题${NC}"
fi

echo -e "\n${YELLOW}7. 生成测试报告...${NC}"

echo -e "\n=========================================="
echo -e "${YELLOW}修复总结${NC}"
echo "=========================================="
echo "1. 修复了 calculateFinalScore() 方法中的类型不匹配问题"
echo "2. 将返回值从 int 0 改为 BigDecimal.ZERO"
echo "3. 修复了 BigDecimal 和 Integer 的混合运算问题"
echo "4. 统一了所有得分相关字段的数据类型为 BigDecimal"
echo "5. 更新了 getScoreDesc() 方法中的变量类型"

echo -e "\n=========================================="
echo -e "${YELLOW}字段类型统一${NC}"
echo "=========================================="
echo "answerScore:   BigDecimal ✓"
echo "reverseValue:  BigDecimal ✓"
echo "originalScore: BigDecimal ✓"
echo "finalScore:    BigDecimal ✓"

echo -e "\n=========================================="
echo -e "${GREEN}🎉 所有类型问题已修复！${NC}"
echo "=========================================="

exit 0
