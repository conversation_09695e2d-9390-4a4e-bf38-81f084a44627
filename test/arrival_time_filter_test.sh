#!/bin/bash

# 咨询师到店时间过滤功能测试脚本
# 用于验证所有API接口的功能

BASE_URL="http://localhost:8080"
CONSULTANT_ID=1
CENTER_ID=1

echo "=========================================="
echo "咨询师到店时间过滤功能测试"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local test_name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_code="$5"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "\n${YELLOW}测试 $TOTAL_TESTS: $test_name${NC}"
    echo "请求: $method $url"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$url")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST -H "Content-Type: application/json" -d "$data" "$BASE_URL$url")
    elif [ "$method" = "PUT" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X PUT -H "Content-Type: application/x-www-form-urlencoded" -d "$data" "$BASE_URL$url")
        else
            response=$(curl -s -w "\n%{http_code}" -X PUT "$BASE_URL$url")
        fi
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "$expected_code" ]; then
        echo -e "${GREEN}✓ 通过 (HTTP $http_code)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        echo "响应: $(echo "$body" | jq -r '.msg // .message // "成功"' 2>/dev/null || echo "成功")"
    else
        echo -e "${RED}✗ 失败 (HTTP $http_code, 期望 $expected_code)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "响应: $body"
    fi
}

echo -e "\n${YELLOW}开始测试咨询师个人设置接口...${NC}"

# 1. 获取咨询师配置
test_api "获取咨询师配置" "GET" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID" "" "200"

# 2. 设置到店时间为1.5小时
test_api "设置到店时间" "PUT" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/arrivalTime" "hours=1.5" "200"

# 3. 启用过滤
test_api "启用过滤功能" "PUT" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/toggleFilter" "enabled=true" "200"

# 4. 禁用过滤
test_api "禁用过滤功能" "PUT" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/toggleFilter" "enabled=false" "200"

# 5. 重新启用过滤（为后续测试准备）
test_api "重新启用过滤" "PUT" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/toggleFilter" "enabled=true" "200"

# 6. 重置为默认设置
test_api "重置默认设置" "POST" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/reset" "" "200"

echo -e "\n${YELLOW}开始测试客户端查询接口...${NC}"

# 7. 获取过滤后的时间槽
TODAY=$(date +%Y-%m-%d)
TOMORROW=$(date -d "tomorrow" +%Y-%m-%d)
test_api "获取过滤后时间槽" "GET" "/miniapp/timeSlot/filtered?startDate=$TODAY&endDate=$TOMORROW&consultantId=$CONSULTANT_ID&consultationType=2" "" "200"

# 8. 获取线上咨询时间槽（不应该被过滤）
test_api "获取线上咨询时间槽" "GET" "/miniapp/timeSlot/filtered?startDate=$TODAY&endDate=$TOMORROW&consultantId=$CONSULTANT_ID&consultationType=1" "" "200"

# 9. 查询可用咨询师（线下）
CURRENT_HOUR=$(date +%H)
NEXT_HOUR=$(printf "%02d" $((CURRENT_HOUR + 1)))
test_api "查询线下咨询师" "GET" "/miniapp/timeSlot/counselors?date=$TODAY&startTime=${NEXT_HOUR}:00&endTime=${NEXT_HOUR}:30&consultationType=2" "" "200"

# 10. 查询可用咨询师（线上）
test_api "查询线上咨询师" "GET" "/miniapp/timeSlot/counselors?date=$TODAY&startTime=${NEXT_HOUR}:00&endTime=${NEXT_HOUR}:30&consultationType=1" "" "200"

echo -e "\n${YELLOW}开始测试管理后台接口...${NC}"

# 11. 查看配置列表
test_api "查看配置列表" "GET" "/system/consultant/config/list?pageNum=1&pageSize=10" "" "200"

# 12. 获取指定配置
test_api "获取指定配置" "GET" "/system/consultant/config/consultant/$CONSULTANT_ID/center/$CENTER_ID" "" "200"

# 13. 获取到店时间
test_api "获取到店时间" "GET" "/system/consultant/config/arrivalTime/$CONSULTANT_ID/$CENTER_ID" "" "200"

# 14. 检查过滤状态
test_api "检查过滤状态" "GET" "/system/consultant/config/arrivalFilter/$CONSULTANT_ID/$CENTER_ID" "" "200"

echo -e "\n${YELLOW}开始测试边界情况...${NC}"

# 15. 设置极小到店时间
test_api "设置0.1小时到店" "PUT" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/arrivalTime" "hours=0.1" "200"

# 16. 设置较大到店时间
test_api "设置5小时到店" "PUT" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/arrivalTime" "hours=5.0" "200"

# 17. 测试无效参数
test_api "测试无效到店时间" "PUT" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/arrivalTime" "hours=-1" "400"

# 18. 测试超大到店时间
test_api "测试超大到店时间" "PUT" "/miniapp/consultant/settings/$CONSULTANT_ID/center/$CENTER_ID/arrivalTime" "hours=25" "400"

echo -e "\n${YELLOW}开始测试帮助信息接口...${NC}"

# 19. 获取帮助信息
test_api "获取帮助信息" "GET" "/miniapp/consultant/settings/help" "" "200"

echo -e "\n=========================================="
echo -e "${YELLOW}测试结果统计${NC}"
echo "=========================================="
echo -e "总测试数: $TOTAL_TESTS"
echo -e "${GREEN}通过: $PASSED_TESTS${NC}"
echo -e "${RED}失败: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！功能正常工作。${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查相关功能。${NC}"
    exit 1
fi
