#!/bin/bash

# 量表API完整功能测试脚本
# 测试所有新增字段是否正确返回

BASE_URL="http://localhost:8080"

echo "=========================================="
echo "量表API完整功能测试"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local test_name="$1"
    local method="$2"
    local url="$3"
    local expected_fields="$4"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "\n${YELLOW}测试 $TOTAL_TESTS: $test_name${NC}"
    echo "请求: $method $url"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s "$BASE_URL$url")
    fi
    
    echo "响应: $response" | head -c 200
    echo "..."
    
    # 检查是否包含期望的字段
    local all_fields_present=true
    for field in $expected_fields; do
        if [[ $response != *"\"$field\""* ]]; then
            echo -e "${RED}✗ 缺少字段: $field${NC}"
            all_fields_present=false
        fi
    done
    
    if [ "$all_fields_present" = true ]; then
        echo -e "${GREEN}✓ 通过 - 所有字段都存在${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ 失败 - 缺少必要字段${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 检查字段函数
check_field_value() {
    local response="$1"
    local field="$2"
    local description="$3"
    
    if [[ $response == *"\"$field\":null"* ]] || [[ $response == *"\"$field\":\"\""* ]]; then
        echo -e "${YELLOW}⚠ $description 为空${NC}"
        return 1
    else
        echo -e "${GREEN}✓ $description 有值${NC}"
        return 0
    fi
}

echo -e "\n${BLUE}开始测试小程序量表列表接口...${NC}"

# 1. 测试量表列表接口
echo -e "\n${YELLOW}=== 测试量表列表接口 ===${NC}"
response=$(curl -s "$BASE_URL/miniapp/user/assessment/scale/list?pageNum=1&pageSize=5")
echo "量表列表响应: $response" | head -c 300
echo "..."

# 检查基础字段
basic_fields="scaleName scaleCode description instruction"
detail_fields="testNotice testPurpose testObject testPreparation testProcessing testAttention testTheory testApplication applicableAge"
all_fields="$basic_fields $detail_fields"

echo -e "\n${BLUE}检查基础字段...${NC}"
for field in $basic_fields; do
    check_field_value "$response" "$field" "$field"
done

echo -e "\n${BLUE}检查详细信息字段...${NC}"
for field in $detail_fields; do
    check_field_value "$response" "$field" "$field"
done

# 2. 测试量表详情接口
echo -e "\n${YELLOW}=== 测试量表详情接口 ===${NC}"
# 从列表响应中提取第一个量表的ID
scale_id=$(echo "$response" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)
if [ -n "$scale_id" ]; then
    detail_response=$(curl -s "$BASE_URL/miniapp/user/assessment/scale/$scale_id")
    echo "量表详情响应: $detail_response" | head -c 300
    echo "..."
    
    echo -e "\n${BLUE}检查详情接口字段...${NC}"
    for field in $all_fields; do
        check_field_value "$detail_response" "$field" "$field"
    done
else
    echo -e "${RED}✗ 无法获取量表ID${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# 3. 测试管理后台量表列表接口
echo -e "\n${YELLOW}=== 测试管理后台量表列表接口 ===${NC}"
admin_response=$(curl -s "$BASE_URL/system/assessment/scale/list?pageNum=1&pageSize=5")
echo "管理后台列表响应: $admin_response" | head -c 300
echo "..."

echo -e "\n${BLUE}检查管理后台字段...${NC}"
admin_fields="name code description introduction testNotice testPurpose testObject testPreparation testProcessing testAttention testTheory testApplication applicableAge"
for field in $admin_fields; do
    check_field_value "$admin_response" "$field" "$field"
done

# 4. 测试管理后台量表详情接口
echo -e "\n${YELLOW}=== 测试管理后台量表详情接口 ===${NC}"
if [ -n "$scale_id" ]; then
    admin_detail_response=$(curl -s "$BASE_URL/system/assessment/scale/$scale_id")
    echo "管理后台详情响应: $admin_detail_response" | head -c 300
    echo "..."
    
    echo -e "\n${BLUE}检查管理后台详情字段...${NC}"
    for field in $admin_fields; do
        check_field_value "$admin_detail_response" "$field" "$field"
    done
else
    echo -e "${RED}✗ 无法获取量表ID${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# 5. 测试新增量表接口（POST）
echo -e "\n${YELLOW}=== 测试新增量表接口 ===${NC}"
new_scale_data='{
    "name": "API测试量表",
    "code": "API_TEST_SCALE",
    "categoryId": 1,
    "description": "这是通过API创建的测试量表",
    "introduction": "用于测试API接口的量表",
    "testNotice": "API测试须知",
    "testPurpose": "API测试目的",
    "testObject": "API测试对象",
    "testPreparation": "API测试准备",
    "testProcessing": "API测试处理",
    "testAttention": "API测试注意事项",
    "testTheory": "API测试理论",
    "testApplication": "API测试应用",
    "applicableAge": "API测试年龄",
    "questionCount": 10,
    "duration": "10分钟",
    "price": 0,
    "payMode": 0,
    "status": 1
}'

create_response=$(curl -s -X POST -H "Content-Type: application/json" -d "$new_scale_data" "$BASE_URL/system/assessment/scale")
echo "创建量表响应: $create_response"

if [[ $create_response == *"\"code\":200"* ]]; then
    echo -e "${GREEN}✓ 量表创建成功${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}✗ 量表创建失败${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi

# 6. 字段值验证
echo -e "\n${YELLOW}=== 字段值验证 ===${NC}"
echo -e "\n${BLUE}验证字段值是否符合预期...${NC}"

# 检查是否有非空的详细信息字段
if [[ $response == *"testNotice"* ]] && [[ $response != *"\"testNotice\":null"* ]]; then
    echo -e "${GREEN}✓ testNotice 字段有值${NC}"
else
    echo -e "${YELLOW}⚠ testNotice 字段为空或不存在${NC}"
fi

if [[ $response == *"testPurpose"* ]] && [[ $response != *"\"testPurpose\":null"* ]]; then
    echo -e "${GREEN}✓ testPurpose 字段有值${NC}"
else
    echo -e "${YELLOW}⚠ testPurpose 字段为空或不存在${NC}"
fi

if [[ $response == *"applicableAge"* ]] && [[ $response != *"\"applicableAge\":null"* ]]; then
    echo -e "${GREEN}✓ applicableAge 字段有值${NC}"
else
    echo -e "${YELLOW}⚠ applicableAge 字段为空或不存在${NC}"
fi

# 7. 数据完整性检查
echo -e "\n${YELLOW}=== 数据完整性检查 ===${NC}"
echo -e "\n${BLUE}检查返回数据的完整性...${NC}"

# 检查是否所有量表都有基本信息
scale_count=$(echo "$response" | grep -o '"id":[0-9]*' | wc -l)
echo "找到 $scale_count 个量表"

if [ "$scale_count" -gt 0 ]; then
    echo -e "${GREEN}✓ 成功获取量表列表${NC}"
else
    echo -e "${RED}✗ 未获取到量表数据${NC}"
fi

echo -e "\n=========================================="
echo -e "${YELLOW}测试结果统计${NC}"
echo "=========================================="
echo -e "总测试数: $TOTAL_TESTS"
echo -e "${GREEN}通过: $PASSED_TESTS${NC}"
echo -e "${RED}失败: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！量表字段映射正常工作。${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查相关功能。${NC}"
    exit 1
fi
