-- 更新咨询师配置表，补全缺失字段
-- 确保表结构与实体类字段完全匹配

-- 检查当前表结构
DESCRIBE psy_consultant_center_config;

-- 添加缺失的字段（如果不存在）
ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认配置' AFTER enable_arrival_filter;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS del_flag TINYINT(1) DEFAULT 0 COMMENT '删除标志' AFTER status;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS create_by VARCHAR(64) DEFAULT '' COMMENT '创建者' AFTER del_flag;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER create_by;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS update_by VARCHAR(64) DEFAULT '' COMMENT '更新者' AFTER create_time;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER update_by;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS remark VARCHAR(500) DEFAULT '' COMMENT '备注' AFTER update_time;

-- 查看更新后的表结构
DESCRIBE psy_consultant_center_config;

-- 验证表结构是否正确
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_consultant_center_config'
ORDER BY ORDINAL_POSITION;
