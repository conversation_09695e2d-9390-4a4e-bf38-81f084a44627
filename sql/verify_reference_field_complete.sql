-- 验证引用文献字段的完整性测试脚本
-- 确保数据库表结构与实体类完全匹配

-- 1. 验证数据库表结构
SELECT 'Database Table Structure Verification:' as info;
DESCRIBE psy_t_scale;

-- 2. 验证引用文献字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale'
  AND COLUMN_NAME = 'reference_literature';

-- 3. 验证字段位置是否正确（应该在test_application之后）
SELECT 
    COLUMN_NAME,
    ORDINAL_POSITION,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale'
  AND COLUMN_NAME IN ('test_application', 'reference_literature', 'applicable_age')
ORDER BY ORDINAL_POSITION;

-- 4. 验证所有测评详细信息字段
SELECT 'All Test Detail Fields Verification:' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale'
  AND COLUMN_NAME IN (
    'test_notice', 'test_purpose', 'test_object', 'test_preparation',
    'test_processing', 'test_attention', 'test_theory', 'test_application',
    'reference_literature', 'applicable_age'
  )
ORDER BY ORDINAL_POSITION;

-- 5. 插入测试数据验证字段功能
INSERT INTO psy_t_scale (
    name, code, category_id, description, introduction,
    test_notice, test_purpose, test_object, test_preparation, test_processing,
    test_attention, test_theory, test_application, reference_literature, applicable_age,
    question_count, scoring_type, duration, price, pay_mode, status,
    create_by, create_time, del_flag
) VALUES (
    '引用文献字段测试量表',
    'REF_TEST',
    1,
    '这是一个用于测试引用文献字段的量表',
    '本量表用于验证引用文献字段是否正确工作',
    '测试须知：请确保引用文献字段能正确显示',
    '测试目的：验证引用文献字段的完整性',
    '测试对象：系统开发人员和测试人员',
    '测试准备：确保数据库字段已正确添加',
    '测试处理：检查引用文献字段是否正确返回',
    '注意事项：请仔细检查引用文献的格式',
    '测试理论：基于软件测试理论和数据映射原理',
    '测试应用：用于系统功能验证和质量保证',
    'Test Reference: Smith, J. (2023). Software Testing Principles. Tech Journal, 15(3), 123-145.\n\nAdditional Reference: Johnson, A. (2022). Database Field Validation. Data Science Review, 8(2), 67-89.',
    '开发测试阶段',
    5, 'LIKERT', '2分钟', 0, 0, 1,
    'system', NOW(), '0'
) ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    description = VALUES(description),
    reference_literature = VALUES(reference_literature),
    update_time = NOW();

-- 6. 验证数据插入和查询
SELECT 'Data Insertion and Query Verification:' as info;
SELECT 
    id, name, code,
    test_notice,
    test_purpose,
    test_application,
    reference_literature,
    applicable_age,
    create_time
FROM psy_t_scale 
WHERE code = 'REF_TEST';

-- 7. 验证引用文献字段的完整性
SELECT 'Reference Literature Field Verification:' as info;
SELECT 
    id,
    name,
    CASE 
        WHEN reference_literature IS NULL THEN '空值'
        WHEN reference_literature = '' THEN '空字符串'
        WHEN LENGTH(reference_literature) < 50 THEN '内容较短'
        ELSE '内容完整'
    END as reference_status,
    LEFT(reference_literature, 100) as reference_preview,
    LENGTH(reference_literature) as reference_length
FROM psy_t_scale 
WHERE del_flag = '0' 
ORDER BY create_time DESC 
LIMIT 10;

-- 8. 测试更新操作
UPDATE psy_t_scale SET 
    reference_literature = '更新后的引用文献: Updated Reference: Brown, C. (2024). Advanced Testing Methods. Quality Assurance Journal, 20(1), 45-67.',
    update_time = NOW()
WHERE code = 'REF_TEST';

-- 9. 验证更新结果
SELECT 'Update Operation Verification:' as info;
SELECT 
    name,
    reference_literature,
    update_time
FROM psy_t_scale 
WHERE code = 'REF_TEST';

-- 10. 验证字段在查询中的表现
SELECT 'Query Performance Verification:' as info;
SELECT 
    COUNT(*) as total_scales,
    COUNT(CASE WHEN reference_literature IS NOT NULL AND reference_literature != '' THEN 1 END) as scales_with_reference,
    COUNT(CASE WHEN LENGTH(reference_literature) > 100 THEN 1 END) as scales_with_detailed_reference,
    AVG(LENGTH(reference_literature)) as avg_reference_length
FROM psy_t_scale 
WHERE del_flag = '0';

-- 11. 测试JSON格式输出（模拟API返回）
SELECT 'JSON Format Simulation:' as info;
SELECT JSON_OBJECT(
    'id', id,
    'scaleName', name,
    'scaleCode', code,
    'description', description,
    'testNotice', test_notice,
    'testPurpose', test_purpose,
    'testTheory', test_theory,
    'testApplication', test_application,
    'referenceLiterature', reference_literature,
    'applicableAge', applicable_age,
    'questionCount', question_count,
    'duration', duration,
    'createTime', DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s')
) as json_output
FROM psy_t_scale 
WHERE code = 'REF_TEST';

-- 12. 验证所有字段的完整映射
SELECT 'Complete Field Mapping Verification:' as info;
SELECT 
    id,
    name as scaleName,
    code as scaleCode,
    description,
    introduction,
    test_notice as testNotice,
    test_purpose as testPurpose,
    test_object as testObject,
    test_preparation as testPreparation,
    test_processing as testProcessing,
    test_attention as testAttention,
    test_theory as testTheory,
    test_application as testApplication,
    reference_literature as referenceLiterature,
    applicable_age as applicableAge,
    question_count as questionCount,
    duration,
    price,
    status,
    DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as createTime
FROM psy_t_scale 
WHERE del_flag = '0' 
ORDER BY create_time DESC 
LIMIT 3;

-- 13. 清理测试数据（可选）
-- DELETE FROM psy_t_scale WHERE code = 'REF_TEST';

-- 14. 最终验证统计
SELECT 'Final Verification Statistics:' as info;
SELECT 
    COUNT(*) as total_scales,
    COUNT(CASE WHEN reference_literature IS NOT NULL AND reference_literature != '' THEN 1 END) as scales_with_reference,
    COUNT(CASE WHEN test_notice IS NOT NULL AND test_notice != '' THEN 1 END) as scales_with_notice,
    COUNT(CASE WHEN test_theory IS NOT NULL AND test_theory != '' THEN 1 END) as scales_with_theory,
    COUNT(CASE WHEN applicable_age IS NOT NULL AND applicable_age != '' THEN 1 END) as scales_with_age,
    ROUND(AVG(LENGTH(reference_literature)), 2) as avg_reference_length
FROM psy_t_scale 
WHERE del_flag = '0';

COMMIT;
