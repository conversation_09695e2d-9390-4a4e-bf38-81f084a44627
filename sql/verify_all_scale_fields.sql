-- 验证量表相关字段的完整性测试脚本
-- 确保数据库、实体类、XML映射等所有组件都正确配置

-- 1. 验证数据库表结构
SELECT 'Database Table Structure Verification:' as info;
DESCRIBE psy_t_scale;

-- 2. 验证新增字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale'
  AND COLUMN_NAME IN (
    'test_notice', 'test_purpose', 'test_object', 'test_preparation',
    'test_processing', 'test_attention', 'test_theory', 'test_application',
    'applicable_age'
  )
ORDER BY ORDINAL_POSITION;

-- 3. 插入完整的测试数据
INSERT INTO psy_t_scale (
    name, code, category_id, description, introduction,
    test_notice, test_purpose, test_object, test_preparation, test_processing,
    test_attention, test_theory, test_application, applicable_age,
    question_count, scoring_type, duration, price, pay_mode, status,
    create_by, create_time, del_flag
) VALUES (
    '完整字段测试量表',
    'TEST_COMPLETE',
    1,
    '这是一个用于测试所有字段的量表',
    '本量表用于验证所有字段是否正确映射和显示',
    '测试须知：请确保所有字段都能正确显示',
    '测试目的：验证数据库字段与实体类的完整映射',
    '测试对象：系统开发人员和测试人员',
    '测试准备：确保数据库连接正常',
    '测试处理：检查所有字段是否正确返回',
    '注意事项：请仔细检查每个字段的值',
    '测试理论：基于软件测试理论和数据映射原理',
    '测试应用：用于系统功能验证和质量保证',
    '开发测试阶段',
    10, 'LIKERT', '5分钟', 0, 0, 1,
    'system', NOW(), '0'
) ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    description = VALUES(description),
    test_notice = VALUES(test_notice),
    test_purpose = VALUES(test_purpose),
    test_object = VALUES(test_object),
    test_preparation = VALUES(test_preparation),
    test_processing = VALUES(test_processing),
    test_attention = VALUES(test_attention),
    test_theory = VALUES(test_theory),
    test_application = VALUES(test_application),
    applicable_age = VALUES(applicable_age),
    update_time = NOW();

-- 4. 验证数据插入和查询
SELECT 'Data Insertion and Query Verification:' as info;
SELECT 
    id, name, code,
    test_notice,
    test_purpose,
    test_object,
    test_preparation,
    test_processing,
    test_attention,
    test_theory,
    test_application,
    applicable_age,
    create_time
FROM psy_t_scale 
WHERE code = 'TEST_COMPLETE';

-- 5. 验证所有字段的完整性
SELECT 'Complete Field Verification:' as info;
SELECT 
    id,
    name as scaleName,
    code as scaleCode,
    category_id as categoryId,
    description,
    introduction,
    test_notice as testNotice,
    test_purpose as testPurpose,
    test_object as testObject,
    test_preparation as testPreparation,
    test_processing as testProcessing,
    test_attention as testAttention,
    test_theory as testTheory,
    test_application as testApplication,
    applicable_age as applicableAge,
    question_count as questionCount,
    scoring_type as scoringType,
    duration,
    price,
    pay_mode as payMode,
    status,
    image_url as imageUrl,
    search_keywords as tags,
    view_count as viewCount,
    search_count as testCount,
    create_by as author,
    DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as createTime
FROM psy_t_scale 
WHERE del_flag = '0' 
ORDER BY create_time DESC 
LIMIT 5;

-- 6. 验证字段长度和数据类型
SELECT 'Field Length and Type Verification:' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale'
ORDER BY ORDINAL_POSITION;

-- 7. 测试更新操作
UPDATE psy_t_scale SET 
    test_notice = '更新后的测评须知',
    test_purpose = '更新后的测评目的',
    test_object = '更新后的测评对象',
    test_preparation = '更新后的测评准备',
    test_processing = '更新后的测评处理',
    test_attention = '更新后的注意事项',
    test_theory = '更新后的测评理论',
    test_application = '更新后的测评应用',
    applicable_age = '更新后的适用年龄',
    update_time = NOW()
WHERE code = 'TEST_COMPLETE';

-- 8. 验证更新结果
SELECT 'Update Operation Verification:' as info;
SELECT 
    name,
    test_notice,
    test_purpose,
    test_object,
    applicable_age,
    update_time
FROM psy_t_scale 
WHERE code = 'TEST_COMPLETE';

-- 9. 测试JSON格式输出（模拟API返回）
SELECT 'JSON Format Simulation:' as info;
SELECT JSON_OBJECT(
    'id', id,
    'scaleName', name,
    'scaleCode', code,
    'description', description,
    'instruction', introduction,
    'testNotice', test_notice,
    'testPurpose', test_purpose,
    'testObject', test_object,
    'testPreparation', test_preparation,
    'testProcessing', test_processing,
    'testAttention', test_attention,
    'testTheory', test_theory,
    'testApplication', test_application,
    'applicableAge', applicable_age,
    'questionCount', question_count,
    'duration', duration,
    'price', price,
    'status', status,
    'createTime', DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s')
) as json_output
FROM psy_t_scale 
WHERE code = 'TEST_COMPLETE';

-- 10. 清理测试数据（可选）
-- DELETE FROM psy_t_scale WHERE code = 'TEST_COMPLETE';

-- 11. 最终验证统计
SELECT 'Final Verification Statistics:' as info;
SELECT 
    COUNT(*) as total_scales,
    COUNT(CASE WHEN test_notice IS NOT NULL AND test_notice != '' THEN 1 END) as scales_with_notice,
    COUNT(CASE WHEN test_purpose IS NOT NULL AND test_purpose != '' THEN 1 END) as scales_with_purpose,
    COUNT(CASE WHEN test_theory IS NOT NULL AND test_theory != '' THEN 1 END) as scales_with_theory,
    COUNT(CASE WHEN applicable_age IS NOT NULL AND applicable_age != '' THEN 1 END) as scales_with_age
FROM psy_t_scale 
WHERE del_flag = '0';

COMMIT;
