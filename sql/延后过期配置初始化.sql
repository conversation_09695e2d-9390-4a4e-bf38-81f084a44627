-- 延后过期配置初始化SQL
-- 用于配置咨询师到门店的2小时容错时间

-- 插入延后过期功能开关配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES (
    '时间槽延后过期功能开关', 
    'psy.slot.delay.expiration.enabled', 
    'false', 
    'Y', 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    '是否启用时间槽延后过期功能，true-启用，false-禁用。启用后时间槽将延后指定小时数才标记为过期，用于给咨询师到门店提供容错时间。'
) ON DUPLICATE KEY UPDATE 
    config_name = VALUES(config_name),
    remark = VALUES(remark),
    update_time = NOW();

-- 插入延后过期小时数配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES (
    '时间槽延后过期小时数', 
    'psy.slot.delay.expiration.hours', 
    '2', 
    'Y', 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    '时间槽延后过期的小时数，默认2小时。当启用延后过期功能时，时间槽将在结束时间后延后指定小时数才被标记为过期。取值范围：0-24小时。'
) ON DUPLICATE KEY UPDATE 
    config_name = VALUES(config_name),
    remark = VALUES(remark),
    update_time = NOW();

-- 查询插入结果
SELECT 
    config_id,
    config_name,
    config_key,
    config_value,
    config_type,
    remark,
    create_time,
    update_time
FROM sys_config 
WHERE config_key IN ('psy.slot.delay.expiration.enabled', 'psy.slot.delay.expiration.hours')
ORDER BY config_key;
