-- 验证答题记录表的完整性测试脚本
-- 确保数据库表结构与实体类完全匹配

-- 1. 验证数据库表结构
SELECT 'Database Table Structure Verification:' as info;
DESCRIBE psy_t_answer_record;

-- 2. 验证所有字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_answer_record'
ORDER BY ORDINAL_POSITION;

-- 3. 验证字段类型是否正确
SELECT 'Field Type Verification:' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CASE 
        WHEN COLUMN_NAME = 'id' AND DATA_TYPE = 'bigint' THEN '✓'
        WHEN COLUMN_NAME = 'record_id' AND DATA_TYPE = 'bigint' THEN '✓'
        WHEN COLUMN_NAME = 'question_id' AND DATA_TYPE = 'bigint' THEN '✓'
        WHEN COLUMN_NAME = 'option_id' AND DATA_TYPE = 'bigint' THEN '✓'
        WHEN COLUMN_NAME = 'answer_content' AND DATA_TYPE = 'text' THEN '✓'
        WHEN COLUMN_NAME = 'answer_score' AND DATA_TYPE = 'decimal' THEN '✓'
        WHEN COLUMN_NAME = 'answer_time' AND DATA_TYPE = 'datetime' THEN '✓'
        WHEN COLUMN_NAME = 'response_time' AND DATA_TYPE = 'int' THEN '✓'
        WHEN COLUMN_NAME = 'del_flag' AND DATA_TYPE = 'tinyint' THEN '✓'
        WHEN COLUMN_NAME = 'create_by' AND DATA_TYPE = 'varchar' THEN '✓'
        WHEN COLUMN_NAME = 'create_time' AND DATA_TYPE = 'datetime' THEN '✓'
        WHEN COLUMN_NAME = 'update_by' AND DATA_TYPE = 'varchar' THEN '✓'
        WHEN COLUMN_NAME = 'update_time' AND DATA_TYPE = 'datetime' THEN '✓'
        ELSE '✗'
    END as type_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_answer_record'
ORDER BY ORDINAL_POSITION;

-- 4. 插入完整的测试数据
INSERT INTO psy_t_answer_record (
    record_id, question_id, option_id, answer_content, answer_score, 
    answer_time, response_time, del_flag, create_by, create_time
) VALUES 
-- 单选题答案
(1001, 1, 1, '选择答案A', 2.50, NOW(), 15, 0, 'test_user', NOW()),
(1001, 2, 3, '选择答案C', 3.00, NOW(), 20, 0, 'test_user', NOW()),
-- 填空题答案（无option_id）
(1001, 3, NULL, '这是填空题的详细答案内容', 4.00, NOW(), 30, 0, 'test_user', NOW()),
-- 多选题答案（可能有多条记录）
(1001, 4, 2, '多选答案B', 1.50, NOW(), 25, 0, 'test_user', NOW()),
(1001, 4, 4, '多选答案D', 1.50, NOW(), 25, 0, 'test_user', NOW()),
-- 不同测评记录的答案
(1002, 1, 2, '选择答案B', 1.00, NOW(), 18, 0, 'test_user2', NOW()),
(1002, 2, 1, '选择答案A', 4.00, NOW(), 12, 0, 'test_user2', NOW())
ON DUPLICATE KEY UPDATE 
    answer_content = VALUES(answer_content),
    answer_score = VALUES(answer_score),
    update_time = NOW();

-- 5. 验证数据插入和查询
SELECT 'Data Insertion and Query Verification:' as info;
SELECT 
    id, record_id, question_id, option_id, answer_content,
    answer_score, answer_time, response_time, del_flag,
    create_by, create_time
FROM psy_t_answer_record 
WHERE record_id IN (1001, 1002)
ORDER BY record_id, question_id;

-- 6. 测试各种查询场景
SELECT 'Query Scenarios Testing:' as info;

-- 6.1 按测评记录查询
SELECT '6.1 Query by record_id:' as test_case;
SELECT record_id, COUNT(*) as answer_count, SUM(answer_score) as total_score
FROM psy_t_answer_record 
WHERE record_id = 1001 AND del_flag = 0
GROUP BY record_id;

-- 6.2 按题目查询
SELECT '6.2 Query by question_id:' as test_case;
SELECT question_id, COUNT(*) as answer_count, AVG(response_time) as avg_response_time
FROM psy_t_answer_record 
WHERE question_id = 1 AND del_flag = 0
GROUP BY question_id;

-- 6.3 按选项查询
SELECT '6.3 Query by option_id:' as test_case;
SELECT option_id, COUNT(*) as selection_count
FROM psy_t_answer_record 
WHERE option_id IS NOT NULL AND del_flag = 0
GROUP BY option_id
ORDER BY option_id;

-- 6.4 查询填空题答案
SELECT '6.4 Query text answers:' as test_case;
SELECT question_id, answer_content, answer_score
FROM psy_t_answer_record 
WHERE option_id IS NULL AND answer_content IS NOT NULL AND del_flag = 0;

-- 7. 测试更新操作
UPDATE psy_t_answer_record SET 
    answer_score = 5.00,
    update_by = 'admin',
    update_time = NOW()
WHERE record_id = 1001 AND question_id = 1;

-- 8. 验证更新结果
SELECT 'Update Operation Verification:' as info;
SELECT 
    record_id, question_id, answer_score, update_by, update_time
FROM psy_t_answer_record 
WHERE record_id = 1001 AND question_id = 1;

-- 9. 测试软删除
UPDATE psy_t_answer_record SET 
    del_flag = 1,
    update_by = 'admin',
    update_time = NOW()
WHERE record_id = 1002 AND question_id = 2;

-- 10. 验证软删除结果
SELECT 'Soft Delete Verification:' as info;
SELECT 
    COUNT(CASE WHEN del_flag = 0 THEN 1 END) as active_records,
    COUNT(CASE WHEN del_flag = 1 THEN 1 END) as deleted_records,
    COUNT(*) as total_records
FROM psy_t_answer_record 
WHERE record_id IN (1001, 1002);

-- 11. 测试统计查询
SELECT 'Statistics Query Testing:' as info;

-- 11.1 计算总分
SELECT record_id, SUM(answer_score) as total_score
FROM psy_t_answer_record 
WHERE del_flag = 0
GROUP BY record_id;

-- 11.2 计算平均答题时间
SELECT record_id, AVG(response_time) as avg_response_time
FROM psy_t_answer_record 
WHERE del_flag = 0
GROUP BY record_id;

-- 11.3 答题进度统计
SELECT 
    record_id,
    COUNT(*) as answered_questions,
    SUM(response_time) as total_time_spent,
    AVG(answer_score) as avg_score
FROM psy_t_answer_record 
WHERE del_flag = 0
GROUP BY record_id;

-- 12. 验证索引是否存在
SELECT 'Index Verification:' as info;
SHOW INDEX FROM psy_t_answer_record;

-- 13. 模拟实际业务查询
SELECT 'Business Query Simulation:' as info;

-- 13.1 查询用户某次测评的所有答案
SELECT 
    a.question_id,
    a.option_id,
    a.answer_content,
    a.answer_score,
    a.response_time,
    a.answer_time
FROM psy_t_answer_record a
WHERE a.record_id = 1001 AND a.del_flag = 0
ORDER BY a.question_id;

-- 13.2 查询某题目的答案分布
SELECT 
    option_id,
    COUNT(*) as selection_count,
    ROUND(COUNT(*) * 100.0 / (
        SELECT COUNT(*) FROM psy_t_answer_record 
        WHERE question_id = 1 AND del_flag = 0
    ), 2) as percentage
FROM psy_t_answer_record 
WHERE question_id = 1 AND option_id IS NOT NULL AND del_flag = 0
GROUP BY option_id;

-- 14. 最终验证统计
SELECT 'Final Verification Statistics:' as info;
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN del_flag = 0 THEN 1 END) as active_records,
    COUNT(CASE WHEN option_id IS NOT NULL THEN 1 END) as choice_answers,
    COUNT(CASE WHEN option_id IS NULL AND answer_content IS NOT NULL THEN 1 END) as text_answers,
    AVG(answer_score) as avg_score,
    AVG(response_time) as avg_response_time
FROM psy_t_answer_record;

COMMIT;
