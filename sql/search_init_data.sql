-- 搜索功能初始化数据

-- 插入一些默认的搜索建议
INSERT INTO `psy_search_suggestion` (`keyword`, `suggestion_type`, `search_count`, `priority`, `status`) VALUES
-- 咨询师相关
('心理咨询师', 'manual', 0, 100, '0'),
('焦虑症咨询师', 'manual', 0, 90, '0'),
('抑郁症咨询师', 'manual', 0, 90, '0'),
('婚姻咨询师', 'manual', 0, 85, '0'),
('青少年心理咨询师', 'manual', 0, 80, '0'),
('女咨询师', 'manual', 0, 75, '0'),
('男咨询师', 'manual', 0, 70, '0'),
-- 课程相关
('心理学课程', 'manual', 0, 85, '0'),
('情绪管理课程', 'manual', 0, 80, '0'),
('亲子关系课程', 'manual', 0, 75, '0'),
('职场心理课程', 'manual', 0, 70, '0'),
('免费课程', 'manual', 0, 65, '0'),
-- 冥想相关
('冥想放松', 'manual', 0, 80, '0'),
('睡眠冥想', 'manual', 0, 75, '0'),
('减压冥想', 'manual', 0, 70, '0'),
('正念冥想', 'manual', 0, 65, '0'),
('免费冥想', 'manual', 0, 60, '0'),
-- 测评相关
('心理测评', 'manual', 0, 75, '0'),
('性格测试', 'manual', 0, 70, '0'),
('情绪测评', 'manual', 0, 65, '0'),
('免费测评', 'manual', 0, 60, '0'),
-- 通用关键词
('失眠', 'manual', 0, 85, '0'),
('情感问题', 'manual', 0, 80, '0'),
('职场压力', 'manual', 0, 75, '0'),
('人际关系', 'manual', 0, 70, '0'),
('学习压力', 'manual', 0, 65, '0'),
('自我成长', 'manual', 0, 60, '0');

-- 插入一些热门搜索（模拟数据）
INSERT INTO `psy_hot_search` (`keyword`, `search_type`, `search_count`, `last_search_time`, `hot_score`, `status`) VALUES
-- 全部类型热门搜索
('心理咨询师', 'all', 150, NOW(), 1500.00, '0'),
('失眠', 'all', 120, NOW(), 1200.00, '0'),
('焦虑症', 'all', 110, NOW(), 1100.00, '0'),
('情感问题', 'all', 95, NOW(), 950.00, '0'),
('职场压力', 'all', 85, NOW(), 850.00, '0'),
-- 咨询师类型热门搜索
('婚姻咨询师', 'consultant', 80, NOW(), 800.00, '0'),
('青少年心理咨询师', 'consultant', 70, NOW(), 700.00, '0'),
('女咨询师', 'consultant', 65, NOW(), 650.00, '0'),
('抑郁症咨询师', 'consultant', 60, NOW(), 600.00, '0'),
-- 课程类型热门搜索
('心理学课程', 'course', 55, NOW(), 550.00, '0'),
('情绪管理课程', 'course', 50, NOW(), 500.00, '0'),
('免费课程', 'course', 45, NOW(), 450.00, '0'),
('亲子关系课程', 'course', 40, NOW(), 400.00, '0'),
-- 冥想类型热门搜索
('睡眠冥想', 'meditation', 50, NOW(), 500.00, '0'),
('减压冥想', 'meditation', 45, NOW(), 450.00, '0'),
('正念冥想', 'meditation', 40, NOW(), 400.00, '0'),
('免费冥想', 'meditation', 35, NOW(), 350.00, '0'),
-- 测评类型热门搜索
('心理测评', 'assessment', 45, NOW(), 450.00, '0'),
('性格测试', 'assessment', 40, NOW(), 400.00, '0'),
('情绪测评', 'assessment', 35, NOW(), 350.00, '0'),
('免费测评', 'assessment', 30, NOW(), 300.00, '0');

-- 为现有咨询师添加搜索关键词（示例，需要根据实际数据调整）
-- UPDATE `psy_consultant` SET 
--     `search_keywords` = CONCAT(IFNULL(`name`, ''), ',', IFNULL(`specialty`, ''), ',', IFNULL(`introduction`, ''))
-- WHERE `search_keywords` IS NULL;

-- 为现有匹配问题添加搜索关键词（示例，需要根据实际数据调整）
-- UPDATE `psy_match_question` SET 
--     `search_keywords` = CONCAT(IFNULL(`title`, ''), ',', IFNULL(`description`, ''))
-- WHERE `search_keywords` IS NULL;

-- 创建搜索相关的菜单权限（需要根据实际菜单系统调整）
-- INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
-- ('搜索管理', 2000, 8, 'search', NULL, 1, 0, 'M', '0', '0', NULL, 'search', 'admin', NOW(), '', NULL, '搜索管理目录'),
-- ('搜索记录', 上级菜单ID, 1, 'record', 'search/record/index', 1, 0, 'C', '0', '0', 'search:record:list', 'log', 'admin', NOW(), '', NULL, '搜索记录菜单'),
-- ('热门搜索', 上级菜单ID, 2, 'hot', 'search/hot/index', 1, 0, 'C', '0', '0', 'search:hot:list', 'star', 'admin', NOW(), '', NULL, '热门搜索菜单'),
-- ('搜索建议', 上级菜单ID, 3, 'suggestion', 'search/suggestion/index', 1, 0, 'C', '0', '0', 'search:suggestion:list', 'guide', 'admin', NOW(), '', NULL, '搜索建议菜单');

-- 创建定时任务清理过期搜索记录（可选）
-- INSERT INTO `sys_job` (`job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `remark`) VALUES
-- ('清理过期搜索记录', 'SYSTEM', 'searchTask.cleanExpiredRecords(90)', '0 0 2 * * ?', '2', '1', '1', 'admin', NOW(), '每天凌晨2点清理90天前的搜索记录');

-- 创建索引优化搜索性能
CREATE INDEX idx_psy_search_record_keyword_time ON psy_search_record(keyword, search_time);
CREATE INDEX idx_psy_search_record_user_time ON psy_search_record(user_id, search_time);
CREATE INDEX idx_psy_hot_search_type_score ON psy_hot_search(search_type, hot_score);
CREATE INDEX idx_psy_search_suggestion_keyword_priority ON psy_search_suggestion(keyword, priority);
