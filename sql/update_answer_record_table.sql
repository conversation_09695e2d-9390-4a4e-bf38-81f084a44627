-- 更新答题记录表，补全缺失字段
-- 对比原来的 psy_t_answer 表，添加所有缺失的字段

-- 查看当前表结构
DESCRIBE psy_t_answer_record;

-- 添加缺失的字段
ALTER TABLE psy_t_answer_record 
ADD COLUMN option_id BIGINT DEFAULT NULL COMMENT '选项ID(单选/多选)' AFTER question_id;

ALTER TABLE psy_t_answer_record 
MODIFY COLUMN answer_content TEXT DEFAULT NULL COMMENT '答案内容(填空题/原answer_text字段)';

ALTER TABLE psy_t_answer_record 
MODIFY COLUMN answer_score DECIMAL(10,2) DEFAULT 0.00 COMMENT '得分';

ALTER TABLE psy_t_answer_record 
ADD COLUMN answer_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间' AFTER answer_score;

ALTER TABLE psy_t_answer_record 
MODIFY COLUMN response_time INT(11) DEFAULT 0 COMMENT '答题耗时(秒)';

ALTER TABLE psy_t_answer_record 
ADD COLUMN del_flag TINYINT(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)' AFTER response_time;

ALTER TABLE psy_t_answer_record 
ADD COLUMN create_by VARCHAR(64) DEFAULT '' COMMENT '创建者' AFTER del_flag;

ALTER TABLE psy_t_answer_record 
MODIFY COLUMN create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

ALTER TABLE psy_t_answer_record 
ADD COLUMN update_by VARCHAR(64) DEFAULT '' COMMENT '更新者' AFTER create_time;

ALTER TABLE psy_t_answer_record 
ADD COLUMN update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER update_by;

-- 添加索引
ALTER TABLE psy_t_answer_record 
ADD INDEX idx_option_id (option_id);

-- 添加外键约束（如果相关表存在）
-- ALTER TABLE psy_t_answer_record 
-- ADD CONSTRAINT fk_answer_record_record FOREIGN KEY (record_id) REFERENCES psy_t_assessment_record (id) ON DELETE CASCADE;

-- ALTER TABLE psy_t_answer_record 
-- ADD CONSTRAINT fk_answer_record_question FOREIGN KEY (question_id) REFERENCES psy_t_question (id);

-- ALTER TABLE psy_t_answer_record 
-- ADD CONSTRAINT fk_answer_record_option FOREIGN KEY (option_id) REFERENCES psy_t_question_option (id);

-- 查看更新后的表结构
DESCRIBE psy_t_answer_record;

-- 验证表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_answer_record'
ORDER BY ORDINAL_POSITION;

-- 插入测试数据
INSERT INTO psy_t_answer_record (
    record_id, question_id, option_id, answer_content, answer_score, 
    answer_time, response_time, del_flag, create_by, create_time
) VALUES 
(1, 1, 1, '选择答案A', 2.5, NOW(), 15, 0, 'test_user', NOW()),
(1, 2, 3, '选择答案C', 3.0, NOW(), 20, 0, 'test_user', NOW()),
(1, 3, NULL, '这是填空题的答案', 4.0, NOW(), 30, 0, 'test_user', NOW())
ON DUPLICATE KEY UPDATE 
    answer_content = VALUES(answer_content),
    answer_score = VALUES(answer_score),
    update_time = NOW();

-- 验证数据插入
SELECT * FROM psy_t_answer_record ORDER BY id DESC LIMIT 5;

COMMIT;
