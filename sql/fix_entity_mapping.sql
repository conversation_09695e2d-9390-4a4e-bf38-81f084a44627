-- 修复实体类映射问题的完整脚本
-- 确保数据库表结构与实体类字段完全匹配

-- 1. 检查当前表结构
SELECT 'Current table structure:' as info;
DESCRIBE psy_consultant_center_config;

-- 2. 添加缺失的字段
ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认配置' AFTER enable_arrival_filter;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS del_flag TINYINT(1) DEFAULT 0 COMMENT '删除标志' AFTER status;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS create_by VARCHAR(64) DEFAULT '' COMMENT '创建者' AFTER del_flag;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER create_by;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS update_by VARCHAR(64) DEFAULT '' COMMENT '更新者' AFTER create_time;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER update_by;

ALTER TABLE psy_consultant_center_config 
ADD COLUMN IF NOT EXISTS remark VARCHAR(500) DEFAULT '' COMMENT '备注' AFTER update_time;

-- 3. 查看更新后的表结构
SELECT 'Updated table structure:' as info;
DESCRIBE psy_consultant_center_config;

-- 4. 插入测试数据（包含所有字段）
INSERT INTO psy_consultant_center_config (
    consultant_id, center_id, arrival_time_hours, enable_arrival_filter, 
    is_default, status, del_flag, create_by, create_time, remark
) VALUES 
(1, 1, 1.0, 1, 1, 1, 0, 'admin', NOW(), '测试：住得近的咨询师'),
(2, 1, 2.0, 1, 1, 1, 0, 'admin', NOW(), '测试：标准配置'),
(3, 1, 3.0, 0, 1, 1, 0, 'admin', NOW(), '测试：禁用过滤'),
(4, 1, 0.5, 1, 1, 1, 0, 'admin', NOW(), '测试：快速到达'),
(5, 1, 2.5, 1, 1, 1, 0, 'admin', NOW(), '测试：较远距离')
ON DUPLICATE KEY UPDATE 
    arrival_time_hours = VALUES(arrival_time_hours),
    enable_arrival_filter = VALUES(enable_arrival_filter),
    remark = VALUES(remark),
    update_time = NOW();

-- 5. 验证数据插入
SELECT 'Test data verification:' as info;
SELECT 
    id, consultant_id, center_id, arrival_time_hours, enable_arrival_filter,
    is_default, status, del_flag, create_by, create_time, remark
FROM psy_consultant_center_config 
WHERE consultant_id BETWEEN 1 AND 5
ORDER BY consultant_id;

-- 6. 测试实体类映射
SELECT 'Entity mapping test:' as info;
SELECT 
    consultant_id,
    arrival_time_hours,
    enable_arrival_filter,
    CASE enable_arrival_filter 
        WHEN 1 THEN '启用过滤' 
        WHEN 0 THEN '禁用过滤' 
    END as filter_status,
    is_default,
    status,
    del_flag,
    create_by,
    create_time,
    remark
FROM psy_consultant_center_config 
WHERE del_flag = 0
ORDER BY consultant_id;

COMMIT;
