-- 验证答案映射修复的完整性测试脚本
-- 确保字段映射与数据库表结构完全匹配

-- 1. 验证数据库表结构
SELECT 'Database Table Structure Verification:' as info;
DESCRIBE psy_t_answer_record;

-- 2. 验证字段映射对应关系
SELECT 'Field Mapping Verification:' as info;
SELECT 
    'XML字段 -> 数据库字段' as mapping_type,
    'answerText -> answer_content' as field_1,
    'score -> answer_score' as field_2,
    'timeSpent -> response_time' as field_3,
    'answerTime -> answer_time' as field_4;

-- 3. 验证相关表的结构
SELECT 'Related Tables Verification:' as info;

-- 检查题目表结构
SELECT 'psy_t_question table structure:' as table_info;
DESCRIBE psy_t_question;

-- 检查选项表结构
SELECT 'psy_t_question_option table structure:' as table_info;
DESCRIBE psy_t_question_option;

-- 检查解释表结构（包含dimension字段）
SELECT 'psy_t_interpretation table structure:' as table_info;
DESCRIBE psy_t_interpretation;

-- 4. 插入测试数据
INSERT INTO psy_t_answer_record (
    record_id, question_id, option_id, answer_content, answer_score, 
    answer_time, response_time, del_flag, create_by, create_time
) VALUES 
-- 测试数据
(3001, 1, 1, '选择答案A', 2.50, NOW(), 15, 0, 'test_user', NOW()),
(3001, 2, 3, '选择答案C', 3.00, NOW(), 20, 0, 'test_user', NOW()),
(3001, 3, NULL, '这是填空题答案', 4.00, NOW(), 30, 0, 'test_user', NOW()),
(3002, 1, 2, '选择答案B', 1.00, NOW(), 18, 0, 'test_user2', NOW())
ON DUPLICATE KEY UPDATE 
    answer_content = VALUES(answer_content),
    answer_score = VALUES(answer_score),
    update_time = NOW();

-- 5. 验证基础查询（模拟selectAnswerList）
SELECT 'Basic Query Verification:' as info;
SELECT 
    id, record_id, question_id, option_id, 
    answer_content, answer_score, answer_time, response_time,
    del_flag, create_by, create_time
FROM psy_t_answer_record 
WHERE record_id IN (3001, 3002) AND del_flag = 0
ORDER BY record_id, question_id;

-- 6. 验证按记录ID查询（模拟selectAnswersByRecordId）
SELECT 'Query by Record ID Verification:' as info;
SELECT 
    id, record_id, question_id, option_id, answer_content, answer_score
FROM psy_t_answer_record 
WHERE record_id = 3001 AND del_flag = 0
ORDER BY question_id;

-- 7. 验证总分计算（模拟calculateTotalScore）
SELECT 'Total Score Calculation Verification:' as info;
SELECT 
    record_id,
    IFNULL(SUM(answer_score), 0) as total_score
FROM psy_t_answer_record 
WHERE record_id = 3001 AND del_flag = 0
GROUP BY record_id;

-- 8. 验证关联查询（模拟selectAnswerWithDetails）
SELECT 'Join Query Verification:' as info;
SELECT 
    a.id,
    a.record_id,
    a.question_id,
    a.option_id,
    a.answer_content,
    a.answer_score,
    a.response_time,
    'mock_question_content' as question_content,
    'mock_option_text' as option_text
FROM psy_t_answer_record a
WHERE a.record_id = 3001 AND a.del_flag = 0
ORDER BY a.question_id;

-- 9. 测试插入操作（模拟insertAnswer）
INSERT INTO psy_t_answer_record (
    record_id, question_id, option_id, answer_content, answer_score, 
    answer_time, response_time, del_flag, create_by, create_time, update_by, update_time
) VALUES (
    3003, 1, 1, '新插入的答案', 3.50, NOW(), 12, 0, 'insert_test', NOW(), 'insert_test', NOW()
);

-- 10. 验证插入结果
SELECT 'Insert Operation Verification:' as info;
SELECT 
    record_id, question_id, answer_content, answer_score, create_by
FROM psy_t_answer_record 
WHERE record_id = 3003;

-- 11. 测试更新操作（模拟updateAnswer）
UPDATE psy_t_answer_record SET 
    answer_content = '更新后的答案内容',
    answer_score = 4.50,
    response_time = 25,
    update_by = 'update_test',
    update_time = NOW()
WHERE record_id = 3003 AND question_id = 1;

-- 12. 验证更新结果
SELECT 'Update Operation Verification:' as info;
SELECT 
    record_id, question_id, answer_content, answer_score, response_time, update_by, update_time
FROM psy_t_answer_record 
WHERE record_id = 3003;

-- 13. 测试批量插入（模拟batchInsertAnswers）
INSERT INTO psy_t_answer_record (
    record_id, question_id, option_id, answer_content, answer_score, 
    answer_time, response_time, del_flag, create_by, create_time, update_by, update_time
) VALUES 
(3004, 1, 1, '批量答案1', 2.00, NOW(), 10, 0, 'batch_test', NOW(), 'batch_test', NOW()),
(3004, 2, 2, '批量答案2', 3.00, NOW(), 15, 0, 'batch_test', NOW(), 'batch_test', NOW()),
(3004, 3, NULL, '批量填空答案', 4.00, NOW(), 20, 0, 'batch_test', NOW(), 'batch_test', NOW());

-- 14. 验证批量插入结果
SELECT 'Batch Insert Verification:' as info;
SELECT 
    record_id, question_id, answer_content, answer_score, create_by
FROM psy_t_answer_record 
WHERE record_id = 3004
ORDER BY question_id;

-- 15. 测试软删除（模拟deleteAnswerById）
UPDATE psy_t_answer_record SET del_flag = 1 WHERE record_id = 3004 AND question_id = 3;

-- 16. 验证软删除结果
SELECT 'Soft Delete Verification:' as info;
SELECT 
    record_id, question_id, answer_content, del_flag
FROM psy_t_answer_record 
WHERE record_id = 3004
ORDER BY question_id;

-- 17. 验证统计查询
SELECT 'Statistics Query Verification:' as info;
SELECT 
    COUNT(*) as total_answers,
    COUNT(CASE WHEN del_flag = 0 THEN 1 END) as active_answers,
    COUNT(CASE WHEN option_id IS NOT NULL THEN 1 END) as choice_answers,
    COUNT(CASE WHEN option_id IS NULL AND answer_content IS NOT NULL THEN 1 END) as text_answers,
    AVG(answer_score) as avg_score,
    AVG(response_time) as avg_response_time
FROM psy_t_answer_record 
WHERE record_id BETWEEN 3001 AND 3004;

-- 18. 验证字段类型兼容性
SELECT 'Field Type Compatibility Verification:' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CASE 
        WHEN COLUMN_NAME = 'answer_content' AND DATA_TYPE = 'text' THEN '✓ 对应answerText'
        WHEN COLUMN_NAME = 'answer_score' AND DATA_TYPE = 'decimal' THEN '✓ 对应score'
        WHEN COLUMN_NAME = 'response_time' AND DATA_TYPE = 'int' THEN '✓ 对应timeSpent'
        WHEN COLUMN_NAME = 'answer_time' AND DATA_TYPE = 'datetime' THEN '✓ 对应answerTime'
        ELSE '其他字段'
    END as mapping_note
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_answer_record'
  AND COLUMN_NAME IN ('answer_content', 'answer_score', 'response_time', 'answer_time');

-- 19. 验证维度相关查询的表结构
SELECT 'Dimension Related Tables Verification:' as info;
SELECT 
    'dimension字段在psy_t_interpretation表中' as note,
    COUNT(*) as interpretation_count
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_interpretation'
  AND COLUMN_NAME = 'dimension';

-- 20. 最终验证统计
SELECT 'Final Verification Summary:' as info;
SELECT 
    '字段映射修复完成' as status,
    'answer_content替代answer_text' as fix_1,
    'answer_score替代score' as fix_2,
    'response_time替代time_spent' as fix_3,
    'NOW()替代sysdate()' as fix_4,
    '移除题目表中不存在的dimension字段' as fix_5;

COMMIT;
