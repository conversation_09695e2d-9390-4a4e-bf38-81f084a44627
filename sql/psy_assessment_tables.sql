-- 心理测评系统数据库表结构
-- 表名前缀：psy_t_

-- 修复 category_id 字段缺失问题的SQL脚本
-- 如果 psy_t_scale 表已存在但缺少 category_id 字段，请执行以下语句：
-- ALTER TABLE psy_t_scale ADD COLUMN category_id bigint(20) DEFAULT NULL COMMENT '分类ID' AFTER instruction;
-- ALTER TABLE psy_t_scale ADD KEY idx_category_id (category_id);

-- 1. 测评量表主表
DROP TABLE IF EXISTS `psy_t_scale`;
CREATE TABLE `psy_t_scale` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '量表ID',
  `scale_name` varchar(100) NOT NULL COMMENT '量表名称',
  `scale_code` varchar(50) NOT NULL COMMENT '量表编码',
  `description` text COMMENT '量表描述',
  `instruction` text COMMENT '测评说明',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `author` varchar(100) DEFAULT NULL COMMENT '量表作者',
  `version` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `question_count` int(11) DEFAULT 0 COMMENT '题目数量',
  `time_limit` int(11) DEFAULT NULL COMMENT '时间限制(分钟)',
  `difficulty_level` tinyint(4) DEFAULT 1 COMMENT '难度等级(1=简单 2=中等 3=困难)',
  `price` decimal(10,2) DEFAULT 0.00 COMMENT '价格',
  `is_free` tinyint(4) DEFAULT 1 COMMENT '是否免费(0=付费 1=免费)',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签(JSON格式)',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态(0=未发布 1=已发布 2=下架)',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `test_count` int(11) DEFAULT 0 COMMENT '测试次数',
  `rating_avg` decimal(3,2) DEFAULT 0.00 COMMENT '平均评分',
  `rating_count` int(11) DEFAULT 0 COMMENT '评分人数',
  `search_keywords` varchar(500) DEFAULT NULL COMMENT '搜索关键词',
  `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数',
  `del_flag` tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scale_code` (`scale_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_free` (`is_free`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='心理测评量表主表';

-- 2. 测评题目表
DROP TABLE IF EXISTS `psy_t_question`;
CREATE TABLE `psy_t_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '题目ID',
  `scale_id` bigint(20) NOT NULL COMMENT '量表ID',
  `question_no` int(11) NOT NULL COMMENT '题目序号',
  `question_text` text NOT NULL COMMENT '题目内容',
  `question_type` tinyint(4) DEFAULT 1 COMMENT '题目类型(1=单选 2=多选 3=填空 4=量表)',
  `is_required` tinyint(4) DEFAULT 1 COMMENT '是否必答(0=否 1=是)',
  `score_type` tinyint(4) DEFAULT 1 COMMENT '计分方式(1=选项分值 2=自定义)',
  `dimension` varchar(50) DEFAULT NULL COMMENT '测量维度',
  `order_num` int(11) DEFAULT 0 COMMENT '显示顺序',
  `del_flag` tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale_id` (`scale_id`),
  KEY `idx_question_no` (`question_no`),
  CONSTRAINT `fk_question_scale` FOREIGN KEY (`scale_id`) REFERENCES `psy_t_scale` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='心理测评题目表';

-- 3. 题目选项表
DROP TABLE IF EXISTS `psy_t_question_option`;
CREATE TABLE `psy_t_question_option` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '选项ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `option_text` varchar(500) NOT NULL COMMENT '选项内容',
  `option_value` varchar(50) DEFAULT NULL COMMENT '选项值',
  `score` int(11) DEFAULT 0 COMMENT '选项分值',
  `order_num` int(11) DEFAULT 0 COMMENT '显示顺序',
  `del_flag` tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_question_id` (`question_id`),
  KEY `idx_order_num` (`order_num`),
  CONSTRAINT `fk_option_question` FOREIGN KEY (`question_id`) REFERENCES `psy_t_question` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='心理测评题目选项表';

-- 4. 测评记录表
DROP TABLE IF EXISTS `psy_t_assessment_record`;
CREATE TABLE `psy_t_assessment_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `scale_id` bigint(20) NOT NULL COMMENT '量表ID',
  `session_id` varchar(64) NOT NULL COMMENT '测评会话ID',
  `start_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) DEFAULT 0 COMMENT '测评时长(秒)',
  `total_score` decimal(10,2) DEFAULT 0.00 COMMENT '总分',
  `max_score` decimal(10,2) DEFAULT 0.00 COMMENT '满分',
  `percentage` decimal(5,2) DEFAULT 0.00 COMMENT '得分率(%)',
  `status` tinyint(4) DEFAULT 0 COMMENT '状态(0=进行中 1=已完成 2=已中断)',
  `result_level` varchar(20) DEFAULT NULL COMMENT '结果等级',
  `result_description` text DEFAULT NULL COMMENT '结果描述',
  `suggestions` text DEFAULT NULL COMMENT '建议',
  `dimension_scores` text DEFAULT NULL COMMENT '维度得分(JSON格式)',
  `is_anonymous` tinyint(4) DEFAULT 0 COMMENT '是否匿名(0=否 1=是)',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `del_flag` tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_scale_id` (`scale_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_record_scale` FOREIGN KEY (`scale_id`) REFERENCES `psy_t_scale` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='心理测评记录表';

-- 5. 测评答案表
DROP TABLE IF EXISTS `psy_t_answer`;
CREATE TABLE `psy_t_answer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `record_id` bigint(20) NOT NULL COMMENT '测评记录ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `option_id` bigint(20) DEFAULT NULL COMMENT '选项ID(单选/多选)',
  `answer_text` text DEFAULT NULL COMMENT '答案内容(填空题)',
  `score` decimal(10,2) DEFAULT 0.00 COMMENT '得分',
  `answer_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
  `time_spent` int(11) DEFAULT 0 COMMENT '答题耗时(秒)',
  `del_flag` tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_question_id` (`question_id`),
  KEY `idx_option_id` (`option_id`),
  CONSTRAINT `fk_answer_record` FOREIGN KEY (`record_id`) REFERENCES `psy_t_assessment_record` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_answer_question` FOREIGN KEY (`question_id`) REFERENCES `psy_t_question` (`id`),
  CONSTRAINT `fk_answer_option` FOREIGN KEY (`option_id`) REFERENCES `psy_t_question_option` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='心理测评答案表';

-- 6. 测评订单表
DROP TABLE IF EXISTS `psy_t_order`;
CREATE TABLE `psy_t_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `scale_id` bigint(20) NOT NULL COMMENT '量表ID',
  `scale_name` varchar(100) NOT NULL COMMENT '量表名称',
  `original_price` decimal(10,2) DEFAULT 0.00 COMMENT '原价',
  `actual_price` decimal(10,2) DEFAULT 0.00 COMMENT '实付金额',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '优惠金额',
  `coupon_id` bigint(20) DEFAULT NULL COMMENT '优惠券ID',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `payment_status` tinyint(4) DEFAULT 0 COMMENT '支付状态(0=待支付 1=已支付 2=已退款)',
  `order_status` tinyint(4) DEFAULT 0 COMMENT '订单状态(0=待支付 1=已支付 2=已完成 3=已取消)',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `refund_amount` decimal(10,2) DEFAULT 0.00 COMMENT '退款金额',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `refund_reason` varchar(200) DEFAULT NULL COMMENT '退款原因',
  `del_flag` tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_scale_id` (`scale_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_order_status` (`order_status`),
  CONSTRAINT `fk_order_scale` FOREIGN KEY (`scale_id`) REFERENCES `psy_t_scale` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='心理测评订单表';

-- 7. 测评评价表
DROP TABLE IF EXISTS `psy_t_review`;
CREATE TABLE `psy_t_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `scale_id` bigint(20) NOT NULL COMMENT '量表ID',
  `record_id` bigint(20) DEFAULT NULL COMMENT '测评记录ID',
  `rating` tinyint(4) NOT NULL COMMENT '评分(1-5分)',
  `content` text DEFAULT NULL COMMENT '评价内容',
  `is_anonymous` tinyint(4) DEFAULT 0 COMMENT '是否匿名(0=否 1=是)',
  `status` tinyint(4) DEFAULT 0 COMMENT '审核状态(0=待审核 1=已通过 2=已拒绝)',
  `audit_by` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(200) DEFAULT NULL COMMENT '审核备注',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数',
  `del_flag` tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_scale_id` (`scale_id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_review_scale` FOREIGN KEY (`scale_id`) REFERENCES `psy_t_scale` (`id`),
  CONSTRAINT `fk_review_record` FOREIGN KEY (`record_id`) REFERENCES `psy_t_assessment_record` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='心理测评评价表';

-- 8. 量表分类关联表
DROP TABLE IF EXISTS `psy_t_scale_category_rel`;
CREATE TABLE `psy_t_scale_category_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `scale_id` bigint(20) NOT NULL COMMENT '量表ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scale_category` (`scale_id`, `category_id`),
  KEY `idx_scale_id` (`scale_id`),
  KEY `idx_category_id` (`category_id`),
  CONSTRAINT `fk_rel_scale` FOREIGN KEY (`scale_id`) REFERENCES `psy_t_scale` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量表分类关联表';

-- 9. 测评结果解释表
DROP TABLE IF EXISTS `psy_t_interpretation`;
CREATE TABLE `psy_t_interpretation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '解释ID',
  `scale_id` bigint(20) NOT NULL COMMENT '量表ID',
  `dimension` varchar(50) DEFAULT NULL COMMENT '维度名称(为空表示总分)',
  `min_score` decimal(10,2) NOT NULL COMMENT '最小分数',
  `max_score` decimal(10,2) NOT NULL COMMENT '最大分数',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `level_description` text DEFAULT NULL COMMENT '等级描述',
  `suggestions` text DEFAULT NULL COMMENT '建议',
  `color` varchar(20) DEFAULT NULL COMMENT '显示颜色',
  `order_num` int(11) DEFAULT 0 COMMENT '显示顺序',
  `del_flag` tinyint(4) DEFAULT 0 COMMENT '删除标志(0=正常 1=删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale_id` (`scale_id`),
  KEY `idx_dimension` (`dimension`),
  KEY `idx_score_range` (`min_score`, `max_score`),
  CONSTRAINT `fk_interpretation_scale` FOREIGN KEY (`scale_id`) REFERENCES `psy_t_scale` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测评结果解释表';
