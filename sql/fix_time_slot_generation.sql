-- 修复系统时间槽生成问题的SQL脚本
-- 解决时间段"七零八落"的问题

-- ==================== 1. 检查当前时间段配置 ====================
SELECT '=== 当前时间段配置 ===' AS info;
SELECT 
    id,
    name,
    start_hour,
    end_hour,
    del_flag,
    create_time
FROM psy_time_range 
WHERE del_flag = 0 
ORDER BY start_hour;

-- ==================== 2. 检查时间段连续性 ====================
SELECT '=== 时间段连续性检查 ===' AS info;
SELECT 
    t1.id AS current_id,
    t1.name AS current_name,
    t1.end_hour AS current_end,
    t2.id AS next_id,
    t2.name AS next_name,
    t2.start_hour AS next_start,
    (t2.start_hour - t1.end_hour) AS gap_hours,
    CASE 
        WHEN t2.start_hour > t1.end_hour THEN CONCAT('空隙: ', t1.end_hour, ':00-', t2.start_hour, ':00')
        WHEN t2.start_hour < t1.end_hour THEN CONCAT('重叠: ', t1.start_hour, ':00-', t1.end_hour, ':00 与 ', t2.start_hour, ':00-', t2.end_hour, ':00')
        ELSE '连续'
    END AS continuity_status
FROM psy_time_range t1
LEFT JOIN psy_time_range t2 ON t2.start_hour = (
    SELECT MIN(start_hour) 
    FROM psy_time_range 
    WHERE start_hour > t1.end_hour AND del_flag = 0
)
WHERE t1.del_flag = 0
ORDER BY t1.start_hour;

-- ==================== 3. 删除现有不完整的时间段配置 ====================
SELECT '=== 清理现有时间段配置 ===' AS info;
UPDATE psy_time_range SET del_flag = 1 WHERE del_flag = 0;

-- ==================== 4. 插入完整的时间段配置 ====================
SELECT '=== 插入新的完整时间段配置 ===' AS info;
INSERT INTO psy_time_range (name, icon_url, start_hour, end_hour, del_flag, create_by, create_time) VALUES
-- 上午时段 (9:00-12:00)
('上午早段', 'https://example.com/icons/morning-early.png', 9, 10, 0, 'system', NOW()),
('上午中段', 'https://example.com/icons/morning-mid.png', 10, 11, 0, 'system', NOW()),
('上午晚段', 'https://example.com/icons/morning-late.png', 11, 12, 0, 'system', NOW()),

-- 中午时段 (12:00-14:00)
('中午早段', 'https://example.com/icons/noon-early.png', 12, 13, 0, 'system', NOW()),
('中午晚段', 'https://example.com/icons/noon-late.png', 13, 14, 0, 'system', NOW()),

-- 下午时段 (14:00-18:00)
('下午早段', 'https://example.com/icons/afternoon-early.png', 14, 15, 0, 'system', NOW()),
('下午中段', 'https://example.com/icons/afternoon-mid.png', 15, 16, 0, 'system', NOW()),
('下午晚段', 'https://example.com/icons/afternoon-late.png', 16, 17, 0, 'system', NOW()),
('下午末段', 'https://example.com/icons/afternoon-end.png', 17, 18, 0, 'system', NOW()),

-- 晚上时段 (18:00-21:00)
('晚上早段', 'https://example.com/icons/evening-early.png', 18, 19, 0, 'system', NOW()),
('晚上中段', 'https://example.com/icons/evening-mid.png', 19, 20, 0, 'system', NOW()),
('晚上晚段', 'https://example.com/icons/evening-late.png', 20, 21, 0, 'system', NOW());

-- ==================== 5. 验证新的时间段配置 ====================
SELECT '=== 验证新的时间段配置 ===' AS info;
SELECT 
    id,
    name,
    start_hour,
    end_hour,
    CONCAT(start_hour, ':00-', end_hour, ':00') AS time_range
FROM psy_time_range 
WHERE del_flag = 0 
ORDER BY start_hour;

-- ==================== 6. 检查系统时间槽生成情况 ====================
SELECT '=== 检查今日系统时间槽 ===' AS info;
SELECT 
    DATE(date_key) AS slot_date,
    COUNT(*) AS slot_count,
    MIN(start_time) AS earliest_time,
    MAX(end_time) AS latest_time,
    COUNT(DISTINCT range_id) AS range_count
FROM psy_system_time_slot 
WHERE date_key = CURDATE()
  AND del_flag = 0
GROUP BY DATE(date_key);

-- ==================== 7. 检查时间槽分布情况 ====================
SELECT '=== 检查时间槽按时间段分布 ===' AS info;
SELECT 
    r.name AS range_name,
    r.start_hour,
    r.end_hour,
    COUNT(s.id) AS slot_count,
    MIN(s.start_time) AS first_slot,
    MAX(s.end_time) AS last_slot
FROM psy_time_range r
LEFT JOIN psy_system_time_slot s ON r.id = s.range_id 
    AND s.date_key = CURDATE() 
    AND s.del_flag = 0
WHERE r.del_flag = 0
GROUP BY r.id, r.name, r.start_hour, r.end_hour
ORDER BY r.start_hour;

-- ==================== 8. 清理旧的系统时间槽（可选） ====================
-- 如果需要重新生成系统时间槽，可以执行以下语句
-- DELETE FROM psy_system_time_slot WHERE date_key >= CURDATE();

-- ==================== 9. 检查时间槽连续性 ====================
SELECT '=== 检查系统时间槽连续性 ===' AS info;
SELECT 
    s1.start_time AS current_start,
    s1.end_time AS current_end,
    s2.start_time AS next_start,
    CASE 
        WHEN s2.start_time IS NULL THEN '最后一个时间槽'
        WHEN s1.end_time = s2.start_time THEN '连续'
        WHEN s1.end_time < s2.start_time THEN CONCAT('空隙: ', TIMEDIFF(s2.start_time, s1.end_time))
        ELSE CONCAT('重叠: ', TIMEDIFF(s1.end_time, s2.start_time))
    END AS continuity_status
FROM psy_system_time_slot s1
LEFT JOIN psy_system_time_slot s2 ON s2.start_time = (
    SELECT MIN(start_time) 
    FROM psy_system_time_slot 
    WHERE start_time > s1.end_time 
      AND date_key = s1.date_key 
      AND center_id = s1.center_id
      AND del_flag = 0
)
WHERE s1.date_key = CURDATE()
  AND s1.center_id = 1
  AND s1.del_flag = 0
ORDER BY s1.start_time
LIMIT 20;

-- ==================== 10. 统计信息 ====================
SELECT '=== 统计信息 ===' AS info;
SELECT 
    '时间段总数' AS metric,
    COUNT(*) AS value
FROM psy_time_range 
WHERE del_flag = 0

UNION ALL

SELECT 
    '工作时间跨度(小时)' AS metric,
    (MAX(end_hour) - MIN(start_hour)) AS value
FROM psy_time_range 
WHERE del_flag = 0

UNION ALL

SELECT 
    '今日系统时间槽总数' AS metric,
    COUNT(*) AS value
FROM psy_system_time_slot 
WHERE date_key = CURDATE() 
  AND del_flag = 0

UNION ALL

SELECT 
    '预期时间槽数量(12小时*4槽/小时)' AS metric,
    48 AS value;

-- ==================== 使用说明 ====================
/*
修复步骤：
1. 执行此SQL脚本修复时间段配置
2. 调用API重新生成系统时间槽：
   POST /wechat/systemTimeSlot/regenerate?startDate=2025-07-25&endDate=2025-08-01&centerId=1
3. 使用诊断API检查修复效果：
   GET /wechat/systemTimeSlot/diagnose?date=2025-07-25&centerId=1
4. 如果还有问题，使用一键修复：
   POST /wechat/systemTimeSlot/oneClickFix?startDate=2025-07-25&endDate=2025-08-01&centerId=1

API说明：
- 诊断接口：GET /wechat/systemTimeSlot/diagnose
- 修复时间段连续性：POST /wechat/systemTimeSlot/fixTimeRangeContinuity  
- 重新生成时间槽：POST /wechat/systemTimeSlot/regenerate
- 一键修复：POST /wechat/systemTimeSlot/oneClickFix
*/
