-- ==================== 时间槽生成问题诊断和修复脚本 ====================
-- 用于诊断和修复时间槽生成失败的问题
-- 执行前请备份数据库

-- ==================== 1. 诊断系统状态 ====================
SELECT '=== 系统状态诊断 ===' AS info;

-- 检查时间段配置
SELECT '--- 时间段配置检查 ---' AS info;
SELECT COUNT(*) AS time_range_count FROM psy_time_range WHERE del_flag = 0;
SELECT id, name, start_hour, end_hour FROM psy_time_range WHERE del_flag = 0 ORDER BY start_hour;

-- 检查咨询师数据
SELECT '--- 咨询师数据检查 ---' AS info;
SELECT COUNT(*) AS consultant_count FROM psy_consultants;
SELECT id, name, audit_status, work_status FROM psy_consultants LIMIT 5;

-- 检查排班记录（最近7天）
SELECT '--- 排班记录检查 ---' AS info;
SELECT COUNT(*) AS schedule_count 
FROM psy_time_counselor_schedule 
WHERE schedule_date >= CURDATE() AND schedule_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY);

SELECT counselor_id, schedule_date, start_time, end_time, is_working
FROM psy_time_counselor_schedule 
WHERE schedule_date >= CURDATE() AND schedule_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
ORDER BY counselor_id, schedule_date
LIMIT 10;

-- 检查时间槽数据（最近7天）
SELECT '--- 时间槽数据检查 ---' AS info;
SELECT COUNT(*) AS slot_count 
FROM psy_time_slot 
WHERE slot_date >= CURDATE() AND slot_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY);

SELECT counselor_id, slot_date, start_time, end_time, status
FROM psy_time_slot 
WHERE slot_date >= CURDATE() AND slot_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
ORDER BY counselor_id, slot_date, start_time
LIMIT 10;

-- 检查系统时间槽数据（最近7天）
SELECT '--- 系统时间槽数据检查 ---' AS info;
SELECT COUNT(*) AS system_slot_count 
FROM psy_system_time_slot 
WHERE slot_date >= CURDATE() AND slot_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY);

-- ==================== 2. 修复时间段配置 ====================
SELECT '=== 修复时间段配置 ===' AS info;

-- 如果时间段配置为空，插入默认配置
INSERT IGNORE INTO psy_time_range (name, icon_url, start_hour, end_hour, del_flag, create_by, create_time) VALUES
-- 上午时段 (9:00-12:00)
('上午早段', 'https://example.com/icons/morning-early.png', 9, 10, 0, 'system', NOW()),
('上午中段', 'https://example.com/icons/morning-mid.png', 10, 11, 0, 'system', NOW()),
('上午晚段', 'https://example.com/icons/morning-late.png', 11, 12, 0, 'system', NOW()),

-- 中午时段 (12:00-14:00)
('中午早段', 'https://example.com/icons/noon-early.png', 12, 13, 0, 'system', NOW()),
('中午晚段', 'https://example.com/icons/noon-late.png', 13, 14, 0, 'system', NOW()),

-- 下午时段 (14:00-18:00)
('下午早段', 'https://example.com/icons/afternoon-early.png', 14, 15, 0, 'system', NOW()),
('下午中段', 'https://example.com/icons/afternoon-mid.png', 15, 16, 0, 'system', NOW()),
('下午中晚段', 'https://example.com/icons/afternoon-mid-late.png', 16, 17, 0, 'system', NOW()),
('下午晚段', 'https://example.com/icons/afternoon-late.png', 17, 18, 0, 'system', NOW()),

-- 晚上时段 (18:00-21:00)
('晚上早段', 'https://example.com/icons/evening-early.png', 18, 19, 0, 'system', NOW()),
('晚上中段', 'https://example.com/icons/evening-mid.png', 19, 20, 0, 'system', NOW()),
('晚上晚段', 'https://example.com/icons/evening-late.png', 20, 21, 0, 'system', NOW());

-- 验证时间段配置
SELECT '--- 时间段配置验证 ---' AS info;
SELECT COUNT(*) AS time_range_count_after_fix FROM psy_time_range WHERE del_flag = 0;

-- ==================== 3. 创建测试咨询师（如果没有咨询师数据） ====================
SELECT '=== 创建测试咨询师 ===' AS info;

-- 检查是否需要创建测试咨询师
SET @consultant_count = (SELECT COUNT(*) FROM psy_consultants);

-- 如果没有咨询师，创建测试数据
INSERT INTO psy_consultants (
    name, gender, phone, audit_status, work_status, 
    personal_intro, price, min_fee, max_fee,
    create_time, update_time, create_by
)
SELECT * FROM (
    SELECT 
        '测试咨询师1' as name, '女' as gender, '13800000001' as phone, 
        '1' as audit_status, '0' as work_status,
        '这是一个测试咨询师账号，用于系统测试' as personal_intro,
        200.00 as price, 150 as min_fee, 300 as max_fee,
        NOW() as create_time, NOW() as update_time, 'system' as create_by
    UNION ALL
    SELECT 
        '测试咨询师2' as name, '男' as gender, '13800000002' as phone, 
        '1' as audit_status, '0' as work_status,
        '这是另一个测试咨询师账号，用于系统测试' as personal_intro,
        250.00 as price, 200 as min_fee, 400 as max_fee,
        NOW() as create_time, NOW() as update_time, 'system' as create_by
) AS tmp
WHERE @consultant_count = 0;

-- 验证咨询师数据
SELECT '--- 咨询师数据验证 ---' AS info;
SELECT COUNT(*) AS consultant_count_after_fix FROM psy_consultants;
SELECT id, name, audit_status, work_status FROM psy_consultants LIMIT 5;

-- ==================== 4. 修复现有排班记录的时间 ====================
SELECT '=== 修复现有排班记录 ===' AS info;

-- 首先检查现有排班记录的时间范围
SELECT '--- 修复前的排班记录统计 ---' AS info;
SELECT
    start_time, end_time, COUNT(*) as count
FROM psy_time_counselor_schedule
WHERE schedule_date >= CURDATE()
GROUP BY start_time, end_time
ORDER BY start_time, end_time;

-- 更新所有结束时间为18:00的排班记录为21:00
UPDATE psy_time_counselor_schedule
SET end_time = '21:00:00',
    update_time = NOW(),
    remark = CONCAT(IFNULL(remark, ''), ' [系统自动修复：将结束时间从18:00更新为21:00]')
WHERE end_time = '18:00:00'
  AND schedule_date >= CURDATE()
  AND del_flag = 0;

-- 检查修复结果
SELECT '--- 修复后的排班记录统计 ---' AS info;
SELECT
    start_time, end_time, COUNT(*) as count
FROM psy_time_counselor_schedule
WHERE schedule_date >= CURDATE()
GROUP BY start_time, end_time
ORDER BY start_time, end_time;

-- ==================== 5. 为咨询师生成缺失的排班记录 ====================
SELECT '=== 生成缺失的排班记录 ===' AS info;

-- 为所有咨询师生成未来7天的排班记录（如果不存在）
INSERT IGNORE INTO psy_time_counselor_schedule (
    counselor_id, schedule_date, start_time, end_time,
    is_working, center_id, is_template, del_flag, create_time
)
SELECT
    c.id as counselor_id,
    DATE_ADD(CURDATE(), INTERVAL d.day_offset DAY) as schedule_date,
    '09:00:00' as start_time,
    '21:00:00' as end_time,
    1 as is_working,
    1 as center_id,
    1 as is_template,
    0 as del_flag,
    NOW() as create_time
FROM psy_consultants c
CROSS JOIN (
    SELECT 0 as day_offset UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL
    SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6
) d
WHERE c.audit_status = '1' AND c.work_status = '0';

-- 验证排班记录
SELECT '--- 排班记录验证 ---' AS info;
SELECT COUNT(*) AS schedule_count_after_fix
FROM psy_time_counselor_schedule
WHERE schedule_date >= CURDATE() AND schedule_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY);

-- 显示修复后的排班记录详情
SELECT '--- 排班记录详情（前10条） ---' AS info;
SELECT counselor_id, schedule_date, start_time, end_time, is_working, remark
FROM psy_time_counselor_schedule
WHERE schedule_date >= CURDATE() AND schedule_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
ORDER BY counselor_id, schedule_date
LIMIT 10;

-- ==================== 6. 清理旧的时间槽数据 ====================
SELECT '=== 清理旧数据 ===' AS info;

-- 清理过期的时间槽
DELETE FROM psy_time_slot WHERE slot_date < CURDATE();
DELETE FROM psy_system_time_slot WHERE slot_date < CURDATE();

-- 清理今天和未来的时间槽，强制重新生成
DELETE FROM psy_time_slot WHERE slot_date >= CURDATE();
DELETE FROM psy_system_time_slot WHERE slot_date >= CURDATE();

-- ==================== 7. 最终状态检查 ====================
SELECT '=== 最终状态检查 ===' AS info;

SELECT 
    (SELECT COUNT(*) FROM psy_time_range WHERE del_flag = 0) AS time_ranges,
    (SELECT COUNT(*) FROM psy_consultants WHERE audit_status = '1' AND work_status = '0') AS active_consultants,
    (SELECT COUNT(*) FROM psy_time_counselor_schedule WHERE schedule_date >= CURDATE()) AS future_schedules,
    (SELECT COUNT(*) FROM psy_time_slot WHERE slot_date >= CURDATE()) AS future_slots,
    (SELECT COUNT(*) FROM psy_system_time_slot WHERE slot_date >= CURDATE()) AS future_system_slots;

SELECT '=== 修复脚本执行完成 ===' AS info;
SELECT '请运行时间槽生成任务测试系统是否正常工作' AS next_step;
