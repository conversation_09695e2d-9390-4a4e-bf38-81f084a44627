-- 咨询师配置表数据插入脚本
-- 为 psy_consultant_center_config 表插入测试数据

-- 清理可能存在的测试数据（可选）
-- DELETE FROM psy_consultant_center_config WHERE consultant_id IN (1,2,3,4,5,6,7,8,9,10);

-- 插入咨询师配置数据
INSERT INTO psy_consultant_center_config (
    consultant_id,
    center_id,
    arrival_time_hours,
    enable_arrival_filter,
    is_default,
    status,
    del_flag,
    create_by,
    create_time,
    remark
) VALUES
-- 咨询师1：住得近，启用过滤，1小时到店
(1, 1, 1.0, 1, 1, 1, 0, 'admin', NOW(), '住得很近的咨询师，1小时内可到店'),

-- 咨询师2：标准配置，启用过滤，2小时到店
(2, 1, 2.0, 1, 1, 1, 0, 'admin', NOW(), '标准配置，2小时到店时间'),

-- 咨询师3：住得远但不想限制客户，禁用过滤
(3, 1, 3.5, 0, 1, 1, 0, 'admin', NOW(), '住得远但禁用过滤，不限制客户预约时间'),

-- 咨询师4：快速到达，启用过滤，0.5小时到店
(4, 1, 0.5, 1, 1, 1, 'admin', NOW(), '住在咨询中心附近，30分钟内可到店'),

-- 咨询师5：较远距离，启用过滤，2.5小时到店
(5, 1, 2.5, 1, 1, 1, 'admin', NOW(), '距离较远，需要2.5小时到店'),

-- 咨询师6：弹性工作，禁用过滤
(6, 1, 1.5, 0, 1, 1, 'admin', NOW(), '弹性工作时间，不限制预约'),

-- 咨询师7：多中心配置 - 中心1
(7, 1, 2.0, 1, 1, 1, 'admin', NOW(), '多中心咨询师在中心1的配置'),

-- 咨询师7：多中心配置 - 中心2（假设有第二个中心）
(7, 2, 1.0, 1, 0, 1, 'admin', NOW(), '多中心咨询师在中心2的配置'),

-- 咨询师8：新手咨询师，使用默认配置
(8, 1, 2.0, 1, 1, 1, 'system', NOW(), '新手咨询师，使用系统默认配置'),

-- 咨询师9：经验丰富，自定义配置
(9, 1, 1.2, 1, 1, 1, 'admin', NOW(), '经验丰富的咨询师，精确的到店时间'),

-- 咨询师10：临时禁用过滤
(10, 1, 3.0, 0, 1, 1, 'admin', NOW(), '临时禁用过滤功能的咨询师');

-- 验证插入结果
SELECT 
    consultant_id,
    center_id,
    arrival_time_hours,
    enable_arrival_filter,
    CASE enable_arrival_filter 
        WHEN 1 THEN '启用过滤' 
        WHEN 0 THEN '禁用过滤' 
    END as filter_status,
    remark
FROM psy_consultant_center_config 
ORDER BY consultant_id, center_id;

-- 统计信息
SELECT 
    '总配置数' as metric,
    COUNT(*) as count
FROM psy_consultant_center_config
UNION ALL
SELECT 
    '启用过滤的咨询师数',
    COUNT(*)
FROM psy_consultant_center_config 
WHERE enable_arrival_filter = 1
UNION ALL
SELECT 
    '禁用过滤的咨询师数',
    COUNT(*)
FROM psy_consultant_center_config 
WHERE enable_arrival_filter = 0
UNION ALL
SELECT 
    '平均到店时间(小时)',
    ROUND(AVG(arrival_time_hours), 2)
FROM psy_consultant_center_config;

-- 按到店时间分组统计
SELECT 
    CASE 
        WHEN arrival_time_hours <= 1 THEN '1小时内'
        WHEN arrival_time_hours <= 2 THEN '1-2小时'
        WHEN arrival_time_hours <= 3 THEN '2-3小时'
        ELSE '3小时以上'
    END as time_range,
    COUNT(*) as consultant_count,
    GROUP_CONCAT(consultant_id ORDER BY consultant_id) as consultant_ids
FROM psy_consultant_center_config 
GROUP BY 
    CASE 
        WHEN arrival_time_hours <= 1 THEN '1小时内'
        WHEN arrival_time_hours <= 2 THEN '1-2小时'
        WHEN arrival_time_hours <= 3 THEN '2-3小时'
        ELSE '3小时以上'
    END
ORDER BY MIN(arrival_time_hours);

-- 测试查询：模拟API调用的查询
-- 1. 获取咨询师1的配置
SELECT * FROM psy_consultant_center_config 
WHERE consultant_id = 1 AND center_id = 1;

-- 2. 获取所有启用过滤的咨询师
SELECT consultant_id, arrival_time_hours, remark 
FROM psy_consultant_center_config 
WHERE enable_arrival_filter = 1 
ORDER BY arrival_time_hours;

-- 3. 获取所有禁用过滤的咨询师
SELECT consultant_id, arrival_time_hours, remark 
FROM psy_consultant_center_config 
WHERE enable_arrival_filter = 0 
ORDER BY consultant_id;

-- 4. 查找特定中心的所有配置
SELECT * FROM psy_consultant_center_config 
WHERE center_id = 1 
ORDER BY consultant_id;

COMMIT;
