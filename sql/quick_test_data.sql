-- 快速测试数据插入脚本
-- 用于快速创建测试数据，验证功能是否正常

-- 清理现有测试数据（可选）
DELETE FROM psy_consultant_center_config WHERE consultant_id BETWEEN 1 AND 10;

-- 插入基础测试数据
INSERT INTO psy_consultant_center_config (
    consultant_id, center_id, arrival_time_hours, enable_arrival_filter, 
    is_default, status, create_by, create_time, remark
) VALUES 
-- 测试用例1：住得近，启用过滤
(1, 1, 0.5, 1, 1, 1, 'test', NOW(), '测试：住得近的咨询师'),

-- 测试用例2：标准配置
(2, 1, 2.0, 1, 1, 1, 'test', NOW(), '测试：标准配置咨询师'),

-- 测试用例3：住得远，禁用过滤
(3, 1, 3.0, 0, 1, 1, 'test', NOW(), '测试：住得远但禁用过滤'),

-- 测试用例4：极快到达
(4, 1, 0.1, 1, 1, 1, 'test', NOW(), '测试：极快到达（6分钟）'),

-- 测试用例5：较慢到达，启用过滤
(5, 1, 4.0, 1, 1, 1, 'test', NOW(), '测试：较慢到达但启用过滤');

-- 验证插入结果
SELECT 
    consultant_id,
    arrival_time_hours,
    enable_arrival_filter,
    CASE enable_arrival_filter 
        WHEN 1 THEN '启用' 
        WHEN 0 THEN '禁用' 
    END as filter_status,
    remark
FROM psy_consultant_center_config 
WHERE consultant_id BETWEEN 1 AND 5
ORDER BY consultant_id;

-- 测试查询：模拟当前时间为10:00，查看各咨询师的最早预约时间
SELECT 
    consultant_id,
    arrival_time_hours,
    enable_arrival_filter,
    CASE 
        WHEN enable_arrival_filter = 0 THEN '10:00（不限制）'
        ELSE TIME_FORMAT(
            ADDTIME('10:00:00', SEC_TO_TIME(arrival_time_hours * 3600)), 
            '%H:%i'
        )
    END as earliest_appointment_time,
    remark
FROM psy_consultant_center_config 
WHERE consultant_id BETWEEN 1 AND 5
ORDER BY consultant_id;

COMMIT;
