-- 修复 psy_t_scale 表缺少 category_id 字段的问题
-- 执行日期：2025-01-17
-- 说明：解决 "Unknown column 's.category_id' in 'on clause'" 错误

-- 检查 category_id 字段是否存在
-- 如果不存在则添加该字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'psy_t_scale'
    AND COLUMN_NAME = 'category_id'
);

-- 如果字段不存在，则添加字段
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE psy_t_scale ADD COLUMN category_id bigint(20) DEFAULT NULL COMMENT ''分类ID'' AFTER instruction',
    'SELECT ''category_id column already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'psy_t_scale'
    AND INDEX_NAME = 'idx_category_id'
);

SET @sql = IF(@index_exists = 0,
    'ALTER TABLE psy_t_scale ADD KEY idx_category_id (category_id)',
    'SELECT ''idx_category_id index already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'psy_t_scale'
AND COLUMN_NAME = 'category_id';

-- 显示表结构确认
DESCRIBE psy_t_scale;
