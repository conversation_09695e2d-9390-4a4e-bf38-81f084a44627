-- 为咨询师表添加到店所需时间字段
-- 用于计算咨询师从当前位置到咨询中心所需的时间

-- 方案1：在咨询师主表添加字段
ALTER TABLE psy_consultants 
ADD COLUMN arrival_time_hours DECIMAL(3,1) DEFAULT 2.0 COMMENT '到店所需时间(小时)，用于计算线下咨询的可选时间段';

-- 方案2：创建咨询师-咨询中心关系表（如果咨询师在不同中心需要不同时间）
CREATE TABLE IF NOT EXISTS psy_consultant_center_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    consultant_id BIGINT NOT NULL COMMENT '咨询师ID',
    center_id BIGINT NOT NULL COMMENT '咨询中心ID',
    arrival_time_hours DECIMAL(3,1) DEFAULT 2.0 COMMENT '到该中心所需时间(小时)',
    enable_arrival_filter TINYINT(1) DEFAULT 1 COMMENT '是否启用到店时间过滤(0=禁用 1=启用)',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认配置',
    status TINYINT(1) DEFAULT 1 COMMENT '状态(0=禁用 1=启用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    UNIQUE KEY uk_consultant_center (consultant_id, center_id),
    INDEX idx_consultant_id (consultant_id),
    INDEX idx_center_id (center_id)
) COMMENT='咨询师-咨询中心配置表';

-- 插入默认配置数据（所有咨询师默认2小时到店时间，启用过滤）
INSERT INTO psy_consultant_center_config (consultant_id, center_id, arrival_time_hours, enable_arrival_filter, is_default, create_by)
SELECT
    c.id as consultant_id,
    1 as center_id,  -- 假设默认咨询中心ID为1
    2.0 as arrival_time_hours,
    1 as enable_arrival_filter,  -- 默认启用
    1 as is_default,
    'system' as create_by
FROM psy_consultants c
WHERE c.audit_status = '1' AND c.del_flag = '0'
ON DUPLICATE KEY UPDATE
    arrival_time_hours = VALUES(arrival_time_hours),
    enable_arrival_filter = VALUES(enable_arrival_filter),
    update_time = NOW(),
    update_by = 'system';

-- 系统配置表添加全局默认配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
VALUES
('咨询师默认到店时间', 'psy.consultant.default.arrival.hours', '2.0', 'Y', 'admin', NOW(), '咨询师到店所需的默认时间(小时)，用于过滤线下咨询时间段'),
('新咨询师默认启用到店时间过滤', 'psy.consultant.default.arrival.filter.enabled', 'true', 'Y', 'admin', NOW(), '新注册咨询师是否默认启用到店时间过滤功能')
ON DUPLICATE KEY UPDATE
    config_value = VALUES(config_value),
    update_time = NOW();

-- 查看配置
SELECT * FROM sys_config WHERE config_key LIKE 'psy.consultant%';

-- 测试查询：获取咨询师的到店时间配置
SELECT
    c.id as consultant_id,
    c.name as consultant_name,
    COALESCE(cc.arrival_time_hours, 2.0) as arrival_time_hours,
    COALESCE(cc.enable_arrival_filter, 1) as enable_arrival_filter,
    cc.center_id,
    CASE
        WHEN cc.enable_arrival_filter = 1 THEN '已启用'
        WHEN cc.enable_arrival_filter = 0 THEN '已禁用'
        ELSE '使用默认'
    END as filter_status
FROM psy_consultants c
LEFT JOIN psy_consultant_center_config cc ON c.id = cc.consultant_id AND cc.status = 1
WHERE c.audit_status = '1' AND c.del_flag = '0'
ORDER BY c.id;

-- 添加一些测试数据：不同咨询师的不同配置
INSERT INTO psy_consultant_center_config (consultant_id, center_id, arrival_time_hours, enable_arrival_filter, create_by, remark)
VALUES
(1, 1, 1.5, 1, 'admin', '咨询师1：1.5小时到店，启用过滤'),
(2, 1, 3.0, 0, 'admin', '咨询师2：3小时到店，但禁用过滤（可能住得很远但不想限制预约）'),
(3, 1, 0.5, 1, 'admin', '咨询师3：0.5小时到店，启用过滤（住得很近）')
ON DUPLICATE KEY UPDATE
    arrival_time_hours = VALUES(arrival_time_hours),
    enable_arrival_filter = VALUES(enable_arrival_filter),
    remark = VALUES(remark),
    update_time = NOW();
