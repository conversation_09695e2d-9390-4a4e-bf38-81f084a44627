-- 排班模板功能测试SQL脚本
-- 用于创建测试数据并验证排班模板功能

-- 1. 清理测试数据
DELETE FROM psy_time_template_item WHERE template_id IN (
    SELECT id FROM psy_time_schedule_template WHERE counselor_id IN (1, 2, 3)
);
DELETE FROM psy_time_schedule_template WHERE counselor_id IN (1, 2, 3);

-- 2. 插入测试排班模板
INSERT INTO psy_time_schedule_template (
    counselor_id, name, is_default, effective_start, effective_end, 
    del_flag, create_time, create_by, remark
) VALUES 
-- 咨询师1的模板
(1, '标准工作日排班', 1, '2025-01-01', '2025-12-31', 0, NOW(), 'admin', '周一到周五标准工作时间'),
(1, '弹性工作排班', 0, '2025-01-01', '2025-12-31', 0, NOW(), 'admin', '弹性工作时间安排'),
(1, '周末排班', 0, '2025-01-01', '2025-12-31', 0, NOW(), 'admin', '周末工作安排'),

-- 咨询师2的模板
(2, '上午排班', 1, '2025-01-01', '2025-12-31', 0, NOW(), 'admin', '只安排上午时间'),
(2, '下午排班', 0, '2025-01-01', '2025-12-31', 0, NOW(), 'admin', '只安排下午时间'),

-- 咨询师3的模板
(3, '全天排班', 1, '2025-01-01', '2025-12-31', 0, NOW(), 'admin', '全天工作安排');

-- 3. 获取插入的模板ID（假设从1开始）
SET @template1_id = (SELECT id FROM psy_time_schedule_template WHERE counselor_id = 1 AND name = '标准工作日排班');
SET @template2_id = (SELECT id FROM psy_time_schedule_template WHERE counselor_id = 1 AND name = '弹性工作排班');
SET @template3_id = (SELECT id FROM psy_time_schedule_template WHERE counselor_id = 1 AND name = '周末排班');
SET @template4_id = (SELECT id FROM psy_time_schedule_template WHERE counselor_id = 2 AND name = '上午排班');
SET @template5_id = (SELECT id FROM psy_time_schedule_template WHERE counselor_id = 2 AND name = '下午排班');
SET @template6_id = (SELECT id FROM psy_time_schedule_template WHERE counselor_id = 3 AND name = '全天排班');

-- 4. 插入模板明细数据

-- 咨询师1 - 标准工作日排班（周一到周五，上午9-12点，下午2-6点）
INSERT INTO psy_time_template_item (
    template_id, day_of_week, start_time, end_time, center_id, 
    del_flag, create_time, create_by
) VALUES 
-- 周一
(@template1_id, 1, '09:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template1_id, 1, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
-- 周二
(@template1_id, 2, '09:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template1_id, 2, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
-- 周三
(@template1_id, 3, '09:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template1_id, 3, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
-- 周四
(@template1_id, 4, '09:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template1_id, 4, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
-- 周五
(@template1_id, 5, '09:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template1_id, 5, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin');

-- 咨询师1 - 弹性工作排班（周一到周五，10-19点）
INSERT INTO psy_time_template_item (
    template_id, day_of_week, start_time, end_time, center_id, 
    del_flag, create_time, create_by
) VALUES 
(@template2_id, 1, '10:00:00', '19:00:00', 1, 0, NOW(), 'admin'),
(@template2_id, 2, '10:00:00', '19:00:00', 1, 0, NOW(), 'admin'),
(@template2_id, 3, '10:00:00', '19:00:00', 1, 0, NOW(), 'admin'),
(@template2_id, 4, '10:00:00', '19:00:00', 1, 0, NOW(), 'admin'),
(@template2_id, 5, '10:00:00', '19:00:00', 1, 0, NOW(), 'admin');

-- 咨询师1 - 周末排班（周六周日，10-16点）
INSERT INTO psy_time_template_item (
    template_id, day_of_week, start_time, end_time, center_id, 
    del_flag, create_time, create_by
) VALUES 
(@template3_id, 6, '10:00:00', '16:00:00', 1, 0, NOW(), 'admin'),
(@template3_id, 7, '10:00:00', '16:00:00', 1, 0, NOW(), 'admin');

-- 咨询师2 - 上午排班（周一到周五，8-12点）
INSERT INTO psy_time_template_item (
    template_id, day_of_week, start_time, end_time, center_id, 
    del_flag, create_time, create_by
) VALUES 
(@template4_id, 1, '08:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template4_id, 2, '08:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template4_id, 3, '08:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template4_id, 4, '08:00:00', '12:00:00', 1, 0, NOW(), 'admin'),
(@template4_id, 5, '08:00:00', '12:00:00', 1, 0, NOW(), 'admin');

-- 咨询师2 - 下午排班（周一到周五，14-18点）
INSERT INTO psy_time_template_item (
    template_id, day_of_week, start_time, end_time, center_id, 
    del_flag, create_time, create_by
) VALUES 
(@template5_id, 1, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template5_id, 2, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template5_id, 3, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template5_id, 4, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template5_id, 5, '14:00:00', '18:00:00', 1, 0, NOW(), 'admin');

-- 咨询师3 - 全天排班（周一到周日，9-18点）
INSERT INTO psy_time_template_item (
    template_id, day_of_week, start_time, end_time, center_id, 
    del_flag, create_time, create_by
) VALUES 
(@template6_id, 1, '09:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template6_id, 2, '09:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template6_id, 3, '09:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template6_id, 4, '09:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template6_id, 5, '09:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template6_id, 6, '09:00:00', '18:00:00', 1, 0, NOW(), 'admin'),
(@template6_id, 7, '09:00:00', '18:00:00', 1, 0, NOW(), 'admin');

-- 5. 验证查询
-- 查看所有模板
SELECT 
    t.id,
    t.counselor_id,
    t.name,
    t.is_default,
    t.effective_start,
    t.effective_end,
    COUNT(i.id) as item_count
FROM psy_time_schedule_template t
LEFT JOIN psy_time_template_item i ON t.id = i.template_id AND i.del_flag = 0
WHERE t.del_flag = 0
GROUP BY t.id
ORDER BY t.counselor_id, t.is_default DESC, t.create_time;

-- 查看模板明细
SELECT 
    t.name as template_name,
    i.day_of_week,
    CASE i.day_of_week 
        WHEN 1 THEN '周一'
        WHEN 2 THEN '周二'
        WHEN 3 THEN '周三'
        WHEN 4 THEN '周四'
        WHEN 5 THEN '周五'
        WHEN 6 THEN '周六'
        WHEN 7 THEN '周日'
    END as day_name,
    i.start_time,
    i.end_time
FROM psy_time_schedule_template t
JOIN psy_time_template_item i ON t.id = i.template_id
WHERE t.del_flag = 0 AND i.del_flag = 0
ORDER BY t.counselor_id, t.name, i.day_of_week, i.start_time;

-- 6. 测试查询语句
-- 查询咨询师1的所有模板
SELECT * FROM psy_time_schedule_template 
WHERE counselor_id = 1 AND del_flag = 0
ORDER BY is_default DESC, create_time DESC;

-- 查询咨询师1的默认模板
SELECT * FROM psy_time_schedule_template 
WHERE counselor_id = 1 AND is_default = 1 AND del_flag = 0
LIMIT 1;

-- 查询指定日期有效的模板（以2025-07-18为例，周五）
SELECT * FROM psy_time_schedule_template 
WHERE counselor_id = 1 AND del_flag = 0
AND (effective_start IS NULL OR effective_start <= '2025-07-18')
AND (effective_end IS NULL OR effective_end >= '2025-07-18')
ORDER BY is_default DESC, create_time DESC
LIMIT 1;

-- 查询模板的明细
SELECT * FROM psy_time_template_item 
WHERE template_id = @template1_id AND del_flag = 0
ORDER BY day_of_week, start_time;

COMMIT;
