-- 初始化搜索关键词数据

-- 1. 为咨询师表初始化搜索关键词
UPDATE `psy_consultants` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`name`, ''),
        IFNULL(`personal_intro`, ''),
        IFNULL(`personal_title`, ''),
        IFNULL(`location`, ''),
        IFNULL(`province`, ''),
        IFNULL(`city`, ''),
        IFNULL(`district`, ''),
        CASE 
            WHEN `gender` = '0' THEN '男咨询师'
            WHEN `gender` = '1' THEN '女咨询师'
            ELSE ''
        END,
        CASE 
            WHEN `can_teach` = 1 THEN '可讲课'
            ELSE ''
        END,
        CASE 
            WHEN `can_travel` = 1 THEN '可出差'
            ELSE ''
        END,
        CASE 
            WHEN `work_status` = '0' THEN '可预约'
            WHEN `work_status` = '1' THEN '休息中'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- 2. 为课程表初始化搜索关键词
UPDATE `psy_course` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`title`, ''),
        IFNULL(`summary`, ''),
        IFNULL(`tags`, ''),
        CASE 
            WHEN `difficulty_level` = 1 THEN '入门课程'
            WHEN `difficulty_level` = 2 THEN '进阶课程'
            WHEN `difficulty_level` = 3 THEN '高级课程'
            ELSE ''
        END,
        CASE 
            WHEN `is_free` = 1 THEN '免费课程'
            WHEN `is_free` = 0 THEN '付费课程'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '已发布'
            WHEN `status` = 0 THEN '未发布'
            WHEN `status` = 2 THEN '已下架'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- 3. 为冥想表初始化搜索关键词
UPDATE `psy_meditation` SET 
    `search_keywords` = CONCAT_WS(',',
        IFNULL(`title`, ''),
        IFNULL(`description`, ''),
        IFNULL(`narrator`, ''),
        IFNULL(`tags`, ''),
        CASE 
            WHEN `difficulty_level` = 1 THEN '入门冥想'
            WHEN `difficulty_level` = 2 THEN '进阶冥想'
            WHEN `difficulty_level` = 3 THEN '高级冥想'
            ELSE ''
        END,
        CASE 
            WHEN `is_free` = 1 THEN '免费冥想'
            WHEN `is_free` = 0 THEN '付费冥想'
            ELSE ''
        END,
        CASE 
            WHEN `status` = 1 THEN '已发布'
            WHEN `status` = 0 THEN '未发布'
            WHEN `status` = 2 THEN '已下架'
            ELSE ''
        END,
        CASE 
            WHEN `duration` < 300 THEN '短时冥想'
            WHEN `duration` BETWEEN 300 AND 1200 THEN '中时冥想'
            WHEN `duration` > 1200 THEN '长时冥想'
            ELSE ''
        END
    )
WHERE `search_keywords` IS NULL OR `search_keywords` = '';

-- 4. 清理空的关键词（去除多余的逗号）
UPDATE `psy_consultants` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(REPLACE(`search_keywords`, ',,', ','), ',,', ',')) WHERE `search_keywords` IS NOT NULL;
UPDATE `psy_course` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(REPLACE(`search_keywords`, ',,', ','), ',,', ',')) WHERE `search_keywords` IS NOT NULL;
UPDATE `psy_meditation` SET `search_keywords` = TRIM(BOTH ',' FROM REPLACE(REPLACE(`search_keywords`, ',,', ','), ',,', ',')) WHERE `search_keywords` IS NOT NULL;

-- 5. 初始化搜索和查看次数（如果需要模拟数据）
-- UPDATE `psy_consultants` SET `search_count` = FLOOR(RAND() * 100), `view_count` = FLOOR(RAND() * 500) WHERE `search_count` = 0;
-- UPDATE `psy_course` SET `search_count` = FLOOR(RAND() * 50) WHERE `search_count` = 0;
-- UPDATE `psy_meditation` SET `search_count` = FLOOR(RAND() * 30) WHERE `search_count` = 0;
