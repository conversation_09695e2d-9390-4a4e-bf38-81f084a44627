-- 为量表表添加详细测评信息字段
-- 包括测评须知、目的、对象等详细信息

-- 添加测评详细信息字段
ALTER TABLE psy_t_scale 
ADD COLUMN test_notice TEXT COMMENT '测评须知' AFTER introduction;

ALTER TABLE psy_t_scale 
ADD COLUMN test_purpose TEXT COMMENT '测评目的' AFTER test_notice;

ALTER TABLE psy_t_scale 
ADD COLUMN test_object TEXT COMMENT '测评对象' AFTER test_purpose;

ALTER TABLE psy_t_scale 
ADD COLUMN test_preparation TEXT COMMENT '测评准备' AFTER test_object;

ALTER TABLE psy_t_scale 
ADD COLUMN test_processing TEXT COMMENT '测评后的处理' AFTER test_preparation;

ALTER TABLE psy_t_scale 
ADD COLUMN test_attention TEXT COMMENT '注意事项' AFTER test_processing;

ALTER TABLE psy_t_scale 
ADD COLUMN test_theory TEXT COMMENT '测评基础理论' AFTER test_attention;

ALTER TABLE psy_t_scale 
ADD COLUMN test_application TEXT COMMENT '测评应用' AFTER test_theory;

ALTER TABLE psy_t_scale 
ADD COLUMN applicable_age VARCHAR(100) COMMENT '适用年龄' AFTER test_application;

-- 查看更新后的表结构
DESCRIBE psy_t_scale;

-- 为现有的量表数据添加示例数据
UPDATE psy_t_scale SET 
    test_notice = '受试者一般需具有初中文化水平。',
    test_purpose = '区别评定短暂的焦虑情绪状态和人格特质性焦虑倾向，为不同的研究目的和临床实践服务。',
    test_object = '受试者一般为具有初中文化水平及以上的成人。',
    test_preparation = '无',
    test_processing = '无',
    test_attention = '无',
    test_theory = 'Cattell (1961-1966)和Spielberger (1966-1979)提出状态焦虑(State Anxiety)和特质焦虑(Trait Anxiety)的概念。前者描述一种不愉快的情绪体验，如紧张、恐惧、忧虑和神经质，伴有植物神经系统的功能亢进，一般为短暂性的。特质焦虑则用来描述相对稳定的，作为一种人格特质且具有个体差异的焦虑倾向。',
    test_application = '旨在临床学家、行为学家和内科学家提供一种工具以区别评定短暂的焦虑情绪状态和人格特质性焦虑倾向，为不同的研究目的和临床实践服务。',
    applicable_age = '成人'
WHERE name LIKE '%焦虑%' OR name LIKE '%STAI%' OR name LIKE '%SAS%';

-- 为其他量表添加通用的默认数据
UPDATE psy_t_scale SET 
    test_notice = '请在安静的环境中进行测评，确保能够专心作答。',
    test_purpose = '通过科学的心理测评方法，了解个体的心理状态和特征。',
    test_object = '适用于具有一定文化水平的成年人。',
    test_preparation = '无特殊准备要求。',
    test_processing = '测评完成后，系统将自动生成测评报告。',
    test_attention = '请根据自己的真实情况作答，不要过度思考。',
    test_theory = '基于现代心理学理论和实证研究开发的标准化测评工具。',
    test_application = '适用于心理健康评估、个人发展指导、临床诊断辅助等场景。',
    applicable_age = '18岁以上成人'
WHERE test_notice IS NULL OR test_notice = '';

-- 验证数据更新
SELECT 
    id,
    name,
    test_notice,
    test_purpose,
    test_object,
    applicable_age
FROM psy_t_scale 
WHERE del_flag = '0'
ORDER BY id
LIMIT 5;

COMMIT;
