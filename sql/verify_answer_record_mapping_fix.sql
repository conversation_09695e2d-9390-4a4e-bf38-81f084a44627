-- 验证答题记录表映射修复的完整性测试脚本
-- 确保数据库表结构与实体类、XML映射完全匹配

-- 1. 验证数据库表结构
SELECT 'Database Table Structure Verification:' as info;
DESCRIBE psy_t_answer_record;

-- 2. 验证所有字段是否存在且类型正确
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    CASE 
        WHEN COLUMN_NAME = 'id' AND DATA_TYPE = 'bigint' THEN '✓'
        WHEN COLUMN_NAME = 'record_id' AND DATA_TYPE = 'bigint' THEN '✓'
        WHEN COLUMN_NAME = 'question_id' AND DATA_TYPE = 'bigint' THEN '✓'
        WHEN COLUMN_NAME = 'option_id' AND DATA_TYPE = 'bigint' THEN '✓'
        WHEN COLUMN_NAME = 'answer_content' AND DATA_TYPE = 'text' THEN '✓'
        WHEN COLUMN_NAME = 'answer_score' AND DATA_TYPE = 'decimal' THEN '✓'
        WHEN COLUMN_NAME = 'answer_time' AND DATA_TYPE = 'datetime' THEN '✓'
        WHEN COLUMN_NAME = 'response_time' AND DATA_TYPE = 'int' THEN '✓'
        WHEN COLUMN_NAME = 'del_flag' AND DATA_TYPE = 'tinyint' THEN '✓'
        WHEN COLUMN_NAME = 'create_by' AND DATA_TYPE = 'varchar' THEN '✓'
        WHEN COLUMN_NAME = 'create_time' AND DATA_TYPE = 'datetime' THEN '✓'
        WHEN COLUMN_NAME = 'update_by' AND DATA_TYPE = 'varchar' THEN '✓'
        WHEN COLUMN_NAME = 'update_time' AND DATA_TYPE = 'datetime' THEN '✓'
        ELSE '✗'
    END as field_check
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_answer_record'
ORDER BY ORDINAL_POSITION;

-- 3. 验证索引是否存在
SELECT 'Index Verification:' as info;
SHOW INDEX FROM psy_t_answer_record;

-- 4. 插入完整的测试数据
INSERT INTO psy_t_answer_record (
    record_id, question_id, option_id, answer_content, answer_score, 
    answer_time, response_time, del_flag, create_by, create_time
) VALUES 
-- 单选题答案
(2001, 1, 1, '选择答案A', 2.50, NOW(), 15, 0, 'test_user', NOW()),
(2001, 2, 3, '选择答案C', 3.00, NOW(), 20, 0, 'test_user', NOW()),
-- 填空题答案（无option_id）
(2001, 3, NULL, '这是填空题的详细答案内容', 4.00, NOW(), 30, 0, 'test_user', NOW()),
-- 多选题答案（可能有多条记录）
(2001, 4, 2, '多选答案B', 1.50, NOW(), 25, 0, 'test_user', NOW()),
(2001, 4, 4, '多选答案D', 1.50, NOW(), 25, 0, 'test_user', NOW()),
-- 不同测评记录的答案
(2002, 1, 2, '选择答案B', 1.00, NOW(), 18, 0, 'test_user2', NOW()),
(2002, 2, 1, '选择答案A', 4.00, NOW(), 12, 0, 'test_user2', NOW())
ON DUPLICATE KEY UPDATE 
    answer_content = VALUES(answer_content),
    answer_score = VALUES(answer_score),
    update_time = NOW();

-- 5. 验证数据插入和基础查询
SELECT 'Data Insertion and Basic Query Verification:' as info;
SELECT 
    id, record_id, question_id, option_id, answer_content,
    answer_score, answer_time, response_time, del_flag,
    create_by, create_time, update_by, update_time
FROM psy_t_answer_record 
WHERE record_id IN (2001, 2002)
ORDER BY record_id, question_id;

-- 6. 测试各种查询场景（模拟XML中的查询）
SELECT 'Query Scenarios Testing:' as info;

-- 6.1 按测评记录查询（模拟selectAnswersByRecordId）
SELECT '6.1 Query by record_id:' as test_case;
SELECT id, record_id, question_id, option_id, answer_content, answer_score, response_time
FROM psy_t_answer_record 
WHERE record_id = 2001 AND del_flag = 0
ORDER BY question_id;

-- 6.2 按题目查询（模拟selectAnswersByQuestionId）
SELECT '6.2 Query by question_id:' as test_case;
SELECT record_id, question_id, option_id, answer_score, response_time
FROM psy_t_answer_record 
WHERE question_id = 1 AND del_flag = 0
ORDER BY record_id;

-- 6.3 查询特定记录和题目的答案（模拟selectAnswerByRecordAndQuestion）
SELECT '6.3 Query by record_id and question_id:' as test_case;
SELECT id, record_id, question_id, option_id, answer_content, answer_score
FROM psy_t_answer_record 
WHERE record_id = 2001 AND question_id = 1 AND del_flag = 0
LIMIT 1;

-- 7. 测试统计查询（模拟XML中的统计方法）
SELECT 'Statistics Query Testing:' as info;

-- 7.1 计算总分（模拟calculateTotalScore）
SELECT '7.1 Calculate total score:' as test_case;
SELECT record_id, IFNULL(SUM(answer_score), 0) as total_score
FROM psy_t_answer_record 
WHERE record_id = 2001 AND del_flag = 0
GROUP BY record_id;

-- 7.2 统计答案数量（模拟countAnswersByRecordId）
SELECT '7.2 Count answers by record:' as test_case;
SELECT record_id, COUNT(1) as answer_count
FROM psy_t_answer_record 
WHERE record_id = 2001 AND del_flag = 0
GROUP BY record_id;

-- 8. 测试更新操作
UPDATE psy_t_answer_record SET 
    answer_score = 5.00,
    update_by = 'admin',
    update_time = NOW()
WHERE record_id = 2001 AND question_id = 1;

-- 9. 验证更新结果
SELECT 'Update Operation Verification:' as info;
SELECT 
    record_id, question_id, answer_score, update_by, update_time
FROM psy_t_answer_record 
WHERE record_id = 2001 AND question_id = 1;

-- 10. 测试软删除
UPDATE psy_t_answer_record SET 
    del_flag = 1,
    update_by = 'admin',
    update_time = NOW()
WHERE record_id = 2002 AND question_id = 2;

-- 11. 验证软删除结果
SELECT 'Soft Delete Verification:' as info;
SELECT 
    COUNT(CASE WHEN del_flag = 0 THEN 1 END) as active_records,
    COUNT(CASE WHEN del_flag = 1 THEN 1 END) as deleted_records,
    COUNT(*) as total_records
FROM psy_t_answer_record 
WHERE record_id IN (2001, 2002);

-- 12. 测试关联查询（模拟selectAnswersWithDetailsByRecordId）
SELECT 'Join Query Testing:' as info;
SELECT 
    a.id,
    a.record_id,
    a.question_id,
    a.option_id,
    a.answer_content,
    a.answer_score,
    a.response_time,
    'mock_question_content' as question_content,
    'mock_option_text' as option_text
FROM psy_t_answer_record a
WHERE a.record_id = 2001 AND a.del_flag = 0
ORDER BY a.question_id;

-- 13. 测试批量操作（模拟batchInsertAnswerRecords）
INSERT INTO psy_t_answer_record (
    record_id, question_id, option_id, answer_content, answer_score, 
    answer_time, response_time, del_flag, create_by, create_time
) VALUES 
(2003, 1, 1, '批量插入答案1', 2.00, NOW(), 10, 0, 'batch_user', NOW()),
(2003, 2, 2, '批量插入答案2', 3.00, NOW(), 15, 0, 'batch_user', NOW()),
(2003, 3, NULL, '批量插入填空答案', 4.00, NOW(), 20, 0, 'batch_user', NOW());

-- 14. 验证批量插入结果
SELECT 'Batch Insert Verification:' as info;
SELECT 
    record_id, question_id, answer_content, answer_score, create_by
FROM psy_t_answer_record 
WHERE record_id = 2003 AND del_flag = 0
ORDER BY question_id;

-- 15. 测试字段类型兼容性
SELECT 'Field Type Compatibility Testing:' as info;
SELECT 
    id,
    record_id,
    question_id,
    option_id,
    CHAR_LENGTH(answer_content) as content_length,
    answer_score,
    answer_time,
    response_time,
    del_flag,
    create_by,
    create_time,
    update_by,
    update_time
FROM psy_t_answer_record 
WHERE record_id = 2001
ORDER BY question_id;

-- 16. 最终验证统计
SELECT 'Final Verification Statistics:' as info;
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN del_flag = 0 THEN 1 END) as active_records,
    COUNT(CASE WHEN option_id IS NOT NULL THEN 1 END) as choice_answers,
    COUNT(CASE WHEN option_id IS NULL AND answer_content IS NOT NULL THEN 1 END) as text_answers,
    AVG(answer_score) as avg_score,
    AVG(response_time) as avg_response_time,
    COUNT(DISTINCT record_id) as unique_records,
    COUNT(DISTINCT question_id) as unique_questions
FROM psy_t_answer_record
WHERE record_id BETWEEN 2001 AND 2003;

-- 17. 验证表名修复
SELECT 'Table Name Fix Verification:' as info;
SELECT 'All table references should be psy_t_answer_record (not psy_t_answer_record_record)' as note;

COMMIT;
