-- 批量插入咨询师配置数据脚本
-- 适用于已有咨询师数据的情况，自动为所有咨询师创建默认配置

-- 方法1：为所有现有咨询师创建默认配置
INSERT INTO psy_consultant_center_config (
    consultant_id, 
    center_id, 
    arrival_time_hours, 
    enable_arrival_filter, 
    is_default, 
    status, 
    create_by, 
    create_time, 
    remark
)
SELECT 
    c.id as consultant_id,
    1 as center_id,  -- 假设默认咨询中心ID为1
    2.0 as arrival_time_hours,  -- 默认2小时到店
    1 as enable_arrival_filter,  -- 默认启用过滤
    1 as is_default,
    1 as status,
    'system' as create_by,
    NOW() as create_time,
    '系统自动创建的默认配置' as remark
FROM psy_consultants c 
WHERE c.audit_status = '1' 
  AND c.del_flag = '0'
  AND NOT EXISTS (
      SELECT 1 FROM psy_consultant_center_config cc 
      WHERE cc.consultant_id = c.id AND cc.center_id = 1
  );

-- 方法2：为指定的咨询师ID列表创建配置
-- 如果您知道具体的咨询师ID，可以使用这种方式
INSERT INTO psy_consultant_center_config (
    consultant_id, 
    center_id, 
    arrival_time_hours, 
    enable_arrival_filter, 
    is_default, 
    status, 
    create_by, 
    create_time, 
    remark
) VALUES 
-- 根据实际情况修改咨询师ID
(11, 1, 2.0, 1, 1, 1, 'admin', NOW(), '咨询师11的配置'),
(12, 1, 1.5, 1, 1, 1, 'admin', NOW(), '咨询师12的配置'),
(13, 1, 2.5, 0, 1, 1, 'admin', NOW(), '咨询师13的配置，禁用过滤'),
(14, 1, 1.0, 1, 1, 1, 'admin', NOW(), '咨询师14的配置'),
(15, 1, 3.0, 1, 1, 1, 'admin', NOW(), '咨询师15的配置'),
(16, 1, 0.5, 1, 1, 1, 'admin', NOW(), '咨询师16的配置'),
(17, 1, 2.0, 0, 1, 1, 'admin', NOW(), '咨询师17的配置，禁用过滤'),
(18, 1, 1.8, 1, 1, 1, 'admin', NOW(), '咨询师18的配置'),
(19, 1, 2.2, 1, 1, 1, 'admin', NOW(), '咨询师19的配置'),
(20, 1, 1.3, 1, 1, 1, 'admin', NOW(), '咨询师20的配置')
ON DUPLICATE KEY UPDATE 
    arrival_time_hours = VALUES(arrival_time_hours),
    enable_arrival_filter = VALUES(enable_arrival_filter),
    remark = VALUES(remark),
    update_time = NOW();

-- 方法3：创建多样化的测试数据
-- 模拟不同类型的咨询师配置
INSERT INTO psy_consultant_center_config (
    consultant_id, center_id, arrival_time_hours, enable_arrival_filter, 
    is_default, status, create_by, create_time, remark
) VALUES 
-- 住得很近的咨询师们
(21, 1, 0.3, 1, 1, 1, 'admin', NOW(), '住在咨询中心楼上'),
(22, 1, 0.5, 1, 1, 1, 'admin', NOW(), '步行5分钟可到'),
(23, 1, 0.8, 1, 1, 1, 'admin', NOW(), '骑车10分钟可到'),

-- 标准距离的咨询师们
(24, 1, 1.5, 1, 1, 1, 'admin', NOW(), '地铁1.5小时'),
(25, 1, 2.0, 1, 1, 1, 'admin', NOW(), '开车2小时'),
(26, 1, 2.3, 1, 1, 1, 'admin', NOW(), '公交2.3小时'),

-- 距离较远但有不同策略的咨询师们
(27, 1, 3.0, 1, 1, 1, 'admin', NOW(), '距离远，启用过滤保证服务质量'),
(28, 1, 3.5, 0, 1, 1, 'admin', NOW(), '距离远，但禁用过滤不限制客户'),
(29, 1, 4.0, 0, 1, 1, 'admin', NOW(), '跨城咨询师，禁用过滤'),

-- 特殊情况的咨询师们
(30, 1, 1.0, 0, 1, 1, 'admin', NOW(), '住得近但禁用过滤（可能有其他考虑）')
ON DUPLICATE KEY UPDATE 
    arrival_time_hours = VALUES(arrival_time_hours),
    enable_arrival_filter = VALUES(enable_arrival_filter),
    remark = VALUES(remark),
    update_time = NOW();

-- 验证批量插入结果
SELECT 
    COUNT(*) as total_configs,
    COUNT(CASE WHEN enable_arrival_filter = 1 THEN 1 END) as enabled_filter,
    COUNT(CASE WHEN enable_arrival_filter = 0 THEN 1 END) as disabled_filter,
    MIN(arrival_time_hours) as min_arrival_time,
    MAX(arrival_time_hours) as max_arrival_time,
    AVG(arrival_time_hours) as avg_arrival_time
FROM psy_consultant_center_config;

-- 按过滤状态分组查看
SELECT 
    enable_arrival_filter,
    CASE enable_arrival_filter 
        WHEN 1 THEN '启用过滤' 
        WHEN 0 THEN '禁用过滤' 
    END as filter_status,
    COUNT(*) as count,
    AVG(arrival_time_hours) as avg_arrival_time,
    MIN(arrival_time_hours) as min_arrival_time,
    MAX(arrival_time_hours) as max_arrival_time
FROM psy_consultant_center_config 
GROUP BY enable_arrival_filter;

COMMIT;
