-- 测试量表数据映射的SQL脚本
-- 验证字段是否正确映射

-- 查看量表表结构
DESCRIBE psy_t_scale;

-- 查看现有量表数据
SELECT 
    id,
    name,
    code,
    category_id,
    description,
    introduction,
    question_count,
    duration,
    image_url,
    price,
    pay_mode,
    search_keywords,
    view_count,
    search_count,
    status,
    create_by,
    create_time
FROM psy_t_scale 
WHERE del_flag = '0' 
ORDER BY id 
LIMIT 5;

-- 验证字段映射
SELECT 
    id,
    name as scaleName,
    code as scaleCode,
    description,
    introduction as instruction,
    category_id as categoryId,
    create_by as author,
    '1.0' as version,
    question_count as questionCount,
    duration,
    CASE 
        WHEN question_count <= 20 THEN 1
        WHEN question_count <= 50 THEN 2
        ELSE 3
    END as difficultyLevel,
    CASE 
        WHEN question_count <= 20 THEN '简单'
        WHEN question_count <= 50 THEN '中等'
        ELSE '困难'
    END as difficultyDesc,
    price,
    CASE WHEN pay_mode = 0 THEN 1 ELSE 0 END as isFree,
    CASE WHEN pay_mode = 0 THEN '免费' ELSE CONCAT('¥', price) END as priceDesc,
    image_url as coverImage,
    search_keywords as tags,
    status,
    CASE 
        WHEN status = 0 THEN '停用'
        WHEN status = 1 THEN '已发布'
        WHEN status = 2 THEN '草稿'
        ELSE '未知'
    END as statusDesc,
    view_count as viewCount,
    search_count as testCount,
    DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as createTime
FROM psy_t_scale 
WHERE del_flag = '0' AND status = 1
ORDER BY id 
LIMIT 10;
