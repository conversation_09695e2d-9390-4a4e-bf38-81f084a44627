-- 插入量表测试数据，包含完整的测评详细信息

-- 插入STAI状态-特质焦虑量表数据
INSERT INTO psy_t_scale (
    name, code, category_id, description, introduction,
    test_notice, test_purpose, test_object, test_preparation, test_processing,
    test_attention, test_theory, test_application, applicable_age,
    question_count, scoring_type, duration, price, pay_mode, status,
    create_by, create_time, del_flag
) VALUES (
    'STAI状态-特质焦虑量表',
    'STAI',
    1,
    'STAI是目前国际上使用最广泛的焦虑评估工具之一，能够有效区分状态焦虑和特质焦虑。',
    '本量表用于评估个体的焦虑水平，包括状态焦虑和特质焦虑两个维度。请根据您的真实感受进行作答。',
    '受试者一般需具有初中文化水平。',
    '区别评定短暂的焦虑情绪状态和人格特质性焦虑倾向，为不同的研究目的和临床实践服务。',
    '受试者一般为具有初中文化水平及以上的成人。',
    '无',
    '无',
    '无',
    'Cattell (1961-1966)和Spielberger (1966-1979)提出状态焦虑(State Anxiety)和特质焦虑(Trait Anxiety)的概念。前者描述一种不愉快的情绪体验，如紧张、恐惧、忧虑和神经质，伴有植物神经系统的功能亢进，一般为短暂性的。特质焦虑则用来描述相对稳定的，作为一种人格特质且具有个体差异的焦虑倾向。',
    '旨在临床学家、行为学家和内科学家提供一种工具以区别评定短暂的焦虑情绪状态和人格特质性焦虑倾向，为不同的研究目的和临床实践服务。',
    '成人',
    40, 1, '15-20分钟', 0, 0, 1,
    'admin', NOW(), '0'
);

-- 插入SAS焦虑自评量表数据
INSERT INTO psy_t_scale (
    name, code, category_id, description, introduction,
    test_notice, test_purpose, test_object, test_preparation, test_processing,
    test_attention, test_theory, test_application, applicable_age,
    question_count, scoring_type, duration, price, pay_mode, status,
    create_by, create_time, del_flag
) VALUES (
    'SAS焦虑自评量表',
    'SAS',
    1,
    'SAS是一个简单易用的焦虑自评工具，广泛应用于临床和研究领域。',
    '本量表用于自我评估焦虑症状的严重程度。请根据您最近一周的感受进行评分。',
    '适用于具有一定阅读理解能力的成年人。',
    '评估个体焦虑症状的严重程度，为临床诊断和治疗提供参考。',
    '16岁以上具有一定文化水平的个体。',
    '选择安静的环境，确保不被打扰。',
    '根据评分结果提供相应的建议和指导。',
    '请根据真实感受作答，不要过度思考。',
    '基于焦虑症状的临床表现和心理测量学理论开发。',
    '适用于焦虑症的筛查、症状评估和治疗效果监测。',
    '16岁以上',
    20, 1, '10-15分钟', 0, 0, 1,
    'admin', NOW(), '0'
);

-- 插入抑郁自评量表数据
INSERT INTO psy_t_scale (
    name, code, category_id, description, introduction,
    test_notice, test_purpose, test_object, test_preparation, test_processing,
    test_attention, test_theory, test_application, applicable_age,
    question_count, scoring_type, duration, price, pay_mode, status,
    create_by, create_time, del_flag
) VALUES (
    'SDS抑郁自评量表',
    'SDS',
    2,
    'SDS是评估抑郁症状的经典工具，具有良好的信效度。',
    '本量表用于评估抑郁症状的严重程度。请根据您最近一周的情况进行评分。',
    '需要具备基本的阅读理解能力。',
    '筛查和评估抑郁症状，为心理健康评估提供客观依据。',
    '适用于青少年及成年人群。',
    '在安静、私密的环境中进行测评。',
    '根据测评结果提供专业建议，必要时建议寻求专业帮助。',
    '请诚实作答，避免社会期望偏差。',
    '基于抑郁症的临床症状学和心理测量学原理。',
    '广泛应用于抑郁症筛查、临床评估和疗效监测。',
    '13岁以上',
    20, 1, '10-15分钟', 0, 0, 1,
    'admin', NOW(), '0'
);

-- 插入大五人格量表数据
INSERT INTO psy_t_scale (
    name, code, category_id, description, introduction,
    test_notice, test_purpose, test_object, test_preparation, test_processing,
    test_attention, test_theory, test_application, applicable_age,
    question_count, scoring_type, duration, price, pay_mode, status,
    create_by, create_time, del_flag
) VALUES (
    'NEO-PI-R大五人格量表',
    'NEO-PI-R',
    3,
    '基于大五人格理论的权威人格测评工具，全面评估个体的人格特征。',
    '本量表评估五个主要人格维度：神经质、外向性、开放性、宜人性和尽责性。',
    '需要具备高中以上文化水平，能够理解抽象概念。',
    '全面评估个体的人格特征，为个人发展、职业规划和心理咨询提供科学依据。',
    '适用于18岁以上的成年人。',
    '确保有充足的时间完成测评，避免疲劳作答。',
    '提供详细的人格分析报告，包括各维度得分和解释。',
    '请根据平时的行为和感受作答，不要刻意迎合社会期望。',
    '基于Costa和McCrae的大五人格理论，经过大量实证研究验证。',
    '广泛应用于人格研究、职业咨询、临床评估和人才选拔。',
    '18岁以上成人',
    240, 1, '45-60分钟', 29.9, 1, 1,
    'admin', NOW(), '0'
);

-- 验证插入结果
SELECT 
    id, name, code, 
    test_notice, test_purpose, test_object, 
    applicable_age, question_count, duration
FROM psy_t_scale 
WHERE del_flag = '0' 
ORDER BY create_time DESC 
LIMIT 5;

COMMIT;
