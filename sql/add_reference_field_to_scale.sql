-- 为量表表添加引用文献字段
-- 用于存储量表的参考文献信息

-- 查看当前表结构
DESCRIBE psy_t_scale;

-- 添加引用文献字段
ALTER TABLE psy_t_scale 
ADD COLUMN reference_literature TEXT COMMENT '引用文献' AFTER test_application;

-- 查看更新后的表结构
DESCRIBE psy_t_scale;

-- 为现有量表添加示例引用文献数据
UPDATE psy_t_scale SET 
    reference_literature = '<PERSON><PERSON><PERSON><PERSON>, C. D., Gorsuch, R. L., Lushene, R., V<PERSON>, P. <PERSON>, & <PERSON>, G. A. (1983). Manual for the State-Trait Anxiety Inventory. Palo Alto, CA: Consulting Psychologists Press.'
WHERE name LIKE '%STAI%' OR name LIKE '%状态-特质焦虑%';

UPDATE psy_t_scale SET 
    reference_literature = 'Zung, W. W. K. (1971). A rating instrument for anxiety disorders. Psychosomatics, 12(6), 371-379.'
WHERE name LIKE '%SAS%' OR name LIKE '%焦虑自评%';

UPDATE psy_t_scale SET 
    reference_literature = 'Zung, W. W. K. (1965). A self-rating depression scale. Archives of General Psychiatry, 12(1), 63-70.'
WHERE name LIKE '%SDS%' OR name LIKE '%抑郁自评%';

UPDATE psy_t_scale SET 
    reference_literature = 'Costa, P. T., & McCrae, R. R. (1992). Revised NEO Personality Inventory (NEO-PI-R) and NEO Five-Factor Inventory (NEO-FFI): Professional manual. Psychological Assessment Resources.'
WHERE name LIKE '%NEO%' OR name LIKE '%大五人格%';

UPDATE psy_t_scale SET 
    reference_literature = 'McCroskey, J. C. (1982). An introduction to rhetorical communication (4th ed.). Englewood Cliffs, NJ: Prentice-Hall.'
WHERE name LIKE '%PRCA%' OR name LIKE '%人际交往焦虑%';

-- 为其他量表添加通用的引用文献格式
UPDATE psy_t_scale SET 
    reference_literature = '请参考相关心理学文献和量表开发者的原始研究论文。'
WHERE reference_literature IS NULL OR reference_literature = '';

-- 验证数据更新
SELECT 
    id,
    name,
    reference_literature
FROM psy_t_scale 
WHERE del_flag = '0'
ORDER BY id
LIMIT 10;

-- 验证字段是否正确添加
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_t_scale'
  AND COLUMN_NAME = 'reference_literature';

COMMIT;
