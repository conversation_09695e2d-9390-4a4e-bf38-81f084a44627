-- 测试时间槽生成功能的SQL脚本
-- 用于创建测试数据并验证时间槽生成是否正常

-- 1. 插入测试咨询师数据（如果不存在）
INSERT IGNORE INTO `psy_consultants` (
    `id`, `user_id`, `name`, `gender`, `phone`, `province`, `city`, `district`, 
    `personal_intro`, `personal_title`, `price`, `work_status`, `audit_status`, 
    `create_time`, `create_by`
) VALUES 
(1, 1, '测试咨询师1', '1', '13800138001', '广东省', '深圳市', '南山区', 
 '专业心理咨询师，擅长焦虑抑郁治疗', '高级心理咨询师', 500.00, '0', '1', 
 NOW(), 'admin'),
(2, 2, '测试咨询师2', '0', '13800138002', '广东省', '深圳市', '福田区', 
 '资深心理咨询师，专注婚姻家庭咨询', '资深心理咨询师', 680.00, '0', '1', 
 NOW(), 'admin');

-- 2. 插入测试排班数据（未来7天）
-- 清理可能存在的测试排班数据
DELETE FROM `psy_time_counselor_schedule` WHERE `counselor_id` IN (1, 2) AND `schedule_date` >= CURDATE();

-- 为咨询师1创建排班（工作日 9:00-17:00）
INSERT INTO `psy_time_counselor_schedule` (
    `counselor_id`, `schedule_date`, `start_time`, `end_time`, `center_id`, 
    `is_working`, `is_template`, `create_time`, `create_by`
) VALUES 
-- 明天开始的7天排班
(1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00:00', '17:00:00', 1, 1, 0, NOW(), 'admin'),
(1, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '09:00:00', '17:00:00', 1, 1, 0, NOW(), 'admin'),
(1, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '09:00:00', '17:00:00', 1, 1, 0, NOW(), 'admin'),
(1, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '09:00:00', '17:00:00', 1, 1, 0, NOW(), 'admin'),
(1, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '09:00:00', '17:00:00', 1, 1, 0, NOW(), 'admin'),
(1, DATE_ADD(CURDATE(), INTERVAL 6 DAY), '09:00:00', '17:00:00', 1, 1, 0, NOW(), 'admin'),
(1, DATE_ADD(CURDATE(), INTERVAL 7 DAY), '09:00:00', '17:00:00', 1, 1, 0, NOW(), 'admin');

-- 为咨询师2创建排班（工作日 10:00-18:00）
INSERT INTO `psy_time_counselor_schedule` (
    `counselor_id`, `schedule_date`, `start_time`, `end_time`, `center_id`, 
    `is_working`, `is_template`, `create_time`, `create_by`
) VALUES 
-- 明天开始的7天排班
(2, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '10:00:00', '18:00:00', 1, 1, 0, NOW(), 'admin'),
(2, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '10:00:00', '18:00:00', 1, 1, 0, NOW(), 'admin'),
(2, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '10:00:00', '18:00:00', 1, 1, 0, NOW(), 'admin'),
(2, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '10:00:00', '18:00:00', 1, 1, 0, NOW(), 'admin'),
(2, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '10:00:00', '18:00:00', 1, 1, 0, NOW(), 'admin'),
(2, DATE_ADD(CURDATE(), INTERVAL 6 DAY), '10:00:00', '18:00:00', 1, 1, 0, NOW(), 'admin'),
(2, DATE_ADD(CURDATE(), INTERVAL 7 DAY), '10:00:00', '18:00:00', 1, 1, 0, NOW(), 'admin');

-- 3. 清理可能存在的时间槽数据（避免重复生成）
DELETE FROM `psy_time_slot` WHERE `counselor_id` IN (1, 2) AND `date_key` >= CURDATE();
DELETE FROM `psy_system_time_slot` WHERE `center_id` = 1 AND `date_key` >= CURDATE();

-- 4. 查询验证数据
SELECT '=== 咨询师数据 ===' as info;
SELECT id, name, work_status, audit_status FROM `psy_consultants` WHERE id IN (1, 2);

SELECT '=== 排班数据 ===' as info;
SELECT counselor_id, schedule_date, start_time, end_time, is_working 
FROM `psy_time_counselor_schedule` 
WHERE counselor_id IN (1, 2) AND schedule_date >= CURDATE()
ORDER BY counselor_id, schedule_date;

SELECT '=== 现有时间槽数据 ===' as info;
SELECT COUNT(*) as slot_count FROM `psy_time_slot` WHERE counselor_id IN (1, 2) AND date_key >= CURDATE();

SELECT '=== 现有系统时间槽数据 ===' as info;
SELECT COUNT(*) as system_slot_count FROM `psy_system_time_slot` WHERE center_id = 1 AND date_key >= CURDATE();
