#!/bin/bash

# 编译验证脚本
# 验证删除 PsyAssessment 系列文件后的编译状态

echo "=========================================="
echo "编译验证脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="D:/code/XiHuan"

echo -e "\n${YELLOW}1. 清理编译缓存...${NC}"
cd "$PROJECT_ROOT"

# 清理 Maven 缓存
mvn clean -q

echo -e "\n${YELLOW}2. 检查已删除的 PsyAssessment 文件...${NC}"

# 检查实体类是否已删除
DELETED_ENTITIES=(
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentScale.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentQuestion.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentAnswer.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentRecord.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentOption.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentInterpretation.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentOrder.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentReview.java"
)

for file in "${DELETED_ENTITIES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${RED}✗ 文件仍存在: $file${NC}"
    else
        echo -e "${GREEN}✓ 文件已删除: $(basename $file)${NC}"
    fi
done

echo -e "\n${YELLOW}3. 检查 PsyT 系列文件是否存在...${NC}"

# 检查 PsyT 系列文件
PSY_T_FILES=(
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTScale.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTAnswerRecord.java"
    "xihuan-system/src/main/resources/mapper/system/PsyTScaleMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTAnswerRecordMapper.xml"
)

for file in "${PSY_T_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ 文件存在: $(basename $file)${NC}"
    else
        echo -e "${RED}✗ 文件缺失: $file${NC}"
    fi
done

echo -e "\n${YELLOW}4. 编译 xihuan-common 模块...${NC}"
if mvn -f xihuan-common/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-common 编译成功${NC}"
else
    echo -e "${RED}✗ xihuan-common 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-common/pom.xml compile
    exit 1
fi

echo -e "\n${YELLOW}5. 编译 xihuan-system 模块...${NC}"
if mvn -f xihuan-system/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-system 编译成功${NC}"
else
    echo -e "${RED}✗ xihuan-system 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-system/pom.xml compile
    exit 1
fi

echo -e "\n${YELLOW}6. 编译 xihuan-admin 模块...${NC}"
if mvn -f xihuan-admin/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-admin 编译成功${NC}"
else
    echo -e "${RED}✗ xihuan-admin 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-admin/pom.xml compile
    exit 1
fi

echo -e "\n${YELLOW}7. 全项目编译验证...${NC}"
if mvn compile -q; then
    echo -e "${GREEN}✓ 全项目编译成功${NC}"
else
    echo -e "${RED}✗ 全项目编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn compile
    exit 1
fi

echo -e "\n${YELLOW}8. 检查剩余的引用问题...${NC}"

# 搜索可能的引用问题
echo "搜索 PsyAssessment 引用..."
if grep -r "PsyAssessment" --include="*.java" xihuan-common/src/ xihuan-system/src/ xihuan-admin/src/ 2>/dev/null; then
    echo -e "${RED}✗ 发现 PsyAssessment 引用${NC}"
else
    echo -e "${GREEN}✓ 无 PsyAssessment 引用${NC}"
fi

echo -e "\n=========================================="
echo -e "${GREEN}🎉 编译验证完成！${NC}"
echo "=========================================="
echo "第一阶段修复状态："
echo "✓ PsyAssessment 系列文件已删除"
echo "✓ PsyT 系列文件正常工作"
echo "✓ 所有模块编译成功"
echo "✓ 无引用错误"

echo -e "\n下一步："
echo "1. 重启应用进行功能测试"
echo "2. 验证数据库操作正常"
echo "3. 继续第三阶段：完善 PsyT 系列"

exit 0
