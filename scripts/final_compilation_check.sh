#!/bin/bash

# 最终编译验证脚本
# 验证所有 PsyAssessment 系列文件删除后的编译状态

echo "=========================================="
echo "最终编译验证脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="D:/code/XiHuan"

echo -e "\n${YELLOW}1. 清理所有编译缓存...${NC}"
cd "$PROJECT_ROOT"

# 清理 Maven 缓存
echo "清理 Maven 缓存..."
mvn clean -q

# 清理 IDE 缓存
echo "清理 IDE 缓存..."
find . -name "*.class" -delete 2>/dev/null || true
find . -name "target" -type d -exec rm -rf {} + 2>/dev/null || true

echo -e "\n${YELLOW}2. 检查已删除的 PsyAssessment 文件...${NC}"

# 检查所有应该被删除的文件
DELETED_FILES=(
    # 实体类
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentScale.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentQuestion.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentAnswer.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentRecord.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentOption.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentInterpretation.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentOrder.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyAssessmentReview.java"
    
    # Mapper 接口
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentScaleMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentQuestionMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentAnswerMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentRecordMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentInterpretationMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentOptionMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentOrderMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyAssessmentReviewMapper.java"
    
    # XML 映射文件
    "xihuan-system/src/main/resources/mapper/system/PsyAssessmentScaleMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyAssessmentQuestionMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyAssessmentAnswerMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyAssessmentRecordMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyAssessmentInterpretationMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyAssessmentOptionMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyAssessmentOrderMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyAssessmentReviewMapper.xml"
    
    # 服务接口
    "xihuan-system/src/main/java/com/xihuan/system/service/IPsyAssessmentScaleService.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/IPsyAssessmentQuestionService.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/IPsyAssessmentRecordService.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/IPsyAssessmentOrderService.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/IPsyAssessmentReviewService.java"
    
    # 服务实现
    "xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyAssessmentScaleServiceImpl.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyAssessmentQuestionServiceImpl.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyAssessmentRecordServiceImpl.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyAssessmentOrderServiceImpl.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyAssessmentReviewServiceImpl.java"
    
    # 控制器
    "xihuan-admin/src/main/java/com/xihuan/web/controller/system/PsyAssessmentScaleController.java"
    "xihuan-admin/src/main/java/com/xihuan/web/controller/system/PsyAssessmentQuestionController.java"
    "xihuan-admin/src/main/java/com/xihuan/web/controller/system/PsyAssessmentRecordController.java"
    "xihuan-admin/src/main/java/com/xihuan/web/controller/system/PsyAssessmentOrderController.java"
    "xihuan-admin/src/main/java/com/xihuan/web/controller/system/PsyAssessmentReviewController.java"
    "xihuan-admin/src/main/java/com/xihuan/web/controller/miniapp/user/MiniAppUserAssessmentController.java"
)

DELETED_COUNT=0
REMAINING_COUNT=0

for file in "${DELETED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${RED}✗ 文件仍存在: $file${NC}"
        ((REMAINING_COUNT++))
    else
        echo -e "${GREEN}✓ 文件已删除: $(basename $file)${NC}"
        ((DELETED_COUNT++))
    fi
done

echo -e "\n${BLUE}删除统计: 已删除 $DELETED_COUNT 个文件, 剩余 $REMAINING_COUNT 个文件${NC}"

echo -e "\n${YELLOW}3. 检查 PsyT 系列文件是否存在...${NC}"

# 检查 PsyT 系列文件
PSY_T_FILES=(
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTScale.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTAnswerRecord.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTAssessmentRecord.java"
    "xihuan-system/src/main/resources/mapper/system/PsyTScaleMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTAnswerRecordMapper.xml"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTScaleMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTAnswerRecordMapper.java"
)

PSY_T_EXISTS=0
PSY_T_MISSING=0

for file in "${PSY_T_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ 文件存在: $(basename $file)${NC}"
        ((PSY_T_EXISTS++))
    else
        echo -e "${RED}✗ 文件缺失: $file${NC}"
        ((PSY_T_MISSING++))
    fi
done

echo -e "\n${BLUE}PsyT 系列统计: 存在 $PSY_T_EXISTS 个文件, 缺失 $PSY_T_MISSING 个文件${NC}"

echo -e "\n${YELLOW}4. 搜索剩余的 PsyAssessment 引用...${NC}"

# 搜索可能的引用问题
echo "搜索 Java 文件中的 PsyAssessment 引用..."
if find . -name "*.java" -exec grep -l "PsyAssessment" {} \; 2>/dev/null | head -10; then
    echo -e "${RED}✗ 发现 PsyAssessment 引用${NC}"
else
    echo -e "${GREEN}✓ 无 PsyAssessment 引用${NC}"
fi

echo -e "\n${YELLOW}5. 编译验证...${NC}"

echo -e "\n${YELLOW}5.1 编译 xihuan-common 模块...${NC}"
if mvn -f xihuan-common/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-common 编译成功${NC}"
else
    echo -e "${RED}✗ xihuan-common 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-common/pom.xml compile | head -50
fi

echo -e "\n${YELLOW}5.2 编译 xihuan-system 模块...${NC}"
if mvn -f xihuan-system/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-system 编译成功${NC}"
else
    echo -e "${RED}✗ xihuan-system 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-system/pom.xml compile | head -50
fi

echo -e "\n${YELLOW}5.3 编译 xihuan-admin 模块...${NC}"
if mvn -f xihuan-admin/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-admin 编译成功${NC}"
else
    echo -e "${RED}✗ xihuan-admin 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-admin/pom.xml compile | head -50
fi

echo -e "\n${YELLOW}6. 全项目编译验证...${NC}"
if mvn compile -q; then
    echo -e "${GREEN}✓ 全项目编译成功${NC}"
    COMPILATION_SUCCESS=true
else
    echo -e "${RED}✗ 全项目编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn compile | head -100
    COMPILATION_SUCCESS=false
fi

echo -e "\n=========================================="
echo -e "${YELLOW}最终验证结果${NC}"
echo "=========================================="

if [ $REMAINING_COUNT -eq 0 ] && [ $PSY_T_MISSING -eq 0 ] && [ "$COMPILATION_SUCCESS" = true ]; then
    echo -e "${GREEN}🎉 第一阶段完全成功！${NC}"
    echo "✓ 所有 PsyAssessment 文件已删除"
    echo "✓ 所有 PsyT 文件正常存在"
    echo "✓ 编译完全成功"
    echo ""
    echo "可以继续进行第三阶段：完善 PsyT 系列"
    exit 0
else
    echo -e "${RED}❌ 第一阶段未完全成功${NC}"
    echo "剩余问题："
    [ $REMAINING_COUNT -gt 0 ] && echo "- 还有 $REMAINING_COUNT 个 PsyAssessment 文件未删除"
    [ $PSY_T_MISSING -gt 0 ] && echo "- 缺失 $PSY_T_MISSING 个 PsyT 文件"
    [ "$COMPILATION_SUCCESS" != true ] && echo "- 编译仍有错误"
    echo ""
    echo "需要继续修复这些问题"
    exit 1
fi
