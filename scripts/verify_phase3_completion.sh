#!/bin/bash

# 第三阶段完成验证脚本
# 验证 PsyT 系列完善工作的完成情况

echo "=========================================="
echo "第三阶段完成验证脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="D:/code/XiHuan"

echo -e "\n${YELLOW}1. 清理编译缓存...${NC}"
cd "$PROJECT_ROOT"

# 清理 Maven 缓存
mvn clean -q

echo -e "\n${YELLOW}2. 检查新创建的 PsyT 系列文件...${NC}"

# 检查第三阶段新创建的文件
PHASE3_FILES=(
    # 新创建的实体类
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTInterpretation.java"
    
    # 新创建的 Mapper 接口
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTInterpretationMapper.java"
    
    # 新创建的 XML 映射文件
    "xihuan-system/src/main/resources/mapper/system/PsyTInterpretationMapper.xml"
    
    # 新创建的服务接口
    "xihuan-system/src/main/java/com/xihuan/system/service/IPsyTInterpretationService.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/IPsyTQuestionOptionService.java"
    
    # 新创建的服务实现
    "xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyTInterpretationServiceImpl.java"
    "xihuan-system/src/main/java/com/xihuan/system/service/impl/PsyTQuestionOptionServiceImpl.java"
)

CREATED_COUNT=0
MISSING_COUNT=0

for file in "${PHASE3_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ 文件已创建: $(basename $file)${NC}"
        ((CREATED_COUNT++))
    else
        echo -e "${RED}✗ 文件缺失: $file${NC}"
        ((MISSING_COUNT++))
    fi
done

echo -e "\n${BLUE}第三阶段文件统计: 已创建 $CREATED_COUNT 个文件, 缺失 $MISSING_COUNT 个文件${NC}"

echo -e "\n${YELLOW}3. 检查现有的 PsyT 系列文件...${NC}"

# 检查现有的 PsyT 系列文件
EXISTING_PSY_T_FILES=(
    # 实体类
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTScale.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTAnswerRecord.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTAssessmentRecord.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTQuestion.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTQuestionOption.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTScoringRule.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTSubscale.java"
    "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/PsyTEnterprise.java"
    
    # Mapper 接口
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTScaleMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTAnswerRecordMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTAssessmentRecordMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTQuestionMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTQuestionOptionMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTScoringRuleMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTSubscaleMapper.java"
    "xihuan-system/src/main/java/com/xihuan/system/mapper/PsyTEnterpriseMapper.java"
    
    # XML 映射文件
    "xihuan-system/src/main/resources/mapper/system/PsyTScaleMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTAnswerRecordMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTAssessmentRecordMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTQuestionMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTQuestionOptionMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTScoringRuleMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTSubscaleMapper.xml"
    "xihuan-system/src/main/resources/mapper/system/PsyTEnterpriseMapper.xml"
)

EXISTING_COUNT=0
EXISTING_MISSING=0

for file in "${EXISTING_PSY_T_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ 文件存在: $(basename $file)${NC}"
        ((EXISTING_COUNT++))
    else
        echo -e "${YELLOW}? 文件缺失: $(basename $file)${NC}"
        ((EXISTING_MISSING++))
    fi
done

echo -e "\n${BLUE}现有 PsyT 文件统计: 存在 $EXISTING_COUNT 个文件, 缺失 $EXISTING_MISSING 个文件${NC}"

echo -e "\n${YELLOW}4. 编译验证...${NC}"

echo -e "\n${YELLOW}4.1 编译 xihuan-common 模块...${NC}"
if mvn -f xihuan-common/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-common 编译成功${NC}"
    COMMON_COMPILE=true
else
    echo -e "${RED}✗ xihuan-common 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-common/pom.xml compile | head -50
    COMMON_COMPILE=false
fi

echo -e "\n${YELLOW}4.2 编译 xihuan-system 模块...${NC}"
if mvn -f xihuan-system/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-system 编译成功${NC}"
    SYSTEM_COMPILE=true
else
    echo -e "${RED}✗ xihuan-system 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-system/pom.xml compile | head -50
    SYSTEM_COMPILE=false
fi

echo -e "\n${YELLOW}4.3 编译 xihuan-admin 模块...${NC}"
if mvn -f xihuan-admin/pom.xml compile -q; then
    echo -e "${GREEN}✓ xihuan-admin 编译成功${NC}"
    ADMIN_COMPILE=true
else
    echo -e "${RED}✗ xihuan-admin 编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn -f xihuan-admin/pom.xml compile | head -50
    ADMIN_COMPILE=false
fi

echo -e "\n${YELLOW}5. 全项目编译验证...${NC}"
if mvn compile -q; then
    echo -e "${GREEN}✓ 全项目编译成功${NC}"
    FULL_COMPILE=true
else
    echo -e "${RED}✗ 全项目编译失败${NC}"
    echo -e "\n${YELLOW}详细编译错误：${NC}"
    mvn compile | head -100
    FULL_COMPILE=false
fi

echo -e "\n${YELLOW}6. 检查 PsyT 系列完整性...${NC}"

# 统计各类文件数量
ENTITY_COUNT=$(find xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/ -name "PsyT*.java" 2>/dev/null | wc -l)
MAPPER_COUNT=$(find xihuan-system/src/main/java/com/xihuan/system/mapper/ -name "PsyT*Mapper.java" 2>/dev/null | wc -l)
XML_COUNT=$(find xihuan-system/src/main/resources/mapper/system/ -name "PsyT*Mapper.xml" 2>/dev/null | wc -l)
SERVICE_COUNT=$(find xihuan-system/src/main/java/com/xihuan/system/service/ -name "IPsyT*Service.java" 2>/dev/null | wc -l)
IMPL_COUNT=$(find xihuan-system/src/main/java/com/xihuan/system/service/impl/ -name "PsyT*ServiceImpl.java" 2>/dev/null | wc -l)

echo -e "${BLUE}PsyT 系列文件统计：${NC}"
echo -e "实体类: $ENTITY_COUNT 个"
echo -e "Mapper接口: $MAPPER_COUNT 个"
echo -e "XML映射: $XML_COUNT 个"
echo -e "服务接口: $SERVICE_COUNT 个"
echo -e "服务实现: $IMPL_COUNT 个"

echo -e "\n${YELLOW}7. 验证核心功能...${NC}"

# 检查关键类是否存在
CORE_CLASSES=(
    "PsyTScale"
    "PsyTQuestion"
    "PsyTQuestionOption"
    "PsyTAnswerRecord"
    "PsyTAssessmentRecord"
    "PsyTInterpretation"
    "PsyTScoringRule"
)

CORE_MISSING=0
for class in "${CORE_CLASSES[@]}"; do
    if [ -f "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/${class}.java" ]; then
        echo -e "${GREEN}✓ 核心类存在: $class${NC}"
    else
        echo -e "${RED}✗ 核心类缺失: $class${NC}"
        ((CORE_MISSING++))
    fi
done

echo -e "\n=========================================="
echo -e "${YELLOW}第三阶段验证结果${NC}"
echo "=========================================="

# 计算总体成功率
TOTAL_SUCCESS=true

if [ $MISSING_COUNT -gt 0 ]; then
    TOTAL_SUCCESS=false
fi

if [ "$COMMON_COMPILE" != true ] || [ "$SYSTEM_COMPILE" != true ] || [ "$ADMIN_COMPILE" != true ] || [ "$FULL_COMPILE" != true ]; then
    TOTAL_SUCCESS=false
fi

if [ $CORE_MISSING -gt 0 ]; then
    TOTAL_SUCCESS=false
fi

if [ "$TOTAL_SUCCESS" = true ]; then
    echo -e "${GREEN}🎉 第三阶段完全成功！${NC}"
    echo "✓ 所有新文件已创建"
    echo "✓ 所有模块编译成功"
    echo "✓ 核心功能完整"
    echo ""
    echo "第三阶段成果："
    echo "- 创建了 $CREATED_COUNT 个新文件"
    echo "- PsyT 系列包含 $ENTITY_COUNT 个实体类"
    echo "- 支持完整的测评功能"
    echo ""
    echo "可以继续进行第四阶段：统一控制器"
    exit 0
else
    echo -e "${RED}❌ 第三阶段未完全成功${NC}"
    echo "剩余问题："
    [ $MISSING_COUNT -gt 0 ] && echo "- 还有 $MISSING_COUNT 个文件未创建"
    [ "$FULL_COMPILE" != true ] && echo "- 编译仍有错误"
    [ $CORE_MISSING -gt 0 ] && echo "- 缺失 $CORE_MISSING 个核心类"
    echo ""
    echo "需要继续修复这些问题"
    exit 1
fi
