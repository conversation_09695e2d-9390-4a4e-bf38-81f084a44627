#!/bin/bash

# 第三阶段进度检查脚本
# 检查 PsyT 系列服务的完成情况

echo "=========================================="
echo "第三阶段进度检查脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="D:/code/XiHuan"
cd "$PROJECT_ROOT"

echo -e "\n${YELLOW}1. 检查核心 PsyT 实体类...${NC}"

# 核心实体类
CORE_ENTITIES=(
    "PsyTScale"
    "PsyTQuestion"
    "PsyTQuestionOption"
    "PsyTAnswerRecord"
    "PsyTAssessmentRecord"
    "PsyTInterpretation"
    "PsyTScoringRule"
    "PsyTSubscale"
    "PsyTEnterprise"
)

ENTITY_EXISTS=0
ENTITY_MISSING=0

for entity in "${CORE_ENTITIES[@]}"; do
    if [ -f "xihuan-common/src/main/java/com/xihuan/common/core/domain/entity/${entity}.java" ]; then
        echo -e "${GREEN}✓ 实体类存在: $entity${NC}"
        ((ENTITY_EXISTS++))
    else
        echo -e "${RED}✗ 实体类缺失: $entity${NC}"
        ((ENTITY_MISSING++))
    fi
done

echo -e "\n${BLUE}实体类统计: 存在 $ENTITY_EXISTS 个, 缺失 $ENTITY_MISSING 个${NC}"

echo -e "\n${YELLOW}2. 检查核心 PsyT 服务接口...${NC}"

# 核心服务接口
CORE_SERVICES=(
    "IPsyTScaleService"
    "IPsyTQuestionService"
    "IPsyTQuestionOptionService"
    "IPsyTAnswerRecordService"
    "IPsyTAssessmentRecordService"
    "IPsyTInterpretationService"
    "IPsyTScoringRuleService"
    "IPsyTSubscaleService"
    "IPsyTEnterpriseService"
)

SERVICE_EXISTS=0
SERVICE_MISSING=0

for service in "${CORE_SERVICES[@]}"; do
    if [ -f "xihuan-system/src/main/java/com/xihuan/system/service/${service}.java" ]; then
        echo -e "${GREEN}✓ 服务接口存在: $service${NC}"
        ((SERVICE_EXISTS++))
    else
        echo -e "${RED}✗ 服务接口缺失: $service${NC}"
        ((SERVICE_MISSING++))
    fi
done

echo -e "\n${BLUE}服务接口统计: 存在 $SERVICE_EXISTS 个, 缺失 $SERVICE_MISSING 个${NC}"

echo -e "\n${YELLOW}3. 检查核心 PsyT 服务实现...${NC}"

# 核心服务实现
CORE_IMPLS=(
    "PsyTScaleServiceImpl"
    "PsyTQuestionServiceImpl"
    "PsyTQuestionOptionServiceImpl"
    "PsyTAnswerRecordServiceImpl"
    "PsyTAssessmentRecordServiceImpl"
    "PsyTInterpretationServiceImpl"
    "PsyTScoringRuleServiceImpl"
    "PsyTSubscaleServiceImpl"
    "PsyTEnterpriseServiceImpl"
)

IMPL_EXISTS=0
IMPL_MISSING=0

for impl in "${CORE_IMPLS[@]}"; do
    if [ -f "xihuan-system/src/main/java/com/xihuan/system/service/impl/${impl}.java" ]; then
        echo -e "${GREEN}✓ 服务实现存在: $impl${NC}"
        ((IMPL_EXISTS++))
    else
        echo -e "${RED}✗ 服务实现缺失: $impl${NC}"
        ((IMPL_MISSING++))
    fi
done

echo -e "\n${BLUE}服务实现统计: 存在 $IMPL_EXISTS 个, 缺失 $IMPL_MISSING 个${NC}"

echo -e "\n${YELLOW}4. 检查核心 PsyT Mapper 接口...${NC}"

# 核心 Mapper 接口
CORE_MAPPERS=(
    "PsyTScaleMapper"
    "PsyTQuestionMapper"
    "PsyTQuestionOptionMapper"
    "PsyTAnswerRecordMapper"
    "PsyTAssessmentRecordMapper"
    "PsyTInterpretationMapper"
    "PsyTScoringRuleMapper"
    "PsyTSubscaleMapper"
    "PsyTEnterpriseMapper"
)

MAPPER_EXISTS=0
MAPPER_MISSING=0

for mapper in "${CORE_MAPPERS[@]}"; do
    if [ -f "xihuan-system/src/main/java/com/xihuan/system/mapper/${mapper}.java" ]; then
        echo -e "${GREEN}✓ Mapper接口存在: $mapper${NC}"
        ((MAPPER_EXISTS++))
    else
        echo -e "${RED}✗ Mapper接口缺失: $mapper${NC}"
        ((MAPPER_MISSING++))
    fi
done

echo -e "\n${BLUE}Mapper接口统计: 存在 $MAPPER_EXISTS 个, 缺失 $MAPPER_MISSING 个${NC}"

echo -e "\n${YELLOW}5. 检查核心 PsyT XML 映射文件...${NC}"

# 核心 XML 映射文件
CORE_XMLS=(
    "PsyTScaleMapper"
    "PsyTQuestionMapper"
    "PsyTQuestionOptionMapper"
    "PsyTAnswerRecordMapper"
    "PsyTAssessmentRecordMapper"
    "PsyTInterpretationMapper"
    "PsyTScoringRuleMapper"
    "PsyTSubscaleMapper"
    "PsyTEnterpriseMapper"
)

XML_EXISTS=0
XML_MISSING=0

for xml in "${CORE_XMLS[@]}"; do
    if [ -f "xihuan-system/src/main/resources/mapper/system/${xml}.xml" ]; then
        echo -e "${GREEN}✓ XML映射存在: ${xml}.xml${NC}"
        ((XML_EXISTS++))
    else
        echo -e "${RED}✗ XML映射缺失: ${xml}.xml${NC}"
        ((XML_MISSING++))
    fi
done

echo -e "\n${BLUE}XML映射统计: 存在 $XML_EXISTS 个, 缺失 $XML_MISSING 个${NC}"

echo -e "\n${YELLOW}6. 计算第三阶段完成度...${NC}"

# 计算总体完成度
TOTAL_COMPONENTS=45  # 9个组件 × 5种类型
COMPLETED_COMPONENTS=$((ENTITY_EXISTS + SERVICE_EXISTS + IMPL_EXISTS + MAPPER_EXISTS + XML_EXISTS))
MISSING_COMPONENTS=$((ENTITY_MISSING + SERVICE_MISSING + IMPL_MISSING + MAPPER_MISSING + XML_MISSING))

COMPLETION_RATE=$((COMPLETED_COMPONENTS * 100 / TOTAL_COMPONENTS))

echo -e "\n=========================================="
echo -e "${YELLOW}第三阶段完成度统计${NC}"
echo "=========================================="

echo -e "${BLUE}组件完成情况：${NC}"
echo -e "实体类: $ENTITY_EXISTS/9 ($(($ENTITY_EXISTS * 100 / 9))%)"
echo -e "服务接口: $SERVICE_EXISTS/9 ($(($SERVICE_EXISTS * 100 / 9))%)"
echo -e "服务实现: $IMPL_EXISTS/9 ($(($IMPL_EXISTS * 100 / 9))%)"
echo -e "Mapper接口: $MAPPER_EXISTS/9 ($(($MAPPER_EXISTS * 100 / 9))%)"
echo -e "XML映射: $XML_EXISTS/9 ($(($XML_EXISTS * 100 / 9))%)"

echo -e "\n${BLUE}总体完成度: $COMPLETED_COMPONENTS/$TOTAL_COMPONENTS ($COMPLETION_RATE%)${NC}"

if [ $COMPLETION_RATE -ge 90 ]; then
    echo -e "${GREEN}🎉 第三阶段接近完成！${NC}"
elif [ $COMPLETION_RATE -ge 70 ]; then
    echo -e "${YELLOW}⚡ 第三阶段进展良好！${NC}"
elif [ $COMPLETION_RATE -ge 50 ]; then
    echo -e "${YELLOW}🔄 第三阶段进行中...${NC}"
else
    echo -e "${RED}🚧 第三阶段刚开始...${NC}"
fi

echo -e "\n${YELLOW}7. 缺失组件清单...${NC}"

if [ $SERVICE_MISSING -gt 0 ]; then
    echo -e "${RED}缺失的服务接口：${NC}"
    for service in "${CORE_SERVICES[@]}"; do
        if [ ! -f "xihuan-system/src/main/java/com/xihuan/system/service/${service}.java" ]; then
            echo "  - $service"
        fi
    done
fi

if [ $IMPL_MISSING -gt 0 ]; then
    echo -e "${RED}缺失的服务实现：${NC}"
    for impl in "${CORE_IMPLS[@]}"; do
        if [ ! -f "xihuan-system/src/main/java/com/xihuan/system/service/impl/${impl}.java" ]; then
            echo "  - $impl"
        fi
    done
fi

if [ $MAPPER_MISSING -gt 0 ]; then
    echo -e "${RED}缺失的Mapper接口：${NC}"
    for mapper in "${CORE_MAPPERS[@]}"; do
        if [ ! -f "xihuan-system/src/main/java/com/xihuan/system/mapper/${mapper}.java" ]; then
            echo "  - $mapper"
        fi
    done
fi

if [ $XML_MISSING -gt 0 ]; then
    echo -e "${RED}缺失的XML映射：${NC}"
    for xml in "${CORE_XMLS[@]}"; do
        if [ ! -f "xihuan-system/src/main/resources/mapper/system/${xml}.xml" ]; then
            echo "  - ${xml}.xml"
        fi
    done
fi

echo -e "\n${YELLOW}8. 下一步建议...${NC}"

if [ $SERVICE_MISSING -gt 0 ]; then
    echo "1. 创建缺失的服务接口"
fi

if [ $IMPL_MISSING -gt 0 ]; then
    echo "2. 创建缺失的服务实现类"
fi

if [ $MAPPER_MISSING -gt 0 ]; then
    echo "3. 创建缺失的Mapper接口"
fi

if [ $XML_MISSING -gt 0 ]; then
    echo "4. 创建缺失的XML映射文件"
fi

if [ $MISSING_COMPONENTS -eq 0 ]; then
    echo -e "${GREEN}✅ 所有组件已完成，可以进行编译测试！${NC}"
else
    echo -e "${YELLOW}📝 还需要创建 $MISSING_COMPONENTS 个组件${NC}"
fi

echo -e "\n=========================================="
exit 0
