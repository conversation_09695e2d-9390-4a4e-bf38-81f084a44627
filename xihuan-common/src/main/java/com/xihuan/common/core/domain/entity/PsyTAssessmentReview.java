package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * 测评评价表 psy_t_assessment_review
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTAssessmentReview extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    @Excel(name = "评价ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 测评记录ID */
    @Excel(name = "测评记录ID", cellType = Excel.ColumnType.NUMERIC)
    private Long recordId;

    /** 订单ID */
    @Excel(name = "订单ID", cellType = Excel.ColumnType.NUMERIC)
    private Long orderId;

    /** 评分(1-5星) */
    @Excel(name = "评分")
    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分不能小于1")
    @Max(value = 5, message = "评分不能大于5")
    private Integer rating;

    /** 评价内容 */
    @Excel(name = "评价内容")
    @Size(max = 1000, message = "评价内容不能超过1000个字符")
    private String content;

    /** 是否匿名(0否 1是) */
    @Excel(name = "是否匿名", readConverterExp = "0=否,1=是")
    private Integer isAnonymous;

    /** 审核状态(0待审核 1审核通过 2审核不通过) */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=审核通过,2=审核不通过")
    private Integer status;

    /** 审核意见 */
    @Excel(name = "审核意见")
    @Size(max = 500, message = "审核意见不能超过500个字符")
    private String auditRemark;

    /** 审核时间 */
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核人 */
    @Excel(name = "审核人")
    @Size(max = 50, message = "审核人不能超过50个字符")
    private String auditBy;

    /** 点赞数 */
    @Excel(name = "点赞数")
    private Integer likeCount;

    /** 回复数 */
    @Excel(name = "回复数")
    private Integer replyCount;

    /** 是否置顶(0否 1是) */
    @Excel(name = "是否置顶", readConverterExp = "0=否,1=是")
    private Integer isTop;

    /** 置顶时间 */
    @Excel(name = "置顶时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date topTime;

    /** 删除标志(0正常 1删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    /** 用户信息 */
    private SysUser user;

    /** 测评记录 */
    private PsyTAssessmentRecord record;

    /** 订单信息 */
    private PsyTAssessmentOrder order;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 量表编码 */
    private String scaleCode;

    /** 用户昵称 */
    private String nickName;

    /** 用户头像 */
    private String avatar;

    /** 显示用户名（匿名时显示匿名用户） */
    private String displayName;

    /** 是否已点赞 */
    private Boolean liked;

    /** 评分星级显示 */
    private String ratingStars;

    /** 审核状态描述 */
    private String statusDesc;

    /** 评价时间描述 */
    private String createTimeDesc;

    // 常量定义
    /** 匿名：否 */
    public static final Integer ANONYMOUS_NO = 0;
    
    /** 匿名：是 */
    public static final Integer ANONYMOUS_YES = 1;

    /** 审核状态：待审核 */
    public static final Integer STATUS_PENDING = 0;
    
    /** 审核状态：审核通过 */
    public static final Integer STATUS_APPROVED = 1;
    
    /** 审核状态：审核不通过 */
    public static final Integer STATUS_REJECTED = 2;

    /** 置顶：否 */
    public static final Integer TOP_NO = 0;
    
    /** 置顶：是 */
    public static final Integer TOP_YES = 1;

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取审核状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "待审核";
            case 1: return "审核通过";
            case 2: return "审核不通过";
            default: return "未知";
        }
    }

    /**
     * 获取评分星级显示
     */
    public String getRatingStars() {
        if (rating == null) return "";
        StringBuilder stars = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            if (i < rating) {
                stars.append("★");
            } else {
                stars.append("☆");
            }
        }
        return stars.toString();
    }

    /**
     * 获取显示用户名
     */
    public String getDisplayName() {
        if (isAnonymous != null && isAnonymous == 1) {
            return "匿名用户";
        }
        return nickName != null ? nickName : "用户" + userId;
    }

    /**
     * 是否匿名评价
     */
    public boolean isAnonymous() {
        return isAnonymous != null && isAnonymous == 1;
    }

    /**
     * 是否已审核通过
     */
    public boolean isApproved() {
        return status != null && status == 1;
    }

    /**
     * 是否置顶
     */
    public boolean isTop() {
        return isTop != null && isTop == 1;
    }

    /**
     * 是否可以编辑
     */
    public boolean canEdit() {
        return status == null || status == 0; // 待审核状态可以编辑
    }

    /**
     * 是否可以删除
     */
    public boolean canDelete() {
        return status == null || status != 1; // 非审核通过状态可以删除
    }
}
