package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 时间段定义表实体类
 * 对应数据库表：psy_time_range
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTimeRange extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 时间段ID */
    private Long id;

    /** 时间段名称（上午/下午/晚上） */
    private String name;

    /** 图标URL */
    private String iconUrl;

    /** 开始小时（0-23） */
    private Integer startHour;

    /** 结束小时（0-23） */
    private Integer endHour;

    /** 删除标志 */
    private Integer delFlag;
}
