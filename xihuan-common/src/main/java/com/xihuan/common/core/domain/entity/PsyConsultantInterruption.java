package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 咨询中断记录表 psy_consultant_interruption
 * 
 * <AUTHOR>
 */
@Data
public class PsyConsultantInterruption {
    private static final long serialVersionUID = 1L;

    /** 中断ID */
    private Long id;

    /** 咨询记录ID */
    @Excel(name = "咨询记录ID")
    private Long recordId;

    /** 中断类型 */
    @Excel(name = "中断类型")
    private String interruptType;

    /** 中断原因 */
    @Excel(name = "中断原因")
    private String reason;

    /** 中断时长(分钟) */
    @Excel(name = "中断时长(分钟)")
    private Integer duration;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 关联对象
    /** 咨询记录信息 */
    private PsyConsultationRecord record;

    // 扩展字段
    /** 中断类型描述 */
    private String interruptTypeDesc;

    /** 是否可以恢复 */
    private Boolean canRestore;
}
