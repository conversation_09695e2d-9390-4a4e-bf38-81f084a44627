package com.xihuan.common.core.domain.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 预约请求数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class PsyAppointmentRequestDTO {
    
    /** 用户ID */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /** 咨询中心ID */
    @NotNull(message = "咨询中心ID不能为空")
    private Long centerId;
    
    /** 起始时间槽ID */
    @NotNull(message = "起始时间槽ID不能为空")
    private Long startSlotId;
    
    /** 预约的时间槽ID列表（支持连续多个15分钟时段） */
    @NotEmpty(message = "时间槽列表不能为空")
    private List<Long> slotIds;
    
    /** 咨询时长（分钟） */
    private Integer duration;
    
    /** 预约备注 */
    private String remark;
    
    /** 支付方式 */
    private String paymentMethod;
    
    /** 产品ID（如果通过产品预约） */
    private Long productId;
}
