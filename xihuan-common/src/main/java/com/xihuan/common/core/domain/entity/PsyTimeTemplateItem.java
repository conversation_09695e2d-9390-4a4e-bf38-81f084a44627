package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalTime;

/**
 * 模板时间段详情表实体类
 * 对应数据库表：psy_time_template_item
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTimeTemplateItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 明细ID */
    private Long id;

    /** 模板ID */
    private Long templateId;

    /** 星期几(1-7) */
    private Integer dayOfWeek;

    /** 开始时间 */
    private LocalTime startTime;

    /** 结束时间 */
    private LocalTime endTime;

    /** 咨询中心ID */
    private Long centerId;

    /** 删除标志 */
    private Integer delFlag;

    // 关联对象（非数据库字段）
    /** 排班模板信息 */
    private PsyTimeScheduleTemplate template;
}
