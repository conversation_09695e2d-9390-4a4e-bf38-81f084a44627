package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;
import java.util.Date;

@Data
public class PsyMessageConversation {
    private static final long serialVersionUID = 1L;

    @Excel(name = "会话ID")
    private Long conversationId;

    @Excel(name = "用户ID")
    private Long userId;

    @Excel(name = "咨询师ID")
    private Long consultantId;

    @Excel(name = "最后消息发送者ID")
    private Long lastSenderId;

    @Excel(name = "最后消息摘要")
    private String lastMessage;

    @Excel(name = "最后消息时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastMessageTime;

    @Excel(name = "未读数")
    private Integer userUnreadCount;

    @Excel(name="咨询师未读数")
    private Integer consultantUnreadCount;
    
    /** 用户昵称 */
    private String userName;
    
    /** 用户头像 */
    private String userAvatar;
    
    /** 咨询师昵称 */
    private String counselorName;
    
    /** 咨询师头像 */
    private String consultantAvatar;
}