package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalTime;

/**
 * 系统公共时间槽实体类
 * 用于小程序展示，聚合所有咨询师的可用时间
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsySystemTimeSlot extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 系统时间槽ID */
    private Long id;

    /** 咨询中心ID */
    private Long centerId;

    /** 日期KEY（YYYY-MM-DD） */
    private String dateKey;

    /** 星期几 */
    private String weekDay;

    /** 所属时间段ID */
    private Long rangeId;

    /** 开始时间 */
    private LocalTime startTime;

    /** 结束时间 */
    private LocalTime endTime;

    /** 可用咨询师数量 */
    private Integer availableCounselors;

    /** 总咨询师数量 */
    private Integer totalCounselors;

    /** 是否有可用咨询师 */
    private Boolean hasAvailable;

    /** 状态（0-可用，2-已过期） */
    private Integer status;

    /** 删除标志 */
    private Integer delFlag;

    // 关联对象（非数据库字段）
    /** 时间段信息 */
    private PsyTimeRange timeRange;

    /** 可用咨询师列表 */
    private java.util.List<com.xihuan.common.core.domain.consultant.PsyConsultant> availableCounselorList;

    // Getter and Setter methods

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public String getDateKey() {
        return dateKey;
    }

    public void setDateKey(String dateKey) {
        this.dateKey = dateKey;
    }

    public String getWeekDay() {
        return weekDay;
    }

    public void setWeekDay(String weekDay) {
        this.weekDay = weekDay;
    }

    public Long getRangeId() {
        return rangeId;
    }

    public void setRangeId(Long rangeId) {
        this.rangeId = rangeId;
    }

    public java.time.LocalTime getStartTime() {
        return startTime;
    }

    public void setStartTime(java.time.LocalTime startTime) {
        this.startTime = startTime;
    }

    public java.time.LocalTime getEndTime() {
        return endTime;
    }

    public void setEndTime(java.time.LocalTime endTime) {
        this.endTime = endTime;
    }

    public Integer getAvailableCounselors() {
        return availableCounselors;
    }

    public void setAvailableCounselors(Integer availableCounselors) {
        this.availableCounselors = availableCounselors;
    }

    public Integer getTotalCounselors() {
        return totalCounselors;
    }

    public void setTotalCounselors(Integer totalCounselors) {
        this.totalCounselors = totalCounselors;
    }

    public Boolean getHasAvailable() {
        return hasAvailable;
    }

    public void setHasAvailable(Boolean hasAvailable) {
        this.hasAvailable = hasAvailable;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public PsyTimeRange getTimeRange() {
        return timeRange;
    }

    public void setTimeRange(PsyTimeRange timeRange) {
        this.timeRange = timeRange;
    }

    public java.util.List<com.xihuan.common.core.domain.consultant.PsyConsultant> getAvailableCounselorList() {
        return availableCounselorList;
    }

    public void setAvailableCounselorList(java.util.List<com.xihuan.common.core.domain.consultant.PsyConsultant> availableCounselorList) {
        this.availableCounselorList = availableCounselorList;
    }
}
