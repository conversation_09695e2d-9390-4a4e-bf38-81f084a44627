package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 心理咨询课程主表对象 psy_course
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyCourse extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 课程ID */
    @Excel(name = "课程ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String title;

    /** 课程简介 */
    @Excel(name = "课程简介")
    private String summary;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal price;

    /** 已售数量 */
    @Excel(name = "已售数量")
    private Integer salesCount;

    /** 章节数量 */
    @Excel(name = "章节数量")
    private Integer chapterCount;

    /** 试听章节数量 */
    @Excel(name = "试听章节数量")
    private Integer trialChapterCount;

    /** 讲师ID */
    @Excel(name = "讲师ID", cellType = Excel.ColumnType.NUMERIC)
    private Long instructorId;

    /** 课程封面图 */
    @Excel(name = "课程封面图")
    private String coverImage;

    /** 难度等级（1=入门 2=进阶 3=高级） */
    @Excel(name = "难度等级", readConverterExp = "1=入门,2=进阶,3=高级")
    private Integer difficultyLevel;

    /** 课程总时长（秒） */
    @Excel(name = "课程总时长")
    private Integer durationTotal;

    /** 观看次数 */
    @Excel(name = "观看次数")
    private Integer viewCount;

    /** 平均评分 */
    @Excel(name = "平均评分")
    private BigDecimal ratingAvg;

    /** 评分人数 */
    @Excel(name = "评分人数")
    private Integer ratingCount;

    /** 是否免费（0=付费 1=免费） */
    @Excel(name = "是否免费", readConverterExp = "0=付费,1=免费")
    private Integer isFree;

    /** 课程标签（JSON格式） */
    @Excel(name = "课程标签")
    private String tags;

    /** 课程状态（0:未发布 1:已发布 2:下架） */
    @Excel(name = "课程状态", readConverterExp = "0=未发布,1=已发布,2=下架")
    private Integer status;

    /** 删除标志（0:正常 1:删除） */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    /** 搜索关键词 */
    @Excel(name = "搜索关键词")
    private String searchKeywords;

    /** 被搜索次数 */
    @Excel(name = "被搜索次数")
    private Integer searchCount;

    // 关联对象
    /** 讲师信息 */
    private PsyCourseInstructor instructor;

    /** 课程章节列表 */
    private List<PsyCourseChapter> chapters;

    /** 分类ID列表（用于多分类关系） */
    private List<Long> categoryIds;

    /** 分类列表（用于多分类关系） */
    private List<PsyCategory> categories;

    /** 课程图片资源列表 */
    private List<PsyCourseImageResource> images;

    /** 用户是否已购买 */
    private Boolean purchased;

    /** 用户学习进度 */
    private BigDecimal userProgress;
}
