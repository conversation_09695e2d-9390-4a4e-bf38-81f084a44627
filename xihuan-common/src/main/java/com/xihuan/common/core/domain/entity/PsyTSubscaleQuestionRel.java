package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 分量表题目关系表 psy_t_subscale_question_rel
 * 
 * <AUTHOR>
 */
@Data
public class PsyTSubscaleQuestionRel {
    private static final long serialVersionUID = 1L;

    /** 分量表ID */
    @Excel(name = "分量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "分量表ID不能为空")
    private Long subscaleId;

    /** 题目ID */
    @Excel(name = "题目ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /** 权重 */
    @Excel(name = "权重")
    @DecimalMin(value = "0.01", message = "权重不能小于0.01")
    @DecimalMax(value = "10.00", message = "权重不能大于10.00")
    private BigDecimal weight;

    // 关联对象
    /** 分量表信息 */
    private PsyTSubscale subscale;

    /** 题目信息 */
    private PsyTQuestion question;

    // 扩展字段
    /** 分量表名称 */
    private String subscaleName;

    /** 题目内容 */
    private String questionContent;

    /** 题目序号 */
    private Integer questionNo;

    /** 量表ID */
    private Long scaleId;

    /** 量表名称 */
    private String scaleName;

    // 常量定义
    /** 默认权重 */
    public static final BigDecimal DEFAULT_WEIGHT = new BigDecimal("1.00");

    /**
     * 获取权重描述
     */
    public String getWeightDesc() {
        if (weight == null) return "1.00";
        return weight.toString();
    }

    /**
     * 是否默认权重
     */
    public boolean isDefaultWeight() {
        return DEFAULT_WEIGHT.equals(weight);
    }

    /**
     * 计算加权分数
     */
    public BigDecimal calculateWeightedScore(Integer score) {
        if (score == null || weight == null) return BigDecimal.ZERO;
        return new BigDecimal(score).multiply(weight);
    }
}
