package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 题目表 psy_t_question
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTQuestion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 题目唯一ID */
    @Excel(name = "题目ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 关联psy_t_scale.id */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 题号(从1开始) */
    @Excel(name = "题号")
    @NotNull(message = "题号不能为空")
    @Min(value = 1, message = "题号不能小于1")
    private Integer questionNo;

    /** 题目内容 */
    @Excel(name = "题目内容")
    @NotBlank(message = "题目内容不能为空")
    private String content;

    /** 题目文本 */
    @Excel(name = "题目文本")
    private String questionText;

    /** 题目类型 */
    @Excel(name = "题目类型", readConverterExp = "SINGLE=单选,MULTIPLE=多选,TEXT=文本,COMPOSITE=复合")
    private String questionType;

    /** 是否反向计分 */
    @Excel(name = "是否反向计分", readConverterExp = "0=否,1=是")
    private Integer isReverse;

    /** 反向计分值 */
    @Excel(name = "反向计分值")
    private Integer reverseValue;

    /** 选项配置(JSON格式) */
    @Excel(name = "选项配置")
    private String options;

    /** 分量表关联标识 */
    @Excel(name = "分量表关联标识")
    @Size(max = 50, message = "分量表关联标识不能超过50个字符")
    private String subscaleRef;

    /** 分量表ID */
    @Excel(name = "分量表ID", cellType = Excel.ColumnType.NUMERIC)
    private Long subscaleId;

    /** 是否必答 */
    @Excel(name = "是否必答", readConverterExp = "0=否,1=是")
    private Integer isRequired;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer orderNum;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private Integer status;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    /** 选项列表 */
    private List<PsyTQuestionOption> optionList;

    /** 复合题子项列表 */
    private List<PsyTCompositeQuestion> compositeQuestions;

    /** 用户答案 */
    private PsyTAnswerRecord userAnswer;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 量表编码 */
    private String scaleCode;

    /** 是否已答题 */
    private Boolean answered;

    /** 答题内容 */
    private String answerContent;

    /** 答题得分 */
    private Integer answerScore;

    /** 答题耗时 */
    private Integer responseTime;

    // 常量定义
    /** 题目类型：单选 */
    public static final String QUESTION_TYPE_SINGLE = "SINGLE";
    
    /** 题目类型：多选 */
    public static final String QUESTION_TYPE_MULTIPLE = "MULTIPLE";
    
    /** 题目类型：文本 */
    public static final String QUESTION_TYPE_TEXT = "TEXT";
    
    /** 题目类型：复合 */
    public static final String QUESTION_TYPE_COMPOSITE = "COMPOSITE";

    /** 是否反向计分：否 */
    public static final Integer IS_REVERSE_NO = 0;
    
    /** 是否反向计分：是 */
    public static final Integer IS_REVERSE_YES = 1;

    /** 是否必答：否 */
    public static final Integer IS_REQUIRED_NO = 0;
    
    /** 是否必答：是 */
    public static final Integer IS_REQUIRED_YES = 1;

    /** 状态：停用 */
    public static final Integer STATUS_DISABLED = 0;
    
    /** 状态：启用 */
    public static final Integer STATUS_ENABLED = 1;

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取题目类型描述
     */
    public String getQuestionTypeDesc() {
        if (questionType == null) return "";
        switch (questionType) {
            case "SINGLE": return "单选";
            case "MULTIPLE": return "多选";
            case "TEXT": return "文本";
            case "COMPOSITE": return "复合";
            default: return "未知";
        }
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "停用";
            case 1: return "启用";
            default: return "未知";
        }
    }

    /**
     * 是否反向计分
     */
    public boolean isReverseScore() {
        return IS_REVERSE_YES.equals(isReverse);
    }

    /**
     * 是否必答
     */
    public boolean isRequiredQuestion() {
        return IS_REQUIRED_YES.equals(isRequired);
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return STATUS_ENABLED.equals(status);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 是否单选题
     */
    public boolean isSingleChoice() {
        return QUESTION_TYPE_SINGLE.equals(questionType);
    }

    /**
     * 是否多选题
     */
    public boolean isMultipleChoice() {
        return QUESTION_TYPE_MULTIPLE.equals(questionType);
    }

    /**
     * 是否文本题
     */
    public boolean isTextQuestion() {
        return QUESTION_TYPE_TEXT.equals(questionType);
    }

    /**
     * 是否复合题
     */
    public boolean isCompositeQuestion() {
        return QUESTION_TYPE_COMPOSITE.equals(questionType);
    }
}
