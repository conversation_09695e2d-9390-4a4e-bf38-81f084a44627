package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程章节内容表对象 psy_course_chapter
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyCourseChapter extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 章节ID */
    @Excel(name = "章节ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 课程ID */
    @Excel(name = "课程ID", cellType = Excel.ColumnType.NUMERIC)
    private Long courseId;

    /** 父章节ID（0表示根章节） */
    @Excel(name = "父章节ID", cellType = Excel.ColumnType.NUMERIC)
    private Long parentId;

    /** 章节层级（1=一级，2=二级） */
    @Excel(name = "章节层级", readConverterExp = "1=一级,2=二级")
    private Integer level;

    /** 章节标题 */
    @Excel(name = "章节标题")
    private String chapterTitle;

    /** 章节详细内容 */
    @Excel(name = "章节详细内容")
    private String chapterContent;

    /** 内容类型（0:视频 1:音频 2:文档） */
    @Excel(name = "内容类型", readConverterExp = "0=视频,1=音频,2=文档")
    private Integer contentType;

    /** 时长（秒） */
    @Excel(name = "时长（秒）")
    private Integer duration;

    /** 章节排序（同一层级内排序） */
    @Excel(name = "章节排序")
    private Integer chapterOrder;

    /** 是否试听章节（0=否 1=是） */
    @Excel(name = "是否试听章节", readConverterExp = "0=否,1=是")
    private Integer isTrial;

    /** 媒体文件URL（视频/音频/文档地址） */
    @Excel(name = "媒体文件URL")
    private String mediaUrl;

    /** 媒体文件名称 */
    @Excel(name = "媒体文件名称")
    private String mediaFileName;

    /** 媒体文件大小（字节） */
    @Excel(name = "媒体文件大小")
    private Long mediaFileSize;

    /** 删除标志（0:正常 1:删除） */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    /** 子章节列表 */
    private List<PsyCourseChapter> children = new ArrayList<>();

    /** 课程信息 */
    private PsyCourse course;
}
