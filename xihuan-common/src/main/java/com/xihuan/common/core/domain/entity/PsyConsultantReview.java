package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import com.xihuan.common.core.domain.consultant.PsyConsultant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 咨询师评价表 psy_consultant_review
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyConsultantReview extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    private Long id;

    /** 咨询师ID */
    @Excel(name = "咨询师ID")
    private Long consultantId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 关联咨询记录ID */
    @Excel(name = "关联咨询记录ID")
    private Long recordId;

    /** 综合评分(0.0-5.0) */
    @Excel(name = "综合评分")
    private BigDecimal rating;

    /** 评价内容 */
    @Excel(name = "评价内容")
    private String content;

    /** 是否匿名评价(用户隐私保护) */
    @Excel(name = "是否匿名评价", readConverterExp = "0=否,1=是")
    private Integer isAnonymous;

    /** 基于哪种咨询类型的评价 */
    @Excel(name = "咨询类型")
    private String consultType;

    /** 评价时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "评价时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 管理员审核状态(0未审 1通过 2不通过) */
    @Excel(name = "审核状态", readConverterExp = "0=未审核,1=审核通过,2=审核不通过")
    private Integer adminCheck;

    /** 咨询师回复内容 */
    @Excel(name = "咨询师回复内容")
    private String consultantReply;

    /** 删除标志(0正常 2删除) */
    private String delFlag;

    // 关联对象
    /** 用户信息 */
    private SysUser user;

    /** 咨询师信息 */
    private PsyConsultant consultant;

    /** 咨询记录信息 */
    private PsyConsultationRecord record;

    // 扩展字段
    /** 用户昵称（匿名时显示） */
    private String displayName;

    /** 是否可以回复 */
    private Boolean canReply;

    /** 回复时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date replyTime;
}
