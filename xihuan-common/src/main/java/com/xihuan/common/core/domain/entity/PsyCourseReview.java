package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程评价表对象 psy_course_review
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyCourseReview extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    @Excel(name = "评价ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 课程ID */
    @Excel(name = "课程ID", cellType = Excel.ColumnType.NUMERIC)
    private Long courseId;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    private Long userId;

    /** 评价内容 */
    @Excel(name = "评价内容")
    private String content;

    /** 评分（1-5星） */
    @Excel(name = "评分", readConverterExp = "1=1星,2=2星,3=3星,4=4星,5=5星")
    private Integer rating;

    /** 删除标志（0:正常 1:删除） */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    /** 关联的课程信息 */
    private PsyCourse course;

    /** 关联的用户信息 */
    private SysUser user;
}
