package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图片资源表对象 psy_course_image_resource
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyCourseImageResource extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 图片ID */
    @Excel(name = "图片ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 关联资源ID */
    @Excel(name = "关联资源ID", cellType = Excel.ColumnType.NUMERIC)
    private Long resourceId;

    /** 资源类型（1:课程头图 2:讲师头像 3:课程封面） */
    @Excel(name = "资源类型", readConverterExp = "1=课程头图,2=讲师头像,3=课程封面")
    private Integer resourceType;

    /** 图片URL */
    @Excel(name = "图片URL")
    private String imageUrl;

    /** 图片排序（小的在前） */
    @Excel(name = "图片排序")
    private Integer imageOrder;

    /** 是否主图 */
    @Excel(name = "是否主图", readConverterExp = "0=否,1=是")
    private Integer isMain;

    /** 删除标志（0:正常 1:删除） */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;
}
