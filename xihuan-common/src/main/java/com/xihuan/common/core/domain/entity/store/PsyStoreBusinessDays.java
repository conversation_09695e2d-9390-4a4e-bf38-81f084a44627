package com.xihuan.common.core.domain.entity.store;

import com.xihuan.common.core.domain.BaseEntity;
import com.xihuan.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店营业日配置对象 psy_store_business_days
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyStoreBusinessDays extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long storeId;

    /** 周一是否营业：1是 0否 */
    @Excel(name = "周一是否营业", readConverterExp = "1=是,0=否")
    private Integer monday;

    /** 周二是否营业：1是 0否 */
    @Excel(name = "周二是否营业", readConverterExp = "1=是,0=否")
    private Integer tuesday;

    /** 周三是否营业：1是 0否 */
    @Excel(name = "周三是否营业", readConverterExp = "1=是,0=否")
    private Integer wednesday;

    /** 周四是否营业：1是 0否 */
    @Excel(name = "周四是否营业", readConverterExp = "1=是,0=否")
    private Integer thursday;

    /** 周五是否营业：1是 0否 */
    @Excel(name = "周五是否营业", readConverterExp = "1=是,0=否")
    private Integer friday;

    /** 周六是否营业：1是 0否 */
    @Excel(name = "周六是否营业", readConverterExp = "1=是,0=否")
    private Integer saturday;

    /** 周日是否营业：1是 0否 */
    @Excel(name = "周日是否营业", readConverterExp = "1=是,0=否")
    private Integer sunday;
} 