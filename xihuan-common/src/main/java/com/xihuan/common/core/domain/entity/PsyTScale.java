package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 量表基础信息表 psy_t_scale
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTScale extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 量表唯一ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 量表名称 */
    @Excel(name = "量表名称")
    @NotBlank(message = "量表名称不能为空")
    @Size(max = 100, message = "量表名称不能超过100个字符")
    private String name;

    /** 量表编码 */
    @Excel(name = "量表编码")
    @NotBlank(message = "量表编码不能为空")
    @Size(max = 50, message = "量表编码不能超过50个字符")
    private String code;

    /** 分类ID */
    @Excel(name = "分类ID", cellType = Excel.ColumnType.NUMERIC)
    private Integer categoryId;

    /** 量表描述 */
    @Excel(name = "量表描述")
    private String description;

    /** 量表介绍 */
    @Excel(name = "量表介绍")
    private String introduction;

    /** 测评须知 */
    @Excel(name = "测评须知")
    private String testNotice;

    /** 测评目的 */
    @Excel(name = "测评目的")
    private String testPurpose;

    /** 测评对象 */
    @Excel(name = "测评对象")
    private String testObject;

    /** 测评准备 */
    @Excel(name = "测评准备")
    private String testPreparation;

    /** 测评后的处理 */
    @Excel(name = "测评后的处理")
    private String testProcessing;

    /** 注意事项 */
    @Excel(name = "注意事项")
    private String testAttention;

    /** 测评基础理论 */
    @Excel(name = "测评基础理论")
    private String testTheory;

    /** 测评应用 */
    @Excel(name = "测评应用")
    private String testApplication;

    /** 引用文献 */
    @Excel(name = "引用文献")
    private String referenceLiterature;

    /** 题目数量 */
    @Excel(name = "题目数量", cellType = Excel.ColumnType.NUMERIC)
    private Integer questionCount;

    /** 计分类型 */
    @Excel(name = "计分类型")
    private String scoringType;

    /** 测评时长 */
    @Excel(name = "测评时长")
    private String duration;

    /** 常模均值 */
    @Excel(name = "常模均值")
    private java.math.BigDecimal normMean;

    /** 常模标准差 */
    @Excel(name = "常模标准差")
    private java.math.BigDecimal normSd;

    /** 适用年龄 */
    @Excel(name = "适用年龄")
    private String applicableAge;

    /** 量表图片 */
    @Excel(name = "量表图片")
    private String imageUrl;

    /** 价格 */
    @Excel(name = "价格")
    private java.math.BigDecimal price;

    /** 付费模式(0=免费 1=付费) */
    @Excel(name = "付费模式", readConverterExp = "0=免费,1=付费")
    private Integer payMode;

    /** 付费阶段(0=测评前 1=报告前) */
    @Excel(name = "付费阶段", readConverterExp = "0=测评前,1=报告前")
    private Integer payPhase;

    /** 免费VIP等级 */
    @Excel(name = "免费VIP等级", cellType = Excel.ColumnType.NUMERIC)
    private Integer freeVipLevel;

    /** 免费报告等级 */
    @Excel(name = "免费报告等级", cellType = Excel.ColumnType.NUMERIC)
    private Integer freeReportLevel;

    /** 付费报告等级 */
    @Excel(name = "付费报告等级", cellType = Excel.ColumnType.NUMERIC)
    private Integer paidReportLevel;

    /** 企业ID */
    @Excel(name = "企业ID", cellType = Excel.ColumnType.NUMERIC)
    private Long enterpriseId;

    /** 状态(0=禁用 1=启用) */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用")
    private Integer status;

    /** 排序 */
    @Excel(name = "排序", cellType = Excel.ColumnType.NUMERIC)
    private Integer sort;

    /** 搜索关键词 */
    @Excel(name = "搜索关键词")
    private String searchKeywords;

    /** 搜索次数 */
    @Excel(name = "搜索次数", cellType = Excel.ColumnType.NUMERIC)
    private Integer searchCount;

    /** 查看次数 */
    @Excel(name = "查看次数", cellType = Excel.ColumnType.NUMERIC)
    private Integer viewCount;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    /** 计分方法 */
    @Excel(name = "计分方法")
    private String scoringMethod;

    /** 是否有反向计分题(0=否 1=是) */
    @Excel(name = "是否有反向计分题", readConverterExp = "0=否,1=是")
    private Integer hasReverseItems;

    /** 是否需要标准分转换(0=否 1=是) */
    @Excel(name = "是否需要标准分转换", readConverterExp = "0=否,1=是")
    private Integer hasStandardScore;

    /** 标准分转换系数 */
    @Excel(name = "标准分转换系数")
    private BigDecimal standardScoreMultiplier;

    /** 维度数量 */
    @Excel(name = "维度数量")
    private Integer dimensionCount;

    /** 原始分范围 */
    @Excel(name = "原始分范围")
    private String rawScoreRange;

    /** 标准分范围 */
    @Excel(name = "标准分范围")
    private String standardScoreRange;

    // 关联对象
    /** 题目列表 */
    private List<PsyTQuestion> questions;

    /** 分量表列表 */
    private List<PsyTSubscale> subscales;

    /** 计分规则列表 */
    private List<PsyTScoringRule> scoringRules;

    /** 功能损害评估列表 */
    private List<PsyTFunctionImpairment> functionImpairments;

    /** 企业信息 */
    private PsyTEnterprise enterprise;

    // 扩展字段
    /** 用户是否已购买 */
    private Boolean purchased;

    /** 用户测评记录 */
    private PsyTAssessmentRecord userRecord;

    /** 是否可以测评 */
    private Boolean canTest;

    /** 剩余测评次数 */
    private Integer remainingTests;

    /** 最近测评时间 */
    private String lastTestTime;

    /** 最高分数 */
    private BigDecimal bestScore;

    /** 用户测评次数 */
    private Integer userTestCount;

    // 统计字段
    /** 今日测试次数 */
    private Integer todayTestCount;

    /** 本周测试次数 */
    private Integer weekTestCount;

    /** 本月测试次数 */
    private Integer monthTestCount;

    /** 完成率 */
    private BigDecimal completionRate;

    /** 平均用时 */
    private Integer avgDuration;

    // 常量定义
    /** 状态：停用 */
    public static final Integer STATUS_DISABLED = 0;
    
    /** 状态：启用 */
    public static final Integer STATUS_ENABLED = 1;

    /** 付费模式：免费 */
    public static final Integer PAY_MODE_FREE = 0;
    
    /** 付费模式：付费 */
    public static final Integer PAY_MODE_PAID = 1;

    /** 付费阶段：测试 */
    public static final Integer PAY_PHASE_TEST = 0;
    
    /** 付费阶段：报告 */
    public static final Integer PAY_PHASE_REPORT = 1;

    /** 计分类型：李克特量表 */
    public static final String SCORING_TYPE_LIKERT = "LIKERT";
    
    /** 计分类型：二分法 */
    public static final String SCORING_TYPE_BINARY = "BINARY";
    
    /** 计分类型：复合计分 */
    public static final String SCORING_TYPE_COMPOSITE = "COMPOSITE";

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "停用";
            case 1: return "启用";
            default: return "未知";
        }
    }

    /**
     * 获取付费模式描述
     */
    public String getPayModeDesc() {
        if (payMode == null) return "";
        switch (payMode) {
            case 0: return "免费";
            case 1: return "付费";
            default: return "未知";
        }
    }

    /**
     * 获取付费阶段描述
     */
    public String getPayPhaseDesc() {
        if (payPhase == null) return "";
        switch (payPhase) {
            case 0: return "测试";
            case 1: return "报告";
            default: return "未知";
        }
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return STATUS_ENABLED.equals(status);
    }

    /**
     * 是否免费
     */
    public boolean isFree() {
        return PAY_MODE_FREE.equals(payMode);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }
}
