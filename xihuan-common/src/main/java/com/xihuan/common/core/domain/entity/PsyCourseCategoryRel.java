package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

/**
 * 课程分类关系表对象 psy_course_category_rel
 * 
 * <AUTHOR>
 */
@Data
public class PsyCourseCategoryRel {
    private static final long serialVersionUID = 1L;

    /** 课程ID */
    @Excel(name = "课程ID", cellType = Excel.ColumnType.NUMERIC)
    private Long courseId;

    /** 分类ID */
    @Excel(name = "分类ID", cellType = Excel.ColumnType.NUMERIC)
    private Long categoryId;

    /** 关联的课程信息 */
    private PsyCourse course;

    /** 关联的分类信息 */
    private PsyCategory category;
}
