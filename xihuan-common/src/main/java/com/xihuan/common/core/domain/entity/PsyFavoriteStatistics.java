package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 收藏统计实体 psy_favorite_statistics
 */
@Data
public class PsyFavoriteStatistics {
    private static final long serialVersionUID = 1L;

    /** 统计ID */
    @Excel(name = "统计ID")
    private Long id;

    /** 目标类型 */
    @Excel(name = "目标类型", readConverterExp = "1=咨询师,2=课程,3=冥想,4=测评")
    private Integer targetType;

    /** 目标ID */
    @Excel(name = "目标ID")
    private Long targetId;

    /** 收藏总数 */
    @Excel(name = "收藏总数")
    private Integer favoriteCount;

    /** 今日收藏数 */
    @Excel(name = "今日收藏数")
    private Integer todayCount;

    /** 本周收藏数 */
    @Excel(name = "本周收藏数")
    private Integer weekCount;

    /** 本月收藏数 */
    @Excel(name = "本月收藏数")
    private Integer monthCount;

    /** 最后收藏时间 */
    @Excel(name = "最后收藏时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastFavoriteTime;

    /** 更新时间 */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
