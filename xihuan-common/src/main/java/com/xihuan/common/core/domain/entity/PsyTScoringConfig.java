package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 计分配置对象 psy_t_scoring_config
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTScoringConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 量表ID */
    @Excel(name = "量表ID")
    private Long scaleId;

    /** 计分类型 */
    @Excel(name = "计分类型", readConverterExp = "SIMPLE_SUM=简单求和,REVERSE_SCORING=反向计分,FORMULA=公式计分,STANDARD_SCORE=标准分转换")
    private String scoringType;

    /** 公式配置(JSON格式) */
    @Excel(name = "公式配置")
    private String formulaConfig;

    /** 反向计分题目(JSON格式) */
    @Excel(name = "反向计分题目")
    private String reverseItems;

    /** 标准分转换系数 */
    @Excel(name = "标准分转换系数")
    private BigDecimal standardMultiplier;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 量表编码（关联查询字段） */
    @Excel(name = "量表编码")
    private String scaleCode;

    /** 量表名称（关联查询字段） */
    @Excel(name = "量表名称")
    private String scaleName;

    /** 计分方法描述（关联查询字段） */
    @Excel(name = "计分方法")
    private String scoringMethodDesc;

    /** 是否启用 */
    @Excel(name = "是否启用", readConverterExp = "0=禁用,1=启用")
    private Integer status;

    /** 配置说明 */
    @Excel(name = "配置说明")
    private String description;

    /** 最后更新人 */
    @Excel(name = "最后更新人")
    private String updateBy;

    /** 最后更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
