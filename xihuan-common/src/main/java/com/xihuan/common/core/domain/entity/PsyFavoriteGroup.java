package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 收藏分组实体 psy_favorite_group
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyFavoriteGroup extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 分组ID */
    @Excel(name = "分组ID")
    private Long groupId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 分组名称 */
    @Excel(name = "分组名称")
    private String groupName;

    /** 分组图标 */
    @Excel(name = "分组图标")
    private String groupIcon;

    /** 分组颜色 */
    @Excel(name = "分组颜色")
    private String groupColor;

    /** 分组描述 */
    @Excel(name = "分组描述")
    private String description;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 是否默认分组 */
    @Excel(name = "是否默认分组", readConverterExp = "0=否,1=是")
    private Integer isDefault;

    /** 是否公开 */
    @Excel(name = "是否公开", readConverterExp = "0=私有,1=公开")
    private Integer isPublic;

    /** 收藏数量 */
    @Excel(name = "收藏数量")
    private Integer favoriteCount;

    /** 状态（0停用 1启用） */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private Integer status;

    /** 删除标志 */
    private String delFlag;
}
