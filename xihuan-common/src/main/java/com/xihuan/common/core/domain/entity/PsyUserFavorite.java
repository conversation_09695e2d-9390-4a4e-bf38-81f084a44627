package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户收藏实体 psy_user_favorite
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyUserFavorite extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 收藏ID */
    @Excel(name = "收藏ID")
    private Long favoriteId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 收藏目标类型
     * 1: 咨询师
     * 2: 课程
     * 3: 冥想
     * 4: 测评
     */
    @Excel(name = "目标类型", readConverterExp = "1=咨询师,2=课程,3=冥想,4=测评")
    private Integer targetType;

    /** 目标对象ID */
    @Excel(name = "目标ID")
    private Long targetId;

    /** 目标对象标题（冗余字段，便于查询） */
    @Excel(name = "目标标题")
    private String targetTitle;

    /** 目标对象图片（冗余字段，便于查询） */
    @Excel(name = "目标图片")
    private String targetImage;

    /** 排序（用户可自定义收藏顺序） */
    @Excel(name = "排序")
    private Integer sort;

    /** 用户自定义标签（多个标签用逗号分隔） */
    @Excel(name = "标签")
    private String tags;

    /** 用户备注 */
    @Excel(name = "备注")
    private String notes;

    /** 是否公开收藏（0私有 1公开） */
    @Excel(name = "是否公开", readConverterExp = "0=私有,1=公开")
    private Integer isPublic;

    /** 收藏时间 */
    @Excel(name = "收藏时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date favoriteTime;

    /** 最后查看时间 */
    @Excel(name = "最后查看时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastViewTime;

    /** 查看次数 */
    @Excel(name = "查看次数")
    private Integer viewCount;

    /** 删除标志（0存在 2删除） */
    private String delFlag;

    // 兼容旧版本的字段
    /** 咨询师ID（当target_type=1时有效） */
    private Long counselorId;

    /** 产品ID（当target_type=2时有效） */
    private Long productId;

    // 非数据库字段
    /** 分组ID */
    private Long groupId;

    /** 目标类型名称 */
    private String targetTypeName;

    /** 实际标题 */
    private String actualTitle;

    /** 实际图片 */
    private String actualImage;

    /** 描述 */
    private String description;

    /** 价格 */
    private String price;

    /** 目标状态 */
    private Integer targetStatus;
}