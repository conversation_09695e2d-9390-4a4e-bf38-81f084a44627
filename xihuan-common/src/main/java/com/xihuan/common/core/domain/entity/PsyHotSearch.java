package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 热门搜索表 psy_hot_search
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyHotSearch extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 热门搜索ID */
    private Long id;

    /** 搜索关键词 */
    @Excel(name = "搜索关键词")
    private String keyword;

    /** 搜索类型 */
    @Excel(name = "搜索类型", readConverterExp = "all=全部,consultant=咨询师,course=课程,meditation=冥想,assessment=测评,question=问题")
    private String searchType;

    /** 搜索次数 */
    @Excel(name = "搜索次数")
    private Integer searchCount;

    /** 最后搜索时间 */
    @Excel(name = "最后搜索时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastSearchTime;

    /** 热度分数 */
    @Excel(name = "热度分数")
    private BigDecimal hotScore;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;
}
