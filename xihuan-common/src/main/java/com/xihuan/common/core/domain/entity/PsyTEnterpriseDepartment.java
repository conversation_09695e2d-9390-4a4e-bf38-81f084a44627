package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 企业部门表 psy_t_enterprise_department
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTEnterpriseDepartment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 部门ID */
    @Excel(name = "部门ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 企业ID */
    @Excel(name = "企业ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "企业ID不能为空")
    private Long enterpriseId;

    /** 父部门ID */
    @Excel(name = "父部门ID", cellType = Excel.ColumnType.NUMERIC)
    private Long parentId;

    /** 部门编码 */
    @Excel(name = "部门编码")
    @NotBlank(message = "部门编码不能为空")
    @Size(max = 50, message = "部门编码不能超过50个字符")
    private String departmentCode;

    /** 部门名称 */
    @Excel(name = "部门名称")
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 100, message = "部门名称不能超过100个字符")
    private String departmentName;

    /** 部门层级 */
    @Excel(name = "部门层级")
    @Min(value = 1, message = "部门层级不能小于1")
    private Integer departmentLevel;

    /** 部门负责人ID */
    @Excel(name = "部门负责人ID", cellType = Excel.ColumnType.NUMERIC)
    private Long managerId;

    /** 部门人数 */
    @Excel(name = "部门人数")
    @Min(value = 0, message = "部门人数不能小于0")
    private Integer employeeCount;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private Integer status;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 企业信息 */
    private PsyTEnterprise enterprise;

    /** 父部门信息 */
    private PsyTEnterpriseDepartment parent;

    /** 子部门列表 */
    private List<PsyTEnterpriseDepartment> children;

    /** 部门负责人信息 */
    private SysUser manager;

    // 扩展字段
    /** 企业名称 */
    private String enterpriseName;

    /** 父部门名称 */
    private String parentName;

    /** 负责人姓名 */
    private String managerName;

    /** 负责人电话 */
    private String managerPhone;

    /** 部门路径 */
    private String departmentPath;

    /** 子部门数量 */
    private Integer childrenCount;

    /** 是否有子部门 */
    private Boolean hasChildren;

    /** 部门全名(包含层级) */
    private String fullName;

    /** 测评参与人数 */
    private Integer participantCount;

    /** 测评完成人数 */
    private Integer completedCount;

    /** 测评完成率 */
    private Double completionRate;

    // 常量定义
    /** 状态：停用 */
    public static final Integer STATUS_DISABLED = 0;
    
    /** 状态：启用 */
    public static final Integer STATUS_ENABLED = 1;

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /** 根部门父ID */
    public static final Long ROOT_PARENT_ID = 0L;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "停用";
            case 1: return "启用";
            default: return "未知";
        }
    }

    /**
     * 获取部门层级描述
     */
    public String getDepartmentLevelDesc() {
        if (departmentLevel == null) return "";
        switch (departmentLevel) {
            case 1: return "一级部门";
            case 2: return "二级部门";
            case 3: return "三级部门";
            case 4: return "四级部门";
            default: return departmentLevel + "级部门";
        }
    }

    /**
     * 构建部门路径
     */
    public String buildDepartmentPath() {
        if (parent == null) {
            return departmentName;
        }
        return parent.buildDepartmentPath() + " > " + departmentName;
    }

    /**
     * 是否根部门
     */
    public boolean isRootDepartment() {
        return ROOT_PARENT_ID.equals(parentId);
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return STATUS_ENABLED.equals(status);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 是否有子部门
     */
    public boolean hasChildDepartments() {
        return children != null && !children.isEmpty();
    }

    /**
     * 计算测评完成率
     */
    public Double calculateCompletionRate() {
        if (participantCount == null || participantCount == 0 || completedCount == null) return 0.0;
        return (double) completedCount / participantCount * 100;
    }

    /**
     * 获取部门编码全路径
     */
    public String getFullDepartmentCode() {
        if (parent == null || isRootDepartment()) {
            return departmentCode;
        }
        return parent.getFullDepartmentCode() + "." + departmentCode;
    }

    /**
     * 获取部门层级缩进
     */
    public String getLevelIndent() {
        if (departmentLevel == null || departmentLevel <= 1) return "";
        StringBuilder indent = new StringBuilder();
        for (int i = 1; i < departmentLevel; i++) {
            indent.append("　　");
        }
        return indent.toString();
    }

    /**
     * 获取带缩进的部门名称
     */
    public String getIndentedName() {
        return getLevelIndent() + departmentName;
    }
}
