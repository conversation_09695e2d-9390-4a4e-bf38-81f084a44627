package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

/**
 * 企业信息表 psy_t_enterprise
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTEnterprise extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 企业ID */
    @Excel(name = "企业ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 企业编码 */
    @Excel(name = "企业编码")
    @NotBlank(message = "企业编码不能为空")
    @Size(max = 50, message = "企业编码不能超过50个字符")
    private String enterpriseCode;

    /** 企业名称 */
    @Excel(name = "企业名称")
    @NotBlank(message = "企业名称不能为空")
    @Size(max = 200, message = "企业名称不能超过200个字符")
    private String enterpriseName;

    /** 企业类型(1普通企业 2政府机构 3学校 4医院) */
    @Excel(name = "企业类型", readConverterExp = "1=普通企业,2=政府机构,3=学校,4=医院")
    private Integer enterpriseType;

    /** 所属行业 */
    @Excel(name = "所属行业")
    @Size(max = 100, message = "所属行业不能超过100个字符")
    private String industry;

    /** 企业规模 */
    @Excel(name = "企业规模", readConverterExp = "SMALL=小型,MEDIUM=中型,LARGE=大型")
    private String scale;

    /** 员工数量 */
    @Excel(name = "员工数量")
    @Min(value = 0, message = "员工数量不能小于0")
    private Integer employeeCount;

    /** 联系人 */
    @Excel(name = "联系人")
    @Size(max = 50, message = "联系人不能超过50个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Size(max = 20, message = "联系电话不能超过20个字符")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "联系邮箱不能超过100个字符")
    private String contactEmail;

    /** 企业地址 */
    @Excel(name = "企业地址")
    @Size(max = 500, message = "企业地址不能超过500个字符")
    private String address;

    /** 营业执照号 */
    @Excel(name = "营业执照号")
    @Size(max = 100, message = "营业执照号不能超过100个字符")
    private String licenseNumber;

    /** 合同开始日期 */
    @Excel(name = "合同开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartDate;

    /** 合同结束日期 */
    @Excel(name = "合同结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    /** 服务套餐 */
    @Excel(name = "服务套餐")
    @Size(max = 50, message = "服务套餐不能超过50个字符")
    private String servicePackage;

    /** 最大测评次数 */
    @Excel(name = "最大测评次数")
    @Min(value = 0, message = "最大测评次数不能小于0")
    private Integer maxAssessmentCount;

    /** 已使用测评次数 */
    @Excel(name = "已使用测评次数")
    @Min(value = 0, message = "已使用测评次数不能小于0")
    private Integer usedAssessmentCount;

    /** 状态(0停用 1启用 2过期) */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用,2=过期")
    private Integer status;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 部门列表 */
    private List<PsyTEnterpriseDepartment> departments;

    /** 测评计划列表 */
    private List<PsyTEnterpriseAssessmentPlan> assessmentPlans;

    /** 量表列表 */
    private List<PsyTScale> scales;

    // 扩展字段
    /** 剩余测评次数 */
    private Integer remainingAssessmentCount;

    /** 使用率 */
    private Double usageRate;

    /** 合同剩余天数 */
    private Integer remainingDays;

    /** 是否即将过期 */
    private Boolean nearExpiry;

    /** 部门数量 */
    private Integer departmentCount;

    /** 进行中的计划数量 */
    private Integer activePlanCount;

    /** 本月测评次数 */
    private Integer monthlyAssessmentCount;

    // 常量定义
    /** 企业类型：普通企业 */
    public static final Integer TYPE_ENTERPRISE = 1;
    
    /** 企业类型：政府机构 */
    public static final Integer TYPE_GOVERNMENT = 2;
    
    /** 企业类型：学校 */
    public static final Integer TYPE_SCHOOL = 3;
    
    /** 企业类型：医院 */
    public static final Integer TYPE_HOSPITAL = 4;

    /** 企业规模：小型 */
    public static final String SCALE_SMALL = "SMALL";
    
    /** 企业规模：中型 */
    public static final String SCALE_MEDIUM = "MEDIUM";
    
    /** 企业规模：大型 */
    public static final String SCALE_LARGE = "LARGE";

    /** 状态：停用 */
    public static final Integer STATUS_DISABLED = 0;
    
    /** 状态：启用 */
    public static final Integer STATUS_ENABLED = 1;
    
    /** 状态：过期 */
    public static final Integer STATUS_EXPIRED = 2;

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取企业类型描述
     */
    public String getEnterpriseTypeDesc() {
        if (enterpriseType == null) return "";
        switch (enterpriseType) {
            case 1: return "普通企业";
            case 2: return "政府机构";
            case 3: return "学校";
            case 4: return "医院";
            default: return "未知";
        }
    }

    /**
     * 获取企业规模描述
     */
    public String getScaleDesc() {
        if (scale == null) return "";
        switch (scale) {
            case "SMALL": return "小型";
            case "MEDIUM": return "中型";
            case "LARGE": return "大型";
            default: return "未知";
        }
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "停用";
            case 1: return "启用";
            case 2: return "过期";
            default: return "未知";
        }
    }

    /**
     * 计算剩余测评次数
     */
    public Integer calculateRemainingAssessmentCount() {
        if (maxAssessmentCount == null || usedAssessmentCount == null) return 0;
        return Math.max(0, maxAssessmentCount - usedAssessmentCount);
    }

    /**
     * 计算使用率
     */
    public Double calculateUsageRate() {
        if (maxAssessmentCount == null || maxAssessmentCount == 0 || usedAssessmentCount == null) return 0.0;
        return (double) usedAssessmentCount / maxAssessmentCount * 100;
    }

    /**
     * 计算合同剩余天数
     */
    public Integer calculateRemainingDays() {
        if (contractEndDate == null) return null;
        Date now = new Date();
        if (contractEndDate.before(now)) return 0;
        return (int) ((contractEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return STATUS_ENABLED.equals(status);
    }

    /**
     * 是否过期
     */
    public boolean isExpired() {
        return STATUS_EXPIRED.equals(status) || (contractEndDate != null && contractEndDate.before(new Date()));
    }

    /**
     * 是否即将过期(30天内)
     */
    public boolean isNearExpiry() {
        Integer remaining = calculateRemainingDays();
        return remaining != null && remaining <= 30 && remaining > 0;
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 是否可以使用测评
     */
    public boolean canUseAssessment() {
        return isEnabled() && !isExpired() && calculateRemainingAssessmentCount() > 0;
    }

    /**
     * 获取测评配额（兼容性方法）
     *
     * @return 测评配额
     */
    public Integer getAssessmentQuota() {
        return maxAssessmentCount;
    }

    /**
     * 设置测评配额（兼容性方法）
     *
     * @param assessmentQuota 测评配额
     */
    public void setAssessmentQuota(Integer assessmentQuota) {
        this.maxAssessmentCount = assessmentQuota;
    }

    /**
     * 获取剩余测评次数（兼容性方法）
     *
     * @return 剩余测评次数
     */
    public Integer getRemainingAssessmentCount() {
        if (remainingAssessmentCount != null) {
            return remainingAssessmentCount;
        }
        return calculateRemainingAssessmentCount();
    }
}
