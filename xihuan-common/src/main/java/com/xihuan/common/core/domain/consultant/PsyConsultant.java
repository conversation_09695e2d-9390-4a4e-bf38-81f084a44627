package com.xihuan.common.core.domain.consultant;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 心理咨询师主表实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyConsultant extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Excel(name = "主键ID")
    private Long id;

    @Excel(name = "关联用户ID")
    private Long userId;

    @Excel(name = "工作照")
    private String imageUrl;

    @Excel(name = "背景图")
    private String bgImg;

    @Excel(name = "所在地")
    private String location;

    @Excel(name = "详细地址")
    private String address;

    @Excel(name = "省份")
    private String province;

    @Excel(name = "城市")
    private String city;

    @Excel(name = "区县")
    private String district;

    @Excel(name = "联系电话")
    private String phone;

    @Excel(name = "微信号")
    private String wechat;

    @Excel(name = "姓名")
    private String name;

    @Excel(name = "出生年份")
    private Integer birthDate;

    @Excel(name = "性别", readConverterExp = "男=0,女=1")
    private String gender;

    @Excel(name = "身份证号")
    private String idCardNumber;

    @Excel(name = "身份证人像面")
    private String idFrontImg;

    @Excel(name = "身份证国徽面")
    private String idBackImg;

    @Excel(name = "从业年份")
    private Integer startYear;

    @Excel(name = "个人介绍")
    private String personalIntro;

    @Excel(name = "个人头衔")
    private String personalTitle;

    @Excel(name = "累计个案")
    private String totalCases;

    @Excel(name = "服务人次")
    private String serviceCount;

    @Excel(name = "服务时长")
    private String serviceHours;

    @Excel(name = "可否讲课", readConverterExp = "0=否,1=是")
    private Boolean canTeach;

    @Excel(name = "能否出差", readConverterExp = "0=否,1=是")
    private Boolean canTravel;

    @Excel(name = "可预约时间")
    private String availableTime;

    @Excel(name = "单价")
    private BigDecimal price;

    @Excel(name = "最低咨询费")
    private Integer minFee;

    @Excel(name = "最高咨询费")
    private Integer maxFee;

    @Excel(name = "咨询案例")
    private String workCase;

    @Excel(name = "审核状态", readConverterExp = "0=待审,1=通过,2=驳回")
    private String auditStatus;

    @Excel(name = "工作状态", readConverterExp = "0=可预约,1=休息,2=离职")
    private String workStatus;

    @Excel(name = "搜索关键词")
    private String searchKeywords;

    @Excel(name = "被搜索次数")
    private Integer searchCount;

    @Excel(name = "查看次数")
    private Integer viewCount;

    // 关联关系
    private List<PsyConsultantMethod> serviceMethods;     // 服务方式
    private List<PsyConsultantStyle> consultStyles;        // 咨询流派
    private List<PsyDictExpertise> expertises;             // 擅长领域
    private List<PsyConsultantSpecial> specialMethods;     // 特色方式
    private List<PsyAvailableSlot> availableSlots;         // 可预约时段
    private List<PsyCertificate> certificates;             // 资质证书
    private List<PsyEducation> educations;                 // 教育经历
    private List<PsySupervision> supervisions;             // 督导经历
    private List<PsyTraining> trainings;                   // 培训经历
}
