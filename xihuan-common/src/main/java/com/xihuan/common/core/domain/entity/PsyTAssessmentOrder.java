package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 测评订单表 psy_t_assessment_order
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTAssessmentOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    @Excel(name = "订单ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @NotBlank(message = "订单编号不能为空")
    @Size(max = 50, message = "订单编号不能超过50个字符")
    private String orderNo;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 原价 */
    @Excel(name = "原价")
    @NotNull(message = "原价不能为空")
    @DecimalMin(value = "0.00", message = "原价不能小于0")
    private BigDecimal originalPrice;

    /** 折扣价格 */
    @Excel(name = "折扣价格")
    @DecimalMin(value = "0.00", message = "折扣价格不能小于0")
    private BigDecimal discountPrice;

    /** 实付金额 */
    @Excel(name = "实付金额")
    @NotNull(message = "实付金额不能为空")
    @DecimalMin(value = "0.00", message = "实付金额不能小于0")
    private BigDecimal paymentAmount;

    /** 支付方式 */
    @Excel(name = "支付方式")
    @Size(max = 20, message = "支付方式不能超过20个字符")
    private String paymentMethod;

    /** 支付时间 */
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    /** 交易流水号 */
    @Excel(name = "交易流水号")
    @Size(max = 100, message = "交易流水号不能超过100个字符")
    private String transactionId;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @DecimalMin(value = "0.00", message = "退款金额不能小于0")
    private BigDecimal refundAmount;

    /** 退款时间 */
    @Excel(name = "退款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /** 退款原因 */
    @Excel(name = "退款原因")
    @Size(max = 500, message = "退款原因不能超过500个字符")
    private String refundReason;

    /** 取消时间 */
    @Excel(name = "取消时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    /** 优惠券ID */
    @Excel(name = "优惠券ID", cellType = Excel.ColumnType.NUMERIC)
    private Long couponId;

    /** 优惠券折扣 */
    @Excel(name = "优惠券折扣")
    @DecimalMin(value = "0.00", message = "优惠券折扣不能小于0")
    private BigDecimal couponDiscount;

    /** 订单状态(0待支付 1已支付 2已取消 3已退款) */
    @Excel(name = "订单状态", readConverterExp = "0=待支付,1=已支付,2=已取消,3=已退款")
    private Integer status;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    /** 用户信息 */
    private SysUser user;

    /** 测评记录 */
    private PsyTAssessmentRecord record;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 量表编码 */
    private String scaleCode;

    /** 用户昵称 */
    private String nickName;

    /** 用户手机号 */
    private String userPhone;

    /** 订单有效期 */
    private Date expireTime;

    /** 是否已使用 */
    private Boolean used;

    /** 使用时间 */
    private Date useTime;

    /** 折扣金额 */
    private BigDecimal discountAmount;

    /** 实际优惠金额 */
    private BigDecimal actualDiscount;

    // 常量定义
    /** 订单状态：待支付 */
    public static final Integer STATUS_PENDING = 0;
    
    /** 订单状态：已支付 */
    public static final Integer STATUS_PAID = 1;
    
    /** 订单状态：已取消 */
    public static final Integer STATUS_CANCELLED = 2;
    
    /** 订单状态：已退款 */
    public static final Integer STATUS_REFUNDED = 3;

    /** 支付方式：微信支付 */
    public static final String PAYMENT_METHOD_WECHAT = "WECHAT";
    
    /** 支付方式：支付宝 */
    public static final String PAYMENT_METHOD_ALIPAY = "ALIPAY";

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取订单状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "待支付";
            case 1: return "已支付";
            case 2: return "已取消";
            case 3: return "已退款";
            default: return "未知";
        }
    }

    /**
     * 获取支付方式描述
     */
    public String getPaymentMethodDesc() {
        if (paymentMethod == null) return "";
        switch (paymentMethod) {
            case "WECHAT": return "微信支付";
            case "ALIPAY": return "支付宝";
            default: return paymentMethod;
        }
    }

    /**
     * 计算折扣金额
     */
    public BigDecimal calculateDiscountAmount() {
        if (originalPrice == null || paymentAmount == null) return BigDecimal.ZERO;
        return originalPrice.subtract(paymentAmount);
    }

    /**
     * 计算折扣率
     */
    public BigDecimal calculateDiscountRate() {
        if (originalPrice == null || originalPrice.compareTo(BigDecimal.ZERO) == 0) return BigDecimal.ZERO;
        BigDecimal discount = calculateDiscountAmount();
        return discount.divide(originalPrice, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
    }

    /**
     * 是否待支付
     */
    public boolean isPending() {
        return STATUS_PENDING.equals(status);
    }

    /**
     * 是否已支付
     */
    public boolean isPaid() {
        return STATUS_PAID.equals(status);
    }

    /**
     * 是否已取消
     */
    public boolean isCancelled() {
        return STATUS_CANCELLED.equals(status);
    }

    /**
     * 是否已退款
     */
    public boolean isRefunded() {
        return STATUS_REFUNDED.equals(status);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 是否可以支付
     */
    public boolean canPay() {
        return isPending() && !isDeleted();
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return isPending() && !isDeleted();
    }

    /**
     * 是否可以退款
     */
    public boolean canRefund() {
        return isPaid() && !isDeleted();
    }
}
