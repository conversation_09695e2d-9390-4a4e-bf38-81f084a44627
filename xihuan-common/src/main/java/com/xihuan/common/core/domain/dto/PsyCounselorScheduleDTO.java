package com.xihuan.common.core.domain.dto;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 咨询师排期数据传输对象（小程序端）
 * 
 * <AUTHOR>
 */
@Data
public class PsyCounselorScheduleDTO {
    
    /** 排期ID */
    private Long id;
    
    /** 咨询师ID */
    private Long counselorId;
    
    /** 咨询师姓名 */
    private String counselorName;
    
    /** 咨询师头像 */
    private String counselorAvatar;
    
    /** 排期日期 */
    private LocalDate scheduleDate;
    
    /** 日期显示（如：2024年1月15日） */
    private String dateDisplay;
    
    /** 星期几显示（如：周一） */
    private String weekDayDisplay;
    
    /** 是否是今天 */
    private Boolean isToday;
    
    /** 开始时间 */
    private LocalTime startTime;
    
    /** 结束时间 */
    private LocalTime endTime;
    
    /** 时间显示（如：09:00-18:00） */
    private String timeDisplay;
    
    /** 咨询中心ID */
    private Long centerId;
    
    /** 咨询中心名称 */
    private String centerName;
    
    /** 是否工作(1工作 0休假) */
    private Integer isWorking;
    
    /** 工作状态显示 */
    private String workingStatusDisplay;
    
    /** 是否模板生成 */
    private Integer isTemplate;
    
    /** 来源模板ID */
    private Long templateId;
    
    /** 调班备注 */
    private String remark;
    
    /** 可预约时间槽数量 */
    private Integer availableSlots;
    
    /** 已预约时间槽数量 */
    private Integer bookedSlots;
    
    /** 总时间槽数量 */
    private Integer totalSlots;
    
    /** 预约率（百分比） */
    private Double bookingRate;
    
    /** 创建时间显示 */
    private String createTimeDisplay;
    
    /** 更新时间显示 */
    private String updateTimeDisplay;
    
    /**
     * 排期统计信息
     */
    @Data
    public static class ScheduleStatistics {
        /** 统计开始日期 */
        private LocalDate startDate;
        
        /** 统计结束日期 */
        private LocalDate endDate;
        
        /** 总天数 */
        private Long totalDays;
        
        /** 工作天数 */
        private Long workingDays;
        
        /** 休假天数 */
        private Long restDays;
        
        /** 工作率（百分比） */
        private Double workingRate;
        
        /** 总工作时长（小时） */
        private Double totalWorkingHours;
        
        /** 平均每日工作时长（小时） */
        private Double averageDailyHours;
    }
    
    /**
     * 排期日历视图数据
     */
    @Data
    public static class ScheduleCalendarDTO {
        /** 年月（如：2024-01） */
        private String yearMonth;
        
        /** 月份显示（如：2024年1月） */
        private String monthDisplay;
        
        /** 日期列表 */
        private List<CalendarDay> days;
        
        @Data
        public static class CalendarDay {
            /** 日期 */
            private LocalDate date;
            
            /** 日期显示（如：15） */
            private String dayDisplay;
            
            /** 是否是今天 */
            private Boolean isToday;
            
            /** 是否是当前月 */
            private Boolean isCurrentMonth;
            
            /** 是否有排期 */
            private Boolean hasSchedule;
            
            /** 工作状态（1工作 0休假 null无排期） */
            private Integer workingStatus;
            
            /** 状态显示类型（work/rest/none） */
            private String statusType;
            
            /** 时间显示（如：09:00-18:00） */
            private String timeDisplay;
            
            /** 排期ID */
            private Long scheduleId;
        }
    }
    
    /**
     * 排期时间段信息
     */
    @Data
    public static class ScheduleTimeRange {
        /** 时间段名称（上午/下午/晚上） */
        private String rangeName;
        
        /** 开始时间 */
        private LocalTime startTime;
        
        /** 结束时间 */
        private LocalTime endTime;
        
        /** 时间显示 */
        private String timeDisplay;
        
        /** 是否工作时间 */
        private Boolean isWorkingTime;
        
        /** 可预约时间槽数量 */
        private Integer availableSlots;
        
        /** 已预约时间槽数量 */
        private Integer bookedSlots;
    }
    
    /**
     * 批量操作请求
     */
    @Data
    public static class BatchOperationRequest {
        /** 操作类型（create/update/delete/copy） */
        private String operationType;
        
        /** 排期ID列表（用于批量更新/删除） */
        private List<Long> scheduleIds;
        
        /** 排期数据列表（用于批量创建/更新） */
        private List<PsyCounselorScheduleDTO> schedules;
        
        /** 源排期ID（用于复制操作） */
        private Long sourceScheduleId;
        
        /** 目标日期列表（用于复制操作） */
        private List<LocalDate> targetDates;
        
        /** 操作备注 */
        private String remark;
    }
    
    /**
     * 排期模板应用请求
     */
    @Data
    public static class ApplyTemplateRequest {
        /** 模板ID */
        private Long templateId;
        
        /** 咨询师ID */
        private Long counselorId;
        
        /** 应用开始日期 */
        private LocalDate startDate;
        
        /** 应用结束日期 */
        private LocalDate endDate;
        
        /** 是否覆盖已存在的排期 */
        private Boolean overrideExisting;
        
        /** 咨询中心ID */
        private Long centerId;
    }
    
    /**
     * 排期冲突检查结果
     */
    @Data
    public static class ConflictCheckResult {
        /** 是否有冲突 */
        private Boolean hasConflict;
        
        /** 冲突日期列表 */
        private List<LocalDate> conflictDates;
        
        /** 冲突详情 */
        private List<ConflictDetail> conflictDetails;
        
        @Data
        public static class ConflictDetail {
            /** 冲突日期 */
            private LocalDate date;
            
            /** 冲突原因 */
            private String reason;
            
            /** 现有排期信息 */
            private String existingSchedule;
            
            /** 新排期信息 */
            private String newSchedule;
        }
    }
}
