package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.util.List;

/**
 * 咨询师排班模板表实体类
 * 对应数据库表：psy_time_schedule_template
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTimeScheduleTemplate extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 模板ID */
    private Long id;

    /** 咨询师ID */
    private Long counselorId;

    /** 模板名称 */
    private String name;

    /** 是否默认模板 */
    private Integer isDefault;

    /** 生效开始日期 */
    private LocalDate effectiveStart;

    /** 生效结束日期 */
    private LocalDate effectiveEnd;

    /** 删除标志 */
    private Integer delFlag;

    // 关联对象（非数据库字段）
    /** 模板明细列表 */
    private List<PsyTimeTemplateItem> templateItems;

    /** 咨询师信息 */
    private com.xihuan.common.core.domain.consultant.PsyConsultant consultant;
}
