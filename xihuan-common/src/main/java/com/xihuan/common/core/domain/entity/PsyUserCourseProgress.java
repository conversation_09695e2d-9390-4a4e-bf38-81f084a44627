package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户课程学习进度表对象 psy_user_course_progress
 * 
 * <AUTHOR>
 */
@Data
public class PsyUserCourseProgress {
    private static final long serialVersionUID = 1L;

    /** 进度ID */
    @Excel(name = "进度ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    private Long userId;

    /** 课程ID */
    @Excel(name = "课程ID", cellType = Excel.ColumnType.NUMERIC)
    private Long courseId;

    /** 章节ID */
    @Excel(name = "章节ID", cellType = Excel.ColumnType.NUMERIC)
    private Long chapterId;

    /** 学习进度百分比 */
    @Excel(name = "学习进度百分比")
    private BigDecimal progressPercent;

    /** 最后播放位置（秒） */
    @Excel(name = "最后播放位置")
    private Integer lastPosition;

    /** 是否完成 */
    @Excel(name = "是否完成", readConverterExp = "0=未完成,1=已完成")
    private Integer isCompleted;

    /** 学习时长（秒） */
    @Excel(name = "学习时长")
    private Integer studyTime;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 关联的用户信息 */
    private SysUser user;

    /** 关联的课程信息 */
    private PsyCourse course;

    /** 关联的章节信息 */
    private PsyCourseChapter chapter;
}
