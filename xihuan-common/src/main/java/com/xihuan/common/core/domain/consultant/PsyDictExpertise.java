package com.xihuan.common.core.domain.consultant;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 心理咨询领域字典实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyDictExpertise extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 类型名称 */
    private String typeName;

    /** 父级ID */
    private Long parentId;

    /** 图标 */
    private String icon;

    /** 排序 */
    private Integer sort;

    /** 备注 */
    private String remark;

    /** 子项列表 */
    private List<PsyDictExpertise> children;
}