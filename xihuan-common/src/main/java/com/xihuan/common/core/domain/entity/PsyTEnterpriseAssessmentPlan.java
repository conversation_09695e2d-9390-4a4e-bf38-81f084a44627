package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 企业测评计划表 psy_t_enterprise_assessment_plan
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTEnterpriseAssessmentPlan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 计划ID */
    @Excel(name = "计划ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 企业ID */
    @Excel(name = "企业ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "企业ID不能为空")
    private Long enterpriseId;

    /** 计划名称 */
    @Excel(name = "计划名称")
    @NotBlank(message = "计划名称不能为空")
    @Size(max = 200, message = "计划名称不能超过200个字符")
    private String planName;

    /** 计划类型(1招聘测评 2定期筛查 3培训评估 4专项调研) */
    @Excel(name = "计划类型", readConverterExp = "1=招聘测评,2=定期筛查,3=培训评估,4=专项调研")
    @NotNull(message = "计划类型不能为空")
    private Integer planType;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 目标类型(1全员 2指定部门 3指定员工) */
    @Excel(name = "目标类型", readConverterExp = "1=全员,2=指定部门,3=指定员工")
    @NotNull(message = "目标类型不能为空")
    private Integer targetType;

    /** 目标部门ID列表(JSON格式) */
    @Excel(name = "目标部门ID列表")
    private String targetDepartments;

    /** 目标员工ID列表(JSON格式) */
    @Excel(name = "目标员工ID列表")
    private String targetEmployees;

    /** 开始时间 */
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /** 结束时间 */
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    /** 是否匿名测评 */
    @Excel(name = "是否匿名测评", readConverterExp = "0=否,1=是")
    private Integer isAnonymous;

    /** 是否强制参与 */
    @Excel(name = "是否强制参与", readConverterExp = "0=否,1=是")
    private Integer isMandatory;

    /** 是否启用提醒 */
    @Excel(name = "是否启用提醒", readConverterExp = "0=否,1=是")
    private Integer reminderEnabled;

    /** 提醒频率(小时) */
    @Excel(name = "提醒频率")
    @Min(value = 1, message = "提醒频率不能小于1小时")
    private Integer reminderFrequency;

    /** 计划描述 */
    @Excel(name = "计划描述")
    private String description;

    /** 完成率 */
    @Excel(name = "完成率")
    @DecimalMin(value = "0.00", message = "完成率不能小于0")
    @DecimalMax(value = "100.00", message = "完成率不能大于100")
    private BigDecimal completionRate;

    /** 目标参与人数 */
    @Excel(name = "目标参与人数")
    @Min(value = 0, message = "目标参与人数不能小于0")
    private Integer targetParticipants;

    /** 实际参与人数 */
    @Excel(name = "实际参与人数")
    @Min(value = 0, message = "实际参与人数不能小于0")
    private Integer actualParticipants;

    /** 总参与人数 */
    @Excel(name = "总参与人数")
    @Min(value = 0, message = "总参与人数不能小于0")
    private Integer totalParticipants;

    /** 已完成人数 */
    @Excel(name = "已完成人数")
    @Min(value = 0, message = "已完成人数不能小于0")
    private Integer completedParticipants;

    /** 状态(0待开始 1进行中 2已结束 3已取消) */
    @Excel(name = "状态", readConverterExp = "0=待开始,1=进行中,2=已结束,3=已取消")
    private Integer status;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 企业信息 */
    private PsyTEnterprise enterprise;

    /** 量表信息 */
    private PsyTScale scale;

    /** 参与记录列表 */
    private List<PsyTEnterpriseAssessmentParticipant> participants;

    /** 目标部门列表 */
    private List<PsyTEnterpriseDepartment> departments;

    // 扩展字段
    /** 企业名称 */
    private String enterpriseName;

    /** 量表名称 */
    private String scaleName;

    /** 量表编码 */
    private String scaleCode;

    /** 计划进度 */
    private String planProgress;

    /** 剩余天数 */
    private Integer remainingDays;

    /** 是否即将结束 */
    private Boolean nearEnd;

    /** 未开始人数 */
    private Integer notStartedCount;

    /** 进行中人数 */
    private Integer inProgressCount;

    /** 已放弃人数 */
    private Integer abandonedCount;

    /** 平均完成时间 */
    private Integer avgCompletionTime;

    /** 目标部门名称列表 */
    private List<String> departmentNames;

    // 常量定义
    /** 计划类型：招聘测评 */
    public static final Integer PLAN_TYPE_RECRUITMENT = 1;
    
    /** 计划类型：定期筛查 */
    public static final Integer PLAN_TYPE_SCREENING = 2;
    
    /** 计划类型：培训评估 */
    public static final Integer PLAN_TYPE_TRAINING = 3;
    
    /** 计划类型：专项调研 */
    public static final Integer PLAN_TYPE_RESEARCH = 4;

    /** 目标类型：全员 */
    public static final Integer TARGET_TYPE_ALL = 1;
    
    /** 目标类型：指定部门 */
    public static final Integer TARGET_TYPE_DEPARTMENT = 2;
    
    /** 目标类型：指定员工 */
    public static final Integer TARGET_TYPE_EMPLOYEE = 3;

    /** 状态：草稿 */
    public static final Integer STATUS_DRAFT = 0;

    /** 状态：待开始 */
    public static final Integer STATUS_PENDING = 0;

    /** 状态：进行中 */
    public static final Integer STATUS_IN_PROGRESS = 1;

    /** 状态：已暂停 */
    public static final Integer STATUS_PAUSED = 2;

    /** 状态：已结束 */
    public static final Integer STATUS_ENDED = 3;

    /** 状态：已取消 */
    public static final Integer STATUS_CANCELLED = 4;

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取计划类型描述
     */
    public String getPlanTypeDesc() {
        if (planType == null) return "";
        switch (planType) {
            case 1: return "招聘测评";
            case 2: return "定期筛查";
            case 3: return "培训评估";
            case 4: return "专项调研";
            default: return "未知";
        }
    }

    /**
     * 获取目标类型描述
     */
    public String getTargetTypeDesc() {
        if (targetType == null) return "";
        switch (targetType) {
            case 1: return "全员";
            case 2: return "指定部门";
            case 3: return "指定员工";
            default: return "未知";
        }
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "待开始";
            case 1: return "进行中";
            case 2: return "已结束";
            case 3: return "已取消";
            default: return "未知";
        }
    }

    /**
     * 计算剩余天数
     */
    public Integer calculateRemainingDays() {
        if (endTime == null) return null;
        Date now = new Date();
        if (endTime.before(now)) return 0;
        return (int) ((endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    }

    /**
     * 计算完成率
     */
    public BigDecimal calculateCompletionRate() {
        if (totalParticipants == null || totalParticipants == 0 || completedParticipants == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(completedParticipants)
                .divide(new BigDecimal(totalParticipants), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100));
    }

    /**
     * 是否待开始
     */
    public boolean isPending() {
        return STATUS_PENDING.equals(status);
    }

    /**
     * 是否进行中
     */
    public boolean isInProgress() {
        return STATUS_IN_PROGRESS.equals(status);
    }

    /**
     * 是否已结束
     */
    public boolean isEnded() {
        return STATUS_ENDED.equals(status);
    }

    /**
     * 是否已取消
     */
    public boolean isCancelled() {
        return STATUS_CANCELLED.equals(status);
    }

    /**
     * 是否匿名测评
     */
    public boolean isAnonymousAssessment() {
        return Integer.valueOf(1).equals(isAnonymous);
    }

    /**
     * 是否强制参与
     */
    public boolean isMandatoryParticipation() {
        return Integer.valueOf(1).equals(isMandatory);
    }

    /**
     * 是否启用提醒
     */
    public boolean isReminderOn() {
        return Integer.valueOf(1).equals(reminderEnabled);
    }

    /**
     * 是否即将结束(3天内)
     */
    public boolean isNearEnd() {
        Integer remaining = calculateRemainingDays();
        return remaining != null && remaining <= 3 && remaining > 0;
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 是否可以开始
     */
    public boolean canStart() {
        return isPending() && !isDeleted() && new Date().after(startTime);
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return (isPending() || isInProgress()) && !isDeleted();
    }
}
