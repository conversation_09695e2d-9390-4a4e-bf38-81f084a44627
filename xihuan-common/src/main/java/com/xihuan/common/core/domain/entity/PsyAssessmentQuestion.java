package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 心理测评题目表对象 psy_t_question
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyAssessmentQuestion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 题目ID */
    @Excel(name = "题目ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 题目序号 */
    @Excel(name = "题目序号")
    @NotNull(message = "题目序号不能为空")
    @Min(value = 1, message = "题目序号不能小于1")
    private Integer questionNo;

    /** 题目内容 */
    @Excel(name = "题目内容")
    @NotBlank(message = "题目内容不能为空")
    private String questionText;

    /** 题目类型(1=单选 2=多选 3=填空 4=量表) */
    @Excel(name = "题目类型", readConverterExp = "1=单选,2=多选,3=填空,4=量表")
    @NotNull(message = "题目类型不能为空")
    @Min(value = 1, message = "题目类型最小值为1")
    @Max(value = 4, message = "题目类型最大值为4")
    private Integer questionType;

    /** 是否必答(0=否 1=是) */
    @Excel(name = "是否必答", readConverterExp = "0=否,1=是")
    private Integer isRequired;

    /** 计分方式(1=选项分值 2=自定义) */
    @Excel(name = "计分方式", readConverterExp = "1=选项分值,2=自定义")
    private Integer scoreType;

    /** 测量维度 */
    @Excel(name = "测量维度")
    @Size(max = 50, message = "测量维度不能超过50个字符")
    private String dimension;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    @Min(value = 0, message = "显示顺序不能为负数")
    private Integer orderNum;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    /** 选项列表 */
    private List<PsyTQuestionOption> options;

   /** 用户答案 */
    private PsyTAnswerRecord userAnswer;

    // 常量定义
    /** 题目类型：单选 */
    public static final Integer TYPE_SINGLE_CHOICE = 1;
    
    /** 题目类型：多选 */
    public static final Integer TYPE_MULTIPLE_CHOICE = 2;
    
    /** 题目类型：填空 */
    public static final Integer TYPE_FILL_BLANK = 3;
    
    /** 题目类型：量表 */
    public static final Integer TYPE_SCALE = 4;

    /** 必答 */
    public static final Integer REQUIRED_YES = 1;
    
    /** 非必答 */
    public static final Integer REQUIRED_NO = 0;

    /** 计分方式：选项分值 */
    public static final Integer SCORE_TYPE_OPTION = 1;
    
    /** 计分方式：自定义 */
    public static final Integer SCORE_TYPE_CUSTOM = 2;

    /** 删除标志：正常 */
    public static final Integer DEL_FLAG_NORMAL = 0;
    
    /** 删除标志：删除 */
    public static final Integer DEL_FLAG_DELETED = 1;

    /**
     * 获取题目类型描述
     */
    public String getQuestionTypeDesc() {
        if (questionType == null) return "";
        switch (questionType) {
            case 1: return "单选";
            case 2: return "多选";
            case 3: return "填空";
            case 4: return "量表";
            default: return "未知";
        }
    }

    /**
     * 获取是否必答描述
     */
    public String getRequiredDesc() {
        if (isRequired == null) return "";
        return isRequired == 1 ? "是" : "否";
    }

    /**
     * 获取计分方式描述
     */
    public String getScoreTypeDesc() {
        if (scoreType == null) return "";
        switch (scoreType) {
            case 1: return "选项分值";
            case 2: return "自定义";
            default: return "未知";
        }
    }

    /**
     * 是否单选题
     */
    public boolean isSingleChoice() {
        return TYPE_SINGLE_CHOICE.equals(questionType);
    }

    /**
     * 是否多选题
     */
    public boolean isMultipleChoice() {
        return TYPE_MULTIPLE_CHOICE.equals(questionType);
    }

    /**
     * 是否填空题
     */
    public boolean isFillBlank() {
        return TYPE_FILL_BLANK.equals(questionType);
    }

    /**
     * 是否量表题
     */
    public boolean isScale() {
        return TYPE_SCALE.equals(questionType);
    }

    /**
     * 是否必答
     */
    public boolean isRequiredQuestion() {
        return REQUIRED_YES.equals(isRequired);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }
}
