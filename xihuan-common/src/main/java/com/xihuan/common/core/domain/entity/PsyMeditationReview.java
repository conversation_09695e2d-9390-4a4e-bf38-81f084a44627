package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 冥想评价表对象 psy_meditation_review
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyMeditationReview extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    @Excel(name = "评价ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 冥想ID */
    @Excel(name = "冥想ID", cellType = Excel.ColumnType.NUMERIC)
    private Long meditationId;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    private Long userId;

    /** 评价内容 */
    @Excel(name = "评价内容")
    private String content;

    /** 评分（1-5星） */
    @Excel(name = "评分", readConverterExp = "1=1星,2=2星,3=3星,4=4星,5=5星")
    private Integer rating;

    /** 删除标志（0:正常 1:删除） */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    /** 关联的冥想信息 */
    private PsyMeditation meditation;

    /** 关联的用户信息 */
    private SysUser user;
}
