package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 搜索建议表 psy_search_suggestion
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsySearchSuggestion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 搜索建议ID */
    private Long id;

    /** 建议关键词 */
    @Excel(name = "建议关键词")
    private String keyword;

    /** 建议类型 */
    @Excel(name = "建议类型", readConverterExp = "auto=自动,manual=手动,hot=热门")
    private String suggestionType;

    /** 被搜索次数 */
    @Excel(name = "被搜索次数")
    private Integer searchCount;

    /** 优先级 */
    @Excel(name = "优先级")
    private Integer priority;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;
}
