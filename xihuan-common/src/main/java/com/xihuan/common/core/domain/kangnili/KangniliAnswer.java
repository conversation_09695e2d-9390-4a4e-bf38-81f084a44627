package com.xihuan.common.core.domain.kangnili;

import lombok.Data;

import java.util.Date;

/**
 * 测评答案明细实体（对应kangnili_answer表）
 */
@Data
public class KangniliAnswer {
    /**
     * 答案自增主键
     */
    private Long answerId;

    /**
     * 关联的会话ID
     */
    private String sessionId;

    /**
     * 题目ID，关联kangnili_question表
     */
    private Long questionId;

    /**
     * 选中选项ID，关联kangnili_option表
     */
    private Long optionId;

    /**
     * 本题得分（0-2分）
     */
    private Integer score;

    /**
     * 作答耗时（秒），从开始展示到提交答案的时间
     */
    private Integer responseSeconds;

    /**
     * 题目出现顺序（1~N）
     */
    private Integer answerOrder;

    /**
     * 答案创建时间，默认当前时间
     */
    private Date createdAt;
}