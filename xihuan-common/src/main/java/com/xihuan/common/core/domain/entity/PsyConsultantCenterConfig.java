package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 咨询师-咨询中心配置实体类
 * 对应数据库表：psy_consultant_center_config
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyConsultantCenterConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long id;

    /** 咨询师ID */
    private Long consultantId;

    /** 咨询中心ID */
    private Long centerId;

    /** 到该中心所需时间(小时) */
    private BigDecimal arrivalTimeHours;

    /** 是否启用到店时间过滤(0=禁用 1=启用) */
    private Integer enableArrivalFilter;

    /** 是否默认配置 */
    private Integer isDefault;

    /** 状态(0=禁用 1=启用) */
    private Integer status;

    /** 删除标志 */
    private Integer delFlag;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 备注 */
    private String remark;

    // 关联对象（非数据库字段）
    /** 咨询师信息 */
    private com.xihuan.common.core.domain.consultant.PsyConsultant consultant;

    /** 咨询中心信息 */
    private Object center; // 根据实际的咨询中心实体类调整
}
