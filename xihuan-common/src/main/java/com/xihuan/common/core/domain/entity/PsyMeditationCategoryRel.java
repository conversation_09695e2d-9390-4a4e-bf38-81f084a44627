package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

/**
 * 冥想分类关系表对象 psy_meditation_category_rel
 * 
 * <AUTHOR>
 */
@Data
public class PsyMeditationCategoryRel {
    private static final long serialVersionUID = 1L;

    /** 冥想ID */
    @Excel(name = "冥想ID", cellType = Excel.ColumnType.NUMERIC)
    private Long meditationId;

    /** 分类ID */
    @Excel(name = "分类ID", cellType = Excel.ColumnType.NUMERIC)
    private Long categoryId;

    /** 关联的冥想信息 */
    private PsyMeditation meditation;

    /** 关联的分类信息 */
    private PsyCategory category;
}
