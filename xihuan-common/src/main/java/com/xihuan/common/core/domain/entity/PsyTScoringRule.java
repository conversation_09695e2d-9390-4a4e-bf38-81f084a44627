package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 计分规则表 psy_t_scoring_rule
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTScoringRule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    @Excel(name = "规则ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 关联psy_t_scale.id */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 关联psy_t_subscale.id(为空时适用总分) */
    @Excel(name = "分量表ID", cellType = Excel.ColumnType.NUMERIC)
    private Long subscaleId;

    /** 规则类型 */
    @Excel(name = "规则类型", readConverterExp = "RANGE=范围,CUTOFF=临界值")
    @NotBlank(message = "规则类型不能为空")
    private String ruleType;

    /** 最小值 */
    @Excel(name = "最小值")
    private BigDecimal minValue;

    /** 最大值 */
    @Excel(name = "最大值")
    private BigDecimal maxValue;

    /** 临界值 */
    @Excel(name = "临界值")
    private BigDecimal cutoffValue;

    /** 结果标签 */
    @Excel(name = "结果标签")
    @NotBlank(message = "结果标签不能为空")
    @Size(max = 50, message = "结果标签不能超过50个字符")
    private String label;

    /** 解释说明 */
    @Excel(name = "解释说明")
    @NotBlank(message = "解释说明不能为空")
    private String description;

    /** 建议措施 */
    @Excel(name = "建议措施")
    private String suggestion;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    /** 分量表信息 */
    private PsyTSubscale subscale;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 分量表名称 */
    private String subscaleName;

    /** 适用范围描述 */
    private String scopeDesc;

    /** 规则条件描述 */
    private String conditionDesc;

    // 常量定义
    /** 规则类型：范围 */
    public static final String RULE_TYPE_RANGE = "RANGE";
    
    /** 规则类型：临界值 */
    public static final String RULE_TYPE_CUTOFF = "CUTOFF";

    /**
     * 获取规则类型描述
     */
    public String getRuleTypeDesc() {
        if (ruleType == null) return "";
        switch (ruleType) {
            case "RANGE": return "范围";
            case "CUTOFF": return "临界值";
            default: return "未知";
        }
    }

    /**
     * 获取适用范围描述
     */
    public String getScopeDescription() {
        if (subscaleId != null && subscaleName != null) {
            return "分量表：" + subscaleName;
        }
        return "总分";
    }

    /**
     * 获取规则条件描述
     */
    public String getConditionDescription() {
        if (RULE_TYPE_RANGE.equals(ruleType)) {
            if (minValue != null && maxValue != null) {
                return minValue + " ≤ 分数 ≤ " + maxValue;
            } else if (minValue != null) {
                return "分数 ≥ " + minValue;
            } else if (maxValue != null) {
                return "分数 ≤ " + maxValue;
            }
        } else if (RULE_TYPE_CUTOFF.equals(ruleType) && cutoffValue != null) {
            return "分数 ≥ " + cutoffValue;
        }
        return "无条件";
    }

    /**
     * 判断分数是否匹配此规则
     */
    public boolean matchesScore(BigDecimal score) {
        if (score == null) return false;
        
        if (RULE_TYPE_RANGE.equals(ruleType)) {
            boolean minMatch = minValue == null || score.compareTo(minValue) >= 0;
            boolean maxMatch = maxValue == null || score.compareTo(maxValue) <= 0;
            return minMatch && maxMatch;
        } else if (RULE_TYPE_CUTOFF.equals(ruleType)) {
            return cutoffValue != null && score.compareTo(cutoffValue) >= 0;
        }
        
        return false;
    }

    /**
     * 是否范围规则
     */
    public boolean isRangeRule() {
        return RULE_TYPE_RANGE.equals(ruleType);
    }

    /**
     * 是否临界值规则
     */
    public boolean isCutoffRule() {
        return RULE_TYPE_CUTOFF.equals(ruleType);
    }

    /**
     * 是否适用于总分
     */
    public boolean isForTotalScore() {
        return subscaleId == null;
    }

    /**
     * 是否适用于分量表
     */
    public boolean isForSubscale() {
        return subscaleId != null;
    }
}
