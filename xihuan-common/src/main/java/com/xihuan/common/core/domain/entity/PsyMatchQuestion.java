package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 极速匹配问题实体类
 */
@Data
public class PsyMatchQuestion {
    
    /** 主键ID */
    private Long id;
    
    /** 问题标题 */
    @Excel(name = "问题标题")
    private String title;

    /** 问题类型：radio/单选 checkbox/多选 */
    @Excel(name = "问题类型", readConverterExp = "radio=单选,checkbox=多选")
    private String type;

    /** 排序字段 */
    @Excel(name = "排序字段")
    private Integer sort;

    /** 父问题ID（0表示顶级问题） */
    @Excel(name = "父问题ID")
    private Long parentId;

    /** 是否必答(0必答 1可选) */
    @Excel(name = "是否必答", readConverterExp = "0=必答,1=可选")
    private String isRequired;
    
    /** 状态：0启用 1停用 */
    private String status;
    
    /** 删除标志（0存在 2删除） */
    private String delFlag;
    
    /** 创建者 */
    private String createBy;
    
    /** 创建时间 */
    private Date createTime;
    
    /** 更新者 */
    private String updateBy;
    
    /** 更新时间 */
    private Date updateTime;
    
    /** 问题选项列表 */
    private List<PsyMatchQuestionOption> options;
} 