package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 咨询师实际排班表实体类
 * 对应数据库表：psy_time_counselor_schedule
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTimeCounselorSchedule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 排班ID */
    private Long id;

    /** 咨询师ID */
    private Long counselorId;

    /** 排班日期 */
    private LocalDate scheduleDate;

    /** 开始时间 */
    private LocalTime startTime;

    /** 结束时间 */
    private LocalTime endTime;

    /** 咨询中心ID */
    private Long centerId;

    /** 是否工作(1工作 0休假) */
    private Integer isWorking;

    /** 是否模板生成 */
    private Integer isTemplate;

    /** 来源模板ID */
    private Long templateId;

    /** 调班备注 */
    private String remark;

    /** 删除标志 */
    private Integer delFlag;

    // 关联对象（非数据库字段）
    /** 咨询师信息 */
    private com.xihuan.common.core.domain.consultant.PsyConsultant consultant;

    /** 来源模板信息 */
    private PsyTimeScheduleTemplate template;
}
