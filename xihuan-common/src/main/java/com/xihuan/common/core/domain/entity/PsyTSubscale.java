package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 分量表定义表 psy_t_subscale
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTSubscale extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 分量表ID */
    @Excel(name = "分量表ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 关联psy_t_scale.id */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 分量表名称 */
    @Excel(name = "分量表名称")
    @NotBlank(message = "分量表名称不能为空")
    @Size(max = 50, message = "分量表名称不能超过50个字符")
    private String name;

    /** 缩写 */
    @Excel(name = "缩写")
    @Size(max = 20, message = "缩写不能超过20个字符")
    private String alias;

    /** 最小分数 */
    @Excel(name = "最小分数")
    @NotNull(message = "最小分数不能为空")
    private Integer minScore;

    /** 最大分数 */
    @Excel(name = "最大分数")
    @NotNull(message = "最大分数不能为空")
    private Integer maxScore;

    /** 基础分数 */
    @Excel(name = "基础分数")
    private Integer baseScore;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    /** 题目关系列表 */
    private List<PsyTSubscaleQuestionRel> questionRels;

    /** 计分规则列表 */
    private List<PsyTScoringRule> scoringRules;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 量表编码 */
    private String scaleCode;

    /** 题目数量 */
    private Integer questionCount;

    /** 题目ID列表 */
    private List<Long> questionIds;

    /** 题目列表 */
    private List<PsyTQuestion> questions;

    /** 用户得分 */
    private Integer userScore;

    /** 得分百分比 */
    private Double scorePercentage;

    /** 结果等级 */
    private String resultLevel;

    /** 结果描述 */
    private String resultDescription;

    /**
     * 获取分数范围描述
     */
    public String getScoreRangeDesc() {
        if (minScore == null || maxScore == null) return "";
        return minScore + "-" + maxScore + "分";
    }

    /**
     * 计算得分百分比
     */
    public Double calculateScorePercentage(Integer score) {
        if (score == null || minScore == null || maxScore == null) return 0.0;
        if (maxScore.equals(minScore)) return 100.0;
        return ((double) (score - minScore) / (maxScore - minScore)) * 100;
    }

    /**
     * 验证分数是否在有效范围内
     */
    public boolean isValidScore(Integer score) {
        if (score == null || minScore == null || maxScore == null) return false;
        return score >= minScore && score <= maxScore;
    }

    /**
     * 获取分数等级
     */
    public String getScoreLevel(Integer score) {
        if (!isValidScore(score)) return "无效分数";
        
        double percentage = calculateScorePercentage(score);
        if (percentage >= 80) return "高";
        if (percentage >= 60) return "中高";
        if (percentage >= 40) return "中等";
        if (percentage >= 20) return "中低";
        return "低";
    }
}
