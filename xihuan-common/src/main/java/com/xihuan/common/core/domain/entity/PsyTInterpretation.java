package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 测评结果解释表 psy_t_interpretation
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTInterpretation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 解释ID */
    @Excel(name = "解释ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 维度名称(为空表示总分) */
    @Excel(name = "维度名称")
    @Size(max = 50, message = "维度名称不能超过50个字符")
    private String dimension;

    /** 最小分数 */
    @Excel(name = "最小分数")
    @NotNull(message = "最小分数不能为空")
    private BigDecimal minScore;

    /** 最大分数 */
    @Excel(name = "最大分数")
    @NotNull(message = "最大分数不能为空")
    private BigDecimal maxScore;

    /** 等级名称 */
    @Excel(name = "等级名称")
    @NotBlank(message = "等级名称不能为空")
    @Size(max = 50, message = "等级名称不能超过50个字符")
    private String levelName;

    /** 等级描述 */
    @Excel(name = "等级描述")
    private String levelDescription;

    /** 建议 */
    @Excel(name = "建议")
    private String suggestions;

    /** 显示颜色 */
    @Excel(name = "显示颜色")
    @Size(max = 20, message = "显示颜色不能超过20个字符")
    private String color;

    /** 显示顺序 */
    @Excel(name = "显示顺序", cellType = Excel.ColumnType.NUMERIC)
    private Integer orderNum;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 量表编码 */
    private String scaleCode;

    /** 分数范围描述 */
    private String scoreRangeDesc;

    /** 是否匹配当前分数 */
    private Boolean matched;

    /** 分数区间宽度 */
    private BigDecimal scoreRange;

    /** 分数中位数 */
    private BigDecimal scoreMedian;

    /** 等级权重 */
    private Integer levelWeight;

    // 常量定义
    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETE = "1";

    /** 默认颜色：绿色 */
    public static final String COLOR_GREEN = "#52c41a";
    
    /** 默认颜色：黄色 */
    public static final String COLOR_YELLOW = "#faad14";
    
    /** 默认颜色：红色 */
    public static final String COLOR_RED = "#f5222d";
    
    /** 默认颜色：蓝色 */
    public static final String COLOR_BLUE = "#1890ff";

    /**
     * 获取分数范围描述
     */
    public String getScoreRangeDesc() {
        if (minScore != null && maxScore != null) {
            return minScore + " - " + maxScore;
        }
        return "";
    }

    /**
     * 检查分数是否在当前区间内
     */
    public boolean isScoreInRange(BigDecimal score) {
        if (score == null || minScore == null || maxScore == null) {
            return false;
        }
        return score.compareTo(minScore) >= 0 && score.compareTo(maxScore) <= 0;
    }

    /**
     * 获取等级权重（用于排序）
     */
    public Integer getLevelWeight() {
        if (orderNum != null) {
            return orderNum;
        }
        // 根据分数范围计算权重
        if (minScore != null) {
            return minScore.intValue();
        }
        return 0;
    }

    /**
     * 获取分数区间宽度
     */
    public BigDecimal getScoreRange() {
        if (minScore != null && maxScore != null) {
            return maxScore.subtract(minScore);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取分数中位数
     */
    public BigDecimal getScoreMedian() {
        if (minScore != null && maxScore != null) {
            return minScore.add(maxScore).divide(new BigDecimal("2"), 2, BigDecimal.ROUND_HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取默认颜色（根据分数范围）
     */
    public String getDefaultColor() {
        if (color != null && !color.trim().isEmpty()) {
            return color;
        }
        
        // 根据等级名称或分数范围推断颜色
        if (levelName != null) {
            String name = levelName.toLowerCase();
            if (name.contains("优秀") || name.contains("良好") || name.contains("正常")) {
                return COLOR_GREEN;
            } else if (name.contains("一般") || name.contains("中等") || name.contains("轻度")) {
                return COLOR_YELLOW;
            } else if (name.contains("差") || name.contains("严重") || name.contains("重度")) {
                return COLOR_RED;
            }
        }
        
        return COLOR_BLUE; // 默认蓝色
    }

    /**
     * 获取简短描述
     */
    public String getShortDescription() {
        if (levelDescription != null && levelDescription.length() > 50) {
            return levelDescription.substring(0, 47) + "...";
        }
        return levelDescription;
    }

    /**
     * 检查是否为总分解释
     */
    public boolean isTotalScoreInterpretation() {
        return dimension == null || dimension.trim().isEmpty() || "总分".equals(dimension);
    }

    /**
     * 检查是否为维度解释
     */
    public boolean isDimensionInterpretation() {
        return !isTotalScoreInterpretation();
    }
}
