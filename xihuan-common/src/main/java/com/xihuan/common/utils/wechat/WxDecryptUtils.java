package com.xihuan.common.utils.wechat;// WxDecryptUtils.java（存放在 ruoyi-system/src/main/java/com/ruoyi/system/wechat/utils/）



import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * 微信解密工具类
 * 功能：
 * 1. 解密用户信息
 * 2. 解密手机号
 * 3. 数据完整性校验
 */
public class WxDecryptUtils {

    /**
     * 解密用户信息
     * @param encryptedData 加密数据
     * @param iv 加密算法的初始向量
     * @param sessionKey 微信会话密钥
     * @return 解密后的用户信息JSON对象
     * @throws Exception 解密失败时抛出异常
     */
    public static JSONObject decryptUserInfo(String encryptedData,
                                             String iv,
                                             String sessionKey)
            throws Exception {

        // Base64解码处理
        byte[] sessionKeyBytes = Base64.decodeBase64(sessionKey);
        byte[] ivBytes = Base64.decodeBase64(iv);
        byte[] encryptedDataBytes = Base64.decodeBase64(encryptedData);

        // 初始化AES解密器
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(sessionKeyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

        // 执行解密
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(encryptedDataBytes);

        // 转换并验证数据
        String result = new String(decrypted, StandardCharsets.UTF_8);
        JSONObject userInfo = JSONObject.parseObject(result);
        // 验证数据有效性
        validateData(userInfo);
        System.out.println(userInfo);
        return userInfo;
    }

    /**
     * 验证数据完整性
     * @param userInfo 解密后的用户信息
     * @throws SecurityException 数据校验失败时抛出
     */
    private static void validateData(JSONObject userInfo) {
        JSONObject watermark = userInfo.getJSONObject("watermark");
        String appid = watermark.getString("appid");
        System.out.println(appid);
        // 替换为实际APPID
        if (!"wx8df6fcafd17d7348".equals(appid)) {
            throw new SecurityException("数据来源验证失败");
        }
    }
}