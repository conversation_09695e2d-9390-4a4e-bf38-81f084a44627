package com.xihuan.common.utils.wechat;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
public class SmsUtil {

    @Value("${aliyun.sms.accessKeyId}")
    private static String accessKeyId;

    @Value("${aliyun.sms.accessKeySecret}")
    private static String accessKeySecret;

    @Value("${aliyun.sms.signName}")
    private static String signName;

    @Value("${aliyun.sms.templateCode}")
    private static String templateCode;
    public static boolean sendCode(String phone, String code) {
        try {
            Config config = new Config()
                    .setAccessKeyId("LTAI5tLK5JNrjSDJxMGxUWtT")
                    .setAccessKeySecret("******************************")
                    .setEndpoint("dysmsapi.aliyuncs.com");

            Client client = new Client(config);

            JSONObject param = new JSONObject();
            param.put("code", code);

            SendSmsRequest request = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName("熙桓心理")
                    .setTemplateCode("SMS_230270626")
                    .setTemplateParam(param.toJSONString());

            RuntimeOptions runtime = new RuntimeOptions();
            runtime.setConnectTimeout(5000);
            runtime.setReadTimeout(5000);

            SendSmsResponse response = client.sendSmsWithOptions(request, runtime);
            return "OK".equalsIgnoreCase(response.getBody().getCode());
        } catch (TeaException e) {
            log.error("阿里云短信错误[Code={}]: {}", e, e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("系统异常", e);
            return false;
        }
    }
}