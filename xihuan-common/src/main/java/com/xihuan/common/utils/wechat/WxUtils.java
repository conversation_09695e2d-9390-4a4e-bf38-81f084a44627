package com.xihuan.common.utils.wechat;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xihuan.common.utils.http.HttpUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class WxUtils {

    /**
     * 微信小程序appId（从配置文件中注入）
     * 说明：在application.yml中配置wx.app.appid的值
     */
    @Value("${wx.app.appid}")
    private String appid;

    /**
     * 微信小程序appSecret（从配置文件中注入）
     * 说明：在application.yml中配置wx.app.secret的值
     */
    @Value("${wx.app.secret}")
    private String secret;

    /**
     * 授权类型 - 固定为authorization_code
     */
    @Value("${wx.app.grant_type}")
    private String grantType;

    /**
     * 微信登录凭证校验接口地址
     */
    @Value("${wx.app.session_url}")
    private String sessionUrl;

    /**
     * 微信登录凭证校验（code转session）
     *
     * @param code 小程序端通过wx.login()获取的临时登录凭证
     * @return JSONObject 包含微信返回的完整session信息
     * @throws WxServiceException 微信接口返回错误时抛出
     *
     * 返回数据结构示例：
     * {
     *   "openid": "用户唯一标识",
     *   "session_key": "会话密钥",
     *   "unionid": "联合ID（如果满足条件）",
     *   "errcode": 0,
     *   "errmsg": "ok"
     * }
     *
     * 典型错误码：
     * - 40029: code无效
     * - 45011: API调用太频繁
     * - 40226: 高风险等级用户
     */
    public JSONObject code2Session(String code) {
        String url = String.format("%s?appid=%s&secret=%s&js_code=%s&grant_type=%s",
                sessionUrl, appid, secret, code, grantType);

        // 发送get请求到微信
        String result = HttpUtils.sendGet(url);
        System.out.println(JSON.parseObject(result));
        return JSON.parseObject(result);
    }

    /**
     * 获取ACCESS_TOKEN
     *
     * @return
     */
    public JSONObject getAccessToken() {
        String url = String.format("%s?appid=%s&secret=%s&grant_type=%s",
                "https://api.weixin.qq.com/cgi-bin/token", appid, secret, "client_credential");

        // 发送get请求到微信
        String result = HttpUtils.sendGet(url);
        System.out.println(JSON.parseObject(result));
        return JSON.parseObject(result);

    }

    /**
     * 获取手机号
     *
     * @param code 手机号获取凭证
     * @return JSONObject 包含手机号信息的JSON对象
     */
    public JSONObject getPhoneNumber(String code) {
        // 1. 获取access_token
        JSONObject tokenResponse = getAccessToken();
        if (tokenResponse == null || !tokenResponse.containsKey("access_token")) {
            throw new RuntimeException("获取access_token失败");
        }
        String accessToken = tokenResponse.getString("access_token");

        // 2. 构建请求URL
        String url = String.format("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s", accessToken);

        // 3. 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("code", code);

        // 4. 发送POST请求到微信API
        String result = HttpUtils.sendPost(url, requestBody.toString(), "application/json");
        JSONObject response = JSON.parseObject(result);

        // 5. 检查响应
        if (response.getIntValue("errcode") != 0) {
            throw new RuntimeException("获取手机号失败：" + response.getString("errmsg"));
        }

        return response;
    }
}