package com.xihuan.common.exception;
// exception/BusinessException.java
public class BusinessException extends RuntimeException {
    private final String errorCode;
    private final String detailMessage;

    public BusinessException(String errorCode, String detailMessage) {
        super("[" + errorCode + "] " + detailMessage);
        this.errorCode = errorCode;
        this.detailMessage = detailMessage;
    }

    public BusinessException(String errorCode, String detailMessage, Throwable cause) {
        super("[" + errorCode + "] " + detailMessage, cause);
        this.errorCode = errorCode;
        this.detailMessage = detailMessage;
    }

    // 支持带参数的错误消息（示例）
    public BusinessException(String errorCode, String template, Object... args) {
        super("[" + errorCode + "] " + String.format(template, args));
        this.errorCode = errorCode;
        this.detailMessage = String.format(template, args);
    }

    // Getters
    public String getErrorCode() {
        return errorCode;
    }

    public String getDetailMessage() {
        return detailMessage;
    }
}